<?php

namespace App\Enums;

use Illuminate\Support\Facades\DB;

abstract class DroptiendaPlan
{
  const PRODUCT_AMOUNT = 500;

  const SPECIAL_USER_IDS = [
    3487,
  ];

  const SPECIAL_USER_PRODUCT_AMOUNT = [
    '3487' => 2874,
  ];

  public static function DtPlanProductAmount($plan_id = 0) {
    $plan_product_amount = DB::table('import_plans')->where('id', $plan_id)->value('product_amount');
    return !empty($plan_product_amount) ? $plan_product_amount : self::PRODUCT_AMOUNT;
  }
}
