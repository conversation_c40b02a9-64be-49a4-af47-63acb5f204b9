<?php

namespace App\Enums\Marketplace;

class ApiResources {
    const ADDRESSES = [
        'AUTH'               => "https://portal.internet-bikes.com/api/twm/auth/authenticate",
        'LIST_ALL_ORDERS'    => "https://portal.internet-bikes.com/api/twm/orders",
        'FETCH_ORDER_BY_ID'  => "https://portal.internet-bikes.com/api/twm/orders/", //parameter = order_id(int)
        "FETCH_ORDERS_BY_LAST_N_DAYS" => "https://portal.internet-bikes.com/api/twm/orders/latest/", //parameter = days(int)
        'SUBMIT_AN_ORDER'    => "https://portal.internet-bikes.com/api/twm/orders",

        "LIST_ALL_PRODUCTS"  => "https://portal.internet-bikes.com/api/twm/products",
        "FETCH_PRODUCT_BY_ID"=> "https://portal.internet-bikes.com/api/twm/products/", //parameter = product_id(int)
        "LIST_ALL_SEGMENT"   => "https://portal.internet-bikes.com/api/twm/segments",
        "LIST_PRODUCTS_PER_SEGMENT" => "https://portal.internet-bikes.com/api/twm/segment/", //parameter = segment_id(int)

        "V1_FETCH_CHANGED_PRODUCTS_FOR_LAST_N_DAYS" => "https://portal.internet-bikes.com/api/twm/segment/1/changes/1",
        "V2_FETCH_CHANGED_PRODUCTS_FOR_LAST_N_DAYS" => "https://portal.internet-bikes.com/api/twm/v2/segment/1/changes/1",

        "FETCH_STOCK_CHANGES_IN_LAST_MINUTES" => "https://portal.internet-bikes.com/api/twm/products/changes/", //parameter = product_id(int)
        "FETCH_STOCK_PER_PRODUCT"             => "https://portal.internet-bikes.com/api/twm/stock/", //parameter = product_id(int)
        "LIST_ALL_TRACKING_CODES_FOR_USER"    => "https://portal.internet-bikes.com/api/twm/trackingcodes",
        "FETCH_TRACKING_CODE_BY_ID"           => "https://portal.internet-bikes.com/api/twm/trackingcodes/", //parameter = ordere_id(int)
    ];
}
