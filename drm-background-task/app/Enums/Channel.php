<?php


namespace App\Enums;

abstract class Channel
{
    const GAMBIO = 1;
    const _GAMBIO = 'gambio';

    const LENGOW = 2;
    const _LENGOW = 'lengow';

    const YATEGO = 3;
    const _YATEGO = 'yatego';

    const EBAY  = 4;
    const _EBAY = 'e_bay';

    const AMAZON  = 5;
    const _AMAZON = 'amazon';

    const SHOPIFY  = 6;
    const _SHOPIFY = 'shopify';

    const WOOCOMMERCE = 7;
    const _WOOCOMMERCE = 'woo_commerce';
    const CLOUSALE = 8;
    const _CLOUSALE = 'clousale';

    const CHRONO24 = 9;
    const _CHRONO24 = 'chrono24';

    const DROPTIENDA = 10;
    const _DROPTIENDA = 'droptienda';

    const ETSY  = 11;
    const _ETSY= 'etsy';

    const OTTO  = 12;
    const _OTTO = 'otto';

    const KAUFLAND = 13;
    const _KAUFLAND = 'kaufland';

    const CHECK24 = 14;
    const _CHECK24 = 'Check24';

    const DECATHLON = 15;
    const _DECATHLON = 'decathlon';

    const TRADEBYTE = 16;
    const _TRADEBYTE = 'trade_byte';

    const SPRINTER = 17;
    const _SPRINTER = 'sprinter';

    const TIENDANIMAL = 18;
    const _TIENDANIMAL = 'tiendanimal';

    const MEDIAMARKT = 19;
    const _MEDIAMARKT = 'mediamarkt';

    const COLIZEY = 20;
    const _COLIZEY = 'colizey';

    const LIMANGO = 21;
    const _LIMANGO = 'limango';

    const CONRAD = 22;
    const _CONRAD = 'conrad';

    const FRESSNAPF = 23;
    const _FRESSNAPF = 'Fressnapf';

    const BLANK = 24;
    const _BLANK = 'blank';

    const VOLKNER = 25;
    const _VOLKNER = 'volkner';

    const MANOR = 26;
    const _MANOR = 'manor';

    const XXXLUTZ = 27;
    const _XXXLUTZ = 'xxxlutz';

    const PERFUMES_CLUB = 28;
    const _PERFUMES_CLUB = 'perfumes_club';

    const HOME24 = 29;
    const _HOME24 = 'home24';

    const ALLTRICKS = 30;
    const _ALLTRICKS = 'alltricks';

    const GALAXUS = 31;
    const _GALAXUS = 'galaxus';

    const CLUBE_FASHION = 32;
    const _CLUBE_FASHION = 'clube_fashion';

    const ZOOPLUS = 33;
    const _ZOOPLUS = 'zooplus';

    const PSS = 34;
    const _PSS = 'pss';

    const BIGBANG = 35;
    const _BIGBANG = 'bigbang';

    const BRICODEPOT = 36;
    const _BRICODEPOT = 'bricodepot';

    const HORNBACH = 37;
    const _HORNBACH = 'hornbach';

    const PLANETAHUERTO = 38;
    const _PLANETAHUERTO = 'planetahuerto';

    const CARREFOUR = 39;
    const _CARREFOUR = 'carrefour';

    const MIRAKL_CONNECT = 40;
    const _MIRAKL_CONNECT = 'mirakl_connect';

    const MAP = [
        self::WOOCOMMERCE => self::_WOOCOMMERCE,
        self::EBAY => self::_EBAY,
        self::AMAZON => self::_AMAZON,
        self::GAMBIO => self::_GAMBIO,
        self::SHOPIFY => self::_SHOPIFY,
        self::ETSY => self::_ETSY,
        self::OTTO => self::_OTTO,
        self::KAUFLAND => self::_KAUFLAND,
        self::DECATHLON => self::_DECATHLON,
        self::TRADEBYTE => self::_TRADEBYTE,
        self::SPRINTER => self::_SPRINTER,
        self::TIENDANIMAL => self::_TIENDANIMAL,
        self::MEDIAMARKT => self::_MEDIAMARKT,
        self::COLIZEY => self::_COLIZEY,
        self::LIMANGO => self::_LIMANGO,
        self::CONRAD => self::_CONRAD,
        self::FRESSNAPF => self::_FRESSNAPF,
        self::VOLKNER => self::_VOLKNER,
        self::MANOR => self::_MANOR,
        self::XXXLUTZ => self::_XXXLUTZ,
        self::PERFUMES_CLUB => self::_PERFUMES_CLUB,
        self::HOME24 => self::_HOME24,
        self::ALLTRICKS => self::_ALLTRICKS,
        self::GALAXUS => self::_GALAXUS,
        self::CLUBE_FASHION => self::_CLUBE_FASHION,
        self::ZOOPLUS => self::_ZOOPLUS,
        self::PSS => self::_PSS,
        self::BIGBANG => self::_BIGBANG,
        self::BRICODEPOT => self::_BRICODEPOT,
        self::HORNBACH => self::_HORNBACH,
        self::PLANETAHUERTO => self::_PLANETAHUERTO,
        self::CARREFOUR => self::_CARREFOUR,
        self::MIRAKL_CONNECT => self::_MIRAKL_CONNECT,
    ];

    const ALL = [
        self::GAMBIO,
        self::LENGOW,
        self::YATEGO,
        self::EBAY,
        self::AMAZON,
        self::ETSY,
        self::SHOPIFY,
        self::WOOCOMMERCE,
        self::CLOUSALE,
        self::CHRONO24,
        self::DROPTIENDA,
        self::OTTO,
        self::KAUFLAND,
        self::CHECK24,
        self::DECATHLON,
        self::TRADEBYTE,
        self::SPRINTER,
        self::TIENDANIMAL,
        self::MEDIAMARKT,
        self::COLIZEY,
        self::LIMANGO,
        self::CONRAD,
        self::FRESSNAPF,
        self::BLANK,
        self::VOLKNER,
        self::MANOR,
        self::XXXLUTZ,
        self::PERFUMES_CLUB,
        self::HOME24,
        self::ALLTRICKS,
        self::GALAXUS,
        self::CLUBE_FASHION,
        self::ZOOPLUS,
        self::PSS,
        self::BIGBANG,
        self::BRICODEPOT,
        self::HORNBACH,
        self::PLANETAHUERTO,
        self::CARREFOUR,
        self::MIRAKL_CONNECT,
    ];


    const COLOR = [
        self::GAMBIO => '#1abc9c',
        self::LENGOW => '#2980b9',
        self::YATEGO => '#8e44ad',
        self::EBAY => '#2c3e50',
        self::AMAZON => '#f1c40f',
        self::ETSY => '#e74c3c',
        self::SHOPIFY => '#d35400',
        self::WOOCOMMERCE => '#84817a',
        self::CLOUSALE => '#b33939',
        self::CHRONO24 => '#2c2c54',
        self::DROPTIENDA => '#fd6500',
        self::OTTO => '#ccae62',
        self::KAUFLAND => '#6D214F',
        self::COLIZEY => '#6D214F',
    ];

    const REST_API = [
        self::EBAY,
//        self::AMAZON,
        self::SHOPIFY,
        self::WOOCOMMERCE,
        self::ETSY,
        self::OTTO,
        self::KAUFLAND,
        self::DECATHLON,
        self::SPRINTER,
        self::TIENDANIMAL,
        self::MEDIAMARKT,
        self::CONRAD,
        self::FRESSNAPF,
        self::VOLKNER,
        self::MANOR,
        self::XXXLUTZ,
        self::PERFUMES_CLUB,
        self::HOME24,
        self::ALLTRICKS,
        self::CLUBE_FASHION,
        self::ZOOPLUS,
        self::PSS,
        self::BIGBANG,
        self::BRICODEPOT,
        self::HORNBACH,
        self::PLANETAHUERTO,
        self::CARREFOUR,
        self::MIRAKL_CONNECT,
    ];

    const MAPPING_CHANNELS = [
        self::ETSY,
        self::EBAY,
        self::AMAZON,
        self::YATEGO,
        self::OTTO,
        self::KAUFLAND
    ];

    const CSV_CHANNELS = [
        self::LENGOW,
        self::YATEGO,
        self::CLOUSALE,
        self::CHRONO24,
        self::AMAZON,
        self::CHECK24,
//        self::DECATHLON,
        self::TRADEBYTE,
        self::COLIZEY,
        self::LIMANGO,
        self::GALAXUS
    ];

    const CATEGORY_LIMITS = [
        self::ETSY => 1,
        self::EBAY => 1,
        self::AMAZON => 1,
        self::YATEGO => 3,
        self::OTTO => 1,
        self::KAUFLAND => 1,
        self::GALAXUS => 1,
    ];

    const MP_BLACKLIST = [
        self::GAMBIO,
        // self::SHOPIFY,
//        self::WOOCOMMERCE,
    ];

    const MP_USER_WHITELIST = [
        self::SHOPIFY => [
            2591,
            2766,
            96
        ],
        self::EBAY => [],
        self::AMAZON => [],
        self::WOOCOMMERCE => [98,2900],
        self::ETSY => [],
        self::OTTO => [],
        self::KAUFLAND => [],
        self::LENGOW => [],
        self::YATEGO => [],
        self::CLOUSALE => [],
        self::CHRONO24 => [],
        self::GAMBIO => [],
        self::DROPTIENDA => [],
        self::CHECK24 => [],
        self::TRADEBYTE => [],
        self::LIMANGO => [],
    ];

    const WITHOUT_CATEGORY = [
        self::AMAZON,
        self::CLOUSALE,
        self::CHRONO24,
        self::TRADEBYTE,
        self::DECATHLON,
        self::SPRINTER,
        self::MEDIAMARKT,
        self::TIENDANIMAL,
        self::CONRAD,
        self::FRESSNAPF,
        self::VOLKNER,
        self::MANOR,
        self::XXXLUTZ,
        self::PERFUMES_CLUB,
        self::HOME24,
        self::ALLTRICKS,
        self::CLUBE_FASHION,
        self::ZOOPLUS,
        self::PSS,
        self::BIGBANG,
        self::BRICODEPOT,
        self::HORNBACH,
        self::PLANETAHUERTO,
        self::CARREFOUR,
    ];

    const MIRAKL_CHANNELS = [
        self::DECATHLON,
        self::SPRINTER,
        self::MEDIAMARKT,
        self::TIENDANIMAL,
        self::CONRAD,
        self::FRESSNAPF,
//        self::LIMANGO,
        self::VOLKNER,
        self::MANOR,
        self::XXXLUTZ,
        self::PERFUMES_CLUB,
        self::HOME24,
        self::ALLTRICKS,
        self::CLUBE_FASHION,
        self::ZOOPLUS,
        self::PSS,
        self::BIGBANG,
        self::BRICODEPOT,
        self::HORNBACH,
        self::PLANETAHUERTO,
        self::CARREFOUR,
    ];

    const REQUIRED_ADDITIONAL = [
        self::EBAY,
        self::ETSY,
        self::KAUFLAND
    ];

    const EXPORT = 'export';
    const DELETE = 'delete';

    const APP_ID = [
        self::GAMBIO => 95,
        self::LENGOW => 96,
        self::YATEGO => 97,
        self::EBAY => 98,
        self::AMAZON => 99,
        self::SHOPIFY => 100,
        self::WOOCOMMERCE => 101,
        self::CLOUSALE => 102,
        self::CHRONO24 => 103,
        self::DROPTIENDA => 104,
        self::ETSY => 105,
        self::OTTO => 106,
        self::KAUFLAND => 107,
        self::CHECK24 => 108,
    ];

    const CHEAPEST_APPLICABLE_CHANNELS_PREV = [
        self::EBAY, // 4
        self::AMAZON, // 5
        self::SHOPIFY, // 6
        self::DROPTIENDA, // 10
    ];

    const CREATE = 1;
    const UPDATE = 2;
    const DISCONNECT = 3;
    const DEL = 4;
    const _CREATE = 'create';
    const _UPDATE = 'update';
    const _DISCONNECT = 'disconnect';
    const _DEL = 'delete';

    const EVENT_MAPPING = [
        self::_CREATE => self::CREATE,
        self::_UPDATE => self::UPDATE,
        self::_DEL => self::DEL,
        self::_DISCONNECT => self::DISCONNECT
    ];

    const ACTION_MAPPING = [
        self::_CREATE => Actions::EXPORT_PRODUCT,
        self::_UPDATE => Actions::EXPORT_PRODUCT,
        self::_DEL => Actions::DELETE_PRODUCT,
        self::_DISCONNECT => Actions::DISCONNECT_PRODUCT
    ];

    const CHEAPEST_APPLICABLE_CHANNELS = self::ALL;
    const INACTIVE_SHOPS = [485,549,462,214,235,275,502,484,647,711,748,763,818,824,711,748,763,818,824,133,394,401,1017,1169];

    const OTTO_NEW_SHOPS = [];
    const REMOVED_CHANNELS = [
        self::GAMBIO => self::_GAMBIO,
        self::ETSY => self::_ETSY,
        // self::DECATHLON => self::_DECATHLON,
        // 'Cannaable'=> 'Cannaable',
        // 'dealo Direktkauf'=> 'dealo Direktkauf',
        // 'Wohlauf'=> 'Wohlauf',
        // 'B2B-Bedarf'=> 'B2B-Bedarf',
        // 'RelaVida'=> 'RelaVida',
    ];

}
