<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Telegram\TelegramChannel;
use NotificationChannels\Telegram\TelegramMessage;
use NotificationChannels\Telegram\TelegramFile;

class DRMTelegramNotification extends Notification
{
    use Queueable;
    protected $message;
    protected $hook;
    protected $url;
    private $file;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($message, $hook="", $url="", $file="")
    {
        $this->message = $message;
        $this->hook = $hook;
        $this->url = $url;
        $this->file = $file;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [TelegramChannel::class];
    }

    public function toTelegram($notifiable){
        if (!empty($notifiable->telegram_user_token)) {
            $telegramUrl = 'https://api.telegram.org/bot';
            if (empty($notifiable->telegram_channel_id)) { // if no chat id then generate one                
                $authorization = @file_get_contents($telegramUrl.$notifiable->telegram_user_token.'/getme');
                if (empty($authorization))
                    return false;
                $authorization = json_decode($authorization,true);
        
                if(!$authorization['ok'])
                    return false;
                $response = json_decode(file_get_contents($telegramUrl.$notifiable->telegram_user_token.'/getupdates'),true);
                if (!empty($response['result'])) {
                    $chatId = $response['result'][0]['message']['chat']['id'];
                    DB::table('cms_users')->where('id','=',$notifiable->id)
                    ->update(['telegram_channel_id'=>$chatId]);
                }else
                    return false;
            }else{
                $chatId = $notifiable->telegram_channel_id;
            }

            if (!empty($this->url) && $this->url != '#') {
                $btn = '<a href="'.$this->url.'">View Details</a>';
                $txt = urlencode($this->message." \n ".$btn." \n ");
            }else{
                $txt = urlencode($this->message." \n ");
            }

            if (empty($this->file)) {
                return file_get_contents($telegramUrl.$notifiable->telegram_user_token.'/sendMessage?chat_id='.$chatId.'&text='.$txt.'&parse_mode=HTML');
            }
            
            return file_get_contents($telegramUrl.$notifiable->telegram_user_token.'/sendDocument?chat_id='.$chatId.'&document='.$this->file.'&caption='.$txt.'&parse_mode=HTML');
        }
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->line('The introduction to the notification.')
                    ->action('Notification Action', url('/'))
                    ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'data' => $this->message,
            'hook' => $this->hook,
            'url' => $this->url,
        ];
    }
}