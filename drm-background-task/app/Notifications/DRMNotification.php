<?php

namespace App\Notifications;

use App\User;
use App\UserNotificationSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use NotificationChannels\Telegram\TelegramChannel;
// use App\Notifications\Channels\FirebaseChannel;
use App\Notifications\Channels\DrmMailChannel;

class DRMNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    protected $message;
    protected $hook;
    protected $url;
    protected $email_address;
    private $mail_data;
    private $file;

    public $body;
    public $subject;
    public $have_send_permission = true;

    private $hook_id = null;

    public function __construct($message, $hook = "", $url = "", $file = "")
    {
        $this->message = $message;
        $this->hook = $hook;
        $this->url = $url;
        $this->file = $file;

        if ($hook) {
            $db_hook = DB::table('notification_trigger')->where('hook', $hook)->select('status', 'title', 'id')->first();
            if ($db_hook) {
                $this->hook_id = $db_hook->id;
                if ($db_hook->status == 0) {
                    $this->message = generateNotificationMessage($db_hook->title, $message);
                } else {
                    $this->have_send_permission = false;
                }
            }
        }

        $this->mail_data = DRMParseMailTemplate([
            'notification_message' => $message,
            'notification_url' => $url,
        ], 'drm_notification_email');

        $this->body = $this->mail_data['body'];
        $this->subject = $this->mail_data['subject'];
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        if (!$this->have_send_permission) return [];

        $drivers = getNotificationDriver($this->hook, $notifiable->id);

        // // Has mobile
        // $hasMobile = $this->hook_id ? DB::table('user_mobile_notification_triggers')
        // ->where('hook_id', $this->hook_id)
        // ->where('user_id', $notifiable->id)
        // ->where('is_active', 1)
        // ->exists() : false;
        
        // if($hasMobile)
        // {
        //     $drivers['mobile_app'] = true;
        // }

        $driverArray = $this->getDriver($drivers, false);

        if (!is_null($driverArray)) {
            $driverArray[] = 'database';
            return array_unique($driverArray);
        }

        $driverArray = $this->getDriver($drivers);

        if (!is_null($driverArray)) {
            $driverArray[] = 'database';
            return array_unique($driverArray);
        }

        $user_email = User::where(['id' => $notifiable->id, 'email_notification' => 1])->first()->email;

        if ($user_email) {
            return ['mail', 'database'];
        } else {
            return ['database'];
        }
    }

    public function toTelegram($notifiable)
    {
        if (!empty($notifiable->telegram_user_token)) {
            $telegramUrl = 'https://api.telegram.org/bot';
            if (empty($notifiable->telegram_channel_id)) { // if no chat id then generate one
                $authorization = @file_get_contents($telegramUrl . $notifiable->telegram_user_token . '/getme');
                if (empty($authorization))
                    return false;
                $authorization = json_decode($authorization, true);

                if (!$authorization['ok'])
                    return false;
                $response = json_decode(@file_get_contents($telegramUrl . $notifiable->telegram_user_token . '/getupdates'), true);
                if (!empty($response['result'])) {
                    $chatId = $response['result'][0]['message']['chat']['id'];
                    DB::table('cms_users')->where('id', '=', $notifiable->id)
                        ->update(['telegram_channel_id' => $chatId]);
                } else
                    return false;
            } else {
                $chatId = $notifiable->telegram_channel_id;
            }

            if (!empty($this->url) && $this->url != '#') {
                $btn = '<a href="' . $this->url . '">View Details</a>';
                $txt = urlencode($this->message . " \n " . $btn . " \n ");
            } else {
                $txt = urlencode($this->message . " \n ");
            }

            if (empty($this->file)) {
                return @file_get_contents($telegramUrl . $notifiable->telegram_user_token . '/sendMessage?chat_id=' . $chatId . '&text=' . $txt . '&parse_mode=HTML');
            }

            return @file_get_contents($telegramUrl . $notifiable->telegram_user_token . '/sendDocument?chat_id=' . $chatId . '&document=' . $this->file . '&caption=' . $txt . '&parse_mode=HTML');
        }
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toMail($notifiable)
    {
        $sentEmail = false;
        $sidebar = isHookRemainOnSidebar($this->hook);
        if (!empty($sidebar)) { // sidebar custom email
            $settings = UserNotificationSetting::where('user_id', '=', $notifiable->id)->where('sidebar_id', '=', $sidebar->sidebar_pos)->whereNotNull('notify_email')->first();
            if (!empty($settings)) {
                $notifiable->email = $settings->notify_email;
                $sentEmail = true;
            }
        } else { // heeader notification email
            if (!empty($notifiable->notify_email)) {
                $notifiable->email = $notifiable->notify_email;
                $sentEmail = true;
            }
        }

        if ($sentEmail) {
            return [
                'email' => $notifiable->email,
                'subject' => $this->subject,
                'view' => 'emails.notification',
                'body' => $this->body,
            ];
        }

        return [];

        // return (new MailMessage)->subject($this->subject)->view('emails.notification', ['body' => $this->body]); //RAKIB
    }


    //send notification in devices
    public function toFirebase($notifiable){
        return [
            'subject' => $this->subject,
            'url' => $this->url,
            'message' => $this->message,
            'body' => $this->body,
            'hook_id' => $this->hook_id,
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'data' => mb_convert_encoding($this->message, 'UTF-8', 'UTF-8'),
            'hook' => (string)$this->hook,
            'hook_id' => (int)$this->hook_id,
            'room_id' => 0,
            'url' => (string)$this->url,
        ];
    }

    private function getDriver($drivers,$header = true)
    {
        // $telegram = 'telegram';
        $mail = 'mail';
        // $mobile_app = 'mobile_app';

        if ($header) {
            // $telegram = 'header_telegram';
            $mail = 'header_email';
        }

        $driver_arr = [];
        // if(array_key_exists($telegram, $drivers)){
        //     $driver_arr[] = TelegramChannel::class;
        // }

        if(array_key_exists($mail, $drivers)){
            $driver_arr[] = DrmMailChannel::class;
        }

        // if(array_key_exists($mobile_app, $drivers)){
        //     $driver_arr[] = FirebaseChannel::class;
        // }

        return empty($driver_arr)? null : $driver_arr;
    }
}
