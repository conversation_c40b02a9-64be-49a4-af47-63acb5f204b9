<?php
namespace App\Notifications\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use App\Services\Notification\FirebasePushNotification;

class FirebaseChannel
{
    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        try{

            // api_key
            $api_key = config('app.google_firebase_token');
            if(empty($api_key)) return;

            $message = $notification->toFirebase($notifiable);

            if (empty($message['hook_id'])) {
                return;
            }

            $mobile = DB::table('user_mobile_notification_triggers')
            ->where('hook_id', $message['hook_id'])
            ->where('user_id', $notifiable->id)
            ->where('is_active', 1)
            ->select('id', 'sound')
            ->first();

            if(blank($mobile)) return;

            (new FirebasePushNotification)->send($notifiable->id, $message, $mobile->sound ?? 'default');

        } catch(\Exception $e) {}
    }
}