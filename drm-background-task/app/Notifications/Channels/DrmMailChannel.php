<?php
namespace App\Notifications\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Exception;

class DrmMailChannel
{
	/**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        try{
            $message = $notification->toMail($notifiable);
            if(empty($message)) return;

            $email = $message['email'];
            $subject = $message['subject'];
            $view = $message['view'];
            $body = $message['body'];

            $emails = $this->geMail($email);
            if(empty($emails)) return;

            foreach($emails as $to)
            {
                $this->sendTo($to, $subject, $view, $body);
            }

            return [
                'success' => true,
                'status' => 200,
                'message' => 'Send to Mail!',
            ];
        }catch(Exception $e){
            return [
                'success' => false,
                'status' => 400,
                'message' => $e->getMessage()
            ];
        }
    }


    // Send mail
    private function sendTo($to, $subject, $view, $body)
    {
        try {
            Mail::mailer('campaign')->send($view, ['body' => $body], function ($messages) use ($subject, $to) {
                $messages->getHeaders()->addTextHeader('X-CSA-Complaints', '<EMAIL>');
                $messages->getHeaders()->addTextHeader('List-Help', 'mailto:<EMAIL>');
                $messages->to($to);
                $messages->subject($subject);
            });
        } catch(Exception $e) {}
    }

    // Get mail
    private function geMail($email): array
    {
        $email = trim($email);
        $email = preg_replace('/\s+/', ',', $email);
        $email = trim($email);

        $emails = [];
        if(strpos($email, ',') !== false)
        {
            $emails = explode(',', $email);
        } else {
            $emails = [$email];
        }

        $emails = array_map('trim', $emails);
        $emails = array_filter($emails);
        $emails = array_filter($emails, function($item) {
            return filter_var($item, FILTER_VALIDATE_EMAIL);
        });

        return array_unique($emails);
    }
}