<?php
namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use App\UpcomingInvoice;
use App\NewOrder;

class InvoicesExport implements FromView
{
    use Exportable;

	protected $invoices;
	protected $orders;

    public function __construct(array $data)
    {
        $this->invoices = UpcomingInvoice::with(['supplier', 'invoice_category'])->find($data['invoice']);
        $this->orders = NewOrder::with(['customer', 'client'])->find($data['order']);
    }

    public function view(): View
    {
        return view('admin.accounting.exports.xls', [
            'invoices' => $this->invoices,
            'orders' => $this->orders,
        ]);
    }
}