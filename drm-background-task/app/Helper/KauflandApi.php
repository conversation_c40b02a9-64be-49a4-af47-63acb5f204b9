<?php
namespace App\Helper;

class KauflandApi {

    public $secretKey, $clientKey,$baseUrl = 'https://sellerapi.kaufland.com/v2';

    public function __construct($secretKey, $clientKey)
    {
        $this->secretKey = $secretKey;
        $this->clientKey = $clientKey;

    }

    public function getOrder($last_order_date,$offsetOrder){

        $retrievingOrderUrl = $this->baseUrl. '/order-units';
        $fulfillment_type = 'fulfilled_by_merchant';
        $params = [
            // 'status'     => 'need_to_be_sent',//need_to_be_sent
            'limit'      => 30,
            'ts_created_from_iso' => date_format($last_order_date,'Y-m-d\TH:i:s\Z'),
            'offset' => $offsetOrder,
            'fulfillment_type'=>$fulfillment_type

        ];
        $retrievingOrderUrl .= '?' . http_build_query($params);
        $getApiOrderResponse = $this->headerAndResponse('GET', $retrievingOrderUrl,'');
        return $getApiOrderResponse['data'];
    }
    public function ApiMonitoring(){
      $apiPingUrl = $this->baseUrl. '/status/ping/';
      $getApiPingResponse = $this->headerAndResponse('GET', $apiPingUrl,'');
      return $getApiPingResponse['data'];
    }

    public function getSingleOrder($id_order)
    {
      $apiPingUrl = $this->baseUrl. '/orders/'.$id_order;
      $getApiPingResponse = $this->headerAndResponse('GET', $apiPingUrl, '');
      return $getApiPingResponse['data'];
    }

    //Create order shipping
    public function createOrderShipping($id_order_unit, array $payload){
      $apiPingUrl = $this->baseUrl. '/order-units/'.$id_order_unit.'/send';
      $getApiPingResponse = $this->headerAndResponse('PATCH', $apiPingUrl, json_encode($payload));
      return (int)$getApiPingResponse === 204;
    }

    //Add order shipping
    public function addOrderShipping($id_order_unit, array $payload)
    {
        $req = [
            'id_order_unit' => $id_order_unit,
            'shipment_information' => $payload,
        ];

        $apiPingUrl = $this->baseUrl. '/shipments';
        $getApiPingResponse = $this->headerAndResponse('POST', $apiPingUrl, json_encode($payload));
        return (int)$getApiPingResponse === 204;
    }

    public function getHeaders($method,$url,$jsonData)
    {
        $timestamp = time();
        $secretKey =  $this->secretKey;
        $clientKey = $this->clientKey;
        $userAgent = "Inhouse development";

        $headers = [

            'Accept: application/json',
            'Shop-Client-Key: ' . $clientKey,
            'Shop-Timestamp: ' . $timestamp,
            'Shop-Signature: ' . $this->signRequest($method, $url, $jsonData, $timestamp, $secretKey),
            'User-Agent: ' . $userAgent,
        ];
        if($method == 'POST'|| $method == 'PUT' || $method == 'PATCH'){
            array_unshift($headers,'Content-Type: application/json');
        }
        return $headers;
    }


    function signRequest($method, $uri, $body, $timestamp, $secretKey)
    {
        $string = implode("\n", [
            $method,
            $uri,
            $body,
            $timestamp,
        ]);

        return hash_hmac('sha256', $string, $secretKey);
    }

    public function sendCurl($url,$headers, $ProductJsonData = null, $request_type = 'POST', $response = false)
    {

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        if($request_type =='GET'){
            $items = json_decode(curl_exec($ch), true);
            return $items;
        }
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $request_type);
        if($request_type != 'DELETE'){
          curl_setopt($ch, CURLOPT_POST, 1);
          curl_setopt($ch, CURLOPT_POSTFIELDS, $ProductJsonData);
        }
        curl_setopt($ch, CURLOPT_HEADER, 1);
        curl_setopt($ch, CURLINFO_HEADER_OUT, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        if($request_type == 'PATCH')
        {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
        }
        $output = curl_exec($ch);
        $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if($response)
        {
            return [
                'status' => $responseCode,
                'data' => json_decode($output, true),
            ];
        }

        return $responseCode;
    }
    public function headerAndResponse($method,$url,$ProductJsonData, $response = false){

        $headers = $this->getHeaders($method,$url,$ProductJsonData);
        $response = $this->sendCurl($url,$headers,$ProductJsonData,$method,$response);

        return $response;
    }




}
