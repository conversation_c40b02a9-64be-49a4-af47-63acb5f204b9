<?php
namespace App\Helper;
use DB;
use CRUDBooster;
use App\Http\Controllers\AdminManualImportTarrifController;
class AppStore{

    public static function CheckAppPurchaseBoolean($id){
        $assigned=DB::table('app_assigns')
        ->where(['app_id'=>$id,'user_id'=>CRUDBooster::myId()])
        ->count();

        $purchased=DB::table('purchase_apps')
        ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
        ->select('app_stores.id','app_stores.menu_name','purchase_apps.type','purchase_apps.subscription_date_end')
        ->where(['app_stores.id'=>$id,'purchase_apps.cms_user_id'=>CRUDBooster::myId()])
        ->where('purchase_apps.subscription_date_end','>',date('Y-m-d'))
        ->first();

        if(($purchased==null) && (CRUDBooster::myPrivilegeId() == 3) && ($assigned==0)){
            return false;
        }else{
            return [true, $purchased];
        }
    }

    public static function CheckAppPurchaseBooleanConsole($app_id, $user_id){
        $assigned=DB::table('app_assigns')
        ->where(['app_id'=>$app_id,'user_id'=>$user_id])
        ->count();

        $purchased=DB::table('purchase_apps')
        ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
        ->select('app_stores.id','app_stores.menu_name','purchase_apps.type','purchase_apps.subscription_date_end')
        ->where(['app_stores.id'=>$app_id,'purchase_apps.cms_user_id'=>$user_id])
        ->where('purchase_apps.subscription_date_end','>',date('Y-m-d'))
        ->first();

        if(($purchased==null) && ($assigned==0)){
            return false;
        }else{
            return true;
        }
    }


	//app  store
    public static function ActiveApp($first_name, $second_name =null){

    	$assign_menus=DB::table('app_assigns')
        ->join('app_stores','app_stores.id','app_assigns.app_id')
        ->where(['app_stores.menu_name'=>$first_name,'app_assigns.user_id'=>CRUDBooster::myId()])
        ->count();


        $data=DB::table('purchase_apps')
        ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
        ->select('app_stores.id','app_stores.menu_name','purchase_apps.type','purchase_apps.subscription_date_end')
        ->where(['purchase_apps.cms_user_id'=>CRUDBooster::myId()])
        ->where(function($q) use ($first_name, $second_name){
                    $q->where('app_stores.menu_name',$first_name)
                        ->orwhere('app_stores.menu_name',$second_name );
        })->where('purchase_apps.subscription_date_end','>=',date('Y-m-d'))
        ->first();

        if(($data==null) and (CRUDBooster::myPrivilegeId() ==3) and ($assign_menus==0)){
            CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
        }else{
            return $data;
        }
    }

	public static function ActiveFeature($name){
		$assign_menus=DB::table('app_assigns')
        ->join('app_stores','app_stores.id','app_assigns.app_id')
        ->where(['app_stores.menu_name'=>$name,'app_assigns.user_id'=>CRUDBooster::myId()])
        ->count();

        $data=DB::table('purchase_apps')
        ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
        ->where(['app_stores.menu_name'=>$name,'purchase_apps.cms_user_id'=>CRUDBooster::myId()])
        ->where('purchase_apps.subscription_date_end','>=',date('Y-m-d'))
        ->count();

        if(($data==0) and (CRUDBooster::myPrivilegeId() ==3) and ($assign_menus==0)){
           return false;

        }else{
            return true;
        }
	}

	public static function getRemainDays($date){
		$remain_days=0;
        $start=strtotime(date('Y-m-d'));
        $end = strtotime($date);
        return $remain_days +=($end - $start) / (60 * 60 * 24);
	}


     public static function CheckDrmProject($id){
        $myId=CRUDBooster::myId();
        $assigned=DB::table('app_assigns')
        ->where(['app_id'=>$id,'user_id'=>$myId])
        ->count();

        $project=DB::table('drm_project_members')->where('cms_user_id',$myId)->count();

        $purchased=DB::table('purchase_apps')
        ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
        ->select('app_stores.id','app_stores.menu_name','purchase_apps.type','purchase_apps.subscription_date_end')
        ->where(['app_stores.id'=>$id,'purchase_apps.cms_user_id'=>$myId])
        ->where('purchase_apps.subscription_date_end','>',date('Y-m-d'))
        ->first();

        if(($purchased==null) && (CRUDBooster::myPrivilegeId() == 3) && ($assigned==0) && ($project==0)) {
            return false;
        }else{
            return [true,$purchased];
        }
    }


    public static function ShowDrmProjectMenu($id){
        $myId=CRUDBooster::myId();
        $assigned=DB::table('app_assigns')
        ->where(['app_id'=>$id,'user_id'=>$myId])
        ->count();

        $project=DB::table('drm_project_members')->where('cms_user_id',$myId)->count();

        $purchased=DB::table('purchase_apps')
        ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
        ->select('app_stores.id','app_stores.menu_name','purchase_apps.type','purchase_apps.subscription_date_end')
        ->where(['app_stores.id'=>$id,'purchase_apps.cms_user_id'=>$myId])
        ->where('purchase_apps.subscription_date_end','>',date('Y-m-d'))
        ->count();

        if($assigned==0 and $purchased==0 and $project>0){
            return true;
        }else{
            return false;
        }
    }


    public static function ShowImportLimitBanner($user_id){
      $days='';
      $first_import=DB::table('drm_products')
        ->where(['user_id'=>$user_id,'deleted_at'=>null])
        ->select('created_at')
        ->orderby('id','asc')
        ->first();

      $unlimited_days=0;
      if(!empty($first_import)){
        $date=date("Y-m-d", strtotime($first_import->created_at));
        $time= importFirstDate($date);
        $days=date("Y-m-d", strtotime("+".extendedTrialDay()." days", $time)); //App trial extend
        $start=strtotime(date('Y-m-d'));
        $end=strtotime($days);
        $days = $unlimited_days +=($end - $start) / (60 * 60 * 24);
        return $days;
      }
    }


}
