<?php

use App\DrmUserCredit;
use App\DrmUserCreditAddLog;
use App\Jobs\UpdateProductRq;
use App\Services\Tariff\Credit\CreditService;
use App\UserNotificationSetting;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\User;
use Illuminate\Support\Facades\DB;

function drm_convert_european_to_decimal($number,$format = 1){
  $sign_normal = array('£','€','$');
  $signs_utf8 = makeArrayUtf8($sign_normal);
  $number = str_replace($signs_utf8,'',$number);
  $number = str_replace($sign_normal,'',$number);
  $number = str_replace(' ', '', $number);
  try {
      if($format == 1){
        $fmt = new NumberFormatter( 'de_DE', NumberFormatter::DECIMAL );
      }
      else{
        $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
      }
      $price = $fmt->parse($number);

      // if($price == 0){
      //   if($format == 1){
      //     $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
      //     $price = $fmt->parse($number);
      //   }
      //   else {
      //     $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
      //     $price = $fmt->parse($number);
      //   }
      // }
      return $price;
    } catch (Exception $e) {
      $val = str_replace(",",".",$number);
      $val = preg_replace('/\.(?=.*\.)/', '', $val);
      return floatval($val);
    }
}

function deNumberFormatterAuto($number,$format = 1){
  $number = str_replace(' ', '', $number);
  try {
      if($format == 1){
        $fmt = new NumberFormatter( 'de_DE', NumberFormatter::DECIMAL );
      }
      else{
        $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
      }
      $price = $fmt->parse($number);

      if($price == 0){
        if($format == 1){
          $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
          $price = $fmt->parse($number);
        }
        else {
          $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
          $price = $fmt->parse($number);
        }
      }
      return $price;
    } catch (\Exception $e) {
      $val = str_replace(",",".",$number);
      $val = preg_replace('/\.(?=.*\.)/', '', $val);
      return floatval($val);
    }
}

function replace($text){
  $text = str_replace('www.drm.software/public/storage/custom_images','https://drm-file.fra1.digitaloceanspaces.com/image',$text);
  $text = str_replace('https://www.drm.software/public/storage/custom_images','https://drm-file.fra1.digitaloceanspaces.com/image',$text);
  $text = str_replace('https://drm.software/public/storage/custom_images','https://drm-file.fra1.digitaloceanspaces.com/image',$text);
  $text = str_replace('drm.software/public/storage/custom_images','https://drm-file.fra1.digitaloceanspaces.com/image',$text);
  $text = str_replace('drm.software/storage/custom_images','https://drm-file.fra1.digitaloceanspaces.com/image',$text);
  $text = str_replace('drm.software/uploads/custom_product_images','https://drm-file.fra1.digitaloceanspaces.com/image',$text);
  $text = str_replace('uploads/custom_product_images','https://drm-file.fra1.digitaloceanspaces.com/image',$text);
  $text = str_replace('public/storage/custom_images','https://drm-file.fra1.digitaloceanspaces.com/image',$text);
  $text = str_replace('www.https://','https://',$text);
  return $text;
}

function makeInt($str){
  return (int)$str;
}

  function jsonFixer($json){
    $patterns     = [];
    /** garbage removal */
    $patterns[0]  = "/([\s:,\{}\[\]])\s*'([^:,\{}\[\]]*)'\s*([\s:,\{}\[\]])/"; //Find any character except colons, commas, curly and square brackets surrounded or not by spaces preceded and followed by spaces, colons, commas, curly or square brackets...
    $patterns[1]  = '/([^\s:,\{}\[\]]*)\{([^\s:,\{}\[\]]*)/'; //Find any left curly brackets surrounded or not by one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[2]  =  "/([^\s:,\{}\[\]]+)}/"; //Find any right curly brackets preceded by one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[3]  = "/(}),\s*/"; //JSON.parse() doesn't allow trailing commas
    /** reformatting */
    $patterns[4]  = '/([^\s:,\{}\[\]]+\s*)*[^\s:,\{}\[\]]+/'; //Find or not one or more of any character except spaces, colons, commas, curly and square brackets followed by one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[5]  = '/["\']+([^"\':,\{}\[\]]*)["\']+/'; //Find one or more of quotation marks or/and apostrophes surrounding any character except colons, commas, curly and square brackets...
    $patterns[6]  = '/(")([^\s:,\{}\[\]]+)(")(\s+([^\s:,\{}\[\]]+))/'; //Find or not one or more of any character except spaces, colons, commas, curly and square brackets surrounded by quotation marks followed by one or more spaces and  one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[7]  = "/(')([^\s:,\{}\[\]]+)(')(\s+([^\s:,\{}\[\]]+))/"; //Find or not one or more of any character except spaces, colons, commas, curly and square brackets surrounded by apostrophes followed by one or more spaces and  one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[8]  = '/(})(")/'; //Find any right curly brackets followed by quotation marks...
    $patterns[9]  = '/,\s+(})/'; //Find any comma followed by one or more spaces and a right curly bracket...
    $patterns[10] = '/\s+/'; //Find one or more spaces...
    $patterns[11] = '/^\s+/'; //Find one or more spaces at start of string...

    $replacements     = [];
    /** garbage removal */
    $replacements[0]  = '$1 "$2" $3'; //...and put quotation marks surrounded by spaces between them;
    $replacements[1]  = '$1 { $2'; //...and put spaces between them;
    $replacements[2]  = '$1 }'; //...and put a space between them;
    $replacements[3]  = '$1'; //...so, remove trailing commas of any right curly brackets;
    /** reformatting */
    $replacements[4]  = '"$0"'; //...and put quotation marks surrounding them;
    $replacements[5]  = '"$1"'; //...and replace by single quotation marks;
    $replacements[6]  = '\\$1$2\\$3$4'; //...and add back slashes to its quotation marks;
    $replacements[7]  = '\\$1$2\\$3$4'; //...and add back slashes to its apostrophes;
    $replacements[8]  = '$1, $2'; //...and put a comma followed by a space character between them;
    $replacements[9]  = ' $1'; //...and replace by a space followed by a right curly bracket;
    $replacements[10] = ' '; //...and replace by one space;
    $replacements[11] = ''; //...and remove it.

    $result = preg_replace($patterns, $replacements, $json);
    return $result;
  }

function drm_fix_image($string,$assoc=false, $fixNames=true){

   // $string='[{id:1,src:VSPOQ0618.jpg},];';
    // $string = str_replace("id",'"id"',$string);
    // $string = str_replace("src",'"src"',$string);
    // $string= preg_replace('!(http|https|ftp|scp)(s)?:\/\/[a-zA-Z0-9.?%=&_/]+!', "\"\\0\"", $string);
    // $string = str_replace(",];",']',$string);

    // //$s = str_replace(":",':"',$s);
    // $s = str_replace(',"',',',$s);
    // $s = str_replace('}','"}',$s);
    // $s = str_replace(';','',$s);
   // dd($string);
   $string = str_replace("\r\n",'', $string);
 //  dd($string);
  return json_decode($string, $assoc);
}

function getProductListByUserId($user_id, $limit=null)
{
  $products_list = DB::table('drm_products')
  ->join('drm_imports', 'drm_products.drm_import_id', '=', 'drm_imports.id')
  ->select('drm_products.*')
  ->where('drm_imports.user_id', $user_id);

  if($limit !=null )
    $products_list->limit($limit);

  // $products_list->get();

  return $products_list->get();

}
function checImageUrl($external_link) {
    if (@getimagesize($external_link)) {
        return true;
    } else {
        return false;
    }
}

function checkImageUrl($external_link): bool
{
    $curl = curl_init($external_link);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    $data = curl_exec($curl);
    curl_close($curl);
    return $data;
}

function set_option($key, $group, $data = null, $user_id)
{
    $op = DB::table('options')->select('*');
    $op->where('option_key', '=', $key);
    $op->where('option_group', '=', $group);
    $op->where('user_id', '=', $user_id);

    if(!$op->get()->count()){
        $option_data = [
            'option_key' => $key,
            'option_group' => $group,
            'option_value' => $data,
            'user_id' => $user_id
        ];
        return DB::table('options')->insert($option_data);
    }else{
        $option_data = [
            'option_value' => $data,
        ];
        return $op->update($option_data);
    }
    return false;
}

/**
 * Return single option
 */
function get_option($key, $group, $user_id = null)
{
    $op = DB::table('options')->select('*');
    $op->where('option_key', '=', $key);
    $op->where('option_group', '=', $group);
    if($user_id){
        $op->where('user_id', '=', $user_id);
    }

    return $op->first();
}


function checkRemoteFile($url)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL,$url);
    // don't download content
    curl_setopt($ch, CURLOPT_NOBODY, 1);
    curl_setopt($ch, CURLOPT_FAILONERROR, 1);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

    $result = curl_exec($ch);
    curl_close($ch);
    if($result !== FALSE)
    {
        return true;
    }
    else
    {
        return false;
    }
}
/**
 * URL before:
 * https://example.com/orders/123?order=ABC009
 *
 * 1. add_query_params(['status' => 'shipped'])
 * 2. add_query_params(['status' => 'shipped', 'coupon' => 'CCC2019'])
 *
 * URL after:
 * 1. https://example.com/orders/123?order=ABC009&status=shipped
 * 2. https://example.com/orders/123?order=ABC009&status=shipped&coupon=CCC2019
 */
function drm_helper_add_query_params(array $params = [])
{
    $query = array_merge(
        request()->query(),
        $params
    ); // merge the existing query parameters with the ones we want to add

    return url()->current() . '?' . http_build_query($query); // rebuild the URL with the new parameters array
}

function csvToArray($url){
    ini_set('memory_limit', -1);
    $csvData = file_get_contents_utf8($url);
    $lines = explode(",", $csvData);
    if($lines[0]!=null){
        $key = str_getcsv($lines[0]);
    }
    unset($lines[0]);
    $array = array();
    $chunks = array_chunk($lines,10);
    foreach($chunks as $chunk){
        foreach ($chunk as $line){
            $spreadsheet = str_getcsv($line);
            $diff = count($key) - count($spreadsheet);
            if($diff>0){
                for($i = 0; $i < $diff; $i++){
                    $spreadsheet[] = "";
                }
            }
            if(!containsOnlyNull($spreadsheet)){
                if(count($key) == count($spreadsheet)){
                    $array[] = array_combine($key, $spreadsheet);
                }
            }

        }
    }
    return $array;
}


function getCsvHeader($url){
    ini_set('memory_limit', -1);
    $csvData = file_get_contents_utf8($url);
    $lines = explode(PHP_EOL, $csvData);
    if($lines[0]!=null){
        $key = str_getcsv($lines[0]);
    }
    $key_utf8 = makeArrayUtf8($key);
    $headers = json_encode($key_utf8);
    return $headers;
}

function file_get_contents_utf8($fn){
    $content = file_get_contents($fn);
    $text = mb_convert_encoding($content, "UTF-8", "WINDOWS-1252");
    $utf8 = mb_convert_encoding($text, 'UTF-8',
    mb_detect_encoding($text, 'UTF-8, ISO-8859-15', true));
    return $utf8;
}

function getDemoData($url){
    ini_set('memory_limit', -1);
    $csvData = file_get_contents_utf8($url);
    $lines = explode(PHP_EOL, $csvData);
    $demo = str_getcsv($lines[1]);
    //$demo_utf8 = makeArrayUtf8($demo);
    //dd($demo);
    return json_encode($demo);
}

function makeArrayUtf8($array = []){
  if(is_array($array)){
    $array = trimArray($array);
    $keys = array_keys($array);
    $keys_utf8 = array_map('makeUtf8',$keys);
    $keys_utf8 = array_map('removeDots',$keys_utf8);
    $utf8 = array_map('makeUtf8',$array);
    $utf8 = array_combine($keys_utf8,$utf8);
    return $utf8;
  }else{
    return $array;
  }
}

function removeDots($key){
  return str_replace('.','',$key);
}
function makeUtf8($text){
  if(!is_array($text)){
    $incov = iconv('UTF-8', 'WINDOWS-1252//TRANSLIT', $text);
    if($incov==false){
        $incov = iconv('UTF-8', 'ISO-8859-15',$text);
    }
    if($incov == false){
      return $text;
    }
    else{
      $utf8 = mb_convert_encoding($incov, 'UTF-8',
      mb_detect_encoding($incov, 'UTF-8, ISO-8859-15', true));
    }
    return $utf8;
  }
  else{
    $array = makeArrayUtf8($text);
    return $array;
  }
}

function checkArrayKey($key,$array){
  array_walk($array, function (&$item) use($key){
    $item = array_combine($key, $item);
  });

  $valid = true;
  array_walk($array, function (&$item) use(&$valid){
    foreach ($item as $key => $value) {
      if($key == "" && $value!=null && $value!=""){
        $valid = false;
      }
    }
  });
  return $valid;
}
function build_query_parameter(array $params = []){
  $old_param = request()->all();
  return url()->current().'?'.http_build_query(array_merge(request()->all(),$params));
}

function containsOnlyNull($input){
    if($input!=null){
        return empty(array_filter($input, function ($a) { return $a !== null;}));
    }
    else{
        return true;
    }
}

function removeNullKeys($array = []){
  $finalArray = [];
  foreach($array as $key => $value) {
    if($key!="" && $key!=null){
      $finalArray[$key] = $value;
    }
  }
  return $finalArray;
}

function trimArray($array){
  if(is_array($array)){
    $a = array_map('trimRecursive', array_keys($array));
    $b = array_map('trimRecursive', $array);
    $array = array_combine($a, $b);
  }
  return $array;
}
function trimRecursive($value){
  if(!is_array($value)){
    $value = trim($value);
  }
  else {
    $value = array_map('trim', $value);
  }
  return $value;
}

function generateTags($array = []){
    $tags = "";
    $i = 0;
    $array = removeNulls($array);
    foreach($array as $value){
        $i++;
        if($i==count($array)){
            if(strpos($value, '#')!== false){
                $tags.=$value;
            }
            else{
                $tags.="#".$value;
            }
        }
        else{
            if(strpos($value, '#')!== false){
                $tags.=$value.",";
            }
            else{
                $tags.="#".$value.",";
            }
        }
    }
    return $tags;
}

function removeNulls($array =[]){
  if(is_array($array)){
    foreach($array as $key => $value){
        $i++;
        if($value == null && $value == ""){
            unset($array[$key]);
        }
    }
  }
  return $array;
}

function arrayNullCount($array =[]){
  $i = 0;
  foreach($array as $key => $value){
      if($value == null || $value == ""){
          $i++;
      }
  }
  return $i;
}


function drmPriceCalculation($fields,$data,$drm,$categories=[]){
  $user = $drm->user_id;
  if($fields->vk_price != '' || $fields->vk_price != null){
    $temp_price=drm_convert_european_to_decimal($data[$fields->vk_price],$drm->money_format);
  }
  else{
    $temp_price=drm_convert_european_to_decimal($data[$fields->ek_price],$drm->money_format);
  }
  $price=$temp_price;
  if($fields->vk_price==null){
    if($fields->category!=null){
      ($drm->custom_category==1)?$category=$fields->category:$category=strip_tags($data[$fields->category]);
      $country = DB::table('countries')->where('id',$drm->country_id)->first();
      $trans_cat = 'category_name_'.$country->language_shortcode;
      $category_id = DB::table('drm_category')->where($trans_cat,$category)->where('user_id',$drm->user_id)->first();
      $profit_margin = DB::table('drm_product_profit_margins')
                  ->where('category_id',$category_id->id)
                  ->where('drm_import_id',$drm->id)
                  ->where('price_from','<=',$temp_price)
                   ->where('price_to','>=',$temp_price)
                  ->first();
    }
    else {
      if(is_array($categories)){
        $profit_margin = DB::table('drm_product_profit_margins')
                    ->whereIn('category_id',$categories)
                    ->where('drm_import_id',$drm->id)
                    ->where('price_from','<=',$temp_price)
                     ->where('price_to','>=',$temp_price)
                    ->first();
      }
    }

    if($profit_margin!=null){
      $price = $temp_price + $temp_price * (floatval($profit_margin->profit_percent)/100) + drm_convert_european_to_decimal($profit_margin->shipping_cost,2)+ drm_convert_european_to_decimal($profit_margin->additional_charge_fixed,2);
    }
    else{
      $fallback = DB::table('drm_fallback_calculations')
      ->where('id',$drm->fallback)
      ->first();

      if($fallback!=null){
        $price = $temp_price + $temp_price * (floatval($fallback->profit_percent)/100) + drm_convert_european_to_decimal($fallback->shipping_cost,2)+ drm_convert_european_to_decimal($fallback->additional_charge,2);
      }
    }
  }

  elseif ($temp_price == null || $temp_price == 0){
    $temp_price=drm_convert_european_to_decimal($data[$fields->ek_price],$drm->money_format);
    $fallback = DB::table('drm_fallback_calculations')
    ->where('id',$drm->fallback)
    ->first();

    if($fallback!=null){
      $price = $temp_price + $temp_price * (floatval($fallback->profit_percent)/100) + drm_convert_european_to_decimal($fallback->shipping_cost,2)+ drm_convert_european_to_decimal($fallback->additional_charge,2);
    }
    else {
      $price = $temp_price;
    }
  }

  // dd($temp_price);
  if($profit_margin->round_scale!=null){
    $has_price = explode('.',$price);
    $first_item_array = $has_price[0];
    $last_item_array = $has_price[1];
    if($last_item_array == 0){
      $price;
    }
    else{
      $price = $first_item_array+$profit_margin->round_scale;
    }
  }
  $price = (float)str_replace(',','',number_format($price,2));
  return $price;
}


function drmPriceCalculationNew($config){
  $profit = false;
  if($config['vk_field']==null){
    $profit = true;
  }
  $ek_price = drm_convert_european_to_decimal($config['ek_price'],$config['money_format']);

  if($profit){
    $profit_margin = $config['profit_margins']
                    ->where('price_from','<=',$ek_price)
                    ->where('price_to','>=',$ek_price)
                    ->first();
    if(!empty($profit_margin)){
      $price = $ek_price + $ek_price
      *(floatval($profit_margin->profit_percent)/100)
      + drm_convert_european_to_decimal($profit_margin->shipping_cost,2)
      + drm_convert_european_to_decimal($profit_margin->additional_charge_fixed,2);

      if($profit_margin->round_scale!=null){
        $has_price = explode('.',$price);
        $first_item_array = $has_price[0];
        $last_item_array = $has_price[1];
        if($last_item_array == 0){
          $price;
        }
        else{
          $price = $first_item_array+$profit_margin->round_scale;
        }
      }
    }
  }
  else {
    $price = drm_convert_european_to_decimal($config['vk_price'],$config['money_format']);
  }
  if((!$profit && $price == 0) || ($profit && empty($profit_margin))){
    $fallback = $config['fallback'];
    // dd($fallback);
    if($fallback!=null){
      $price = $ek_price + $ek_price
      *(floatval($fallback->profit_percent)/100)
      +drm_convert_european_to_decimal($fallback->shipping_cost,$config['money_format'])
      +drm_convert_european_to_decimal($fallback->additional_charge,$config['money_format']);
    }
  }
  $price = (float)str_replace(',','',number_format($price,2));
  return $price;
}



function files_are_equal($a,$b){
  if(filesize($a) !== filesize($b))
  return false;
  $ah = fopen($a, 'rb');
  $bh = fopen($b, 'rb');
  $result = true;
  while(!feof($ah))
  {
    if(fread($ah, 8192) != fread($bh, 8192))
    {
      $result = false;
      break;
    }
  }
  fclose($ah);
  fclose($bh);

  return $result;
}

function pathIsUrl($path){
  if (filter_var($path, FILTER_VALIDATE_URL)) {
      return true;
  } else {
      return false;
  }
}


function hasBigString($array){
  foreach($array as $key => $value){
    if(strlen($value)>100){
      return true;
    }
  }
  return false;
}

function formatBytes($bytes, $precision = 2) {
  $units = array("b", "kb", "mb", "gb", "tb");
  $bytes = max($bytes, 0);
  $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
  $pow = min($pow, count($units) - 1);
  $bytes /= (1 << (10 * $pow));
  return round($bytes, $precision) . " " . $units[$pow];
}

function insertChangeLog($product_id,$value,$status){
  $product_id = (int)$product_id;
  $user_id = CRUDBooster::myId();
  $ldate = date('Y-m-d H:i:s');
  $log = DB::table('product_change_logs')->where('product_id',$product_id)->first();
  $log_json = json_decode($log->log,true);
  $insert_log = [
    'time' => $ldate,
    'ip' => $_SERVER['SERVER_ADDR'],
    'field' => $value,
    'status' => $status,
    'user' => $user_id
  ];
  if(is_array($log_json)){
    if(count($log_json)>=5){
      unset($log_json[0]);
      $log_json = array_values($log_json);
    }
  }
  $log_json[] = $insert_log;
  $data = [
    'log' => json_encode($log_json)
  ];
  if($log!=null){
      DB::table('product_change_logs')->where('product_id',$product_id)->update($data);
  }
  else {
    $data['product_id'] = $product_id;
    DB::table('product_change_logs')->insert($data);
  }
}


function drmTotalProduct($user_id)
{
  $user = User::find($user_id);
  if ( User::isSupplier($user_id) ) {
    return \App\Models\Marketplace\Product::where('supplier_id', $user_id)->count();


    // return $user->suppliersProducts ? $user->suppliersProducts()->count() : 0;
  } else {
    return Cache::remember('user_products_' . $user_id, 05.0, function () use ($user) {
      return $user ? @$user->products()->count() : 0;
    });
  }
}

  function removeCommaFromPrice($amount){
    $total = $amount;
    if(strpos($total,"," ))
    {
      $have = [".", ","];
      $will_be   = ["", "."];
      $total = str_replace($have, $will_be, $total);
    }
    return $total;
  }

  function getGlobalorderstatus($val){
    $order_status = [
      0 => 'Canceled',
      1 => 'Shipped',
    ];
    return isset($order_status[$val])? $order_status[$val] : '';
  }

  //drm insert type codes
  function getInsertTypeName($id){
    $arr = [
      1 => 'API', //Shop order sync
      2 => 'VOD', //Daily order sync
      3 => 'Stripe',
      4 => 'Charge', //Not used - paywall charge
      5 => 'Import',
      6 => 'Manual',
      7 => 'Marketplace',
      8 => 'Marketplace sell',
    ];
    return (isset($arr[$id]))? $arr[$id] : '';
  }


  function getUserSavedLang($email)
  {
      return DB::table('cms_users')->where('email', $email)->value('last_lang');
  }

  //Get email conent, subject from email page
  function DRMParseMailTemplate($tags, $slug, $lang = 'de'){
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    // $tags = [
    //  'app_name' => 'app TEST',
    //  'app_price' => 30,
    //  'subscription_interval' => 'Monthly',
    //  'period_start' => 'pp',
    //  'period_end' => 'p end',
    //  'INTERVAL' => false,
    //  'FIXED' => false,
    //  'FREESCOUT' => true,
    // ];
    // $slug = 'app_purchase_confirmation';
    $content = null;
    $subject = null;
      $page = Cache::rememberForever($slug.'-'.$lang, function () use ($slug, $lang) {
          return DB::table('cms_email_templates')
              ->where('slug', $slug)
              ->select(['subject', DB::raw('IF(`content_'.$lang.'` IS NOT NULL, `content_'.$lang.'`, `content`) `content`')])
              ->first();
      });

    if($page){
      $content = $page->content;
      $subject = $page->subject;

      foreach ($tags as $k => $value) {
        $find = '['.$k.']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
          $content = str_replace($find, $value, $content);
        }
        if($tags[$tag] == 'true'){
          $content = preg_replace('~\{\/?'.$tag.'\}~', '', $content);
        }else{
          $content =  preg_replace('/{'.$tag.'}[\s\S]+?{'.$tag.'}/', '', $content);
          // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
          $subject = str_replace($find, $value, $subject);
        }
        if($tags[$tag] == 'true'){
          $subject = preg_replace('~\{\/?'.$tag.'\}~', '', $subject);
        }else{
          $subject =  preg_replace('/{'.$tag.'}[\s\S]+?{'.$tag.'}/', '', $subject);
          // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
      }
    }

    return ['subject' => $subject, 'body' => $content];
  }


  function orderTaxRate($order_id){
    $order = DB::table('drm_orders_new')->join('drm_customers', 'drm_orders_new.drm_customer_id', '=', 'drm_customers.id')->where('drm_orders_new.id', $order_id)->select('drm_customers.country as country')->first();
    return (float)countryCodeTax($order->country);
  }


  //Return tax by country name
  function countryCodeTax($country_code, $country_id = false){
    $tax_rate = 16;
    $tax_rate_data = null;

    $id = null;
    if($country_code){
      // $country_code = strtoupper($country_code);
      $tax_rate_data = DB::table('tax_rates')
                      ->select('id', 'charge')
                      ->where('country_code', 'like', $country_code)
                      ->orWhere('country', 'like', $country_code)
                      ->orWhere('country_de', 'like', $country_code)
                      ->orWhere('country_es', 'like', $country_code)
                      ->first();
      $id = $tax_rate_data->id;
    }


    // $tax_rate_data = Cache::rememberForever($country_code, function() use($country_code){
    //   return DB::table('tax_rates')->where('country_code', $country_code)->first();
    // });
    if($country_id) return $id;
    return ($tax_rate_data)? (float)$tax_rate_data->charge : (float)$tax_rate;

  }

  function removeNullArray($array){
    if(is_array($array)){
      if(!containsOnlyNull($array)){
        return $array;
      }
    }
  }
  //Param amount, rate
  function orderTaxForNet($amount, $tax_rate){
    return $amount/(100+ $tax_rate)* $tax_rate; // Tax
  }

  //Param amount, rate
  function orderNetForNet($amount, $tax_rate){
    return $amount/(100+ $tax_rate)* 100;
  }

  //shop exist check
  function isShopExist($shop_id){
    $shop = \App\Shop::find((int)$shop_id, ['id']);
    return ($shop)? true : false;
  }

  //Billing address format
  function formatBillingAddress($address, $one_line = false, $order_id = null){
    $tax_number = ($order_id)? orderTaxNumber($order_id) : null;
    $countries = DB::table('tax_rates')->pluck( 'country', 'country_code');
    if(drmIsJSON($address)){
      $address_json = json_decode($address);
      if($address_json){
        $html = '';
        $html .= ($address_json->name)? '<b>'.$address_json->name.'</b>' : '';
        $html .= ($address_json->name)? ($one_line)? ', ': '<br>' : '';
        $html .= ($address_json->company)? '<b>'.$address_json->company.'</b>' : '';
        $html .= ($address_json->company)? ($one_line)?  ', ': '<br>' : '';

        $html .= ($tax_number)? 'Steuernummer: <b>'.$tax_number .'</b>': '';
        $html .= ($tax_number)? ($one_line)?  ', ': '<br>' : '';

        $html .= ($address_json->street)? $address_json->street : '';
        $html .= ($address_json->street)? ($one_line)?  ', ': '<br>' : '';
        // $html .= ($address_json->address)? $address_json->address : '';
        // $html .= ($address_json->address)? ($one_line)?  ', ': '<br>' : '';
        // $html .= ($address_json->state)? $address_json->state : '';
        // $html .= ($address_json->state)? ($one_line)?  ', ': '<br>' : '';
        $html .= ($address_json->zip_code)? $address_json->zip_code .' ': '';
        $html .= ($address_json->city)? $address_json->city : '';
        $html .= ($address_json->city)? ($one_line)? ', ': '<br>' : '';

        $country_name = ($address_json->country)? $address_json->country: null;
        if($countries && $country_name && (strlen($country_name)<5) ){
          foreach ($countries as $iso => $value) {
            if(strcasecmp($iso, $country_name)==0){
              $country_name = $value;
              break;
            }
          }
        }
        // dd($country_name);
        $html .= ($country_name)? $country_name : 'Germany';
        $html .= '<br>';
        return $html;
      }
    }else{
      if ($countries) {
        foreach ($countries as $iso => $value) {
          $iso_len = strlen($iso);
          $pos = strripos($address, '>'.$iso);
          if ($pos === false) {
           continue;
          } else {
            $sub = trim(strip_tags(substr($address, ($pos+$iso_len+1) )));
           if(strlen($sub) === 0) return preg_replace('~'.$iso.'(?!.*'.$iso.')~i', $value, $address);
          }
        }
      }
    }
    return $address;
  }

  //Stripe billing address
  function stripeBillingAddress($customer, $one_line = false, $order_id = null){
    $tax_number = ($order_id)? orderTaxNumber($order_id) : null;
    $countries = DB::table('tax_rates')->pluck( 'country', 'country_code');
      $html = '';
      $html .= ($customer->full_name)? '<b>'.$customer->full_name .'</b><br>': '';
      $html .= ($customer->company_name)? '<b>'.$customer->company_name .'</b><br>': '';
      $html .= ($tax_number)? 'Tax Number: <b>'.$tax_number .'</b><br>': '';
      $html .= ($customer->address)? $customer->address .'<br>': '';
      // $html .= ($customer->state)? $customer->state .'<br>': '';
      $html .= ($customer->zip_code)? $customer->zip_code .' ': '';
      $html .= ($customer->city)? $customer->city .'<br>': '';

      $country_name = ($customer->country)? $customer->country: null;

      if($countries && $country_name && (strlen($country_name)<5) ){
        foreach ($countries as $iso => $value) {
          if(strcasecmp($iso, $country_name)==0){
            $country_name = $value;
            break;
          }
        }
      }
      $html .= ($country_name)? $country_name : 'Germany';
      $html .= '<br>';
      return $html;
  }


    //Billing address format
  function formatCustomerInfo($customer){
    $countries = DB::table('tax_rates')->pluck( 'country', 'country_code');
    if($customer){

        $html = '';
        $html .= ($customer->full_name)? 'Name: <b>'.$customer->full_name .'</b><br>' : '';
        $html .= ($customer->company_name)? 'Company Name: <b>'.$customer->company_name .'</b>' : '';

        $html .= '<br>';
        $html .= ($customer->address)? 'Address: '.$customer->address : '';
        // $html .= ($customer->state)? ', '.$customer->state : '';
        $html .= ($customer->zip_code)? ', '.$customer->zip_code : '';
        $html .= ($customer->city)? ', '.$customer->city : '';

        $country_name = $customer->country;
        if($country_name){
          if($countries && $country_name && (strlen($country_name)<5) ){
            foreach ($countries as $iso => $value) {
              if(strcasecmp($iso, $country_name)==0){
                $country_name = $value;
                break;
              }
            }
          }
        }


        $html .= ($country_name)? ', '.$country_name : 'Germany';
        $html .= '<br>';

        $html .= '<p>';
        $html .= ($customer->email)? 'E-mail: '.$customer->email .'<br>' : '';
        $html .= ($customer->phone)? 'Phone: '.$customer->phone .'<br>' : '';
        $html .= '</p>';

        return $html;
    }
  }

  //Formate currency
  function formatCurrency($currency){
    $currency_symbols = array(
      'AED' => '&#1583;.&#1573;', // ?
      'AFN' => '&#65;&#102;',
      'ALL' => '&#76;&#101;&#107;',
      'AMD' => '',
      'ANG' => '&#402;',
      'AOA' => '&#75;&#122;', // ?
      'ARS' => '&#36;',
      'AUD' => '&#36;',
      'AWG' => '&#402;',
      'AZN' => '&#1084;&#1072;&#1085;',
      'BAM' => '&#75;&#77;',
      'BBD' => '&#36;',
      'BDT' => '&#2547;', // ?
      'BGN' => '&#1083;&#1074;',
      'BHD' => '.&#1583;.&#1576;', // ?
      'BIF' => '&#70;&#66;&#117;', // ?
      'BMD' => '&#36;',
      'BND' => '&#36;',
      'BOB' => '&#36;&#98;',
      'BRL' => '&#82;&#36;',
      'BSD' => '&#36;',
      'BTN' => '&#78;&#117;&#46;', // ?
      'BWP' => '&#80;',
      'BYR' => '&#112;&#46;',
      'BZD' => '&#66;&#90;&#36;',
      'CAD' => '&#36;',
      'CDF' => '&#70;&#67;',
      'CHF' => '&#67;&#72;&#70;',
      'CLF' => '', // ?
      'CLP' => '&#36;',
      'CNY' => '&#165;',
      'COP' => '&#36;',
      'CRC' => '&#8353;',
      'CUP' => '&#8396;',
      'CVE' => '&#36;', // ?
      'CZK' => '&#75;&#269;',
      'DJF' => '&#70;&#100;&#106;', // ?
      'DKK' => '&#107;&#114;',
      'DOP' => '&#82;&#68;&#36;',
      'DZD' => '&#1583;&#1580;', // ?
      'EGP' => '&#163;',
      'ETB' => '&#66;&#114;',
      'EUR' => '&#8364;',
      'FJD' => '&#36;',
      'FKP' => '&#163;',
      'GBP' => '&#163;',
      'GEL' => '&#4314;', // ?
      'GHS' => '&#162;',
      'GIP' => '&#163;',
      'GMD' => '&#68;', // ?
      'GNF' => '&#70;&#71;', // ?
      'GTQ' => '&#81;',
      'GYD' => '&#36;',
      'HKD' => '&#36;',
      'HNL' => '&#76;',
      'HRK' => '&#107;&#110;',
      'HTG' => '&#71;', // ?
      'HUF' => '&#70;&#116;',
      'IDR' => '&#82;&#112;',
      'ILS' => '&#8362;',
      'INR' => '&#8377;',
      'IQD' => '&#1593;.&#1583;', // ?
      'IRR' => '&#65020;',
      'ISK' => '&#107;&#114;',
      'JEP' => '&#163;',
      'JMD' => '&#74;&#36;',
      'JOD' => '&#74;&#68;', // ?
      'JPY' => '&#165;',
      'KES' => '&#75;&#83;&#104;', // ?
      'KGS' => '&#1083;&#1074;',
      'KHR' => '&#6107;',
      'KMF' => '&#67;&#70;', // ?
      'KPW' => '&#8361;',
      'KRW' => '&#8361;',
      'KWD' => '&#1583;.&#1603;', // ?
      'KYD' => '&#36;',
      'KZT' => '&#1083;&#1074;',
      'LAK' => '&#8365;',
      'LBP' => '&#163;',
      'LKR' => '&#8360;',
      'LRD' => '&#36;',
      'LSL' => '&#76;', // ?
      'LTL' => '&#76;&#116;',
      'LVL' => '&#76;&#115;',
      'LYD' => '&#1604;.&#1583;', // ?
      'MAD' => '&#1583;.&#1605;.', //?
      'MDL' => '&#76;',
      'MGA' => '&#65;&#114;', // ?
      'MKD' => '&#1076;&#1077;&#1085;',
      'MMK' => '&#75;',
      'MNT' => '&#8366;',
      'MOP' => '&#77;&#79;&#80;&#36;', // ?
      'MRO' => '&#85;&#77;', // ?
      'MUR' => '&#8360;', // ?
      'MVR' => '.&#1923;', // ?
      'MWK' => '&#77;&#75;',
      'MXN' => '&#36;',
      'MYR' => '&#82;&#77;',
      'MZN' => '&#77;&#84;',
      'NAD' => '&#36;',
      'NGN' => '&#8358;',
      'NIO' => '&#67;&#36;',
      'NOK' => '&#107;&#114;',
      'NPR' => '&#8360;',
      'NZD' => '&#36;',
      'OMR' => '&#65020;',
      'PAB' => '&#66;&#47;&#46;',
      'PEN' => '&#83;&#47;&#46;',
      'PGK' => '&#75;', // ?
      'PHP' => '&#8369;',
      'PKR' => '&#8360;',
      'PLN' => '&#122;&#322;',
      'PYG' => '&#71;&#115;',
      'QAR' => '&#65020;',
      'RON' => '&#108;&#101;&#105;',
      'RSD' => '&#1044;&#1080;&#1085;&#46;',
      'RUB' => '&#1088;&#1091;&#1073;',
      'RWF' => '&#1585;.&#1587;',
      'SAR' => '&#65020;',
      'SBD' => '&#36;',
      'SCR' => '&#8360;',
      'SDG' => '&#163;', // ?
      'SEK' => '&#107;&#114;',
      'SGD' => '&#36;',
      'SHP' => '&#163;',
      'SLL' => '&#76;&#101;', // ?
      'SOS' => '&#83;',
      'SRD' => '&#36;',
      'STD' => '&#68;&#98;', // ?
      'SVC' => '&#36;',
      'SYP' => '&#163;',
      'SZL' => '&#76;', // ?
      'THB' => '&#3647;',
      'TJS' => '&#84;&#74;&#83;', // ? TJS (guess)
      'TMT' => '&#109;',
      'TND' => '&#1583;.&#1578;',
      'TOP' => '&#84;&#36;',
      'TRY' => '&#8356;', // New Turkey Lira (old symbol used)
      'TTD' => '&#36;',
      'TWD' => '&#78;&#84;&#36;',
      'TZS' => '',
      'UAH' => '&#8372;',
      'UGX' => '&#85;&#83;&#104;',
      'USD' => '&#36;',
      'UYU' => '&#36;&#85;',
      'UZS' => '&#1083;&#1074;',
      'VEF' => '&#66;&#115;',
      'VND' => '&#8363;',
      'VUV' => '&#86;&#84;',
      'WST' => '&#87;&#83;&#36;',
      'XAF' => '&#70;&#67;&#70;&#65;',
      'XCD' => '&#36;',
      'XDR' => '',
      'XOF' => '',
      'XPF' => '&#70;',
      'YER' => '&#65020;',
      'ZAR' => '&#82;',
      'ZMK' => '&#90;&#75;', // ?
      'ZWL' => '&#90;&#36;',
    );

    $currency = strtoupper($currency);

    if (array_key_exists($currency, $currency_symbols)) {
      return $currency_symbols[$currency];
    }
    return $currency_symbols['EUR'];

  }


  function drmIsJSON($string){
    return is_string($string) && is_array(json_decode($string, true)) && (json_last_error() == JSON_ERROR_NONE) ? true : false;
  }

  //Return customer info as json
  function customerInfoJson($customer_info){
    $country = isset($customer_info['country'])? $customer_info['country'] : null;
    // $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
      'name' => isset($customer_info['customer_full_name'])? $customer_info['customer_full_name'] : null,
      'company' => isset($customer_info['company_name'])? $customer_info['company_name'] : null,
      'address' => isset($customer_info['address'])? $customer_info['address'] : null,
      'zip_code' => isset($customer_info['zip_code'])? $customer_info['zip_code'] : null,
      'city' => isset($customer_info['city'])? $customer_info['city'] : null,
      'state' => isset($customer_info['state'])? $customer_info['state'] : null,
      'country' => $country,
    ]);
  }

  function billingInfoJson($customer_info){
    $country = isset($customer_info['country_billing'])? $customer_info['country_billing'] : (isset($customer_info['country'])? $customer_info['country'] : null);
    // $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
      'name' => isset($customer_info['billing_name'])? $customer_info['billing_name'] : (isset($customer_info['customer_full_name'])? $customer_info['customer_full_name'] : null),
      'company' => isset($customer_info['billing_company'])? $customer_info['billing_company'] : (isset($customer_info['company_name'])? $customer_info['company_name'] : null),
      'street' => isset($customer_info['street_billing'])? $customer_info['street_billing'] : (isset($customer_info['address'])? $customer_info['address'] : null),
      'address' => isset($customer_info['address_billing'])? $customer_info['address_billing'] : null,
      'zip_code' => isset($customer_info['zipcode_billing'])? $customer_info['zipcode_billing'] : (isset($customer_info['zip_code'])? $customer_info['zip_code'] : null),
      'city' => isset($customer_info['city_billing'])? $customer_info['city_billing'] : (isset($customer_info['city'])? $customer_info['city'] : null),
      'state' => isset($customer_info['state_billing'])? $customer_info['state_billing'] : (isset($customer_info['state'])? $customer_info['state'] : null),
      'country' => $country,
    ]);
  }

  function shippingInfoJson($customer_info){
    $country = isset($customer_info['country_shipping'])? $customer_info['country_shipping'] : (isset($customer_info['country'])? $customer_info['country'] : null);
    // $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
      'name' =>  isset($customer_info['shipping_name'])? $customer_info['shipping_name'] : (isset($customer_info['customer_full_name'])? $customer_info['customer_full_name'] : null),
      'company' => isset($customer_info['shipping_company'])? $customer_info['shipping_company'] : (isset($customer_info['company_name'])? $customer_info['company_name'] : null),
      'street' => isset($customer_info['street_shipping'])? $customer_info['street_shipping'] : (isset($customer_info['address'])? $customer_info['address'] : null),
      'address' => isset($customer_info['address_shipping'])? $customer_info['address_shipping'] : null,
      'zip_code' => isset($customer_info['zipcode_shipping'])? $customer_info['zipcode_shipping'] : (isset($customer_info['zip_code'])? $customer_info['zip_code'] : null),
      'city' => isset($customer_info['city_shipping'])? $customer_info['city_shipping'] : (isset($customer_info['city'])? $customer_info['city'] : null),
      'state' => isset($customer_info['state_shipping'])? $customer_info['state_shipping'] : (isset($customer_info['state'])? $customer_info['state'] : null),
      'country' => $country,
    ]);
  }

  function customerExportBillShippJson($customer){
    $country = isset($customer['country'])? $customer['country'] : null;
    // $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
      'name' =>  isset($customer['full_name'])? $customer['full_name'] : null,
      'company' => isset($customer['company_name'])? $customer['company_name'] : null,
      'street' => isset($customer['address'])? $customer['address'] : null,
      'address' => null,
      'zip_code' => isset($customer['zip_code'])? $customer['zip_code'] : null,
      'city' => isset($customer['city'])? $customer['city'] : null,
      'state' => isset($customer['state'])? $customer['state'] : null,
      'country' => $country
    ]);
  }

  //Stripe payment
  function userToBillingJson($user_id){
    $user = DB::table('cms_users')->find($user_id, ['name']);
    $billilg_details = DB::table('billing_details')->where('user_id', $user_id)->first();
    if ($billilg_details) {
      $country = DB::table('countries')->find($billilg_details->country_id, ['name']);
      $data = [
        'customer_full_name' =>  $user->name,
        'company_name' => $billilg_details->company_name,
        'street_billing' => $billilg_details->address,
        'zipcode_billing' => $billilg_details->zip,
        'city_billing' => $billilg_details->city,
        'country_billing' => $country->name,
      ];
      return billingInfoJson($data);
    }
    return null;
  }

  //Customer to billing
  function customerToBillingJson($customer){
    if ($customer) {
      $data = [
        'customer_full_name' => $customer->full_name,
        'company_name' => $customer->company_name,
        'street_billing' => $customer->address,
        'zipcode_billing' => $customer->zip_code,
        'city_billing' => $customer->city,
        'state_billing' => $customer->state,
        'country_billing' => $customer->country,
      ];
      return billingInfoJson($data);
    }
    return null;
  }
    //Customer to billing
  function customerToShippingJson($customer){
    if ($customer) {
      $data = [
        'customer_full_name' => $customer->full_name,
        'company_name' => $customer->company_name,
        'street_shipping' => $customer->address,
        'zipcode_shipping' => $customer->zip_code,
        'city_shipping' => $customer->city,
        'state_shipping' => $customer->state,
        'country_shipping' => $customer->country,
      ];
      return shippingInfoJson($data);
    }
    return null;
  }

  //update billing / shipping address json
  function updateBillingShippingAddress($new_value, $old_value){
    if (drmIsJSON($new_value) && drmIsJSON($old_value)) {
      $json_data = [];
      $new_json = json_decode($new_value, true);
      $old_json = json_decode($old_value, true);

      foreach ($new_json as $key => $value) {
        $json_data[$key] = ( (is_null($new_json[$key]) || ($new_json[$key]=='') ) && isset($old_json[$key]) )? $old_json[$key] : $new_json[$key];
      }
      foreach ($old_json as $key => $value) {
        if ( !isset($json_data[$key]) ) $json_data[$key] = $old_json[$key];
      }
      return json_encode($json_data);
    }
    return drmIsJSON($new_value)? $new_value : (drmIsJSON($old_value)? $old_value : billingInfoJson([]));
  }

  function vodCustomerAddress($customer){
    if($customer){
      return json_encode([
          'name' => $customer->full_name,
          'company' => $customer->company_name,
          'street' => $customer->address,
          'zip_code' => $customer->zip_code,
          'city' => $customer->city,
          'state' => $customer->state,
          'country' => $customer->country,
      ]);
    }else{
      return null;
    }
  }
  function vodCustomerInfo($customer){
    if($customer){
      return json_encode([
          'name' => $customer->full_name,
          'company' => $customer->company_name,
          'address' => $customer->address,
          'zip_code' => $customer->zip_code,
          'city' => $customer->city,
          'state' => $customer->state,
          'country' => $customer->country,
      ]);
    }else{
      return null;
    }
  }

  function billingAddressFields(){
    return [
      'street',
      'zip_code',
      'city',
      // 'state',
      'country',
    ];
  }

  function customerInfoFields(){
    return [
      'full_name',
      'company_name',
      'address',
      'city',
      // 'state',
      'zip_code',
      'country',
    ];
  }

  //Customer  to customer_info json data generate -- After update customer - order update
  function customerToCustomerInfoJson($customer){
    return json_encode([
      'name' => $customer->full_name,
      'company' => $customer->company_name,
      'address' => $customer->address,
      'zip_code' => $customer->zip_code,
      'city' => $customer->city,
      'state' => $customer->state,
      'country' => $customer->country,
    ]);
  }


  function getStatusText($id){
    if($id == 1){
      $text = "Created a new category";
    }
    if($id == 2){
      $text = "Changed";
    }
    if($id == 3){
      $text = "Enabled update for";
    }
    return $text;
  }

  function makeUpdateStatusJson(){
    $status = [
      'title' => 1,
      'description' => 1,
      'image' => 1,
      'ek_price' => 1,
      'vk_price' => 1,
      'stock' => 1,
      'status' => 1,
      'gender' => 1,
      'item_weight' => 1,
      'item_color' => 1,
      'production_year' => 1,
      'materials' => 1,
      'brand' => 1,
      'item_size' => 1,
    ];
    return json_encode($status);
  }

  function getUniqueCategories($ids){
    $category_array = array_map('json_decode',$ids);
    $flattened = Illuminate\Support\Arr::flatten($category_array);
    $category_id = array_unique($flattened);
    return $category_id;
  }

  function sentProgress($message,$type){
    try{
      if($type == 'import'){
        event(new App\Events\ProgressEvent($message));
      }
      if($type == 'importSync'){
        event(new App\Events\SyncProgressEvent($message));
      }
      if($type == 'manualUpdate'){
        event(new App\Events\ManualUpdateEvent($message));
      }
    }catch (\Exception $e){
      // echo "BroadcastException";
    }
  }

  function peakMemory(){
    return (memory_get_peak_usage(true)/1024/1024)." MB";
  }


  function orderStatisticsArrData(){
    $currency = $performa = [];
    $currency['EUR'] = $performa['EUR'] = 0;

    $orders_group = null;
    $total = $other = $shipped_order = $proforma_invoice = 0;
    $total_sum = $pro_sum = 0;

    $orders_group = \App\NewOrder::select('status', 'currency', 'invoice_number', 'total', 'test_order')->where('new_orders.status', '!=', 'Canceled');
    if (!CRUDBooster::isSuperadmin()){ $orders_group->where('cms_user_id', '=', CRUDBooster::myId());}
    $orders_group = $orders_group->get()->groupBy('currency');

    if($orders_group->isNotEmpty()){

      foreach ($orders_group as $key => $orders) {
        $order_sum = $orders->where('test_order', '!=', 1)->whereNotIn('status',['Storniert','Canceled'])->sum("total");
        $total += $orders->whereNotIn('status',['Storniert','Canceled'])->count();
        $total_sum += $order_sum;


        $proforma_invoice += $orders->where('invoice_number',-1)->count();
        $prof_sum = $orders->where('invoice_number',-1)->sum("total");
        $pro_sum += $prof_sum;


        if( ($key == '') ||  (strtolower($key) == 'eur')){
          $performa['EUR'] += $prof_sum;
          $currency['EUR'] += $order_sum;
        }else{
          $currency[$key] = $order_sum;
          $performa[$key] = $prof_sum;
        }
        $shipped_order +=  $orders->filter(function ($item) use ($attribute, $value) {
            return strtolower($item['status']) == 'shipped';
        })->count();
      }
    }

    $others_order = $total - $shipped_order; // - $proforma_invoice;
    $performa_statt = ['count' => $proforma_invoice, 'amount' => $performa];
    return (object)['total_order' => $total, 'total_amount' => $total_sum, 'currency' => $currency, 'performa' => $performa_statt, 'shipped_order' => $shipped_order, 'others_order' => $others_order];
  }

  function orderStatisticsData(){
    return Cache::remember('order_statt_'.CRUDBooster::myId(), 05.0, function(){
      return orderStatisticsArrData();
    });
  }

  function removeCommaPrice($rate){
    if(strpos($rate,"," ))
      {
          $have = [".", ","];
          $will_be   = ["", "."];
          $rate = str_replace($have, $will_be, $rate);
      }
      return round($rate, 2);
  }

  function orderTaxNumber($order_id){
    return \App\NewOrder::where('id', '=', $order_id)->value('vat_number');
  }

  function test_order_btn(){
    return (userTestOrderCount() < 5)? true : false;
  }
  function testOrderYesBtn(){
    $btn = 'Yes';
    $last_val = 5;
    $order = userTestOrderCount();
    if($order < 5) return $order.'/'.$last_val.' '.$btn;
    return '5/'.$last_val.' '.$btn;
  }
  function testOrderNoBtn(){
    $btn = 'No';
    $last_val = 5;
    $order = userTestOrderCount();
    if($order < 5) return $order.'/'.$last_val.' '.$btn;
    return '5/'.$last_val.' '.$btn;
  }
  function testOrderYesBtnShow(){
    $order = userTestOrderCount();
    return ($order < 5)? 'true' : 'false';
  }

  function userTestOrderCount(){
    return Cache::rememberForever('test_order_count_'.CRUDBooster::myId(), function () {
      return DB::table('new_orders')->where('cms_user_id', CRUDBooster::myId())->where('test_order', 1)->count();
    });
  }

  function extendedTrialDay(){
    // return (in_array(\CRUDBooster::myId(), [52, 61]))? env('TRIAL_DAYS', '65') : 14;
    // return env('TRIAL_DAYS', '65');
    return env('TRIAL_DAYS', '14');
  }

  function importFirstDate($date){
    // $time=strtotime("2020-5-11");
    // return (in_array(\CRUDBooster::myId(), [52, 61]))? $time : strtotime($date);
    return strtotime($date);
  }
  function ddtest($var){
    if (app()->environment('development')) {
    	dd($var);
    }
  }

  function DRM_Inverval_App_Minute($user_id, $app_id){
    $user_plan = app_user_plan_id($user_id, $app_id);
    $time = 1440;
    switch($user_plan){
      case config('global.interval_bronze_plan'):
          $time = 720;
        break;
      case config('global.interval_silver_plan'):
        $time = 360;
        break;
      case config('global.interval_gold_plan'):
        $time = 180;
        break;
      case config('global.interval_platinum_plan'):
        $time = 30;
        break;
    }
    return $time;
  }

function app_user_plan_id($user_id, $app_id){
  $user_has_plan =  \DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('plan_id')->first();
  $user_has_assign = \DB::table('app_assigns')->where(['app_id'=>$app_id, 'user_id'=>$user_id])->select('plan_id')->first();
  $purchase_plan = ($user_has_plan)? $user_has_plan->plan_id : 0;
  $assign_plan = ($user_has_assign)? $user_has_assign->plan_id : 0;
  $fast_plan =  ($purchase_plan > $assign_plan)? $purchase_plan : $assign_plan;

  $user_has_trial = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'status' => 'active', 'is_free_trail' => 1, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->first();

  return ($fast_plan)? $fast_plan : (($user_has_trial)? config('global.interval_platinum_plan') : null );
}

function app_user_interval($plan = null){
  $text = '24 Hours';
  switch ($plan) {
    case config('global.interval_bronze_plan'):
      $text = '12 Hours';
      break;
    case config('global.interval_silver_plan'):
      $text = '6 Hours';
      break;
    case config('global.interval_gold_plan'):
      $text = '3 Hours';
      break;
    case config('global.interval_platinum_plan'):
      $text = '30 Minutes';
      break;
  }
  return $text;
}


//DRM Order email parse
  //Get email conent, subject from email page
function DRMParseOrderEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

    if(!empty($channel)){
      $cache_name = 'drm_order_mail_' . $user_id . '_' . $channel;
    }else{
      $cache_name = 'drm_order_mail_' . $user_id;
    }

    // if(isLocal() || in_array($user_id, [212, 2592, 2991])){
      $page = Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
        return findSystemMail('drm_order_mail', $user_id, $channel);
      });
    // }else{
    // $page = Cache::rememberForever('drm_order_mail_' . $user_id, function () use ($user_id) {
    //     return DB::table('drm_order_mail')->where('cms_user_id', $user_id)->first();
    // });
    // }

    if ($page) {
        $content = empty($page->email_template) ? config('system_email_settings.email_settings') : $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;

        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}


  //DRM Supplier email parse
  //Get email conent, subject from email page
function DRMParseSupplierEmailTemplate($tags, $user_id)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
    $page = Cache::rememberForever('drm_supplier_mail_' . $user_id, function () use ($user_id) {
        return DB::table('drm_supplier_mail')->where('cms_user_id', $user_id)->first();
    });


    $content = empty($page->email_template)? config('system_email_settings.supplier_email_body') : $page->email_template;
    $subject = empty($page->mail_subject)? config('system_email_settings.supplier_email_subject') : $page->mail_subject;
    $bcc = $page->bcc_email;

    if (!empty($page->sender_email)) {
        $senderEmail = $page->sender_email;
    }

    foreach ($tags as $k => $value) {
        $find = '[' . $k . ']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
            $content = str_replace($find, $value, $content);
        }
        if ($tags[$tag] == 'true') {
            $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
        } else {
            $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
            // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
            $subject = str_replace($find, $value, $subject);
        }
        if ($tags[$tag] == 'true') {
            $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
        } else {
            $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
            // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}




//Droptienda product stock mail
function DRMParseDtProductStockTemplate($tags, $user_id, $table = 'drm_dt_stock_mail')
{
    if(!in_array($table, ['drm_dt_stock_mail', 'drm_dt_stock_mail_entry', 'df_csv_email_template'])) throw new \Exception('Invalid email template!');
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
    $bcc = null;
    $page = Cache::rememberForever($table.'_' . $user_id, function () use ($user_id, $table) {
        return DB::table($table)->where('cms_user_id', $user_id)->first();
    });


    $content = empty($page->email_template)? config('system_email_settings.'.$table.'_body') : $page->email_template;
    $subject = empty($page->mail_subject)? config('system_email_settings.'.$table.'_subject') : $page->mail_subject;
    $bcc = $page->bcc_email;

    if (!empty($page->sender_email)) {
        $senderEmail = $page->sender_email;
    }

    foreach ($tags as $k => $value) {
        $find = '[' . $k . ']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
            $content = str_replace($find, $value, $content);
        }
        if ($tags[$tag] == 'true') {
            $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
        } else {
            $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
            // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
            $subject = str_replace($find, $value, $subject);
        }
        if ($tags[$tag] == 'true') {
            $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
        } else {
            $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
            // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}


  //DRM Offer email parse
  //Get email conent, subject from email page
  function DRMParseOfferEmailTemplate($tags, $user_id, $table = 'drm_offer_mail', $channel = null)
  {
      if(!in_array($table, ['drm_offer_mail', 'drm_offer_remainder_mail'])) throw new \Exception('Invalid email!');
      $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
      $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

      $content = null;
      $subject = null;
      $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
      $bcc = null;

      if($channel){
        $page = Cache::rememberForever($table.'_' . $user_id . '_' . $channel, function () use ($user_id, $table, $channel) {
            return DB::table($table)->where('cms_user_id', $user_id)->where('channel', $channel)->first();
        });
      }else{
      $page = Cache::rememberForever($table.'_' . $user_id, function () use ($user_id, $table) {
          return DB::table($table)->where('cms_user_id', $user_id)->first();
      });
      }


      $content = empty($page->email_template)? config('system_email_settings.'.$table.'_body') : $page->email_template;
      $subject = empty($page->mail_subject)? config('system_email_settings.'.$table.'_subject') : $page->mail_subject;
      $bcc = $page->bcc_email;

      if (!empty($page->sender_email)) {
          $senderEmail = $page->sender_email;
      }

      foreach ($tags as $k => $value) {
          $find = '[' . $k . ']';
          $tag = $k;
          if (strpos($content, $find) !== false) {
              $content = str_replace($find, $value, $content);
          }
          if ($tags[$tag] == 'true') {
              $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
          } else {
              $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
              // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
          }

          if (strpos($subject, $find) !== false) {
              $subject = str_replace($find, $value, $subject);
          }
          if ($tags[$tag] == 'true') {
              $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
          } else {
              $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
              // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
          }
      }

      return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
  }



  //Overdue email---
  //check if an user purchased upcomming invoice pro version
  function checkUserHasAppBoolean($id, $user_id){
    $assigned=DB::table('app_assigns')
    ->where(['app_id'=>$id, 'user_id'=> $user_id])
    ->count();

    $purchased=DB::table('purchase_apps')
    ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
    ->select('app_stores.id','app_stores.menu_name','purchase_apps.type','purchase_apps.subscription_date_end')
    ->where(['app_stores.id'=>$id, 'purchase_apps.cms_user_id'=> $user_id])
    ->where('purchase_apps.subscription_date_end','>=',date('Y-m-d'))
    ->first();

    return ($assigned || $purchased)? true : false;
  }

  function generateNotificationMessage($title, $message = ''){
    $find = '[message]';
    if (strpos($title, $find) !== false) {
      $title = str_replace($find, $message, $title);
    }
    return $title;
  }

  function getSeperateNotificationHooks($position){
    $settings = DB::table('notification_settings')
                    ->join('notification_trigger as nt','notification_settings.trigger_id','=','nt.id')
                    ->where('notification_settings.position','=',$position)
                    ->select('notification_settings.*','nt.hook as notification_hook')
                    ->get();
    $hook = [];
    if ($settings->isNotEmpty()) {
      foreach ($settings as $setting) {
        $hook[] = $setting->notification_hook;
      }
    }
    return $hook;
  }


  function isHookRemainOnSidebar($hook){
    $notificationTrigger = DB::table('notification_trigger as nt')
                              ->join('notification_settings as ns','nt.id','=','ns.trigger_id')
                              ->where('hook','=',$hook)
                              ->select('nt.*','ns.position as sidebar_pos')
                              ->first();
    if (!empty($notificationTrigger))
      return $notificationTrigger; //$notificationTrigger;

    return null; //null;
  }

  function getNotificationDriver($hook, $id)
  {
      $sidebar = isHookRemainOnSidebar($hook);
      if (!empty($sidebar)) {
          $sidebarDriver = [];
          $setting = \App\UserNotificationSetting::where('user_id', '=', $id)->where('sidebar_id', '=', $sidebar->sidebar_pos)->first();

          // if ($setting->is_telegram){
          //     $sidebarDriver['telegram'] = true;
          // }

          if ($setting->is_email){
              $sidebarDriver['mail'] = true;
          }

          // if ($setting->is_mobile_notify){
          //     $sidebarDriver['mobile_app'] = true;
          // }

          return $sidebarDriver;
      } else {
          $user = App\User::find($id);
          $drivers = [];
          if (!empty($user)) {

              if(!empty($user->email_notification) && !empty($user->notify_email)){
                  $drivers['header_email'] = true;
              }

              // if (!empty($user->is_telegram)) {
              //     $drivers['header_telegram'] = true;
              // }

              // if (!empty($user->is_mobile_notify)) {
              //     $drivers['mobile_app'] = true;
              // }
          }
          return $drivers;
      }
  }
  //has Invoice translate access
  function hasInvoiceTranlateAccess($user_id){
    $app_id = config('global.invoice_translate_app_id');
    $assigned = DB::table('app_assigns')->where(['app_id'=> $app_id,'user_id'=> $user_id])->count();
    $purchased = DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->count();
    return ($assigned || $purchased)? true : false;
  }

    function invoiceLabel($label, $data = ''){
    $find = '[_]';
    if (strpos($label, $find) !== false) {
      $label = str_replace($find, $data, $label);
    }
    return $label;
  }


  //Islocal
  function isLocal(){
    if(!app()->environment('production')){
      return true;
    }
    else {
      return false;
    }
  }

  //Recurring invoices purchased or not
  function hasRecurringInvoiceAccess($user_id){
    $app_id = config('global.recuring_invoice_app_id');
    $assigned = DB::table('app_assigns')->where(['app_id'=> $app_id,'user_id'=> $user_id])->count();
    $purchased = null;
    // $purchased = DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->count();
    return ($assigned || $purchased)? true : false;
  }

  if(! function_exists('trace')) {
    function trace(string $message,array $data = []) {
        Log::error(
            'IP ' . request()->ip() .' Message ' . $message,
            $data
        );
    }
}

// DRM Handling time email parse

function DRMParseHandlingTimeEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

    if($channel){
      $cache_name = 'marketing_email_settings_' . $user_id . '_' . $channel;
    }else{
      $cache_name = 'marketing_email_settings_' . $user_id;
    }

    // if(isLocal() || in_array($user_id, [212, 2592, 2991])){
      $page = Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
        return findSystemMail('marketing_email_settings', $user_id, $channel);
      });
    // }else{
    // $page = Cache::rememberForever('marketing_email_settings_' . $user_id, function () use ($user_id) {
    //     return DB::table('marketing_email_settings')->where('cms_user_id', $user_id)->first();
    // });
    // }

    if ($page) {
        $content = empty($page->email_template) ? config('system_email_settings.handling_time_email_settings') : $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;

        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}

    //Get shop type name by type id value
  function drm_shop_type_name($value){
    $data = [
      1 => 'GAMBIO',
      2 => 'LENGOW',
      3 => 'YATEGO',
      4 => 'EBAY',
      5 => 'AMAZON',
      6 => 'SHOPIFY',
      7 => 'WooCommerce',
      8 => 'ClouSale',
      9 => 'Chrono24',
      10 => 'Droptienda',
      200 => 'VOD',
      201 => 'Marketplace',
      11 => 'Etsy',
      12 => 'Otto',
    ];

    return isset($data[$value])? $data[$value] : '';
  }

  // Update order history log
  function updateOrderHistory($order, $status, $message)
  {
    try{

        $payload = [
            'status' => $status,
            'time' => date('Y-m-d H:i:s'),
            'action_by' => null,
            'user_name' => 'SYSTEM',
            'message'=> $message,
        ];

       // $history = $order->order_history ?? [];
       // $history[] = $payload;

       // $order->update(['order_history'=>$history]);

        // TODO:: DROPMATIX
        DB::table('order_logs')->insert([
            'order_id' => $order->id,
            'payload' => json_encode($payload),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

     }catch(\Exception $e){}
  }

  function createNewOrderLog($order, $status, $message)
  {
    try{
        $payload = [
            'status' => $status,
            'time' => date('Y-m-d H:i:s'),
            'action_by' => null,
            'user_name' => 'SYSTEM',
            'message'=> $message,
        ];

        // TODO:: DROPMATIX
        DB::table('order_logs')->insert([
            'order_id' => $order->id,
            'payload' => json_encode($payload),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

     }catch(\Exception $e){}
  }

//Order history label
function drmHistoryLabel($status){
  $drm_status = config('global.drm_order_status');
  return isset($drm_status[$status])? $drm_status[$status] : $status;
}

//Order label by group name
function drmOrderLabelByGroupId($status){

  try{
    $group = \App\OrderStatusGroup::has('statuses')->whereNotNull('group_name')->where('group_name', 'LIKE', '%'.$status.'%')->orWhereHas('statuses', function($q) use($status) {
      $q->where('status_name', 'LIKE', $status);
    })->select('id')->first();
    if($group && $group->id){
      $statuses = config('order_status.statuses');
      $group_id = $group->id;
      return isset($statuses[$group_id])? $statuses[$group_id] : $status;
    }
  }catch(\Exception $e){}

  return $status;
}

//Order created first time history
function drmOrderFirstHistory($order){
  $insert_type_name = getInsertTypeName($order->insert_type);
  $shop_channel_name = null;

  $history = $order->order_history ?? [];

  $shop_id = $order->shop_id;
  $shop_type = ($shop_id)? \App\Shop::find($shop_id, ['channel'])->channel : null;
  if($shop_type){
    $shop_channel_name = drm_shop_type_name($shop_type);
  }

  $insert_message = '';
  if($insert_type_name){
    $insert_message = $insert_type_name.' ';
  }

  $time = date('Y-m-d H:i:s');

  $insert_message .= (in_array($order->insert_type, [1, 2, 7]))? 'Order sycronized' : 'Order inserted';

  $insert_message .= ($shop_channel_name)? ' from '.$shop_channel_name.'.' : '.';
  $history[] = ['status' => $order->status, 'time' => $time, 'action_by' => null, 'message'=> $insert_message];

  $invoice_create_message = 'Invoice number '.inv_number_string($order->invoice_number, $order->inv_pattern).' created.';
  $history[] = ['status' => $order->status, 'time' => $time, 'action_by' => null, 'message'=> $invoice_create_message];

  $order->update(['order_history' => $history]);

    // TODO:: DROPMATIX
    $historyPayload = array_map(function($payload) use ($order) {

        $payload['action_by'] = $payload['action_by'] ?? null;
        $payload['user_name'] = $payload['user_name'] ?? 'SYSTEM';

        return [
            'order_id' => $order->id,
            'payload' => json_encode($payload),
            'created_at' => now(),
            'updated_at' => now(),
        ];

    }, $history);

    DB::table('order_logs')->insert($historyPayload);

    if (in_array($order->status, ['mp_return', 'return_received']) || ($order->insert_type == 9 && str_starts_with($order->order_id_api, 'mp-return'))) {
      UpdateProductRq::dispatch(json_decode($order->cart), $order->cms_user_id, $order->shop_id);
    }
}

function DRMParseRemainderEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

    if($channel){
      $cache_name = 'remainder_email_settings_' . $user_id . '_' . $channel;
    }else{
      $cache_name = 'remainder_email_settings_' . $user_id;
    }

    // if(isLocal() || in_array($user_id, [212, 2592, 2991])){
      $page = Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
        return findSystemMail('remainder_email_settings', $user_id, $channel);
      });
    // }else{
    // $page = Cache::rememberForever('remainder_email_settings_' . $user_id, function () use ($user_id) {
    //     return DB::table('remainder_email_settings')->where('cms_user_id', $user_id)->first();
    // });
    // }

    if ($page) {
        $content = empty($page->email_template) ? config('system_email_settings.remainder_email_settings') : $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;

        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}


// Droptienda order confirmation email parse
// Get email conent, subject from email page
function DRMParseDroptiendaOrderEmailTemplate($tags, $user_id)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
    $page = Cache::rememberForever('droptienda_order_confirmation_template_' . $user_id, function () use ($user_id) {
        return DB::table('droptienda_order_confirmation_template')->where('cms_user_id', $user_id)->first();
    });

    if ($page) {
        $content = $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;
        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }
        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }
    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}

//Country name full
function drmCountryNameFull($country_code){
  $tax_country = null;
  if($country_code){
    $tax_country = DB::table('tax_rates')
    ->where('country_code', 'like', $country_code)
    ->orWhere('country', 'like', $country_code)
    ->orWhere('country_de', 'like', $country_code)
    ->orWhere('country_es', 'like', $country_code)
    ->select('country')->first();
  }
  return ($tax_country && $tax_country->country)? $tax_country->country : $country_code;
}

//Recurring invoices purchased or not
function DrmUserHasPurchasedApp($user_id, $app_id){
  $assigned = DB::table('app_assigns')->where(['app_id'=> $app_id,'user_id'=> $user_id])->count();
  $purchased = null;
  $purchased = DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->count();
  return ($assigned || $purchased)? true : false;
}

//invoice number format -- used to show inv number
function inv_number_string($inv, $inv_number_string = null){
  return ($inv_number_string)? $inv_number_string : $inv;
}

//drm invoice prefix generate
function drm_invoice_number_format($inv, $format = null){
  $format = trim($format);
  if(empty($format)) return $inv;

  $find = '[inv]';
  return (strpos($format, $find) !== false)? str_replace($find, $inv, $format) : $inv.'-'.$format;
}

function messageFromException(\Exception $e)
{
    // if ($e instanceof ClientException) {
    //     $err = curl_client_response($e);
    //     return $err['errors'][0]['longMessage'] ?? $err['errors'][0]['message'] ?? $e->getMessage();
    // }
    try {
        $err = curl_client_response($e);
        return $err['errors'][0]['longMessage'] ?? $err['errors'][0]['message'] ?? $e->getMessage();
    } catch (\Exception $e2) {
        return $e->getMessage();
    }
}

//Send internel order to drm
function send_order_drm_to_internel($order_id)
{
  try {

    $order = DB::table('new_orders')->select('order_id_api', 'marketplace_order_ref')->where('id', $order_id)->first();
    if(str_starts_with($order->order_id_api, 'internel_s'))
    {
        $order_id = $order->marketplace_order_ref;
    }

    if (!isLocal()) {

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => "http://207.154.254.191/api/v1/send-drm-order-to-mp/".$order_id,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);
    }

    return;


    // $curl = curl_init();
    // curl_setopt_array($curl, array(
    //   CURLOPT_URL => config('app.drm_url').'/api/send-order-to-internel/'.$id.'?token=tyMcR63U78vg1Nm',
    //   CURLOPT_RETURNTRANSFER => true,
    //   CURLOPT_ENCODING => '',
    //   CURLOPT_MAXREDIRS => 10,
    //   CURLOPT_TIMEOUT => 0,
    //   CURLOPT_FOLLOWLOCATION => true,
    //   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    //   CURLOPT_CUSTOMREQUEST => 'GET',
    // ));

    // $response = curl_exec($curl);

    // curl_close($curl);
    // return $response;

  } catch (\Exception $e) {
    return $e->getMessage();
  }
}



//Order status mail send
function send_order_email($order_id)
{
  try{
    app('App\Http\Controllers\AdminDrmAllOrdersController')->orderStatusMailSend($order_id);
  }catch(\Exception $e){}
}

//EAN13 Barcode
if(!function_exists('drmGetBarcodeEAN13'))
{
  function drmGetBarcodeEAN13($code, $type = 'C39+', $w = 2, $h = 30, $color = array(0, 0, 0), $showCode = true)
  {
    if(is_null($code)) return;
    try{
      return '<img src="data:image/png;base64,' . \Milon\Barcode\Facades\DNS1DFacade::getBarcodePNG($code, $type, $w, $h, $color, $showCode) . '" alt="'.$code.'"/>';
    }catch(\Exception $e){
      return $code;
    }
  }
}

function notificationToTelegram($txt)
{
    return;
    try {
        $telegramUrl = 'https://api.telegram.org/bot';
        $token = '**********************************************';
        $chatId = 1226369782;
        file_get_contents($telegramUrl . $token . '/sendMessage?chat_id=' . $chatId . '&text=' . $txt . '&parse_mode=HTML');
    } catch(Exception $exception) {

    }
}

function findProductByEan($user_id, $ean) {

      // $data['ean'] = "1223464"; // test product ean
      // $id = 10; //test drm product id

      //determine is_marketplace product or not
      $drm_product = DB::table('drm_products')->where('user_id', $user_id)->where('ean', $ean)->whereNotNull('marketplace_product_id')->first();


       // find fake(DROPMATRIX) or Real Supplier and Product ID's
       if( $drm_product ) {
          //find product from mp_products
          $mp_product = DB::connection('marketplace')->table('marketplace_products')->where('id', $drm_product->marketplace_product_id)->where('ean', $drm_product->ean)->first();

          if( $mp_product ) {

              if($mp_product->supplier_id != null) { //Real supplier
                  $mp_product_details = $mp_product;
                  $supplier_details = DB::table("cms_users")->find($mp_product_details->supplier_id);
                  //send notification with product id to real supplier(supplier_id)
                  //call notify function here
                dd($mp_product_details, $supplier_details);
              } else if($mp_product->supplier_id == null){ //Fake supplier = DROPMATRIX(2455)
                  $mp_product_details = $mp_product;
                  //send notification with product id to fake supplier(DROPMATRIX = 2455)
                  //call notify function here

              }
          }
      }
      //dd("finish line");
  }

function sendMarketplaceNotification($orderId) {

  $findProduct = DB::table('new_orders')->where('id',$orderId)->first();
  $orders = json_decode($findProduct->cart, true);

  // foreach ($orders as $order) {

      app('App\Http\Controllers\AdminDrmAllOrdersController')->getMarketplaceOrderSupplier($orderId);

      dd("executation done");
      // findProductByEan($findProduct->cms_user_id, $order['ean']);

  // }

}

if (!function_exists('curl_client_response')) {
  function curl_client_response(Exception $e): string
  {
      try {
          return json_decode((string)$e->getResponse()->getBody(), true);
      } catch (\Throwable $th) {
          return $e->getMessage();
      }
  }
}

if (!function_exists('notify_stock_update')) {
  function notify_stock_update($product_id, $user_id)
  {
      $ch = curl_init();
      curl_setopt_array($ch, array(
          CURLOPT_URL => "http://165.22.24.129/api/notify_stock_update",
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POSTFIELDS => [
              'user_id' => $user_id,
              'product_id' => $product_id
          ]
      ));
      curl_exec($ch);
      curl_close($ch);
  }
}

if (!function_exists('notify_uvp_update')) {
  function notify_uvp_update($product_id, $user_id)
  {
      $ch = curl_init();
      curl_setopt_array($ch, array(
          CURLOPT_URL => "http://165.22.24.129/api/notify_uvp_update",
          CURLOPT_CUSTOMREQUEST => "POST",
          CURLOPT_POSTFIELDS => [
              'user_id' => $user_id,
              'product_id' => $product_id
          ]
      ));
      curl_exec($ch);
      curl_close($ch);
  }
}


if(!function_exists('_log')){
    function _log($string, $filename = 'logs.txt'){
        $filename = public_path($filename);
        $string = print_r($string, true);
        file_put_contents($filename, "[".microtime(true)."] ".$string.PHP_EOL , FILE_APPEND | LOCK_EX);
    }
}

//SS ENV
if(!function_exists('_ssENV')){
    function _ssENV($key, $default = null){
        $string = env($key);
        if(is_null($string)) return $default;
        $ciphering = "AES-128-CTR"; //Cipher method
        $iv_length = openssl_cipher_iv_length($ciphering); // Use OpenSSl Encryption method
        $options = 0;
        $encryption_iv = '1234567341011121'; // Non-NULL Initialization Vector for encryption
        $encryption_key = "mcXaqcTOFqG"; //encryption key
        return openssl_decrypt ($string, $ciphering, $encryption_key, $options, $encryption_iv);
    }
}

/**
 * Return single option
 */
if(!function_exists('get_option_drm')){
  function get_option_drm($key, $group, $user_id = null)
  {
    $op = DB::table('options')->select('*');
    $op->where('option_key', '=', $key);
    $op->where('option_group', '=', $group);
    if($user_id){
        $op->where('user_id', '=', $user_id);
    }

    return $op->first();
  }
}


/**
 * Channel products shipping cost
 */
if(!function_exists('channel_product_shipping_cost'))
{
    function channel_product_shipping_cost($drm_product_id, $channel)
    {
        $product = \App\Models\ChannelProduct::with('calculation:id,dynamic_shipping_cost,shipping_cost')
            ->where('drm_product_id', '=', $drm_product_id)
            ->where('channel', '=', $channel)
            ->select('calculation_id', 'shipping_cost')
            ->first();

        if ($product->calculation_id && $product->calculation) {
            if ($product->calculation->dynamic_shipping_cost) {
                return $product->shipping_cost;
            }
            return $product->calculation->shipping_cost;
        }

        return $product->shipping_cost ?? 0;
    }
}

/**
 * Get channel name
 */
if(!function_exists('getChannelName'))
{
    function getChannelName($channel)
    {
        $channels = config('channel.list');
        $channelName = collect($channels)->where('type', $channel)->first();
        $channelName = ucfirst(strtolower($channelName['name']));
        return $channelName;
    }
}

if (!function_exists('debug_log')) {
  function debug_log(string $message, string $flag)
  {
      \App\Models\Log::create([
          'message' => $message,
          'module' => $flag
      ]);
  }
}

if(!function_exists('orderCountryName')){
    function orderCountryName($order): string
    {
        if(@$order->customer_info)
        {
            return @json_decode($order->billing, true)['country'] ?? "de";
        }
        return "";
    }
}

if(!function_exists('orderCustomerName')){
    function orderCustomerName($order): string
    {
        if(@$order->customer_info)
        {
            return @json_decode($order->billing, true)['name'] ?? "";
        }
        return "";
    }
}


//Tax.php Rate calculate NewOrder, country, is_small
function drm_order_tax_rate($order, $customer_country = 'de', $small_business = null)
{
    $tax_rate = 21;
    $tax = 0;
    $manual_tax_rate = 0;

    $country_code = countryCodeTax($customer_country, true);
    $is_german = (is_null($customer_country) || ($customer_country == '') || ($country_code == 4)) ? true : false;

    if ($order->insert_type == 6) {
        $products = $order->products;
        if ($products) {
            $tax_col = array_column($products, 'tax');
            $manual_tax_rate = reset($tax_col);
        }

        return ['tax_rate' => $manual_tax_rate, 'tax' => $order->total_tax, 'total' => $order->total];

    } else if (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) {
        $tax_rate = 21;
        $tax_rate = ($is_german && ($small_business == 1)) ? 0 : $tax_rate;

        if( (int)$order->cms_user_id === 2454 && !is_null($order->tax_rate) ) {
          $tax_rate = $order->tax_rate;
        }

        if (in_array($order->insert_type, [4, 8])) {
            $vat_number = $order->vat_number;
            if ($vat_number) {
                $tax_rate = 0;
                $tax = 0;
            } else {
                $tax = (($order->total * 21) / 100);
            }
            $tax_rate = ($is_german && ($small_business == 1)) ? 0 : $tax_rate;
            return ['tax_rate' => $tax_rate, 'tax' => $tax, 'total' => $order->total];
        }

    } else {
        // Order date before 1 July
        $order_date = strtotime($order->order_date);
        $date = strtotime("07/01/2020");

        //From july 7 to decmber 1st 2020 german tax 16 percent
        $january_first = strtotime("01/01/2021");
        $tax_rate = ($is_german && (($order_date >= $date) && ($order_date < $january_first))) ? 16 : countryCodeTax($customer_country);
        $tax_rate = !empty($order->tax_rate)? $order->tax_rate : $tax_rate;

        // API ORDER FOR GENERAL CUSTOMER WITH 0 TAX
        $tax_rate = (int)$order->tax_version === 1 ? (int)$order->tax_rate : $tax_rate;

        //$tax_rate = ($is_german && $order_date<$date)? 19 : countryCodeTax($customer_country);
        $tax_rate = (($small_business == 1) && $is_german) ? 0 : $tax_rate;
    }


    $tax = (float)orderTaxForNet($order->total, $tax_rate);

    return ['tax_rate' => $tax_rate, 'tax' => $tax, 'total' => $order->total];
}

//DRM Order status email parse
//get status email template
function getStatusMailTemplate($user_id, $order_status, $channel = null)
{
  if($channel){
    $cache_name = $order_status . '_email_' . $user_id . '_' . $channel;
  }else{
    $cache_name = $order_status . '_email_' . $user_id;
  }

  // if(isLocal() || in_array($user_id, [212, 2592, 2991])){
    return Cache::rememberForever($cache_name, function () use ($user_id, $order_status, $channel) {
      if($channel){
        return DB::table('order_mail_templates')->where('order_status', $order_status)->where('user_id', $user_id)->where('channel', $channel)->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')->first();
      }else{
        return DB::table('order_mail_templates')->where('order_status', $order_status)->where('user_id', $user_id)->whereNull('channel')->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')->first();
      }
    });
  // }else{
  //   $cache_name = $order_status . '_email_' . $user_id;
  //   return Cache::rememberForever($cache_name, function () use ($user_id, $order_status) {
  //       return DB::table('order_mail_templates')->where('order_status', $order_status)->where('user_id', $user_id)->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')->first();
  //   });
  // }
}

//Get email conent, subject from email page
function DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status, $channel = null)
{
  $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
  $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

  $content = null;
  $subject = null;
  $bcc = null;
  $send_invoice = 0;
  $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

  if($channel){
    $page = getStatusMailTemplate($user_id, $order_status, $channel);
  }else{
    $page = getStatusMailTemplate($user_id, $order_status);
  }

  if ($page) {
    $content = empty($page->email_template) ? config('system_email_settings.email_settings') : $page->email_template;
    $subject = $page->mail_subject;
    $bcc = $page->bcc_email;
    $send_invoice = $page->send_invoice;
    if (!empty($page->sender_email)) {
        $senderEmail = $page->sender_email;
    }

    foreach ($tags as $k => $value) {
        $find = '[' . $k . ']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
            $content = str_replace($find, $value, $content);
        }
        if ($tags[$tag] == 'true') {
            $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
        } else {
            $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
            // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
            $subject = str_replace($find, $value, $subject);
        }
        if ($tags[$tag] == 'true') {
            $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
        } else {
            $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
            // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
    }
  }

  return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'send_invoice' => $send_invoice, 'senderEmail' => $senderEmail];
}

//get combine status email template
function getCombineStatusMailTemplate($user_id, $channel = null)
{
  if($channel){
    $cache_name = 'combine_status_email_settings_' . $user_id . '_' . $channel;
  }else{
    $cache_name = 'combine_status_email_settings_' . $user_id;
  }

  // if(isLocal() || in_array($user_id, [212, 2592, 2991])){
      return Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
        return findSystemMail('combine_status_email_settings', $user_id, $channel);
      });
  // }else{
  // return Cache::rememberForever('combine_status_email_settings_' . $user_id, function () use ($user_id) {
  //     return DB::table('combine_status_email_settings')->where('cms_user_id', $user_id)->first();
  // });
  // }
}

//Combine Status email parse
//Get email conent, subject from email page
function DRMParseOrderCombineStatusEmailTemplate($tags, $user_id, $channel = null)
{
  $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
  $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

  $content = null;
  $subject = null;
  $bcc = null;
  $send_invoice = 0;
  $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

  if($channel){
      $page = getCombineStatusMailTemplate($user_id, $channel);
  }else{
  $page = getCombineStatusMailTemplate($user_id);
  }

  if ($page) {
      $content = empty($page->email_template) ? config('system_email_settings.email_settings') : $page->email_template;
      $subject = $page->mail_subject;
      $bcc = $page->bcc_email;
      $send_invoice = $page->send_invoice;
      if (!empty($page->sender_email)) {
          $senderEmail = $page->sender_email;
      }

      foreach ($tags as $k => $value) {
          $find = '[' . $k . ']';
          $tag = $k;
          if (strpos($content, $find) !== false) {
              $content = str_replace($find, $value, $content);
          }
          if ($tags[$tag] == 'true') {
              $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
          } else {
              $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
              // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
          }

          if (strpos($subject, $find) !== false) {
              $subject = str_replace($find, $value, $subject);
          }
          if ($tags[$tag] == 'true') {
              $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
          } else {
              $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
              // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
          }
      }
  }

  return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'send_invoice' => $send_invoice, 'senderEmail' => $senderEmail];
}

function userWiseVkPriceCalculate($vk_price, $user_id, $product_view = false, $discount = 0.0, $api_id = null)
{
    $agreement = DB::table('mp_payment_agreements')
                ->where('user_id', '=', $user_id)
                ->where('type', '=', 1)
                ->select('price_markup')
                ->first();

    $is_demo_fifteen = DB::table('cms_users')
                ->where('id', $user_id)
                ->where('id_cms_privileges', 13)
                ->exists();

    $price_markup = $agreement ? $agreement->price_markup : 0.0;
    $vk_price = $vk_price + (($price_markup * $vk_price) / 100);
    $vk_price = $vk_price - (($discount * $vk_price) / 100);

    // Apply 5% discount for specific users
    if($user_id == 3938 || ($api_id == 5 && in_array($user_id, [4293, 3602]))){
      $vk_price = $vk_price - ($vk_price * 0.05);
    }

    if($user_id == 2872){
      $vk_price = $vk_price - ($vk_price * 0.08);
    }

    if($is_demo_fifteen){
      $vk_price = $vk_price - ($vk_price * 0.15);
    }

    if($product_view){
      return number_format($vk_price, 2, ',', '.');
    }

    return round($vk_price, 2);
  
}

if(!function_exists('findSystemMail')){
  function findSystemMail($table, $user_id, $channel = null)
  {
    $mail_temp = DB::table($table)
            ->where('cms_user_id', $user_id)
            ->where('channel', $channel)
            ->where(function($q) use ($channel){
                $q->where('channel', $channel);
                $q->orWhereNull('channel');
            })
            ->orderBy('channel', 'desc')
            ->first();

    return $mail_temp;
  }
}

if(!function_exists('getMpTurnoverCreditBasedOnRange')){
  function getMpTurnoverCreditBasedOnRange($value)
  {
    if ($value >= 1 && $value < 5) {
      return 2;
    } elseif ($value >= 5 && $value < 15) {
      return 5;
    } elseif ($value >= 15 && $value < 30) {
      return 7;
    } elseif ($value >= 30 && $value < 100) {
      return 15;
    } elseif ($value >= 100) {
      return 30;
    } else {
      return 1;
    }
  }
}

function get_token_credit($user_id = null)
{
  $creditService = new CreditService;

  return [
      'total_credit' => $creditService->allCredit($user_id),
      'used_credit' => $creditService->usedCredit($user_id),
      'remain_credit' => $creditService->remainingCredit($user_id),
  ];
  
    // $op = DB::table('purchase_import_plans')->select('*');
    // //$op->where('option_key', '=', $key);
    // //$op->where('option_group', '=', $group);
    // if($user_id){
    //     $op->where('cms_user_id', '=', $user_id);
    // }

    $total_credit = DrmUserCreditAddLog::where('user_id', $user_id)->where('status', 1)->sum('credit');
    $remain_credit = DrmUserCredit::where('user_id', $user_id)->value('credit');

    $token_credits['total_credit'] = $total_credit ?? 0;

    $token_credits['remain_credit'] = $remain_credit ?? 0;

    $token_credits['used_credit'] = $token_credits['total_credit'] - $token_credits['remain_credit'];


    // $token_credits['total_credit'] = $import_plan_purchase->total_credit ?? 0;

    // $token_credits['remain_credit'] = $import_plan_purchase->credit ?? 0;

    // $token_credits['used_credit'] = $token_credits['total_credit'] - $token_credits['remain_credit'];
    //dd( $token_credits);
    //$token_credits['credit'] =  $token_credits['remain_credit'];

    return $token_credits;

    //return $op->first();
}


if (!function_exists('getPlanTypeOrHigher')) {
    function getPlanTypeOrHigher($user_id, $plan_type = 'deluxe'): bool {
        $plan_or_higher = false;
        $session_var = 'has_' . $plan_type . '_or_higher_' . $user_id;

        if (checkTariffEligibility($user_id)) {
            if (Session::has($session_var)) {
                $plan_or_higher = Session::get($session_var);
            } else {
                $plan_ids = $plan_type === 'deluxe' ? [25, 26, 27] : ($plan_type === 'professional' ? [26, 27] : [27]);

                $plan_or_higher = DB::table('purchase_import_plans')
                    ->where([
                        ['cms_user_id', $user_id],
                        ['status', 1],
                        ['end_date', '>=', date('Y-m-d')],
                    ])
                    ->whereIn('import_plan_id', $plan_ids)
                    ->exists();

                Session::put($session_var, $plan_or_higher);
            }
        }
        return $plan_or_higher;
    }
}


// user who has deluxe or higher import plan
if (!function_exists('deluxeOrHigher')) {
    function deluxeOrHigher($user_id = 0): bool {
        return getPlanTypeOrHigher($user_id,'deluxe');
    }
}

if (!function_exists('professionalOrHigher')) {
    function professionalOrHigher($user_id = 0): bool {
        return getPlanTypeOrHigher($user_id,'professional');
    }
}

if (!function_exists('deluxeOrHigherPackId')) {
    function deluxeOrHigherPackId($user_id): string {
        $packId = 0;
        if (checkTariffEligibility($user_id)) {
            $planNames = [
                25 => 'deluxe',
                26 => 'professional',
                27 => 'enterprise',
            ];
            $packId = DB::table('purchase_import_plans')
                ->where([
                    ['cms_user_id', $user_id],
                    ['status', 1],
                    ['end_date', '>=', date('Y-m-d')],
                ])
                ->whereIn('import_plan_id', [25, 26, 27])
                ->value('import_plan_id');
        }
        return $planNames[$packId] ?? "";
    }
}

  function checkTariffEligibility($id){

    $oldUserTrial = DB::table('old_users_tariff')->where('user_id',$id)->exists();

    if($oldUserTrial || $id > config('global.greater_user_id')){
        return true;
    }else{
        return false;
    }
  }

  if(!function_exists('checkDtUser')){
    function checkDtUser($user_id){
      return DB::table('user_group_relations')
      ->where('user_id', $user_id)
      ->where('group_id', 2)
      ->exists();
    }
  }

  if(!function_exists('DRMParseUnpaidInvoiceEmailTemplate')){
    function DRMParseUnpaidInvoiceEmailTemplate($tags, $user_id, $order_status)
    {
      $subject = '';
      $content = '';

      if($order_status == 'mahnung'){
        $subject = config('unpaidOrderEmail.mahnung_order_warning_template.subject');
        $content = config('unpaidOrderEmail.mahnung_order_warning_template.body');
      }else if($order_status == 'inkasso'){
        $subject = config('unpaidOrderEmail.inkasso_order_warning_template.subject');
        $content = config('unpaidOrderEmail.inkasso_order_warning_template.body');
      }

      $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

      foreach ($tags as $k => $value) {
        $find = '[' . $k . ']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
            $content = str_replace($find, $value, $content);
        }
        if ($tags[$tag] == 'true') {
            $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
        } else {
            $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
            // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
            $subject = str_replace($find, $value, $subject);
        }
        if ($tags[$tag] == 'true') {
            $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
        } else {
            $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
            // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
      }

      return ['subject' => $subject, 'body' => $content, 'senderEmail' => $senderEmail];

    }
  }


if(!function_exists('job_exists')){
    function job_exists($jobId,$queueName,$searchData = array()): bool
    {
        $jobs = \Illuminate\Support\Facades\Redis::connection()->lrange('queues:' . $queueName, 0, -1);
        foreach ($jobs as $job) {
            $decodedJob = json_decode($job, true);

            // Check if the job matches the provided ID
            if ($decodedJob['id'] === $jobId) {
                // Check if the job data matches the provided data
                if (isset($decodedJob['data']['data'])) {
                    $jobData = json_decode($decodedJob['data']['data'], true);
                    if ($jobData === $searchData) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
}

if(!function_exists('isEnterpricelUser')){
  function isEnterpricelUser($user_id)
  {
      $checkUserTariff = DB::table('purchase_import_plans')
          ->where('cms_user_id', $user_id)
          ->where('import_plan_id', 27)
          ->whereDate('end_date', '>=', now())
          ->exists();
  
      return $checkUserTariff;
  }
}

if(!function_exists('isTrialUser')){
  function isTrialUser($user_id){
      $today = now();
      $is_trial_user = DB::table('app_trials')
          ->where(['user_id' => $user_id, 'app_id' => 0])
          ->whereRaw("DATE_ADD(start_date, INTERVAL trial_days DAY) >= '$today'")
          ->exists();

      return $is_trial_user;
  }
}

if(!function_exists('isEnterpriceOrTrialUser')){
  function isEnterpriceOrTrialUser($user_id)
  {
     return isEnterpricelUser($user_id) || isTrialUser($user_id) ? true : false;
  }
}


if(!function_exists('hasV2Access')){
  function hasV2Access($userId)
  {
     return isset(\App\Enums\V2UserAccess::USERS[$userId]);
  }
}

if (!function_exists('temp_logs_insert')) {
  function temp_logs_insert($msg) {
    $log_info = DB::table('contact_forms')->where('id', 85)->where('user_id', 3351)->value('thank_you_json');
    $log_info = !empty($log_info) ? json_decode($log_info) : [];
    $log_info[] = now() . ' ' . $msg;

    DB::table('contact_forms')->where('id', 85)->where('user_id', 3351)->update([
      'thank_you_json' => json_encode($log_info),
    ]);

    return true;
  }
}
