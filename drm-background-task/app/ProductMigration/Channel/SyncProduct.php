<?php

namespace App\ProductMigration\Channel;

use App\ProductMigration\MigrationHelper;
use App\ProductMigration\Models\ChannelProductOld;
use App\ProductMigration\Models\Product;

class SyncProduct
{
    public function sync(array $productsId)
    {   $templates = MigrationHelper::industryTemplates();
        $service = new NormalizeClone;

        $newProductTable = with(new Product)->getTable();
        $channelProductTable = with(new ChannelProductOld)->getTable();

        $processed = 0;
        ChannelProductOld::with('mainProduct:id,image', 'channelCategories:category_id,channel_product_id,category_type')
            ->leftJoin($newProductTable, function ($join) use ($channelProductTable, $newProductTable) {
                $join->on($channelProductTable.'.drm_product_id', '=', $newProductTable.'.drm_original_id')
                    ->whereColumn($channelProductTable.'.country_id', '=', $newProductTable.'.country_id');
            })
            ->whereNotNull($newProductTable.'.id')
            ->whereIn($channelProductTable.'.id', $productsId)
            ->select($channelProductTable.'.*', $newProductTable.'.id as new_drm_product_id')
            ->orderBy($channelProductTable.'.id', 'asc')
            ->get()
            ->groupBy('channel')
                    ->each(function ($products, $channel) use ($service, $templates, &$processed) {
                        $channelProductCategoryLoader = new ChannelProductCategoryLoader;
                        $channelProductCategoryLoader->load($products, (int) $channel);

                        $products->each(function ($product) use ($channelProductCategoryLoader, $service, $templates) {
                            $channelProductCategoryLoader->setRelation($product);
                            $service->__invoke($product, $templates);
                        });

                        $processed += $products->count();
                        echo "\rProcessed: ".$processed;
                    });

    }

}
