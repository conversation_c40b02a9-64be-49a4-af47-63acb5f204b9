<?php

namespace App\ProductMigration\Channel;

use Illuminate\Support\Facades\DB;

class DeleteProduct
{
    public function sync(array $id)
    {
        if(empty($id)) return;

        DB::table('shop_products')->whereIn('id', $id)->update(['deleted_at' => now()]);

        DB::transaction(function () use ($id) {
            DB::table('channel_product_localizations')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_images')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_ean')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_price')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_droptienda')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_mirakl')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_stock')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_attributes')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_manufacturer')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_template')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_aspects')->whereIn('channel_product_id', $id)->delete();

            // Both side delete
            DB::table('channel_product_variants')
                ->whereIn('channel_product_id', $id)
                ->orWhereIn('variant_product_id', $id)
                ->delete();

            DB::table('channel_product_status')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_universal_feed_items')->whereIn('product_id', $id)->delete();
            DB::table('channel_product_categories_pivot')->whereIn('channel_product_id', $id)->delete();
            DB::table('channel_product_user_categories_pivot')->whereIn('channel_product_id', $id)->delete();
            DB::table('shop_products')->whereIn('id', $id)->delete();
        });
    }
}
