<?php

namespace App\ProductMigration;

use Illuminate\Support\Facades\DB;

class MigrationHelper
{
    /**
     * Category columns
     */
    public static function categoryColumns(): array
    {
        return [
            'category_name_en',
            'category_name_de',
            'category_name_fr',
            'category_name_it',
            'category_name_nl',
            'category_name_es',
            'category_name_sv',
            'category_name_pl',
            'category_name_ru',
            'category_name_bg',
            'category_name_da',
            'category_name_et',
            'category_name_fi',
            'category_name_el',
            'category_name_ga',
            'category_name_hr',
            'category_name_lv',
            'category_name_lt',
            'category_name_lb',
            'category_name_mt',
            'category_name_pt',
            'category_name_ro',
            'category_name_sk',
            'category_name_sl',
            'category_name_cs',
            'category_name_hu',
        ];
    }

    /**
     * Category name string
     */
    public static function sanitizeCategoryName(?string $string): ?string
    {
        if ($string && str_contains($string, '&gt;')) {
            $string = str_replace('&gt;', ' > ', $string);
            $string = preg_replace('!\s+!', ' ', $string);
        }

        return $string ? trim($string) : $string;
    }

    /**
     * Language map
     */
    public static function getCountryMap(): array
    {
        $langMap = DB::table('countries')
            ->select('id', 'language_shortcode')
            ->get()
            ->groupBy('language_shortcode')
            ->map(function ($item) {
                return $item->first()->id;
            })
            ->toArray();

        $langMap['de'] = 1;
        $langMap['en'] = 2;
        $langMap['fr'] = 5;
        $langMap['nl'] = 61;
        $langMap['es'] = 8;
        $langMap['sv'] = 9;
        $langMap['pl'] = 10;
        $langMap['it'] = 68;
        $langMap['lt'] = 71;
        $langMap['pt'] = 75;
        $langMap['el'] = 66;

        return $langMap;
    }

    /**
     * Get user specific map
     */
    public static function getUserSpecificMap(array $data, int $userId): array
    {
        if ($userId == 3288) {
            $data['en'] = 84;
        } else {
            $data['en'] = 2;
        }

        return $data;
    }

    /**
     * Format feed fields
     */
    public static function formatFeedFields($fields): ?string
    {
        $data = $fields && MigrationHelper::isJson($fields) ? json_decode($fields, true) : [];

        if (array_key_exists('images', $data)) {
            $data['image'] = $data['images'];
            unset($data['images']);
        }

        if (array_key_exists('industry_template_data', $data)) {
            $data['industry_template'] = 'industry_template';
            unset($data['industry_template_data']);
        }

        return !empty($data) ? json_encode($data) : null;
    }

    /**
     * Sanitize delimiter
     */
    public static function sanitizedDelimiter(?string $delimiter): ?string
    {
        return ($delimiter == '\t') ? 'tab' : $delimiter;
    }

    /**
     * Json to array
     */
    public static function jsonToArray($data): array
    {
        if (MigrationHelper::isJson($data)) {
            return json_decode($data, true);
        }

        return [];
    }

    /**
     * Is json
     */
    public static function isJson($value): bool
    {
        json_decode($value);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Decode array cast
     */
    public static function decodeArrayCast($value)
    {
        if (is_array($value)) {
            return $value ? json_encode($value) : null;
        }

        return MigrationHelper::isJson($value) ? $value : null;
    }

    /**
     * Industry templates
     */
    public static function industryTemplates(): array
    {
        $defaultTemplate = [
            'juvely' => 1,
            'wine' => 2,
            'supplements' => 3,
            'spielwaren' => 4,
        ];

        $templates = DB::table('custom_industry_templates')
            ->whereNotNull('new_id')
            ->select('fields', 'new_id', 'user_id')
            ->get()
            ->map(function ($item) {
                $fields = @json_decode($item->fields, true) ?? [];

                return [
                    'id' => $item->new_id,
                    'fields' => is_array($fields) ? array_values(array_column($fields, 'name')) : [],
                    'user_id' => $item->user_id,
                ];
            })->filter(function ($item) {
                return ! empty($item['fields']);
            })
            ->values()
            ->toArray();

        $globalTemplates = (new IndustryTemplate)->all();

        foreach ($globalTemplates as $name => $data) {
            if (isset($defaultTemplate[$name])) {
                $templates[] = [
                    'id' => $defaultTemplate[$name],
                    'fields' => array_keys($data),
                    'user_id' => 1,
                ];

            }
        }

        return $templates;
    }


    /**
     * Store log
     */
    public static function logStore($message)
    {
        $string = print_r($message, true);
        $string = substr($string, 0, 1000);

        @file_put_contents('v2_migration.log', "[".date('Y-m-d H:i:s')."] ".$string.PHP_EOL , FILE_APPEND | LOCK_EX);
    }

    /**
     * Format param ID
     */
    public static function formatAPIParamId($id): array
    {
        if(empty($id)) return [];

        return !is_array($id) ? [$id] : $id;
    }

    /**
     * Format param ID for log
     */
    public static function formatAPIParamIdForLog($id)
    {
        return is_array($id) ? '['.implode(',', $id).']' : $id;
    }

        /**
     * Trim string
     */
    public static function trimString($value): ?string
    {
        if (empty($value)) {
            return null;
        }

        return trim($value);
    }

    /**
     * Sanitize title
     */
    public static function sanitizeTitle($value = null): ?string
    {
        if (empty($value)) {
            return null;
        }

        $value = (string) $value;

        if (str_contains($value, '|') && strlen($value) > 254) {
            $parts = explode('|', $value);

            return trim($parts[0]);
        }

        if (str_contains(strtolower($value), 'lorem ipsum') && strlen($value) > 254) {
            $value = substr('Hello world', 0, 254);
        }

        return $value ? trim($value) : null;
    }

    /**
     * Sanitize update status value
     */
    public static function sanitizeUpdateStatusValue($value = null): ?string
    {
        if (empty($value) || ! MigrationHelper::isJson($value)) {
            return null;
        }

        $data = json_decode($value, true);
        if (! is_array($data)) {
            return null;
        }

        if (array_key_exists('images', $data)) {
            $data['image'] = $data['images'];
            unset($data['images']);
        }

        if (array_key_exists('id', $data)) {
            unset($data['id']);
        }

        if (array_key_exists('industry_template_data', $data)) {
            $data['industry_template'] = $data['industry_template_data'];
            unset($data['industry_template_data']);
        }

        $preventKeys = [
            'marketplace_product_id',
            'shipping_method_id',
            'shipping_company_id',
            'mp_price_markup',
            'marketplace_delivery_company_id',
            'marketplace_supplier_id',
            'marketplace_shipping_method',
            'mp_category_offer',
            'mp_offer',
        ];

        foreach ($preventKeys as $key) {
            if (array_key_exists($key, $data)) {
                unset($data[$key]);
            }
        }

        return json_encode($data) ?: null;
    }

    /**
     * Get localize json target value
     */
    public static function getLocalizeJsonTargetValue($value): ?string
    {

        if (empty($value)) {
            return null;
        }

        $arrayValue = is_array($value) ? $value : json_decode($value, true);

        if (empty($arrayValue) || ! is_array($arrayValue)) {
            return null;
        }

        $arrayValue = array_filter($arrayValue);
        $arrayValue = reset($arrayValue);

        return ! empty($arrayValue) ? (string) $arrayValue : null;
    }

    /**
     * Cast status value
     */
    public static function castStatusValue($value): ?int
    {
        $value = (string) $value;


        switch ($value) {
            case '1000':
                return 1;
            case '1500':
                return 2;
            case '1750':
                return 3;
            case '2000':
                return 4;
            case '2500':
                return 5;
            case '2750':
                return 6;
            case '3000':
                return 7;
            case '4000':
                return 8;
            case '5000':
                return 9;
            case '6000':
                return 10;
            case '7000':
                return 11;
            case '0':
                return 0;
            case '1':
                return 21;
            case '2':
                return 22;
            case '3':
                return 23;
            case '4':
                return 24;
            case '5':
                return 25;
            case '6':
                return 26;
            case '7': 
                return 27;
            default:
                return null;
        }
    }

    /**
     * Status json key formated data
     */
    public static function jsonKeyFormattedStatusData($data): ?string
    {
        if (empty($data) || ! is_array($data)) {
            return null;
        }

        $payload = [];

        foreach ($data as $key => $value) {
            $label = MigrationHelper::castStatusValue($key);

            if (empty($label)) {
                continue;
            }

            $payload[$label] = $value;
        }

        return ! empty($payload) ? json_encode($payload) : null;
    }

    /**
     * Sanitize selected value
     */
    public static function sanitizeSelectedValue($value = null)
    {
        if (! empty($value) && str_contains(strtolower($value), 'select')) {
            return null;
        }

        return $value;
    }

    /**
     * Safe cast int
     */
    public static function safeCastInt($value)
    {
        if (is_null($value)) {
            return 0;
        }

        if (round($value, 2) > (int) $value) {
            return round($value, 2);
        }

        return (int) $value;
    }

    /**
    * Normalize string
    */
    public static function normalizeString(string $value)
    {
        $value = preg_replace('/[^A-Za-z0-9]/', '', $value);

        return mb_convert_encoding($value, 'UTF-8', 'UTF-8');
    }
}
