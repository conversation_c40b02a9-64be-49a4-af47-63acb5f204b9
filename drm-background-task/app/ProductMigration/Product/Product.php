<?php

namespace App\ProductMigration\Product;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $table = 'drm_products_new';

    protected $fillable = [
        'user_id',
        'drm_import_id',
        'delivery_company_id',
        'country_id',
        'item_number',
        'ean',
        'rq',
        'product_type',
        'cloned_from',
        'parent_id',
        'brand_id',
        'shipping_method_id',
        'shipping_company_id',

        'drm_original_id', // Temporary
    ];

    public function additionalEan()
    {
        return $this->hasMany(ProductEan::class, 'drm_product_id');
    }

    /**
     * EAN search
     */
    public function scopeEanSearch(Builder $query, array $eanArr)
    {
        $query->whereIn($this->table.'.ean', $eanArr)
            ->orWhereHas('additionalEan', function ($additionalEan) use ($eanArr) {
                $additionalEan->whereIn('drm_product_ean.ean', $eanArr);
            });

        return $query;
    }
}
