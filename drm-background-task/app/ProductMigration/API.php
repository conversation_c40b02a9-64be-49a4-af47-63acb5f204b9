<?php

namespace App\ProductMigration;

use App\ProductMigration\Enums\Resource;
use Illuminate\Support\Facades\DB;

class API
{
    /**
     * Sync drm product
     */
    public static function syncProduct($productId)
    {
        self::insertLog($productId,Resource::DRM_PRODUCT,1);
    }

    /**
     * Delete drm product
     */
    public static function deleteProduct($productId, string $lang)
    {
        self::insertLog($productId,Resource::DRM_PRODUCT,2);
    }

    /**
     * Sync drm product category
     */
    public static function syncProductCategory($categoryId)
    {
        if(!isLocal()) return;
    }

    /**
     * Delete drm product category
     */
    public static function deleteProductCategory($categoryId)
    {
        if(!isLocal()) return;
    }

    /**
     * Sync channel product
     */
    public static function syncChannelProduct($productId)
    {
        self::insertLog($productId,Resource::CHANNEL_PRODUCT,1);
    }

    /**
     * Delete channel product
     */
    public static function deleteChannelProduct($productId)
    {
        self::insertLog($productId,Resource::CHANNEL_PRODUCT,2);
    }

    private static function insertLog($resourceId, int $resourceType, int $event)
    {
        if(!is_array($resourceId)){
            $resourceId = [$resourceId];
        }
        foreach ($resourceId as $id){
            DB::table('resource_change_logs')->upsert([
                'resource_type' => $resourceType,
                'resource_id' => $id,
                'event' => $event
            ], ['resource_type','resource_id'], ['created_at','event']);
        }
    }
}
