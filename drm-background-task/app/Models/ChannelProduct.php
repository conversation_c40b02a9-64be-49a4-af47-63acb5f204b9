<?php

namespace App\Models;

use App\DrmProduct;
use App\Enums\Channel;
use App\Enums\ChannelProductConnectedStatus;
use App\Jobs\ChannelManager\ChangeChannelProductConnectionStatus;
use App\Services\ChannelProductService;
use App\Shop;
use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use App\Models\Product\ProfitCalculation;
use App\ChannelPriceHistory;
use App\Models\Product\AnalysisProduct;
use Carbon\Carbon;
use App\CustomIndustryTemplate;
use App\AllCountry;
use Illuminate\Support\Facades\DB;

class ChannelProduct extends Model
{
    protected $fillable = [
        'user_id',
        'drm_product_id',
        'channel',
        'shop_id',
        'status',
        'title',
        'item_number',
        'ean',
        'additional_eans',
        'description',
        'short_description',
        'internal_comments',
        'images',
        'ek_price',
        'vk_price',
        'min_price',
        'max_price',
        'cheapest_price',
        'stock',
        'category_id',
        'item_weight',
        'item_unit',
        'item_size',
        'item_color',
        'note',
        'production_year',
        'brand',
        'materials',
        'tags',
        'gender',
        'industry_template_data',
        'update_status',
        'calculation_id',
        'delivery_company_id',
        'delivery_days',
        'drm_import_id',
        'country_id',
        'attributes',
        'variants',
        'is_connected',
        'price_droptienda',
        'shipping_cost',
        'connection_status',
        'missing_attributes',
        'error_response',
        'rq_limit',
        'rq',
        'price_on_request',
        'offer_uvp',
        'offer_options',
        'tax_included_price',
        'metadata',
        'drm_categories',
        'manufacturer',
        'manufacturer_link',
        'manufacturer_id',
        'custom_tariff_number',
        'region',
        'country_of_origin',
        'min_stock',
        'gross_weight',
        'net_weight',
        'product_length',
        'product_width',
        'product_height',
        'shipping_method_id',
        'shipping_company_id',
        'options',
        'stock_of_status',
        'price_of_status',
        'packaging_dimensions',
        'packaging_length',
        'packaging_width',
        'packaging_height',
        'min_order',
        'packaging_unit',
        'offer_id',
        'decathlon_status',
        'decathlon_last_sync',
        'job_token',
        'repricing_last_sync',
        'repricing_id',
        'category_aspects',
        'marketplace_delivery_company_id',
        'marketplace_product_id'
    ];

    protected $casts = [
        'title' => 'array',
        'description' => 'array',
        'offer_options' => 'array',
        'short_description' => 'array',
        'update_status' => 'array',
        'attributes' => 'array',
        'variants' => 'array',
        'metadata' => 'array',
        'missing_attributes' => 'array',
        'error_response' => 'array'
    ];


    public static function boot()
    {
        parent::boot();

        static::created(function ($item) {
            ChangeChannelProductConnectionStatus::dispatch($item->id);
        });

        static::updating(function($item) {

            if ($item->isDirty('vk_price') || $item->isDirty('item_weight') || $item->isDirty('item_unit')){
                if ($item->channel == Channel::DROPTIENDA && !empty($item->basic_price)){
                    $base_price = self::updateBasicPrice($item->vk_price, $item->item_weight, $item->item_unit);

                    if($base_price != null){
                        $base_price = round($base_price, 2);

                        $item->basic_price = $base_price;
                    }

                    if( empty($item->vk_price) ){
                        $item->basic_price = null;
                    }else if( empty($item->item_weight) ){
                        $item->basic_price = null;
                    }else if( empty($item->item_unit) ){
                        $item->basic_price = null;
                    }
                }
            }

            if ( $item->isDirty('vk_price') ){

                if($item->getOriginal('vk_price') != null){
                    $item->old_vk_price = $item->getOriginal('vk_price');
                }

                $item->vk_price_updated_at = Carbon::now()->toDateTimeString();
            }

        });

        static::updated(function ($item) {
            $changes = array_keys($item->getChanges());
            if ($item->channel == Channel::DROPTIENDA) {
                if (in_array('stock', $changes)) {
                    $old_stock = $item->getOriginal('stock');
                    if ($old_stock == 0 && $item->stock > 0) {
                        notify_stock_update($item->id,$item->user_id);
                    }
                }
                if (in_array('uvp', $changes)) {
                    $old_uvp= $item->getOriginal('uvp');
                    if ($old_uvp == 0 && $item->uvp > 0) {
                        notify_uvp_update($item->id,$item->user_id);
                    }
                }
            }
            $diff = array_diff($changes, ['connection_status', 'missing_attributes', 'updated_at', 'error_response', 'is_connected','tax_included_price']);
            if (!empty($diff)) {
                ChangeChannelProductConnectionStatus::dispatch($item->id);
            }
        });
    }

    public function getNameAttribute()
    {
        if ($this->country_id == 2) {
            $lang = data_get(request(), 'lang', 'en');
        } else {
            $lang = data_get(request(), 'lang', 'de');
        }
        return Arr::get($this->title, $lang);
    }


    public function getTitle($lang)
    {
        $title = is_array($this->title)?$this->title:json_decode($this->title,true);
        return $title[$lang] ?? "";
    }
    public function getDescription($lang)
    {
        $description = is_array($this->description)?$this->description:json_decode($this->description,true);
        return $description[$lang] ?? "";
    }

    public function getConnectedStatusAttribute()
    {
        if ($this->is_connected) {
            return ChannelProductConnectedStatus::CONNECTED;
        }

        if ($this->country_id == 2) {
            $lang = data_get(request(), 'lang', 'en');
        } else {
            $lang = data_get(request(), 'lang', 'de');
        }

        $requiredFields = array_merge(config('channel.required_fields.default', []), config('channel.required_fields.' . $this->channel, []));
        $readyToExport = true;
        foreach ($requiredFields as $field) {

            if (in_array($field, ['title', 'description'])) {
                if (empty($this->{$field}[$lang])) {
                    $readyToExport = false;
                }
            } elseif ($field == 'vk_price') {
                if (empty((float)$this->{$field}) && empty((float)$this->uvp)) {
                    $readyToExport = false;
                }
            } elseif ($field == 'category') {
                if (empty($this->category_name)) {
                    $readyToExport = false;
                }
            } elseif ($field == 'delivery_days' && $this->channel == 13) {
                if (empty($this->delivery_days)) {
                    $readyToExport = false;
                }
            } elseif ($field == 'brand' && $this->channel == 13) {
                if (empty($this->brand)) {
                    $readyToExport = false;
                }
            } elseif ($field == 'stock') {
                if (!is_numeric($this->stock)) {
                    $readyToExport = false;
                }
            } elseif (empty($this->{$field})) {
                $readyToExport = false;
            }
        }

        return $readyToExport ? ChannelProductConnectedStatus::READY_TO_EXPORT : ChannelProductConnectedStatus::MANDATORY_FIELD_MISSING;
    }

    public function getConnectedStatusLabelAttribute(): array
    {
        return [
            'status' => ChannelProductConnectedStatus::LABEL[$this->connection_status],
            'color' => ChannelProductConnectedStatus::BUTTON_COLOR[$this->connection_status],
            'fieldMissing' => $this->connection_status == ChannelProductConnectedStatus::MANDATORY_FIELD_MISSING,
            'actionBtn' => ChannelProductConnectedStatus::ACTION_ICON[$this->connection_status]
        ];
    }

    public function getImagesAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    public function setImagesAttribute($value)
    {
        $this->attributes['images'] = json_encode($value);
    }

    // Industry templete data
    public function getIndustryTemplateDataAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    public function setIndustryTemplateDataAttribute($value)
    {
        $this->attributes['industry_template_data'] = json_encode($value);
    }

    public function getTransDescriptionAttribute()
    {
        try {
            if ($this->country_id == 2) {
                $lang = data_get(request(), 'lang', 'en');
            } else {
                $lang = data_get(request(), 'lang', 'de');
            }
        } catch (\Throwable $th) {
            $lang = "de";
        }
        return Arr::get($this->description, $lang);
    }

    public function getFirstImageAttribute()
    {
        return Arr::get($this->images, '0');
    }

    public function getProfitAttribute()
    {
        try {
            $sellingPrice = $this->vk_price;
            if ($sellingPrice) {
                return round(($sellingPrice - $this->ek_price) / $this->ek_price * 100, 2) . '%';
            }
        } catch (\Throwable $th) {
            return '0%';
        }
    }

    public function getCategoryNameAttribute()
    {
//        if (isLocal()) {
//            $categories = array();
//            $all_categories[] = '<div class="table-responsive invoice_table_style" style="
//                                border: 1px solid #fd6500;
//                                padding-left: 3px;
//                                border-radius: 2%;
//                                ">';
//
//            foreach ($this->channel_categories as $channel_category) {
//                $names = array_map('trim', explode('>', $channel_category->category->original_full_path));
//                $category = '<ul style="display: revert" class="tags">';
//                foreach ($names as $key => $cat_name) {
//                    if ($cat_name != "" && $cat_name != null) {
//                        $category .= '<li><a href="#" class="tag cu_tag_tpype_li_' . ($key + 1) . ' cus_tag_item" data-id="161005" data-toggle="tooltip">' . $cat_name . '</a>
//                    </li>';
//                    }
//                }
//                $category .= '</ul>';
//                $categories[] = $category;
//            }
//            $all_categories[] = implode('', $categories).'</div>';
//            return implode('<br>', $all_categories);
//        }
//        else{
        $categories = [];
        foreach ($this->channel_categories as $channel_category) {
            $category = $channel_category->categoryName($this->channel);
            $name = $category->original_full_path;
            if ($name != "" && $name != null) {
                $categories[] = $name . '<span onclick="unlinkCategory(`' . $channel_category->category_id . '`,' . $channel_category->channel_product_id . ')" style="color:red" class="btn btn-xs"><i class="fa fa-times"></i></i></span>';
            }
        }
        $categories = array_filter($categories);
        return implode('<br>', array_filter($categories));
//        }
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function suppliers()
    {
        return $this->belongsTo(\App\DeliveryCompany::class, 'delivery_company_id');
    }

    public function base_product()
    {
        return $this->belongsTo(DrmProduct::class, 'drm_product_id');
    }

    public function analysis_product()
    {
        return $this->belongsTo(AnalysisProduct::class, 'product_id','drm_product_id');
    }
    public function shop()
    {
        return $this->belongsTo(Shop::class, 'channel', 'channel')
            ->where(['user_id' => $this->user_id])
            ->first();
    }

    public function channel_categories()
    {
        return $this->hasMany(ChannelProductCategory::class, 'channel_product_id');
    }

    public function calculation()
    {
        return $this->hasOne(ProfitCalculation::class, 'id', 'calculation_id');
    }

    public function calculation_repricing()
    {
        return $this->hasOne(ProfitCalculation::class, 'id', 'repricing_id');
    }

    public function getPercentageAttribute()
    {
        if(in_array($this->channel, [13])){
            if(!$this->channel_categories->isEmpty()){
                $all_fields = DB::table('channel_categories')->where('category_id', $this->channel_categories[0]->category_id)->where('channel', $this->channel)->value('misc');

                $all_fields = json_decode($all_fields)->required_columns;

                $field_fill_up_count = 0;
                $total_fill = count($all_fields);

                foreach ($all_fields as $fields) {
                    $fields = $fields->name;
                    if($fields == 'title'){
                        $title_field_len = strlen($this->getTitleFieldAttribute());
                        if($title_field_len > 0){
                            $field_fill_up_count++;
                        }
                    }else if($fields == 'description'){
                        $description_field_len = strlen($this->getTransDescriptionAttribute());
                        if($description_field_len > 0){
                            $field_fill_up_count++;
                        }
                    }elseif($fields == 'short_description'){
                        $short_desc_len = strlen($this->getShortDescAttribute());
                        if($short_desc_len > 0){
                            $field_fill_up_count++;
                        }
                    }elseif($fields == 'picture'){
                        if(!empty($this->images)){
                            $field_fill_up_count++;
                        }
                    }elseif($fields == 'category'){
                        if(!$this->channel_categories->isEmpty()){
                            $field_fill_up_count++;
                        }
                    }else{
                        if (!empty($this->$fields) || !empty(json_decode($this->category_aspects)->$fields)) {
                            $field_fill_up_count++;
                        }
                    }
                }
            }
            else{
                $total_fill = 1;
                $field_fill_up_count = 0;
            }
            $percentage = $total_fill > 0 ? ($field_fill_up_count * 100) / $total_fill : 0;
        }else{
            $all_fields = \App\Enums\Product::ALL_FIELDS;

            $ind_temp_field = $this->industry_template_data;

            if(!is_array($ind_temp_field)){
                $ind_temp_field = json_decode($ind_temp_field, true);
            }

            $mandatory_ind_temp_fileds = [];

            if($ind_temp_field){

                if( !in_array(key($ind_temp_field), array_keys(config('industry_template'))) ){
                    $associate_temp_fields = CustomIndustryTemplate::where('name', trim(key($ind_temp_field)))->value('fields');

                    if($associate_temp_fields){
                        array_walk($associate_temp_fields, function($field) use(&$mandatory_ind_temp_fileds){
                            if(isset($field['required']) && $field['required']){
                                $mandatory_ind_temp_fileds[] = $field['name'];
                            }
                        });
                    }

                }
            }


            if(!empty($mandatory_ind_temp_fileds)){
                $all_fields = array_merge($all_fields, $mandatory_ind_temp_fileds);
            }

            $field_fill_up_count = 0;
            $total_fill = count($all_fields);

            foreach ($all_fields as $fields) {
                if($fields == 'title'){
                    $title_field_len = strlen($this->getTitleFieldAttribute());
                    if($title_field_len > 0){
                        $field_fill_up_count++;
                    }
                }else if($fields == 'description'){
                    $description_field_len = strlen($this->getTransDescriptionAttribute());
                    if($description_field_len > 0){
                        $field_fill_up_count++;
                    }
                }else if(in_array($fields, $mandatory_ind_temp_fileds)){

                    if ($this->country_id == 2) {
                        $lang = data_get(request(), 'lang', 'en');
                    } else {
                        $lang = data_get(request(), 'lang', 'de');
                    }

                    $ind_temp_field_val = strlen($this->getIndustryTemplateFieldAttribut($fields, $lang));
                    if($ind_temp_field_val > 0){
                        $field_fill_up_count++;
                    }

                }else{
                    if (!empty($this->$fields)) {
                        $field_fill_up_count++;
                    }
                }
            }

            if ($this->channel == \App\Enums\Channel::CHRONO24) {
                $total_fill = count($all_fields) + count(\App\Enums\Product::EXTRA_FIELD);
                $extra_field = \App\Enums\Product::EXTRA_FIELD;
                if ($this->attributes) {
                    $attr = @json_decode($this->attributes['attributes'], true);

                    if ($attr && $attr['additonal_data']) {
                        foreach ($attr['additonal_data'] as $index => $value) {
                            if (!empty($value) && in_array($index, $extra_field)) {
                                $field_fill_up_count++;
                            }
                        }
                    }
                }
            }
            // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2494, 2693])){
            $percentage = $total_fill > 0 ? ($field_fill_up_count * 75) / $total_fill : 0;

            // $percentage = $total_fill > 0 ? ($field_fill_up_count * 80) / $total_fill : 0;
            // }else{
            // $percentage = $total_fill > 0 ? ($field_fill_up_count * 100) / $total_fill : 0;
            // }

            // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2494, 2693])){

            $total_seo_item = 2;
            $total_seo_percentage = 20;
            $title_seo_percentage = 0;
            $short_desc_seo_percentage = 0;
            $title_len = strlen( preg_replace("/\s+/", "", strip_tags($this->getTitleFieldAttribute())) );
            $short_desc_len = strlen( preg_replace("/\s+/", "", strip_tags($this->getShortDescAttribute())) );

            if($title_len > 0){
                if($title_len >= 56 && $title_len <= 80){
                    // $title_seo_percentage = ( ( 17 * (20/$total_seo_item) ) / 17 ); // Main calculation is ( ($item_len * ( 20 / total_seo_item ) ) / $item_max_seo_value )
                    $title_seo_percentage = ($total_seo_percentage/$total_seo_item);
                }else{
                    // $title_seo_percentage = ( ( $title_len * (20/$total_seo_item) ) / 17 );
                    $title_seo_percentage = ($total_seo_percentage/$total_seo_item) / 2;
                }
            }

            if($short_desc_len > 0){
                if($short_desc_len >= 112 && $short_desc_len <= 160){
                    // $short_desc_seo_percentage = ( ( 80 * (20/$total_seo_item) ) / 80 ); // Main calculation is ( ($item_len * ( 20 / total_seo_item ) ) / $item_max_seo_value )
                    $short_desc_seo_percentage = ($total_seo_percentage/$total_seo_item);
                }else{
                    // $short_desc_seo_percentage = ( ( $short_desc_len * (20/$total_seo_item) ) / 80 );
                    $short_desc_seo_percentage = ($total_seo_percentage/$total_seo_item) / 2;
                }
            }

            $ideal_title_length_array = $this->getIdealTitleAttribute();
            $ideal_title_percentage = 0;

            if(count($ideal_title_length_array) > 0){

                if(count($ideal_title_length_array) >= 2){
                    $ideal_title_percentage = 5;
                }else{
                    $ideal_title_percentage = 2;
                }

            }

            $percentage = $percentage + $title_seo_percentage + $short_desc_seo_percentage + $ideal_title_percentage;
        }


        // }

        return number_format((float)$percentage, 2, '.', '');
    }


    public function getMissingFieldsAttribute()
    {
        $missing_fields = [];

        $all_fields = $this->getFieldsBasedOnChannel();

        foreach ($all_fields as $field) {
            if ($field == 'title' || $field == 'description' || $field == 'short_description') {
                $field_length = $this->getFieldLength($field);
                if ($field_length === 0) {
                    $missing_fields[] = $field;
                }
            } elseif ($field == 'category') {
                if ($this->channel_categories->isEmpty()) {
                    $missing_fields[] = $field;
                }
            } else {
                if (!$this->isFieldPresent($field) && !in_array($field, $missing_fields) && $field != 'picture') {
                    $missing_fields[] = $field;
                }
            }
        }

        return $missing_fields;
    }

    private function getFieldsBasedOnChannel()
    {
        $channel = $this->channel;
        if (in_array($channel, [Channel::KAUFLAND, Channel::EBAY,Channel::OTTO])) {
            $fields = $this->getExtraFields();
            if($channel == Channel::KAUFLAND){
                $extra_fields = \App\Enums\Product::ALL_FIELDS_KAUFLAND;
                if ($extra_fields) {
                    $fields = array_merge($fields, $extra_fields);
                }
            }
        } else {
            $fields = \App\Enums\Product::ALL_FIELDS;
            $mandatory_ind_temp_fields = $this->getMandatoryIndustryTemplateFields();
            if (!empty($mandatory_ind_temp_fields)) {
                $fields = array_merge($fields, $mandatory_ind_temp_fields);
            }
        }
        return $fields;
    }

    private function getExtraFields()
    {
        $channelCategories = $this->channel_categories;
        $extra_fields_data = array();
        if (!$channelCategories->isEmpty()) {
            $category_id = $channelCategories[0]->category_id;
            $extra_fields_data = app(ChannelProductService::class)->getAdditionalFields($category_id,$this->channel,$this->user_id);
        }
        return array_map(function ($field) {
            if($this->channel == Channel::EBAY){
                return $field['localizedAspectName'];
            }
            return $field['name'];
        }, array_merge($extra_fields_data['required_columns'] ?? [],$extra_fields_data['optional_columns']  ?? []));
    }

    private function getMandatoryIndustryTemplateFields()
    {
        $mandatory_ind_temp_fields = [];

        $ind_temp_field = $this->industry_template_data;
        if(!is_array($ind_temp_field)){
            $ind_temp_field = json_decode($this->industry_template_data, true);
        }

        if ($ind_temp_field && !in_array(key($ind_temp_field), array_keys(config('industry_template')))) {
            $associate_temp_fields = CustomIndustryTemplate::where('name', trim(key($ind_temp_field)))->value('fields');

            if ($associate_temp_fields) {
                foreach ($associate_temp_fields as $field) {
                    if (isset($field['required']) && $field['required']) {
                        $mandatory_ind_temp_fields[] = $field['name'];
                    }
                }
            }
        }

        return $mandatory_ind_temp_fields;
    }

    private function getFieldLength($field)
    {
        $field_value = $this->getFieldValue($field);
        return strlen($field_value);
    }

    private function getFieldValue($field)
    {
        if($field == 'title'){
            return $this->getTitleFieldAttribute();
        }else if($field == 'description'){
            return $this->getTransDescriptionAttribute();
        }elseif($field == 'short_description'){
            return $this->getShortDescAttribute();
        }elseif($field == 'category'){
            if($this->channel_categories->isEmpty()){
                return null;
            } else {
                return 1;
            }
        }
        else{
            return $this->$field;
        }
    }

    private function isFieldPresent($field)
    {
        if(in_array($this->channel,[Channel::EBAY,Channel::KAUFLAND,Channel::OTTO]) && empty($this->$field)){
            $categoryAspects = json_decode($this->category_aspects,true);
            return !empty($categoryAspects[$field] ?? array());
        } else {
            return !empty($this->$field);
        }

    }


    public function getMissingExtraFieldsAttribute()
    {
        $missing_extra_fields = [];

        if ($this->channel == \App\Enums\Channel::CHRONO24) {
            if ($this->attributes) {
                $attr = @json_decode($this->attributes['attributes'], true);
                if ($attr && $attr['additonal_data']) {
                    foreach ($attr['additonal_data'] as $index => $value) {
                        if (empty($value)) {
                            $missing_extra_fields[] = $index;
                        }
                    }
                }
            }
        }

        return $missing_extra_fields;
    }

    public function getVariantProductsAttribute()
    {
        $variant_ids = $this->variants ?? array();
        return ChannelProduct::find($variant_ids) ?? collect([]);
    }

    public function getVariantSizeAttribute(): string
    {
        $html = "<ul class='list-group'>";
        $sizes = $this->variant_products->pluck('item_size')->toArray();
        foreach (array_filter($sizes) as $size) {
            $html .= "<li class='list-group-item' style='border: 1px solid #ddd'>" . $size . "</li>";
        }
        $html .= "</ul>";
        return $html;
    }

    public function getVariantColorAttribute(): string
    {
        $html = "<ul class='list-group'>";
        $colors = $this->variant_products->pluck('item_color')->toArray();
        foreach (array_filter($colors) as $color) {
            $html .= "<li class='list-group-item' style='border: 1px solid #ddd'>" . $color . "</li>";
        }
        $html .= "</ul>";
        return $html;
    }

    public function getVariantMaterialsAttribute(): string
    {
        $html = "<ul class='list-group'>";
        $materials = $this->variant_products->pluck('materials')->toArray();
        foreach (array_filter($materials) as $material) {
            $html .= "<li class='list-group-item' style='border: 1px solid #ddd'>" . $material . "</li>";
        }
        $html .= "</ul>";
        return $html;
    }

    public function getEkPriceEditableAttribute(): bool
    {
        if ($this->channel == Channel::DROPTIENDA) {
            return ((float)$this->ek_price == 0.0);
        } else {
            return false;
        }
    }

    public function getShippingCostFinalAttribute()
    {
        if ($this->calculation_id) {
            if ($this->calculation->dynamic_shipping_cost) {
                $shipping_cost = $this->shipping_cost;
            } else {
                $shipping_cost = $this->calculation->shipping_cost;
            }
        } else {
            $shipping_cost = $this->shipping_cost;
        }
        return number_format($shipping_cost, 2);
    }

    public function channelPriceHistory()
    {
        return $this->hasMany(ChannelPriceHistory::class, 'channel_products_id', 'id')
            ->whereDate('created_at', '>=', \Carbon\Carbon::now()->subDays(365));
    }

    public function getRqProgressAttribute(){
        $rq_limt = (float)$this->rq_limit;
        $rq = (float)$this->rq;
        return $rq;

        if($rq_limt > 0 && $rq > 0){
            return number_format( ( ($rq * 100) / $rq_limt ), 2 );
        }else{
            return 0;
        }
    }

    public function getTitleFieldAttribute()
    {
        if ($this->country_id == 2) {
            $lang = data_get(request(), 'lang', 'en');
        } else {
            $lang = data_get(request(), 'lang', 'de');
        }
        return Arr::get($this->title, $lang);
    }

    public function getShortDescAttribute()
    {
        if ($this->country_id == 2) {
            $lang = data_get(request(), 'lang', 'en');
        } else {
            $lang = data_get(request(), 'lang', 'de');
        }
        return Arr::get($this->short_description, $lang);
    }

    public function getIdealTitleAttribute()
    {
        $ideal_title = [];

        $product_title = $this->getTitleFieldAttribute();

        $ideal_title_consist_of = ['item_size', 'item_color', 'brand', 'materials', 'gender'];

        foreach($ideal_title_consist_of as $field){

            if(!empty($this->$field)){
                // $pattern = '/\b'.$this->$field.'\b/';
                $pattern = '/'.preg_quote($this->$field,'/').'/';

                if(preg_match($pattern, $product_title)){

                    $ideal_title[] = $field;

                }
            }

        }

        return $ideal_title;
    }

    public function getTitleCalculationAttribute()
    {

        $title_original = $this->getTitleFieldAttribute();
        $title_original = preg_replace('/[\r\s\n]/', "", $title_original);

        $title_original = empty($title_original) ? '' : $title_original;

        $title_len = strlen($title_original);
        $data = [];

        // title score
        $title_progress = $title_len * 100 / 80;
        $data['title_progress'] = $title_progress;

        if( $title_progress >= 70  && $title_progress <= 100){
            $data['color'] = '#16a085';
            $data['status'] = __('Perfect');
            $data['class'] = 'badge-success';
        }
        if( $title_progress < 70 ){
            $data['color'] = '#f1c40f';
            $data['status'] = __('Too Short');
            $data['class'] = 'badge-warning';
        }
        if( $title_progress > 100 ){
            $data['color'] = '#c0392b';
            $data['status'] = __('Too Long');
            $data['class'] = 'badge-danger';
        }

        if($title_progress > 100){
            $data['title_progress'] = 100;
        }

        return $data;

    }

    public function analysisProducts()
    {

        return $this->hasOne(AnalysisProduct::class, 'product_id', 'drm_product_id')->where('source', 1);

    }

    public static function updateBasicPrice($vk_price, $weight, $unit){
        $unit = $unit;
        $weight = $weight;
        $vk_price = $vk_price;

        $item_basic_price = null;

        if($unit == 'Gram' || $unit == 'Milliliter'){
            if(is_numeric($weight)){
                $item_basic_price = ( ($vk_price * 1000) / round($weight, 2) );
            }
        }else if($unit == 'Kilogram' || $unit == 'Liter' || $unit == 'Meter'){
            if(is_numeric($weight)){
                $item_basic_price = ( ($vk_price) / round($weight, 2) );
            }
        }else if($unit == 'Centimeter'){
            if(is_numeric($weight)){
                $item_basic_price = ( ($vk_price * 100) / round($weight, 2) );
            }
        }

        return $item_basic_price;
    }

    public function getVkPriceUpdateAttribute()
    {
        $vk_price_update_status = null;

        if($this->old_vk_price != null)
        {
            $diff = $this->vk_price - $this->old_vk_price;

            $vk_price_updated_at = $this->vk_price_updated_at;
            if (!is_null($this->repricing_last_sync)) {
                if (!is_null($this->vk_price_updated_at)) {
                    if (Carbon::parse($this->repricing_last_sync)->gt(Carbon::parse($this->vk_price_updated_at))) {
                        $vk_price_updated_at = $this->repricing_last_sync;
                    }
                } else {
                    $vk_price_updated_at = $this->repricing_last_sync;
                }
            }

            $vk_price_update_info = "<h4>" . __("Previous Vk-Price") . " : ". number_format($this->old_vk_price, 2) . " " . formatCurrency('EUR') ."</h4>"
                . "<h4>" . __("New Vk-Price") . " : ". number_format($this->vk_price, 2) . " " . formatCurrency('EUR') ."</h4>"
                . "<h4>" . __("Last Selling Change") . " : ".$vk_price_updated_at."</h4>";

            if( $diff > 0 ){
                $title = __("Vk-Price Increased");
                $vk_price_update_status = '<span style="color:green;font-weight:bold;cursor:pointer" onclick="swal({html: true,title:`'.$title.'`,text:`'.$vk_price_update_info.'`})" class="glyphicon glyphicon-triangle-top"></span>';
            }elseif( $diff < 0 ) {
                $title = __("Vk-Price Decreased");
                $vk_price_update_status = '<span style="color:red;font-weight:bold;cursor:pointer" onclick="swal({html: true,title:`'.$title.'`,text:`'.$vk_price_update_info.'`})" class="glyphicon glyphicon-triangle-bottom"></span>';
            }else {
                $vk_price_update_status = '<i style="color:#6cbfd5;cursor:pointer" onclick="swal(`Vk-Price Not Updated Yet`)" class="fa fa-info-circle" aria-hidden="true"></i>';
            }
        }else if( $this->vk_price != null && $this->old_vk_price == null ){

            $vk_price_updated_at = $this->vk_price_updated_at;
            if (!is_null($this->repricing_last_sync)) {
                if (!is_null($this->vk_price_updated_at)) {
                    if (Carbon::parse($this->repricing_last_sync)->gt(Carbon::parse($this->vk_price_updated_at))) {
                        $vk_price_updated_at = $this->repricing_last_sync;
                    }
                } else {
                    $vk_price_updated_at = $this->repricing_last_sync;
                }
            }

            if($this->vk_price_updated_at == null){
                $vk_price_updated_at = $this->updated_at;
            }

            $vk_price_update_info = "<h4>" . __("Previous Vk-Price") . " : ". number_format($this->old_vk_price, 2) . " " . formatCurrency('EUR') ." </h4>"
                . "<h4>" . __("New Vk-Price") . " : ". number_format($this->vk_price, 2) . " " . formatCurrency('EUR') ."</h4>"
                . "<h4>" . __("Last Selling Change") . " : ".$vk_price_updated_at."</h4>";

            $title = __("Vk-Price Increased");
            $vk_price_update_status = '<span style="color:green;font-weight:bold;cursor:pointer" onclick="swal({html: true,title:`'.$title.'`,text:`'.$vk_price_update_info.'`})" class="glyphicon glyphicon-triangle-top"></span>';

        }else {
            $vk_price_update_status = '<i style="color:#6cbfd5;cursor:pointer" onclick="swal(`Vk-Price Not Updated Yet`)" class="fa fa-info-circle" aria-hidden="true"></i>';
        }

        return $vk_price_update_status;
    }

    public function getIndustryTemplateFieldAttribut($field, $lang)
    {
        $industry_temp_data = $this->industry_template_data;

        if(!is_array($industry_temp_data)){
            $industry_temp_data = json_decode($industry_temp_data, true);
        }

        $ind_tmp_field_val = '';

        array_walk($industry_temp_data, function($data) use($field, $lang, &$ind_tmp_field_val){
            $ind_tmp_field_val = $data[$field][$lang];
        });

        return $ind_tmp_field_val;
    }

    public function countryOfOrigin()
    {
        return $this->hasOne(AllCountry::class, 'id', 'country_of_origin');
    }

    public function productRegion()
    {
        return $this->hasOne(AllCountry::class, 'id', 'region');
    }
}
