<?php

namespace App\Models;

use App\Jobs\DroptiendaSyncJob;
use Illuminate\Database\Eloquent\Model;

class DroptiendaSyncHistory extends Model
{
    protected $table = 'droptienda_sync_history';

    protected $fillable = [
        'sync_type',
        'sync_event',
        'model_id',
        'user_id',
        'tries',
        'response',
        'exception',
        'synced_at',
        'meta'
    ];

    protected $casts = [
        'meta' => 'array'
    ];

    // static function boot()
    // {
    //     parent::boot();

    //     static::created(function ($item) {
    //         $postData = [
    //             'app_key' => config('app.app_key'),
    //             'app_secret' => config('app.app_secret'),
    //             'user_id' => $item->user_id
    //         ];
    //         $ch = curl_init(config('app.api_host') . '/dt-export-sync');
    //         curl_setopt(
    //             $ch,
    //             CURLOPT_HTTPHEADER,
    //             array(
    //                 'Content-Type: application/json',
    //             )
    //         );
    //         curl_setopt($ch, CURLOPT_HEADER, false);
    //         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //         curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
    //         curl_exec($ch);
    //         curl_close($ch);
    //     });
    // }
}
