<?php

namespace App\Models;

use App\Models\Logging\Process;
use App\Services\ChannelCategoryService;
use Illuminate\Database\Eloquent\Model;

class ChannelUserCategory extends Model
{
    protected $table = "channel_user_categories";

    protected $fillable = [
        'category_name',
        'user_id',
        'drm_category_id',
        'channel',
        'parent',
        'level',
        'full_path',
        'child_nodes',
        'total_products',
        'connected_products',
        'shop_id'
    ];

    public function channel_products(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ChannelProductCategory::class, 'category_id', 'id')
            ->join('channel_products', 'channel_product_id', '=', 'channel_products.id');
    }

    public function connected_channel_products(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ChannelProductCategory::class, 'category_id', 'id')
            ->join('channel_products', 'channel_product_id', '=', 'channel_products.id')
            ->where('channel_products.is_connected',1);
    }

    public function getChannelProductsCountAttribute(): int
    {
        return count($this->channel_products);
    }

    public function parent_category(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(ChannelUserCategory::class, 'id', 'parent');
    }

    public function child_category(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(ChannelUserCategory::class, 'parent', 'id');
    }

    public function getTreeHtmlAttribute(): string
    {
        return $this->getTree($this);
    }

    public function getConnectedSumCountAttribute(){
        $count = $this->connected_channel_products->count();
        return $count + $this->getChildConnecteProductCount($this);
    }

    public function getSumCountAttribute()
    {
        $count = $this->channel_products->count();
        return $count + $this->getChildProductCount($this);
    }

    public function getChildConnecteProductCount($category)
    {
        $count = 0;
        if ($category->child_category) {

            $child_category = ChannelUserCategory::where([
                'parent' => $category->id
            ])->get();

            foreach ($child_category as $child) {
                $count += $child->connected_channel_products->count();
                $count += $this->getChildConnecteProductCount($child);
            }
        }
        return $count;
    }

    public function getChildProductCount($category)
    {
        $count = 0;
        if ($category->child_category) {
            $child_category = ChannelUserCategory::where([
                'parent' => $category->id
            ])->get();

            foreach ($child_category as $child) {
                $count += $child->channel_products->count();
                $count += $this->getChildProductCount($child);
            }
        }
        return $count;
    }

    public function getTree($category): string
    {
        $html = "";
        if ($category->child_category) {
            $child_category = $category->child_category;

            $html = "<ul class='category_child_$category->id'>";
            foreach ($child_category as $child) {
                $html .= "<li data-id='$child->id'>{$child->category_name}";
                $html .= $this->getTree($child) . "</li>";
            }
            $html .= "</ul>";
        }
        return $html;
    }

    public function getFullPathGeneratedAttribute(): string
    {
        $paths = array_merge([$this->category_name], app(ChannelCategoryService::class)->getParentCategory($this));
        return implode(' > ', array_reverse($paths));
    }

    public function getOriginalFullPathAttribute()
    {
        // if($this->channel == Channel::DROPTIENDA){
        return trim(str_replace('>', '<i style="color:orange" class="fa fa-chevron-circle-right"></i>', $this->full_path));
        // }
        // else{
        //     return trim(str_replace('>','<i style="color:orange" class="fa fa-chevron-circle-right"></i>',$this->category_name));
        // }
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($item) {
//            if ($item->parent != 0) {
//                Process::create([
//
//                ]);
//            }
        });

        static::updated(function ($item) {

        });

        static::deleted(function ($item) {

        });
    }
}
