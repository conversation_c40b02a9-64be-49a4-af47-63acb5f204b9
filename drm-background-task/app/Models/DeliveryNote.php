<?php

namespace App\Models;

use App\NewOrder;
use Illuminate\Database\Eloquent\Model;

class DeliveryNote extends Model
{
    protected $table = 'delivery_note_settings';

    public function channels()
    {
        return $this->hasMany(DeliveryNoteAssigned::class,'note_id', 'id');
    }
    public function order()
    {
        return $this->hasMany(NewOrder::class,'delivery_layout_id', 'id');
    }
}
