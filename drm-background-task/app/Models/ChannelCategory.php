<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
// use Rennokki\QueryCache\Traits\QueryCacheable;

class ChannelCategory extends Model
{
    // use QueryCacheable;

    // protected $cacheFor = 3600;
    protected $primaryKey = "category_id";

    protected $table = 'channel_categories';

    protected $fillable = [
        'category_id',
        'category_name',
        'channel',
        'status',
        'misc',
    ];

    protected $casts = [
        'misc' => 'array',
    ];

    // public function channel_category()
    // {
    //     return $this->morphOne(ChannelProductCategory::class, 'category');
    // }

    public function getFullPathAttribute()
    {
        return $this->category_name;
    }

    public function getOriginalFullPathAttribute()
    {
        return trim(str_replace('>','<i style="color:orange" class="fa fa-chevron-circle-right"></i>',$this->category_name));
    }

    public function getTreeHtmlAttribute(): string
    {
        return "";
    }

}
