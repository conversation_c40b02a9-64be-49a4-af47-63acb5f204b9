<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ChannelExportProcess extends Model
{
    protected $connection = 'logs';

    protected $table = 'channel_export_processes';

    protected $fillable = [
        'process_id',
        'product_id',
        'ean',
        'channel',
        'shop_id',
        'user_id',
        'metadata',
        'event_type',
        'tries',
        'status',
        'logs',
        'completed_at'
    ];

    protected $casts = [
        'metadata' => 'array',
        'logs'     => 'array'
    ];

    public function process()
    {
        return $this->belongsTo('App\Models\Process', 'process_id', 'id');
    }
}
