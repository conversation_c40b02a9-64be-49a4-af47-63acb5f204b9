<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;

class Delivery extends Model
{
    protected $table = 'marketplace_deliveries';


    protected $fillable = [
        'supplier_id',
        'delivery_id',
        'delivery_date',
        'product_ids',
        'status',
    ];

    protected $casts = [
        'product_ids' => 'array',
        'status' => 'array',
    ];



}
