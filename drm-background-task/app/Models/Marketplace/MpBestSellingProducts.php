<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;

class MpBestSellingProducts extends Model
{
    protected $connection = 'marketplace';

    protected $fillable = [
        'mp_product_id',
        'sale_quantity',
        'position',
        'old_position'
    ];

    public function mpProduct()
    {
        return $this->belongsTo(Product::class, 'mp_product_id');
    }
}
