<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;
use App\DrmProduct;


class MpCoreDrmTransferProduct extends Model
{
    protected $connection = 'marketplace';

    protected $table = 'mp_core_drm_transfer_products';

    public function drm_product()
    {
        return $this->setConnection('mysql')->hasOne(DrmProduct::class, 'id', 'drm_product_id')->select('id','brand','user_id','update_status');
    }

}
