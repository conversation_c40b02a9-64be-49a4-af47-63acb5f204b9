<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;
use App\Models\Marketplace\MpVirtualCreditLog;

class MpVirtualCredit extends Model
{
    protected $table = 'mp_virtual_credit';

    protected $fillable = [
        'user_id',
        'amount'
    ];

    public function virtualCreditLog()
    {
        return $this->hasOne(MpVirtualCreditLog::class, 'mp_virtual_credit_id');
    }
}