<?php

namespace App\Models\Marketplace;
use Illuminate\Database\Eloquent\Model;

use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ApiCategory extends Model
{
    protected $connection = 'marketplace';

    protected $table = 'api_category_mapping';

    protected $fillable = [
        'api_category_id',
        'api_category_name',
        'api_id',
        'mp_category_id',
        'is_complete',
        'sync_status',
    ];

}
