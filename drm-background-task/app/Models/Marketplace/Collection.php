<?php

namespace App\Models\Marketplace;

use App\Enums\ProfitType;
use App\User;
use Illuminate\Database\Eloquent\Model;

class Collection extends Model
{
    protected $table = 'marketplace_collections';

    protected $fillable = [
        'name',
        'category_id',
        'supplier_id',
        'delivery_company_id',
        'shipping_method',
        'source_type',
        'status',
        'product_profit_type',
        'product_profit_price',
        'csv_file_link',
        'updated_csv'
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function listing_prices()
    {
        return $this->morphToMany(ListingPrice::class, 'listable', 'marketplace_listables');
    }

    public function supplier()
    {
        return $this->belongsTo(User::class, 'supplier_id');
    }

    protected $casts = [
        'updated_csv' => 'array',
    ];
}
