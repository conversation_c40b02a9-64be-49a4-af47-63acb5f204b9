<?php

namespace App\Models\Marketplace;

use App\Enums\ProfitType;
use App\User;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    protected $table = 'marketplace_orders';

    protected $fillable = [
        'user_id',
        'total',
        'sub_total',
        'order_date',
        'discount',
        'discount_type',
        'total_tax',
        'payment_type',
        'status',
        'currency',
        'adjustment',
        'shop_id',
        'order_id_api',
        'customer_info',
        'billing',
        'shipping',
        'client_note',
        'cart',
        'misc',
    ];

    protected $casts = [
        'customer_info' => 'array',
        'cart' => 'array',
        'misc' => 'array',
    ];

}
