<?php

namespace App\Models;

use App\Jobs\ChannelManager\ChangeChannelProductConnectionStatus;
use App\Jobs\ChannelManager\SyncCategoryProductCount;
use Illuminate\Database\Eloquent\Model;
use App\Enums\CategoryType;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ChannelProductCategory extends Model
{
    protected $fillable = [
        'category_id',
        'channel_product_id',
        'category_type',
    ];

    /**
     * @return HasOne
     * @var mixed
     */

    public function product(): HasOne
    {
        return $this->hasOne(ChannelProduct::class,'id','channel_product_id');
    }

    public function category()
    {
        if($this->category_type == CategoryType::API){
            return $this->hasOne(ChannelCategory::class,'category_id','category_id');
        }
        else{
            return $this->hasOne(ChannelUserCategory::class,'id','category_id');
        }
    }

    public function categoryName($channel = null){
        if($this->category_type == CategoryType::API){
            if($channel){
                return $this->hasOne(ChannelCategory::class, 'category_id', 'category_id')
                    ->where('channel', $channel)->first();
            }else{
                return $this->hasOne(ChannelCategory::class,'category_id','category_id')->first();
            }
        }
        else{
            return $this->hasOne(ChannelUserCategory::class,'id','category_id')->first();
        }
    }

    public function channel_category(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(ChannelProduct::class, 'channel_product_id');
    }

    public static function boot()
    {
        parent::boot();

        static::created(function($item) {
            $product = $item->product;
            if($product) {
                ChangeChannelProductConnectionStatus::dispatch($product->id,[],[],true);
//                if($item->category_type == CategoryType::USER){
//                    SyncCategoryProductCount::dispatch([$item->category_id],$product->channel,$product->user_id);
//                }
            }
        });

        static::updated(function($item) {
            $product = $item->product;
            if($product) {
                ChangeChannelProductConnectionStatus::dispatch($product->id,[],[],true);
//                if($item->category_type == CategoryType::USER){
//                    SyncCategoryProductCount::dispatch([$item->category_id],$product->channel,$product->user_id);
//                }
            }
        });

        static::deleted(function($item) {
            $product = $item->product;
            if($product) {
                ChangeChannelProductConnectionStatus::dispatch($product->id,[],[],true);
//                if($item->category_type == CategoryType::USER){
//                    SyncCategoryProductCount::dispatch([$item->category_id],$product->channel,$product->user_id);
//                }
            }
        });
    }
}
