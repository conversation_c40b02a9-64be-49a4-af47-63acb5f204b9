<?php

namespace App\Models\Stripe;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ManualSubscription extends Model
{
    use SoftDeletes;

    protected $table = 'manual_subscriptions';

    protected $fillable = ['user_id', 'subscription_id', 'invoice_id', 'status', 'interval', 'last_run_at', 'period_start', 'period_end', 'metadata', 'error'];

    protected $casts = [
        'metadata' => 'array',
        'last_run_at' => 'date',
        'period_start' => 'date',
        'period_end' => 'date',
    ];
}
