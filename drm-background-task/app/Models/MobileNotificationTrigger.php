<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MobileNotificationTrigger extends Model
{
    const NEW_SALE_HOOK = 1;
    const ORDER_ERROR_HOOK = 2;
    const LATEST_NEWS_HOOK = 3;

    const MOBILE_HOOKS = [
        self::NEW_SALE_HOOK => 'New sale',
        self::ORDER_ERROR_HOOK => 'Order error',
        self::LATEST_NEWS_HOOK => 'Latest news',
    ];

    protected $table = 'user_mobile_notification_triggers';

    protected $fillable = ['user_id', 'hook_id', 'is_active', 'sound'];
}
