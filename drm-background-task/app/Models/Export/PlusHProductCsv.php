<?php

namespace App\Models\Export;

use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class PlusHProductCsv implements FromCollection, WithHeadings
{
    public $products;

    public function __construct($products)
    {
        $this->products = $products;
    }
    public function collection()
    {
        $transferedProduct = $this->products;
        $result = array();
        foreach ($transferedProduct as $product) {
            $length = $product->additionalInfo ? $product->additionalInfo->product_length : 0;
            $width  = $product->additionalInfo ? $product->additionalInfo->product_width : 0;
            $height = $product->additionalInfo ? $product->additionalInfo->product_height : 0;
            $volume = $product->additionalInfo ? $product->additionalInfo->volume : 0;
            $item_weight = empty($product->item_weight) ? 0 : $product->item_weight;
            $result[] = array(
                'Artikelnummer' => $product->item_number ?? '',
                'Artikelbezeichnung' => $product->name ?? '',
                'Artikelbeschreibung' => '',
                'EAN' => $product->ean,
                'Inhalt einer VE'  => '',
                'Laenge (cm)'      => $length,
                'Breite (cm)'      => $width,
                'Hoehe (cm)'       => $height,
                'Volumen (cm³)'    => $volume,
                'Gewicht (kg)'     => ($item_weight / 1000) ?? '',
                'Chargenverwaltung' => '',
                'Zolltarifnummer'  => '',
                'Herkunftsland'    => 'DE'
            );
        }
        return collect($result);
    }
    public function headings(): array
    {
        return ['Artikelnummer', 'Artikelbezeichnung', 'Artikelbeschreibung', 'EAN', 'Inhalt einer VE', 'Laenge (cm)', 'Breite (cm)', 'Hoehe (cm)', 'Volumen (cm³)', 'Gewicht (kg)', 'Chargenverwaltung', 'Zolltarifnummer', 'Herkunftsland'];
    }
}
