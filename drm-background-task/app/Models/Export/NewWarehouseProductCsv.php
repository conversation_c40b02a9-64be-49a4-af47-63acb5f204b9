<?php

namespace App\Models\Export;

use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class NewWarehouseProductCsv implements FromCollection, WithHeadings
{
    public $products;

    public function __construct($products)
    {
        $this->products = $products;
    }
    public function collection(){ 
        $transferedProduct = ($this->products instanceof Collection) ? $this->products : [$this->products];
        $result = array();
        foreach($transferedProduct as $product){
            $length      =   $product->additionalInfo ? $product->additionalInfo->product_length : 0;
            $width       =   $product->additionalInfo ? $product->additionalInfo->product_width : 0;
            $height      =   $product->additionalInfo ? $product->additionalInfo->product_height : 0;
            $volume      =   $product->additionalInfo ? $product->additionalInfo->volume : 0;
            $package_length      =   $product->additionalInfo ? $product->additionalInfo->packaging_length : 0;
            $package_width       =   $product->additionalInfo ? $product->additionalInfo->packaging_width : 0;
            $package_height      =   $product->additionalInfo ? $product->additionalInfo->packaging_height : 0;
            $package_volume      =   $product->additionalInfo ? $product->additionalInfo->volume_gross : 0;
            $item_weight =   empty($product->item_weight) ? 0 : $product->item_weight;
            $result[] = array(
                'SKU Number / article number'      =>     $product->ean,
                'article name'                     =>     $product->name ?? '',
                'EAN_GTIN'                         =>     $product->ean,
                'Land of origin'                   =>     '',
                'peaces in mastercarton'           =>     $product->atw,
                'Width of the mastercarton in cm'  =>     $package_width,
                'length of the mastercarton in cm' =>     $package_length,
                'height of the mastercarton in cm' =>     $package_height,
                'Weight Masterbox net kg'          =>     '',
                'Weight masterbox gross kg'        =>     $package_volume,
                'Width of the piece in cm'         =>     $width,
                'length of the piece in cm'        =>     $length,
                'height of the piece in cm'        =>     $height,
                'weight of the piece net kg'       =>     $item_weight,
                'weight of the piece gross kg'     =>     $volume,
                'Zolltarifnummer'                  =>     '',
                'Zollbeschreibung'                 =>     '',
                'Chargenpflichtig (JA/NEIN)'       =>     'Nein',
                'MHD Pflicht (JA/NEIN)'            =>     'Nein'
            );
        }

        return collect($result);
    }

    public function headings(): array
    {
        return [
            'SKU Number / article number', 'article name', 'EAN_GTIN', 'Land of origin', 'peaces in mastercarton', 'Width of the mastercarton in cm',
            'length of the mastercarton in cm', 'height of the mastercarton in cm', 'Weight Masterbox net kg', 'Weight masterbox gross kg', 
            'Width of the piece in cm', 'length of the piece in cm', 'height of the piece in cm', 'weight of the piece net kg', 
            'weight of the piece gross kg', 'Zolltarifnummer', 'Zollbeschreibung', 'Chargenpflichtig (JA/NEIN)', 'MHD Pflicht (JA/NEIN)'
        ];
    }

}