<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Process extends Model
{
    protected $connection = 'logs';

    protected $table = 'processes';

    protected $fillable = [
        'action_id',
        'user_id',
        'model_id',
        'metadata',
        'tries',
        'status',
        'completed_at'
    ];

    protected $casts = [
        'metadata' => 'array'
    ];

    public function export_processes(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany('App\ChannelExportProcess', 'process_id', 'id');
    }
}
