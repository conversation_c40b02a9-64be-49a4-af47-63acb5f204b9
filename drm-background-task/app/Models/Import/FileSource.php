<?php

namespace App\Models\Import;

use Illuminate\Database\Eloquent\Model;

class FileSource extends Model
{
    protected $table = 'drm_import_files';
    protected $fillable = [
        'drm_import_id',
        'source_type',
        'source_config',
        'header',
        'delimiter',
        'demo_data',
        'file_name',
        'fields',
        'industry_temp_fields'
    ];
    protected $casts = [
        'header' => 'array',
        'source_config' => 'array',
        'demo_data' => 'array',
        'fields'    => 'array',
        'industry_temp_fields'    => 'array'
    ];
}
