<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\ModelsDRMProductCategory;

class DrmCategory extends Model
{
    protected $table = 'drm_category';

    public $timestamps = false;

    protected $fillable = [
        'category_name_de',
        'category_name_en',
        'category_name_es',
        'user_id',
        'country_id'
    ];

    public function drm_products()
    {
        return $this->hasMany(DRMProductCategory::class,'category_id','id');
    }
}
