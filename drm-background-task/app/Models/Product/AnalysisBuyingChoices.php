<?php

namespace App\Models\Product;

use Illuminate\Database\Eloquent\Model;
use App\Models\Product\AnalysisProduct;

class AnalysisBuyingChoices extends Model
{

    protected $table = 'analysis_buying_choices';

    protected $fillable = [
        'id',
        'user_id',
        'analysis_product_id',
        'loading'
    ];

    public function analysisProduct()
    {
        return $this->belongsTo(AnalysisProduct::class, 'analysis_product_id', 'id');
    }

}
