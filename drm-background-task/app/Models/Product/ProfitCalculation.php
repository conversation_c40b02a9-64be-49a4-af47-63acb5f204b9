<?php

namespace App\Models\Product;

use App\Models\ChannelProduct;
use Illuminate\Database\Eloquent\Model;

class ProfitCalculation extends Model
{
    protected $table = 'profit_calculations';

    protected $fillable = [
        'name',
        'user_id',
        'additional_charge',
        'profit_percent',
        'shipping_cost',
        'round_scale',
        'uvp',
        'price_on_request',
        'dynamic_shipping_cost',
        'default',
        'auto_sync',
        'auto_calculation',
        'is_repricing',
        'repricing_status',
        'repricing_interval',
        'repricing_price_diff',
        'repricing_price_diff_type',
    ];

    public function getTotalProductsAttribute()
    {
        return ChannelProduct::where('calculation_id',$this->id)->count();
    }

    public function getEasyPlanAttribute(): int
    {
        $planTotal = 0;
        if($this->default){
            $planTotal = app('App\Services\ChannelProductService')->getEasyPlanTotal($this->user_id);
        }
        return $planTotal;
    }
}
