<?php

namespace App\Models\Product;
use Illuminate\Database\Eloquent\Model;

class ScCat extends Model
{
	protected $connection = 'drm_scrap';
    protected $table = 'categories';
    protected $parentColumn = 'parent';

    public function bap()
    {
        return $this->belongsTo(ScCat::class,$this->parentColumn);
    }

    public function baps()
    {
        return $this->bap()->with('baps');
    }

    public function children()
    {
        return $this->hasMany(ScCat::class, $this->parentColumn);
    }

    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }
}