<?php

namespace App\Models\Product;

use Illuminate\Database\Eloquent\Model;

class CPAnalysisRequest extends Model
{
    protected $table = 'c_p_analysis_requests';

    protected $fillable = [
        'user_id',
        'ebay_collection_id',
        'amazon_collection_id',
        'google_collection_id',
        'product_ids',
        'total_products',
        'amazon_last_sync',
        'ebay_last_sync',
        'google_last_sync',
        'ebay_request_id',
        'amazon_request_id',
        'default',
        'forced',
        'easy_pricing',
        'payload_id'
    ];

    protected $casts = [
        'last_sync' => 'datetime',
    ];


    public function analysis_products()
    {
        return $this->hasMany(AnalysisProduct::class, 'request_id', 'id');
    }
}
