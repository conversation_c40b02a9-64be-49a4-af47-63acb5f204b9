<?php

namespace App\Models\Product;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\MarketplaceProducts;

class ComparisonAnalysisProduct extends Model
{
    use SoftDeletes;

    protected $table = 'comparison_analysis_products';

    protected $fillable = [
        'id',
        'analysis_product_id',
        'user_id',
        'title',
        'ean',
        'source_ek_price',
        'source_stock',
        'updated_at',
        'created_at',
        'deleted_at'
    ];

    protected $casts = [
        'image' => 'array'
    ];

    public function marketplaceProducts()
    {
        return $this->hasMany(MarketplaceProducts::class, 'ean', 'ean');
    }
}
