<?php

namespace App\Observers;

use App\NewCustomer;
use App\DropfunnelCustomerTag;

class CustomerObserver
{
    /**
     * Handle the new customer "created" event.
     *
     * @param  \App\NewCustomer  $newCustomer
     * @return void
     */
    public function created(NewCustomer $newCustomer)
    {
        DropfunnelCustomerTag::insertLeads($newCustomer->user_id, $newCustomer->id);
    }

    /**
     * Handle the new customer "updated" event.
     *
     * @param  \App\NewCustomer  $newCustomer
     * @return void
     */
    public function updated(NewCustomer $newCustomer)
    {
        //
    }

    /**
     * Handle the new customer "deleted" event.
     *
     * @param  \App\NewCustomer  $newCustomer
     * @return void
     */
    public function deleted(NewCustomer $newCustomer)
    {
        //
    }

    /**
     * Handle the new customer "restored" event.
     *
     * @param  \App\NewCustomer  $newCustomer
     * @return void
     */
    public function restored(NewCustomer $newCustomer)
    {
        //
    }

    /**
     * Handle the new customer "force deleted" event.
     *
     * @param  \App\NewCustomer  $newCustomer
     * @return void
     */
    public function forceDeleted(NewCustomer $newCustomer)
    {
        //
    }
}
