<?php

namespace App\Console\Commands;

use App\Jobs\FetchKeepaProduct;
use App\KeepaCategory;
use App\KeepaProduct;
use App\Services\Keepa\Keepa;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class FetchKeepaProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:fetchKeepaProducts';
    
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate Keepa Products & store keepa products json response from keepa to category spesific folder.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Download json respose from keepa to category spesific folder
     * Folder Path: "keepa_products/".date('Y-m')."/<keepa_category_id>
     * @param Request keepa_cat_idx
     * <url>/admin/drm_products/fetch_keepa_products?keepa_cat_idx=17
     */
    public function fetchKeepaProducts($keepa_category){

        $asin_chunk_size = 100; #max size keepa supports is 100
        
        # chunk product asin's to keepa limit for parsing information
        $asin_chunks = array_chunk($keepa_category->asin_list, $asin_chunk_size);
        $token_uses_count = 0;
        $max_token_use = 1000;
        $time = 0;
        foreach ($asin_chunks as $key => $parsable_list) {
            $token_uses_count+= $asin_chunk_size * 2; # For each product it takes 2 tokens
            if($token_uses_count >= $max_token_use){
                // $time = date("Y-m-d H:i:s", strtotime($time . "+15 minutes"));
                $time = $time + 60;
                $token_uses_count = 0;
            }

            print("Dispatched: $key\n");
            FetchKeepaProduct::dispatch($keepa_category, $parsable_list, $key)->delay( now()->addMinutes($time) )->onQueue('keepa');
        }

    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        print "Started keepa regular products fetching\n";
        try {
            $keepa_cats = KeepaCategory::with('drm_app_id')->where(['range' => 0])->get();
            foreach ($keepa_cats as $key => $cat) {
                $this->fetchKeepaProducts($cat);
            }
            print "Fetchng keepa products.\n";
        }catch( \Exception $e){
            print "Keepa regular products fetching failed.\n";
        }
        
    }
}
// /usr/bin/php7.4 artisan keepa:fetchKeepaProducts
