<?php

namespace App\Console\Commands;

use App\EmailMarketing;
use App\Traits\EmailCampaignTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Exception;
use DB;
use Illuminate\Support\Carbon;

class SendStepMails extends Command
{
    use EmailCampaignTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:stepMail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will send step mails for an email campaign';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // $command = 'Handle method called';
        try {
            $campaigns = EmailMarketing::has('tags')->has('steps')->where('status', 1)->get();
            if ($campaigns->isNotEmpty()) {
                // Log::channel('command')->info('Scheduler Send step mail function called');
                // $command = 'handle loop called';
                foreach ($campaigns as $campaign) {

                    $this->sendStepEmail($campaign);
                }
            }
        } catch (Exception $exception) {
            // $command = 'handle exception called';
            Log::channel('command')->info('Email campaign Failed!! Ids are - '.implode(',',EmailMarketing::has('tags')->has('steps')->where('status', 1)->pluck('id')->toArray()));
            // Log::channel('command')->info('email campaign history email send error start');
            // Log::channel('command')->error($exception->getMessage());
            // Log::channel('command')->info('email campaign history email send error end');
        }

        //get campaign ID of today's new inserted tag & check the mail sent or not.
        // try{

        //     $today = Carbon::today();
        //     $user_tags = DB::table('dropfunnel_customer_tags')
        //                 ->join('campaign_tags','dropfunnel_customer_tags.tag_id','campaign_tags.tag_id')
        //                 ->leftJoin('dropfunnel_email_sent_histories', 'dropfunnel_customer_tags.customer_id','dropfunnel_email_sent_histories.customer_id')
        //                 ->whereNull('dropfunnel_email_sent_histories.customer_id')
        //                 ->whereDate('dropfunnel_customer_tags.created_at', $today)
        //                 ->select('campaign_tags.campaign_id')
        //                 ->groupBy('campaign_tags.campaign_id')
        //                 ->get();

        //     foreach($user_tags as $user){
        //         app('App\Http\Controllers\NewShopSyncController')->campaignMailJob($user->campaign_id);
        //     }


        // }catch(Exception $e){
        //     Log::channel('command')->info('email campaign send failed from SendStepMail');
        //  }

        // try {
        //     $telegramUrl = 'https://api.telegram.org/bot';
        //     $token = '**********************************************';
        //     $chatId = 1226369782;
        //     $txt = $command;
        //     file_get_contents($telegramUrl . $token . '/sendMessage?chat_id=' . $chatId . '&text=' . $txt . '&parse_mode=HTML');
        // } catch (Exception $exception) {

        // }

        return 1;
    }
}
