<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\NotificationClearHistory;

class NotificationClear extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:clear';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notification  clear command';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        NotificationClearHistory::dispatch();
    }
}
