<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\Jobs\Marketplace\BulkMarketplaceProductDelete;
use App\Models\Marketplace\Product;


class DeleteSixtyDaysConsistentlyZeroStockProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:remove-sixty-days-zero-stock-product';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace sixty days consistently zero stock product remove';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $sixty_day = now()->subDays(60)->toDateString();
        $products = Product::with('core_products:marketplace_product_id,id,user_id')
            ->select('id', 'image')
            ->where(function ($query) {
                $query->where(function ($query) {
                    $query->where('shipping_method', 1)
                        ->where('stock', 0);
                // })->orWhere(function ($query) {
                //     $query->where('shipping_method', 2)
                //         ->where('internel_stock', 0);
                });
            })
            ->where(function ($query) use ($sixty_day) {
                $query->where(function ($query) use ($sixty_day) {
                    $query->whereNotNull('stock_updated_at')
                        ->where('stock_updated_at', '<', $sixty_day);
                })->orWhere(function ($query) use ($sixty_day) {
                    $query->whereNull('stock_updated_at')
                        ->where('created_at', '<', $sixty_day);
                });
            })->get();

        if(count($products) > 0){
            foreach ($products->chunk(75) as $product) {
                dispatch(new BulkMarketplaceProductDelete($product));
            }
        }
    }
}
