<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\Jobs\Marketplace\SponsorsCampaignRemove;

class RemoveMarketplaceSponsorsCampaign extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:remove-marketplace-sponsors-campaign';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace campaign remove';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        SponsorsCampaignRemove::dispatch();
    }
}
