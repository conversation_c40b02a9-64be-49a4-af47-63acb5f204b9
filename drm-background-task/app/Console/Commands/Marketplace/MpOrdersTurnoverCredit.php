<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\Jobs\Marketplace\MarketplaceMonthlyTurnover;

class MpOrdersTurnoverCredit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:monthly-orders-turnover';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace monthly orders turnover credit';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        MarketplaceMonthlyTurnover::dispatch();
    }
}
