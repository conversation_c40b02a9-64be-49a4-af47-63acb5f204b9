<?php

namespace App\Console\Commands\Marketplace;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\Category;
use Illuminate\Support\Facades\DB;


class MarketplaceStoreProductCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:storeproductscount';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MP store product count';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('max_execution_time', '0');
        
        $product = Product::where('status', 1);
        $onlyApprovedProduct = clone $product;
        // -----------------Start Only ststus= 1 & stock > 0------------
        $filterProductCount = $product->select('category_id', DB::raw('count(*) as total_product'))
        ->where(function ($asq) {
            $asq->where('shipping_method', '=', 1)->where('stock', '>', 0)
                ->orWhere(function ($nestedAsq) {
                    $nestedAsq->where('shipping_method', '!=', 1)->where('internel_stock', '>', 0);
                });
        })->groupBy('category_id')->get();
        $categoryIds = $filterProductCount->pluck('category_id');
        Category::whereNotIn('id', $categoryIds)->update(['count_product' => 0]); // where not in this ids list set count 0;
        foreach ($filterProductCount as $count) {
            Category::where('id', $count->category_id)->update(['count_product' => $count->total_product]);
        }
        // -----------------Start Only ststus= 1------------
        $onlyApproved = $onlyApprovedProduct->select('category_id', DB::raw('count(*) as total_product'))
        ->groupBy('category_id')->get();
        $catIds = $onlyApprovedProduct->pluck('category_id');
        Category::whereNotIn('id', $catIds)->update(['count_approved_product' => 0]); // where not in this ids list set count 0;
        foreach ($onlyApproved as $count) {
            Category::where('id', $count->category_id)->update(['count_approved_product' => $count->total_product]);
        }
        // -----------------End Only ststus= 1------------
        // -----------------start parent category update------------
        $categoryCount = Category::whereNotNull('parent_id')->where('count_approved_product', '>', 0)
        ->select('parent_id', DB::raw('count(*) as total_category'))->groupBy('parent_id')->get();
        $parentCatIds = $categoryCount->pluck('parent_id');
        \App\Models\Marketplace\MarketplaceParentCategory::whereNotIn('id', $parentCatIds)->update(['count_category' => 0]);
        foreach ($categoryCount as $count) {
            \App\Models\Marketplace\MarketplaceParentCategory::where('id', $count->parent_id)->update(['count_category' => $count->total_category]);
        }
    }
}
