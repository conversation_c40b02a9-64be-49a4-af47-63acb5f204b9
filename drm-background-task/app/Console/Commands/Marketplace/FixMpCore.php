<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;

class FixMpCore extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mp-core:fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace transfer report from local DB';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        return;
        \App\Jobs\Marketplace\fixCoreStocks::dispatch();
    }
}
