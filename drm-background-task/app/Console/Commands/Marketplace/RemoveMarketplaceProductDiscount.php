<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\Jobs\Marketplace\MarketplaceProductDiscountRemoveSync;

class RemoveMarketplaceProductDiscount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:remove-marketplace-product-discount';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace product discount remove';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        MarketplaceProductDiscountRemoveSync::dispatch();
    }
}
