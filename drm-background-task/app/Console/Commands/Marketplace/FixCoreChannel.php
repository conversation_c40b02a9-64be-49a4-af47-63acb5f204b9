<?php

namespace App\Console\Commands\Marketplace;

use App\Jobs\Marketplace\FixChannelStocks;
use Illuminate\Console\Command;

class FixCoreChannel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'core-channel:fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Channel transfer report from local DB';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        FixChannelStocks::dispatch();
    }
}
