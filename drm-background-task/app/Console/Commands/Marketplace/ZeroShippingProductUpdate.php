<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\Jobs\Marketplace\MarketplaceZeroShippingProductSync;
use Illuminate\Support\Facades\DB;

class ZeroShippingProductUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:zero-shipping-product-update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace zero shipping product update';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $enterprise_users = DB::table('purchase_import_plans')
                ->where([
                    ['status', 1],
                    ['import_plan_id', 27],
                ])->select('cms_user_id', 'end_date')
                ->get();
                
                // ->pluck('cms_user_id');
        foreach($enterprise_users as $enterprise_user){
            info('Zero shipping update for user: '.$enterprise_user->cms_user_id);
            MarketplaceZeroShippingProductSync::dispatch($enterprise_user->cms_user_id, $enterprise_user->end_date > now() ? 27 : null);
        }
    }
}
