<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\DrmProduct;
use App\Jobs\Marketplace\MarketplacePreOrder;
use App\Models\Marketplace\ProductSyncHistory;

class MarketplacePreOrderSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:pre-order-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace pre order sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        MarketplacePreOrder::dispatch();
    }
}
