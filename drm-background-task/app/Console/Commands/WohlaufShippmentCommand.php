<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\WohlaufShippmentLabel;

class WohlaufShippmentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wohlauf:shippments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Wohlauf shippment labels';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        WohlaufShippmentLabel::dispatch();
    }
}
