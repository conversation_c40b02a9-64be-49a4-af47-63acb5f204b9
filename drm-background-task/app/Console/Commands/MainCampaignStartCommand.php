<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Jobs\MainCampaignStartJob;
use App\EmailMarketing;
use DB;
use App\Traits\EmailCampaignTrait;

class MainCampaignStartCommand extends Command
{
    use EmailCampaignTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'start:campaign';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Campaign Start';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $emailMarketings = EmailMarketing::with('tags:tag_id,campaign_id,time_type,time')
        ->has('tags')
        ->where('status', 1)
        // ->whereNotExists(function ($query)
        // {
        //     $query->select(DB::raw(1))
        //         ->from('campaign_history')
        //         ->whereRaw('campaign_history.campaign_id = email_marketings.id');
        // })
        ->get();

        if($emailMarketings->isNotEmpty()){
            foreach($emailMarketings as $emailMarketing){

                //Get all campaign customers
                $customers = $this->findCustomersByCampaignId($emailMarketing->id, true);
                
                if ($customers->isNotEmpty()){
                    MainCampaignStartJob::dispatch($emailMarketing);
                }

            }
        }
    }
}
