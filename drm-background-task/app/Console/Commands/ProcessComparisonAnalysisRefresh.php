<?php

namespace App\Console\Commands;

use App\Jobs\ProcessComparisonAnalysisRefreshJob;
use Illuminate\Console\Command;

class ProcessComparisonAnalysisRefresh extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:refresh_comparison_analysis_products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh comparison_analysis_products with marketplace_products';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ProcessComparisonAnalysisRefreshJob::dispatch();
    }
}
