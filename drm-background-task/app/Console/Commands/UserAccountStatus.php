<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\UserAccountStatusJob;

class UserAccountStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:account-lock';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'User Account Lock Based on Order Status "inkasso" and Tariff Active';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        UserAccountStatusJob::dispatch();
    }
}
