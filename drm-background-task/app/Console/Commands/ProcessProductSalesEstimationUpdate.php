<?php

namespace App\Console\Commands;

use App\Jobs\ProcessProductSalesEstimationUpdateJob;
use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ProcessProductSalesEstimationUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:update_sales_estimate_columns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Amazon Sales Estimation Columns';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $asinQuery = "(SELECT(`asin`) FROM amazon_asin_collections WHERE `ean` = analysis_products.ean AND `domain` = 3 LIMIT 1) AS `asin`";

        DB::table('analysis_products')->leftJoin('analysis_category', 'analysis_products.category_id', '=', 'analysis_category.id')->where('analysis_products.archived', '<>', 1)->select(DB::raw("analysis_products.amazon_product_number, analysis_products.ean, analysis_products.amazon_last_sync, analysis_products.ebay_last_sync, analysis_products.ebay_product_sold, analysis_category.type, $asinQuery"))->where(function($p){
            $p->whereNull('analysis_products.amazon_sales_estimation_last_update')->orWhere(function($q) {
                $q->where('analysis_products.amazon_sales_estimation_last_update', '<=', Carbon::now()->subDays(7)->toDateTimeString())->where(function($s){
                    $s->where('analysis_category.type', 'minutes');
                    $s->orWhere('analysis_category.type', 'hours');
                });
            });
        })->orderBy('analysis_products.id')->groupBy('analysis_products.ean')->chunk(100, function($rows){
            ProcessProductSalesEstimationUpdateJob::dispatch($rows);
        });
    }
}
