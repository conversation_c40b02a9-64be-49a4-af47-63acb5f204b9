<?php

namespace App\Console\Commands;

use App\Enums\Channel;
use App\Enums\ChannelProductConnectedStatus;
use App\Enums\CreditType;
use App\Jobs\ChannelManager\UpdateCheapestPriceJob;
use App\Models\ChannelProduct;
use App\Models\Product\ProfitCalculation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChannelCheapestPriceCalculateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'channel:calculate-cheapest-price';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Channel cheapest price will be calculated';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if(empty(\App\Enums\V2UserAccess::USERS)) return;

        try {
            $user_id = 0;
            $repricing_calculations = ProfitCalculation::where([
                    'is_repricing'         => 1,
                    'repricing_status'     => 1,
                    ['repricing_interval', '>', 0],
                ])
                ->whereNotIn('user_id', \App\Enums\V2UserAccess::USERS)
                ->select('id', 'user_id')
                ->get()
                ->groupBy('user_id')
                ->toArray();

            if (!empty($repricing_calculations)) {
                foreach ($repricing_calculations as $user_id => $user_repricing_calculations) {
                    if (deluxeOrHigher($user_id)) {
                        $user_repricing_calculations = collect($user_repricing_calculations)->pluck('id')->toArray();

                        if (!empty($user_repricing_calculations)) {
                            $this->bulkCheapestPriceQuery($user_id, $user_repricing_calculations);
                        }
                    }
                }
            }

        } catch( \Exception $e) {
            // Log::channel('command')->error($e->getMessage());
        } finally {
        }

        return 1;
    }

    public function bulkCheapestPriceQuery($user_id = 0, $user_repricing_calculations = []) {
        ChannelProduct::join('profit_calculations', 'channel_products.repricing_id', '=', 'profit_calculations.id')
            ->join('shops', 'channel_products.shop_id', '=', 'shops.id')
            ->join('analysis_products', function ($join) {
                $join->on('channel_products.drm_product_id', '=', 'analysis_products.product_id')
                    ->orOn('channel_products.marketplace_product_id', '=', 'analysis_products.product_id');
            })
            ->where([
                'shops.status' => 1,
                'channel_products.user_id' => $user_id,
                'connection_status'        => ChannelProductConnectedStatus::CONNECTED,
                ['min_price', '>', 0],
                ['max_price', '>', 0],
            ])
            ->whereNull('analysis_products.deleted_at')
            ->whereNull('job_token') // those has value are already in queue job: so select null
            ->whereIn('channel_products.channel', Channel::CHEAPEST_APPLICABLE_CHANNELS)
            ->whereIn('profit_calculations.id', $user_repricing_calculations)
            ->select('channel_products.id', 'min_price', 'max_price',
                'repricing_interval', 'repricing_price_diff', 'repricing_price_diff_type')
            ->chunk(500, function ($channelProducts) use ($user_id) {
                $remain_credit = get_token_credit($user_id)['remain_credit'];
                if ($remain_credit <= 0) {
                    return false;
                } else {
                    if ($channelProducts->count() > $remain_credit) {
                        $channelProducts = $channelProducts->take($remain_credit);
                    }

                    $channelProducts = $channelProducts->groupBy('repricing_interval')->sort();
                    $this->bulkCheapestDispatch($user_id, $channelProducts);
                }
            });
    }

    public function bulkCheapestDispatch($user_id = 0, $channelProducts = []) {
        foreach ($channelProducts as $interval_duration => $interval_products) {
            $interval_products_arr = $interval_products->toArray();

            foreach ($interval_products_arr as $index => $channel_product) {
                $product = ChannelProduct::find($channel_product['id']);
                $product->job_token = $interval_duration; // add job token
                $product->save();
            }
    
            UpdateCheapestPriceJob::dispatch($user_id, $interval_duration, $interval_products_arr)
                                    ->delay(\Carbon\Carbon::now()->addMinutes($interval_duration));
        }
    }
}
