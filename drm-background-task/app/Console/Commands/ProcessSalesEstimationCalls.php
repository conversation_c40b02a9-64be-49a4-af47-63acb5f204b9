<?php

namespace App\Console\Commands;

use App\Jobs\ProcessSalesEstimationCall;
use Illuminate\Console\Command;

class ProcessSalesEstimationCalls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:make_sales_estimation_calls';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Make Sales Estimation Calls';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ProcessSalesEstimationCall::dispatch();
    }
}
