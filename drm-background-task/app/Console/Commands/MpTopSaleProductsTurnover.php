<?php

namespace App\Console\Commands;

use App\Jobs\ChannelManager\CalculateMpTopSaleProductsTurnover;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MpTopSaleProductsTurnover extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'widget:mp-top-sell-products-turnover';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mp top sell products turnover amount to show in user widget';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $cus_46_widget_users = DB::table('user_special_widgets')
                ->where('status', 1)
                ->where('widget_no', 'cus_46')
                ->pluck('user_id')
                ->toArray();
            
            if (!empty($cus_46_widget_users)) {
                foreach ($cus_46_widget_users as $index => $user_id) {
                    
                    $mp_access = DB::table('cms_users')->where('id', $user_id)->value('marketplace_access');
                    if ($mp_access) {
                        CalculateMpTopSaleProductsTurnover::dispatch($user_id)->delay(\Carbon\Carbon::now()->addMinutes(0));
                    }
                }
            }
        } catch( \Exception $e) {
            // Log::channel('command')->error($e->getMessage());
        } finally {
        }

        return 1;
    }
}
