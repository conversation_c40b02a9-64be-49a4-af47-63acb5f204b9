<?php

namespace App\Console\Commands;

use App\NewCustomer;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Log;

class CustomerAutoDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customer:auto_delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Unconfirmed Customer Auto Delete';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            DB::table('customer_delete_interval')->select('interval', 'user_id')
                ->get()
                ->each(function ($item) {
                    NewCustomer::doesntHave('orders')
                        ->where([
                            'status' => 0,
                            'email_verification' => 0,
                        ])
                        ->where('created_at', '<=', Carbon::now()->subDays($item->interval))
                        // ->whereIn('source', [50, 52])
                        ->whereNull('cc_user_id')
                        ->where('user_id', $item->user_id)
                        ->where('insert_type', '<>', 6)
                        ->delete();
                });


            @file_get_contents('https://drm.software/api/clear-shipcloud-unpaid');

        } catch (Exception $e) {
            Log::channel('command')->info($e);
        }
    }
}
