<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\ProformaInvoiceRestoreJob;

class ProformaInvoiceRestore extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'proforma:restore';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Proforma invoice restore';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ProformaInvoiceRestoreJob::dispatch();
    }
}
