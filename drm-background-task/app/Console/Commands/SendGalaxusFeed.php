<?php

namespace App\Console\Commands;

use App\Enums\Channel;
use App\Models\Export\ChannelCsvCredential;
use App\Services\Modules\Export\Colizey;
use App\Services\Modules\Export\Galaxus;
use App\Shop;
use Illuminate\Console\Command;

class SendGalaxusFeed extends Command
{
    protected $description = 'Send updated feed to FTP';
    protected $signature = 'galaxus:update';
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $shops = Shop::where([
            'channel' => Channel::GALAXUS
        ])->get();

        foreach ($shops as $shop) {
            (new Galaxus())->export($shop->user_id,$shop);
        }
    }
}
