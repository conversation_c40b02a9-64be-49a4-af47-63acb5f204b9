<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SendPaywallActionJob;

use App\Jobs\DtLicenseRenewJob;

class MonthlyPaywallCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'paywall:notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DtLicenseRenewJob::dispatch();
        
        ini_set("memory_limit", -1);
        //app('App\Http\Controllers\AdminMonthlyPaywallsController')->SendPaywallAction();
        SendPaywallActionJob::dispatch();
    }
}
