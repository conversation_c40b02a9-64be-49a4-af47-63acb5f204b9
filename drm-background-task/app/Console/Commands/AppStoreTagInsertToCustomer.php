<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class AppStoreTagInsertToCustomer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'appStoreTag:InsertToCustomer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'App Store Activation and Cancelation Tag Insert To Customer';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try{
            app('App\Http\Controllers\AdminDrmAllCustomersController')->getTagExtensionSave();
        }catch(\Exception $e){

        }
    }
}
