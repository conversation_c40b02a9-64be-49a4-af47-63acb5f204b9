<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\ShopOrderSyncJob;
use App\Jobs\UniversalExport\ImportSync;
use Illuminate\Support\Facades\DB;

class ShopOrderSyncPlatinum extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shop:order-platinum';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Shop order Sync - Platinum';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        print "Shop Orders Sync Start - Platinum\n";
        try{
            $app_id = config('global.interval_app_id');
            $plan_id = config('global.interval_platinum_plan');
            $sync_shop_types = config('global.sync_shop_types');
            if(empty($sync_shop_types)) return;

            $user_has_plan =  \DB::table('purchase_apps')->where(['app_id' => $app_id, 'plan_id' => $plan_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->pluck('cms_user_id')->toArray();
            $user_has_assign = \DB::table('app_assigns')->where(['app_id'=>$app_id, 'plan_id'=>$plan_id])
            ->where(function($amnu){
                $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
            })
            ->select('user_id')->pluck('user_id')->toArray();
            $user_has_trial = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'status' => 'active', 'is_free_trail' => 1])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->pluck('cms_user_id')->toArray();

            $users_all = array_unique(array_merge($user_has_plan, $user_has_assign, $user_has_trial));
            $users = \DB::table('cms_users')->whereIn('id', $users_all)->where('status', 'active')->select('id')->pluck('id')->toArray(); //remove block user


            \App\Shop::where('status', 1)
            ->where('user_id', '<=', config('global.greater_user_id'))
            ->whereIn('user_id', $users)
            ->orderBy('shops.id')
            ->chunk(10, function($chunk) use ($sync_shop_types) {

                $chunk->whereIn('channel', $sync_shop_types)->each(function($shop) {
                    ShopOrderSyncJob::dispatch($shop, $shop->channel)->onQueue('ordersync');
                });

                $users = $chunk->pluck('user_id')->toArray();
                ImportSync::dispatch($users);
            });

        }catch( \Exception $e){
            print "Shop Orders Sync Failed - Platinum\n";
        }finally {
          print "Shop Orders Sync End - Platinum\n";
        }

        try {
            $this->newTariffSync();
        } catch (\Exception $e) {
            print "Shop Orders Sync Failed - new Gold\n";
        }
    }

    // New tariff sync
    protected function newTariffSync()
    {
        $sync_shop_types = config('global.sync_shop_types');
        if(empty($sync_shop_types)) return;

        $plan_id = 27;
        $today = now();

        // Enterprise query
        \App\Shop::join('cms_users', function($join) {
            $join->on('shops.user_id', '=', 'cms_users.id')
                ->where(function($query) {
                    $query->where('cms_users.id', '>', config('global.greater_user_id'))
                        ->orWhere(function($query) {
                            $query->whereExists(function($subQuery) {
                                $subQuery->select(DB::raw(1))
                                    ->from('old_users_tariff')
                                    ->whereColumn('old_users_tariff.user_id', 'cms_users.id');
                            });
                        });
                })
                ->whereNotNull('cms_users.status');
        })
        ->leftjoin('purchase_import_plans', function($join) use ($today, $plan_id) {
            $join->on('shops.user_id', '=', 'purchase_import_plans.cms_user_id')
            ->whereDate('purchase_import_plans.end_date', '>=', $today)
            ->where('purchase_import_plans.import_plan_id', $plan_id);
        })
        ->leftjoin('app_trials', function($join) use ($today) {
            $join->on('shops.user_id', '=', 'app_trials.user_id')
            ->where('app_trials.app_id', 0)
            ->whereRaw("DATE_ADD(app_trials.start_date, INTERVAL app_trials.trial_days DAY) >= '$today'");
        })
        ->where('shops.status', '=', 1)
        ->where(function($q){
            $q->whereNotNull('purchase_import_plans.id')
            ->orWhereNotNull('app_trials.id');
        })
        ->select('shops.*')
        ->groupBy('shops.id')
        ->orderBy('shops.id')
        ->chunk(10, function($chunk) use ($sync_shop_types) {

            $chunk->whereIn('channel', $sync_shop_types)->each(function($shop) {
                ShopOrderSyncJob::dispatch($shop, $shop->channel)->onQueue('ordersync');
            });

            $users = $chunk->pluck('user_id')->toArray();
            ImportSync::dispatch($users);
        });
    }
}
