<?php

namespace App\Console\Commands;

use App\Enums\Channel;
use App\Models\Export\ChannelCsvCredential;
use App\Services\Modules\Export\Colizey;
use Illuminate\Console\Command;

class SendColizeyFeed extends Command
{
    protected $description = 'Send updated feed to Colizey';
    protected $signature = 'colizey:update';
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $feeds = ChannelCsvCredential::where([
            'channel' => Channel::COLIZEY
        ])->with('shop')->get();

        foreach ($feeds as $feed) {
            (new Colizey())->sendCsvToShop($feed->shop,url('api/product/export?token='. $feed->token));
        }
    }
}
