<?php

namespace App\Console\Commands;

use App\Enums\Channel;
use App\Shop;
use Illuminate\Console\Command;

class FetchDecathlonOffers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'decathlon:fetch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Decathlon offers';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
//        $shops = Shop::where([
//            'channel' => Channel::DECATHLON
//        ])->cursor();
//
//        foreach ($shops as $shop) {
//            \App\Jobs\ChannelManager\FetchDecathlonOffers::dispatch($shop->id,$shop->user_id);
//        }
    }
}
