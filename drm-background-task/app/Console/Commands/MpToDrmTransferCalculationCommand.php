<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\MpToDrmTransferCalculationJob;

class MpToDrmTransferCalculationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:drm-transferred-calculation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mp to drm transferred product total calculation';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        MpToDrmTransferCalculationJob::dispatch();
    }
}
