<?php

namespace App\Console\Commands;

use App\Jobs\ProcessKeepaCSVProductsColumnUpdateJob;
use Illuminate\Console\Command;

class ProcessKeepaCSVProductsColumnUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:update_keepa_csv_products_table_mp_available_column';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update mp_available column in keepas_csv_products table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ProcessKeepaCSVProductsColumnUpdateJob::dispatch();
    }
}
