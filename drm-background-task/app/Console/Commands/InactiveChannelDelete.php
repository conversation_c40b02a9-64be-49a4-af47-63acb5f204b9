<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\InactiveChannelDeleteJob;
use App\Shop;

class InactiveChannelDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inactive-channel:delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Users inactive channel will delete from shops table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $shops = Shop::where([
            'status' => 0
        ])->cursor();

        foreach ($shops as $shop) {
            InactiveChannelDeleteJob::dispatch($shop->user_id);
        }
    }
}
