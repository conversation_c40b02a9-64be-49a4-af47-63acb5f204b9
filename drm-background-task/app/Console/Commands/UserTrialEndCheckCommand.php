<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\UserTrialEndCheckJob;

class UserTrialEndCheckCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:trial_end_check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Users Trial Period Check & DT shop Delete';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        UserTrialEndCheckJob::dispatch();
    }
}
