<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\ShopOrderSyncJob;
use App\Jobs\UniversalExport\ImportSync;

class ShopOrderSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shop:order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Shop order Sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        print "Shop Orders Sync Start\n";
        try{
            $app_id = config('global.interval_app_id');
            $sync_shop_types = config('global.sync_shop_types');
            if(empty($sync_shop_types)) return;

            $user_has_plan =  \DB::table('purchase_apps')->where('purchase_apps.app_id', $app_id)->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->pluck('cms_user_id')->toArray();
            $user_has_assign = \DB::table('app_assigns')->where(['app_id'=>$app_id])
            ->where(function($amnu){
                $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
            })
            ->select('user_id')->pluck('user_id')->toArray();
            $users_all = array_unique(array_merge($user_has_plan, $user_has_assign));

            $today = now();
            \App\Shop::join('cms_users', function($join){
                $join->on('shops.user_id', '=', 'cms_users.id')
                ->where('cms_users.id', '<=', config('global.greater_user_id')) // Old users
                ->whereNotNull('cms_users.status');
            })
            ->where('shops.status', '=', 1)
            ->whereIntegerNotInRaw('shops.user_id', $users_all)
            ->select('shops.*')
            ->groupBy('shops.id')
            ->orderBy('shops.id')
            ->chunk(10, function($chunk) use ($sync_shop_types) {

                $chunk->whereIn('channel', $sync_shop_types)->each(function($shop) {
                    ShopOrderSyncJob::dispatch($shop, $shop->channel)->onQueue('ordersync');
                });

                $users = $chunk->pluck('user_id')->toArray();
                ImportSync::dispatch($users);
            });

        }catch( \Exception $e){
            print "Shop Orders Sync Failed\n";
        }finally {
          print "Shop Orders Sync End\n";
        }
    }
}
