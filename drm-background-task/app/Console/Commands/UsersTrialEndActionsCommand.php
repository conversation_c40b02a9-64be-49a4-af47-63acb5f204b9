<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\UsersTrialEndActionsJob;
use App\Jobs\TwoWeeksPassedActionsJob;
use App\Jobs\UsersTariffExpiresActionsJob;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UsersTrialEndActionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:trial-end-actions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'After trial end actions for users';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $trial_end_users_query = User::join('app_trials', 'cms_users.id', '=', 'app_trials.user_id')
			->whereNotExists(function ($query) {
				$query->select(DB::raw(1))->from('purchase_import_plans')
					->whereRaw('purchase_import_plans.cms_user_id = cms_users.id');
			})
			->where([
				'trial_days' => 14,
				'app_id'	 => 0,
			])
            ->where(function($q) {
                $q->where('cms_users.id', '>', config('global.greater_user_id'));
                $q->orWhereExists(function ($sub_query) {
                    $sub_query->select(DB::raw(1))->from('old_users_tariff')
                        ->whereColumn('cms_users.id', 'old_users_tariff.user_id')
                        ->limit(1);
                });
            })
			->whereRaw('DATE_ADD(app_trials.start_date, INTERVAL app_trials.trial_days DAY) < NOW()')
			->select('cms_users.id', 'app_trials.start_date');

        $trial_end_users = $trial_end_users_query
            ->where('start_date', '<', Carbon::now()->subDays(14))
            ->pluck('id')->toArray();

        $two_weeks_passed_users = $trial_end_users_query
            ->where('start_date', '<', Carbon::now()->subDays(28))
            ->pluck('id')->toArray();

        $dt_purchases_users     = DB::table('dt_tariff_purchases')->pluck('user_id')->toArray();

        $trial_end_users        = array_diff($trial_end_users, $dt_purchases_users);
        $two_weeks_passed_users = array_diff($two_weeks_passed_users, $dt_purchases_users);

        // tariff expires
        $tariff_expires_drm_users = DB::table('purchase_import_plans')
            // ->whereNotNull('stripe_subscription_id')
            ->where('end_date', '<', date('Y-m-d'))
            ->pluck('cms_user_id')->toArray();

        $tariff_expires_dt_users = DB::table('dt_tariff_purchases')
            ->where('end_date', '<', date('Y-m-d'))
            ->pluck('user_id')->toArray();

        $tariff_expires_users = array_merge($tariff_expires_drm_users, $tariff_expires_dt_users);

		if (!empty($trial_end_users)) {
            UsersTrialEndActionsJob::dispatch($trial_end_users);
		}

        if (!empty($tariff_expires_users)) {
            UsersTariffExpiresActionsJob::dispatch($tariff_expires_users);
		}

        if (!empty($two_weeks_passed_users)) {
            TwoWeeksPassedActionsJob::dispatch($two_weeks_passed_users);
        }
    }
}
