<?php

namespace App\Console\Commands\Fixes;

use Illuminate\Console\Command;
use App\Models\ChannelUserCategory;

class FixCategoryFullPaths extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'channel-category:fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix channel category issues';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if(empty(\App\Enums\V2UserAccess::USERS)) return;

        $channelCategories = ChannelUserCategory::where('parent','!=',0)
        ->whereNotIn('user_id', \App\Enums\V2UserAccess::USERS)
        ->cursor();

        foreach ($channelCategories as $channelCategory) {
            $full_path = $channelCategory->full_path_generated;
            if($channelCategory->full_path != $full_path)
            {
                $channelCategory->full_path = $full_path;
                $channelCategory->save();
            }
        }
    }
}
