<?php

namespace App\Console\Commands\Fixes;

use App\Enums\Channel;
use App\Models\ChannelProduct;
use App\Services\ChannelProductService;
use Exception;
use Illuminate\Console\Command;
use App\Models\ChannelUserCategory;
use App\Jobs\ChannelManager\SyncCategoryProductCount;

class FixApiStocks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'api-stock:fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix API stocks';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @throws Exception
     */
    public function handle(): bool
    {
        if(empty(\App\Enums\V2UserAccess::USERS)) return true;

        $stockIssues = ChannelProduct::whereIn('channel',[
            Channel::KAUFLAND,
            Channel::EBAY,
//            Channel::TRADEBYTE,
            Channel::OTTO,
            Channel::ETSY,
            Channel::SHOPIFY,
//            Channel::DECATHLON
        ])
            ->where('is_connected',true)
            ->whereNotIn('user_id', \App\Enums\V2UserAccess::USERS)
            ->whereNotIn('shop_id',Channel::INACTIVE_SHOPS)
            ->whereRaw('stock != api_stock')->get()->groupBy('shop_id');

        foreach ($stockIssues as $shop_id => $issues) {
            try {
                app(ChannelProductService::class)->refreshConnection(
                    $issues->pluck('id')->toArray(),
                    $shop_id,
                    $issues[0]->channel,
                    $issues[0]->user_id
                );
            } catch (Exception $e){

            }
        }
        return true;
    }
}
