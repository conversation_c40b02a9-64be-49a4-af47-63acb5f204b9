<?php

namespace App\Console\Commands;

use App\Jobs\DRMStockHistoryStore;
use Illuminate\Console\Command;
use App\Jobs\AppointmentReminderEmail;

class StoreDRMStockHistory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:stock-history';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Store DRM product stock history command';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DRMStockHistoryStore::dispatch();
    }
}
