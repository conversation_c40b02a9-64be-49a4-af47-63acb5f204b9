<?php

namespace App\Events;

namespace App\Events;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Event;
use CRUDBooster;

class ManualUpdateEvent extends Event implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
     public $message;
     public function __construct($message)
     {
         if(!isset($message['user_id'])){
           $message['user_id'] = CRUDBooster::myId();
         }
         $this->message = $message;
     }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
     public function broadcastOn()
     {
         return ['manual_update'];
     }
     public function broadcastAs()
     {
       $user_id = $this->message['user_id'];
       return 'manual_update'.$user_id;
     }
}
