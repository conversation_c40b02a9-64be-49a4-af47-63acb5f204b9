<?php namespace App\Http\Controllers;

use Carbon\Carbon;
use DB;
use Request;
use Session;
use CRUDbooster;
use App\Mail\NewCustomers;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\User;
use App\takeappointment;
use PDF;
use GuzzleHttp\Client;
use App\Mail\DRMSEndMail;
use App\custom_paywall_charge;

class AdminCmsUsersController extends \crocodicstudio\crudbooster\controllers\CBController {


	public function cbInit() {
		# START CONFIGURATION DO NOT REMOVE THIS LINE
		$this->table               = 'cms_users';
		$this->primary_key         = 'id';
		$this->title_field         = "name";
		$this->button_action_style = 'button_icon';
		$this->button_import 	   = FALSE;
		$this->button_export 	   = FALSE;
		# END CONFIGURATION DO NOT REMOVE THIS LINE

		# START COLUMNS DO NOT REMOVE THIS LINE
		$this->col = array();
		$this->col[] = array("label"=>"Id","name"=>"id");
		$this->col[] = array("label"=>"Name","name"=>"name");
		$this->col[] = array("label"=>"Email","name"=>"email");
		$this->col[] = array("label"=>"Privilege","name"=>"id_cms_privileges","join"=>"cms_privileges,name");
		$this->col[] = array("label"=>"Photo","name"=>"photo","image"=>1);
		$this->col[] = array("label"=>"Status","name"=>"status");
		# END COLUMNS DO NOT REMOVE THIS LINE

		# START FORM DO NOT REMOVE THIS LINE
		$this->form = array();
		$this->form[] = array("label"=>"Name","name"=>"name",'required'=>true,'validation'=>'required|alpha_spaces|min:3');
		$this->form[] = array("label"=>"Email","name"=>"email",'required'=>true,'type'=>'email','validation'=>'required|email|unique:cms_users,email,'.CRUDBooster::getCurrentId());
		$this->form[] = array("label"=>"Photo","name"=>"photo","type"=>"upload","help"=>"Recommended resolution is 200x200px",'validation'=>'image|max:1000','resize_width'=>90,'resize_height'=>90);
		$this->form[] = array("label"=>"Privilege","name"=>"id_cms_privileges","type"=>"select","datatable"=>"cms_privileges,name",'required'=>true);
		// $this->form[] = array("label"=>"Password","name"=>"password","type"=>"password","help"=>"Please leave empty if not change");
		$this->form[] = array("label"=>"Password","name"=>"password","type"=>"password",'validation'=>'required',"help"=>"Please leave empty if not change");
		$this->form[] = array("label"=>"Password Confirmation","name"=>"password_confirmation","type"=>"password",'validation'=>'required|same:password',"help"=>"Please leave empty if not change");
		# END FORM DO NOT REMOVE THIS LINE

		 $this->index_button = array();
        // $this->index_button[] = ['label'=>'Send Charged Mail','url'=>route('send_email_charged_all'),'icon'=>'fa fa-envelope-o text-normal','color'=>'success']; //use AdminDrmAllOrdersController


		$this->addaction[] = ['label'=>'Login','url'=>CRUDBooster::mainpath('loginas-user/[id]'),'icon'=>'fa fa-key','color'=>'warning'];
		$this->addaction[] = ['label'=>'','url'=>CRUDBooster::mainpath('lock-user/[id]'),'icon'=>'fa fa-lock','color'=>'danger','showIf' => '[status] == Active'];
		$this->addaction[] = ['label'=>'','url'=>CRUDBooster::mainpath('unlock-user/[id]'),'icon'=>'fa fa-unlock-alt','color'=>'info','showIf' => '[status] != Active'];


	}


public function getLogback() {

	// jahidulhasanzahid
	$userEmail = DB::table('cms_users')->where('id',CRUDBooster::myId())->select('email')->first();
	CRUDBooster::insertLog(trans("crudbooster.log_logout", ['email' => $userEmail->email]), '', 'logout');
	// jahidulhasanzahid

	$old_id=Session::get('logged_as_old');
	$users =Auth::loginUsingId($old_id, true);
	$this->userlogin($users);
	Session::put('logged_as',0);

	return redirect(CRUDBooster::adminPath());
   // $users = DB::table(config('crudbooster.USER_TABLE'))->where("id", $id)->first();

}


public function getLoginasUser($id){

            if(!CRUDBooster::isSuperadmin()) {
				// echo "Invalid request";
				// dd("Invalid request");
				// return add jahiduilhasan and comment up 2 line
				return CRUDBooster::redirectBack("Invalid Request", "warning");
			}

            Session::put('logged_as',1);
            Session::put('logged_as_old',CRUDBooster::myId());
           // $users = DB::table(config('crudbooster.USER_TABLE'))->where("id", $id)->first();
           // dump();
            $users =Auth::loginUsingId($id, true);
            $this->userlogin($users);
            return redirect(CRUDBooster::adminPath());
}



public function getLockUser($id){
	DB::table('cms_users')->where('id',$id)->update(['status' => null]);
	CRUDBooster::redirect(CRUDBooster::adminPath('users'),"Lock User !","success");
}

public function getUnlockUser($id){
	DB::table('cms_users')->where('id',$id)->update(['status' => 'Active']);
	CRUDBooster::redirect(CRUDBooster::adminPath('users'),"Unlock User !","success");
}

public function userlogin($users) {
            $priv = DB::table("cms_privileges")->where("id", $users->id_cms_privileges)->first();
            $roles = DB::table('cms_privileges_roles')->where('id_cms_privileges', $users->id_cms_privileges)->join('cms_moduls', 'cms_moduls.id', '=', 'id_cms_moduls')->select('cms_moduls.name', 'cms_moduls.path', 'is_visible', 'is_create', 'is_read', 'is_edit', 'is_delete')->get();
            $photo = ($users->photo) ? asset($users->photo) : asset('vendor/crudbooster/avatar.jpg');
            Session::put('admin_is_superadmin', $priv->is_superadmin);
            Session::put('admin_id', $users->id);
            Session::put('admin_is_superadmin', $priv->is_superadmin);
            Session::put('admin_is_developer', $priv->is_dev);
            Session::put('admin_name', $users->name);
            Session::put('admin_photo', $photo);
            Session::put('admin_privileges_roles', $roles);
            Session::put("admin_privileges", $users->id_cms_privileges);
            Session::put('admin_privileges_name', $priv->name);
            Session::put('admin_lock', 0);
            Session::put('theme_color', $priv->theme_color);
            Session::put("appname", CRUDBooster::getSetting('appname'));
            CRUDBooster::insertLog(trans("crudbooster.log_login", ['email' => $users->email, 'ip' => Request::server('REMOTE_ADDR')]));
            $cb_hook_session = new \App\Http\Controllers\CBHook;
            $cb_hook_session->afterLogin();
            return true;

}



	public function getProfile() {

		$this->button_addmore = FALSE;
		$this->button_cancel  = FALSE;
		$this->button_show    = FALSE;
		$this->button_add     = FALSE;
		$this->button_delete  = FALSE;
		$this->hide_form 	  = ['id_cms_privileges'];

		$data['page_title'] = trans("crudbooster.label_button_profile");
		$data['row']        = CRUDBooster::first('cms_users',CRUDBooster::myId());
		$this->cbView('crudbooster::default.form',$data);
	}
	public function hook_before_edit(&$postdata,$id) {
		unset($postdata['password_confirmation']);
	}
	public function hook_before_add(&$postdata) {
	    if($postdata['id_cms_privileges'] == 3){

		// Mail::to($postdata['email'])->send(new NewCustomers($postdata));

	        $tags = [
	            'user_name' => $postdata['name'],
	            'user_email' => $postdata['email'],
	            'password_confirmation' => $postdata['password_confirmation']
	        ];
	        $slug = 'welcome_email';
            $lang = getUserSavedLang($postdata['email']);
	        $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
	        // Mail::to($postdata['email'])->send(new DRMSEndMail($mail_data));
			app('drm.mailer')->getMailer()->to($postdata['email'])->send(new DRMSEndMail($mail_data));


		}
	    $postdata['status']='Active';
	    unset($postdata['password_confirmation']);
	}

	public function hook_after_add($postdata){

		$drmUserInfo = User::find($postdata);
		// dd($drmUserInfo->id_cms_privileges);

			$insert_appoinment_data = DB::table('takeappointment')->insert(
				['user_id' => $postdata, 'payment_date_for' => 1, 'payment_date_remaining' => 1]
			);

	}

	public function hook_after_delete($id) {
	        //Your code here
		DB::table('notifications')->where('notifiable_id',$id)->delete();
	}

	public function setEmailNotification(){
	    $value = $_REQUEST['value'];
	    if($value == 'true'){
	        $value = '1';
	    }else{
	        $value = '0';
	    }

	    $id = Crudbooster::myId();
	    DB::table('cms_users')->where('id',$id)->update(['email_notification'=>$value]);
	}




    public function getDetail($id)
	{
		if (!CRUDBooster::isRead() && $this->global_privilege == FALSE || $this->button_edit == FALSE) {
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		}
		$data = [];

		$currentDate= date("Y-m-d");

		$lastLoginDay = DB::table('cms_logs')->select('created_at')->where('id_cms_users', $id)
			->where(function ($query) {
				$query->where('description', 'like', '%login%');
			})->orderBy('id', 'desc')->first();

		// dd($lastLoginDay);

		$user = DB::table('cms_users')->where('id', $id)->first();


		$logInfo = DB::table('cms_logs')->select('created_at','description')->where('id_cms_users', $id)
			->where('created_at', 'like', "%{$currentDate}%")->get();


		// dd($data['logs']);
		// dd($logInfo);
		// foreach($logInfo as $log){

		// 	if (strpos($log->description, 'login') !== false) {
		// 		dd("Login");
		// 	}
		// 	else if(strpos($log->description, 'logout') !== false){
		// 		dd("logout");
		// 	}
		// 	else{
		// 		dd("ddd");
		// 	}
		// }

		$totalLogInfo = DB::table('cms_logs')->where('id_cms_users', $id)
			->whereDate('created_at', '>', Carbon::now()->subDays(30))->orderBy('id', 'desc')->get();


		$last30DaysValue = DB::table('cms_logs')->where('id_cms_users', $id)
			->whereDate('created_at', '>', Carbon::now()->subDays(30))->orderBy('id', 'desc')->get();

		// // dd($last30DaysValue);
		// $lastLoginDay = DB::table('cms_logs')->select('created_at')->where('id_cms_users', $id)
		// 	->where(function ($query) {
		// 		$query->where('description', 'like', '%login%');
		// 	})->orderBy('id', 'desc')->first();

		$takeAppointment = takeappointment::where('user_id',$id)->first();
		$custom_paywall_charge = custom_paywall_charge::where('user_id',$id)->select('charge')->first();

		// $this->cbView('admin.drm_user.detail_user_management',$logInfo, $data);
		//user active app  display
		 $assign_apps=DB::table('app_assigns')
                                ->join('app_stores','app_stores.id','app_assigns.app_id')
                                ->where('app_assigns.user_id',$id)
                                ->select('app_stores.*')
                                ->get();
        $apps=DB::table('purchase_apps')
                ->join('app_stores','app_stores.id','purchase_apps.app_id')
                ->select('app_stores.*','purchase_apps.type as p_type','purchase_apps.subscription_date_end','purchase_apps.subscription_date_start','purchase_apps.is_renew_cancel','purchase_apps.stripe_subscription_id')
                ->where('purchase_apps.cms_user_id',$id)
                ->where('purchase_apps.subscription_date_end','>=', date('Y-m-d'))
                ->get();
		//user active app  display
		return view('admin.drm_user.detail_user_management',compact('logInfo', 'totalLogInfo','user', 'last30DaysValue', 'lastLoginDay','takeAppointment','custom_paywall_charge','assign_apps','apps'));


	}

	public function customPaywallUpdate(Request $request){

		$user_id = request()->user_id;
		// dd($user_id);
		$paywallCharge = request()->charge;
		$paywallCheck = DB::table('custom_paywall_charges')->where('user_id',$user_id)->first();
		if($paywallCheck == null){
			$paywallChargeUpdate = DB::table('custom_paywall_charges')->insert(['user_id' => $user_id ,'charge' => $paywallCharge]);
		}
		else{
			$paywallChargeUpdate = DB::table('custom_paywall_charges')->where('user_id',$user_id)->update(['charge' => $paywallCharge]);
		}

		return CRUDBooster::redirectBack("Users Paywall Charge Percentage are Updated!", "success");

	}

	public function appointmentChange(Request $request){

		$user_id = request()->user_id;
		$appointment = request()->appointmentValue;
		$appointmentUpdate = DB::table('takeappointment')->where('user_id',$user_id)->update(['payment_date_remaining'=>$appointment]);
		return CRUDBooster::redirectBack("Mentor Appointment Dates are Updated!", "success");
	}


	public function hook_after_edit($id) {
		try {
			$cmsuserInfo = DB::table('cms_users')->where('id',$id)->first();

			$apiRequest['id'] = $cmsuserInfo->id;
			$apiRequest['db_host'] = '************';
			$apiRequest['db_user'] = 'forge';
			$apiRequest['db_password'] = 'EVlesfB2hRbs5VE3657S';
			$apiRequest['db_port'] = '3306';
			$apiRequest['name'] = $cmsuserInfo->name;
			$apiRequest['email'] = $cmsuserInfo->email;
			$apiRequest['password'] = $cmsuserInfo->password;

			$url = "https://drm.network/api/change-password";
			$client = new Client();

			$response  = $client->post($url,  ['form_params'=>$apiRequest]);
			$result = json_decode($response->getBody()->getContents(), TRUE);

		  } catch (\Exception $e) {}
	}
}
