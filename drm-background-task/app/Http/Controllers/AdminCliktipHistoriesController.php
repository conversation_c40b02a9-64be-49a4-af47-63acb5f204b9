<?php namespace App\Http\Controllers;

	use App\Jobs\KlickTippWebhookResponseProcessor;
	use Illuminate\Support\LazyCollection;
	use Illuminate\Support\Facades\Storage;
	use App\Notifications\DRMNotification;
	use Illuminate\Support\Facades\Log;

	use App\KlickTippRequest;
	use App\KlickTippHistory;
	use App\subscriptions;
	use App\DrmCustomer;
	use App\CustomerTag;
	use App\User;

	use AppStore;
	use GuzzleHttp\Client;
	use Carbon\Carbon;
	use CRUDBooster;
	use ServiceKey;
	use Session;
	use Request;
	use PDF;
	use DB;
	

	class AdminCliktipHistoriesController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = false;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "cliktip_histories";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Customer","name"=>"user_id","join"=>"cms_users,id"];
			$this->col[] = ["label"=>"CLickTip Request ID","name"=>"request_id"];
			$this->col[] = ["label"=>"Status","name"=>"status"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];

			# END FORM DO NOT REMOVE THIS LINE

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }

		//By the way, you can still create your own method in here... :)
		public function getIndex()
		{
			$purchase_check  = AppStore::CheckAppPurchaseBoolean('40');

			if(!$purchase_check[0]){
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}else{
				$data['app_details'] = $purchase_check[1];
			}

			$data['page_title'] = "KlickTipp Processes";
			if(CRUDBooster::isSuperadmin()){
				$data['klickTippRequests'] = KlickTippRequest::get();
			
				$data['klickTippHistory'] = KlickTippHistory::get();
			}else{
				$data['klickTippRequests'] = KlickTippRequest::where('user_id',CRUDBooster::myId())->get();
			
				$data['klickTippHistory'] = KlickTippHistory::where('klickTippId',$data['klickTippRequests'][0]->id)->get();
			}
		

			$data['url'] = "http://*************/customers/".CRUDBooster::myId()."/init?url=".urlencode(url('admin/klicktipp/send-request?'));

			return view('admin.klickTipp.index', compact('user'))->with($data);
		}

		// KlickTippRequest first response reciever // status, customer id and code will be sent here through get method
		public function klickTippFirstResponse(){
	
			$request['user_id'] = $_GET['customId'];
			$request['status'] = $_GET['status'];
			$request['code'] = $_GET['code'];

			if($request['status'] == 'error'){
				if($request['code'] == "CUSTOMER_ALREADY_EXISTS"){
					CRUDBooster::redirect(CRUDBooster::adminPath('cliktip_histories'),"You have already requested!");
				}else{
					CRUDBooster::redirect(CRUDBooster::adminPath('cliktip_histories'),"Something wentwrong in the KlickTipp Server. Try again later!");
				}
			}else{
				$clicktipRequest = new KlickTippRequest($request);
				$clicktipRequest->save();

				$client = new Client();
				$startSync = "http://*************/customers/".$request['user_id']."/sync";
				$startSyncResponse  = $client->post($startSync);
				
				CRUDBooster::redirect(CRUDBooster::adminPath('cliktip_histories'),"Your request to KlickTipp is sent! When The Subscribers are ready. They will be imported automatically. You do not have to do anything. Just visit this page later. You will be able to see the sync hisories.","success");
			}
		}

		//check connection with KlickTipp
		public static function checkKlickTippConnection($user_id)
		{
			try {
				$url = "http://*************/customers/".$user_id."/test";
				$client = new Client();
	
				$response  = $client->get($url);
				$result = json_decode($response->getBody()->getContents(), TRUE);
				
				if($result['test'] == true && $result['status'] == 'ok'){
					$data = "Connected";
				}else if($result['status'] == 'error' && $result['code'] == 'CUSTOMER_NOT_FOUND'){
					$data = "Request Not Found";
				}else{
					$data = "Disconnected";
				}
			} catch (\Exception $e) {
				$data = "Disconnected";
			}
			return $data;
		}

		// Check Sync Status
		public function checkSyncStatus($user_id)
		{
			try {
				$client = new Client();
				$url = "http://*************/customers/".$user_id."/subscribers?offset=0&limit=10";
				$response  = $client->get($url);
				$result = json_decode($response->getBody()->getContents(), TRUE);
				
				if($result['result']['total'] == 0){
					$data['msg'] = "Your Sync is still processing! Once it's ready we will start syncing!";
				}else{
					$data['msg'] = 'Your '.$result['result']['total'] .' KlickTipp subscribers are ready and already on auto sync!';
				}
			} catch (\Exception $e) {
				$data['msg'] = "Your Sync is still processing! Once it's ready we will start syncing!";
			}
			return view('admin.klickTipp.partial.syncStatus',$data)->render();
		}

		// canceling request
		public function destroyKlickTippRequest($user_id)
		{
			try {
				$url = "http://*************/customers/".$user_id."/remove";
				$client = new Client();
	
				$response  = $client->post($url);
				$request_id = KlickTippRequest::where('user_id',$user_id)->first();
				$request = KlickTippRequest::where('user_id',$user_id)->delete();

				KlickTippHistory::where('klickTippId',$request_id)->delete();
			
				CRUDBooster::redirect(CRUDBooster::adminPath('cliktip_histories'),"Request Deleted Succesfully!","success");
				
			} catch (\Exception $e) {
				CRUDBooster::redirect(CRUDBooster::adminPath('cliktip_histories'),"Something went wrong while deleting request!");
			}
			return back();
		}

		// trigger webhook for test purpose
		public function testWebhookTrigger($user_id)
		{
			try {
				$url = "http://*************/customers/".$user_id."/webhooks/fetch-finished";
				$client = new Client();
	
				$response  = $client->post($url);
				$result = json_decode($response->getBody()->getContents(), TRUE);
				if($result['status'] == 'ok' && $result['result'] == true)
				CRUDBooster::redirect(CRUDBooster::adminPath('cliktip_histories'),"Webhook triggered Successfully!","success");
				
			} catch (\Exception $e) {
				CRUDBooster::redirect(CRUDBooster::adminPath('cliktip_histories'),"Something went wrongwhile triggering webhook!");
			}
			return back();
		}

		// receive reponse that data is ready in that webhook
		public function syncResponse(){
			try{
				$data = json_decode(request()->getContent(), true);
	
				Log::channel('command')->info($data);
	
				$timestampString = 'D, d F Y H:i:s T';
				$timestamp = \Carbon\Carbon::createFromFormat($timestampString, $data['timestamp'])->format('Y-m-d H:i:s');
				KlickTippWebhookResponseProcessor::dispatch($data['id'], $timestamp);
			}catch (\Exception $e) {}
		}

		public function insertTag(){
			$tag_label = request()->tag_value;
			$tag_json = json_encode(array('id' => '', 'label' => $tag_label), true);
			
			CustomerTag::insert([
				'customer_id'=> request()->customer_id,
				'user_id'=>CRUDBooster::myId(),
				'tags'=> '['.$tag_json.']',
				'type'=> 'manual_tags',
				'input_type'=> 'Manual'
			]);

	    	return response(['msg','Tag Inserted']);
		}
		
		public function getManualTags(){
			$jsonTag = CustomerTag::where('user_id',CRUDBooster::myId())->where('customer_id',$_REQUEST['customer_id'])
			->where('type','manual_tags')
			->orderby('id','desc')
			->first();

			return json_decode($jsonTag->tags, TRUE);
		}
	}