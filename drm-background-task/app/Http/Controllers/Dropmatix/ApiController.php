<?php
namespace App\Http\Controllers\Dropmatix;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

use App\User;
use App\NewOrder;
use App\Jobs\InvoiceArchiveJob;
use App\Notifications\DRMNotification;

use App\Jobs\UpdateProductRq;
use App\Services\OrderTracking\OrderTracking;
use App\Services\OrderTracking\OrderCancel;
use App\Services\Marketplace\CategoryAccessModify;
use App\Services\Marketplace\PlushReturnLabelService;
use App\Services\DropCampus\DropmatixCampus;
use App\Services\DropCampus\DroptiendaCampus;
use App\Services\DropCampus\ExpertiseCampus;
use Illuminate\Support\Facades\DB;
use App\Jobs\ChannelManager\AutoTransfer;
use Exception;

class ApiController extends Controller
{
    private array $services  = [
        'ARCHIVE_INVOICE' => 'archiveInvoice',
        'UPDATE_PRODUCT_RQ' => 'updateProductRq',
        'ORDER_CANCEL_API_SEND' => 'orderCancelApiSend',
        'ORDER_TRACKING_NUMBER_SEND' => 'orderTrackingNumberSend',
        'SEND_CAMPAIGN' => 'sendCampaign',
        'MP_ORDER_PLACE' => 'mpOrderPlace',
        'MP_ORDER_PLACE_SUPPLIER_API' => 'mpOrderPlaceSupplierApi',
        'CATEGORY_ACCESS_MODIFY' => 'mpCategoryAccessModify',
        'VALIDATE_MP_RETURN_LABEL'=>'mpReturnLabel',
        'PRODUCT_TRANSFER_TO_CORE' => 'productTransferToCore',
        'TARIFF_PURCHASED' => 'tariffPurchased',
        'TARIFF_BLOCK_UNLOCK' => 'tariffBlockUnlock',
        'DELETE_ACCOUNT' => 'userAccountDelete',
        'TARIFF_DOWNGRADE_RESOLVED' => 'tariffDowngradeResolved',
        'AUTO_TRANSFER_PRODUCT_TO_SHOP' => 'autoTransferProductToShop',
    ];


    public function index(Request $request)
    {
        try {

            $this->verify();

            $serviceFn = $this->serviceFn();

            return call_user_func([$this, $serviceFn], $request);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Action failed. Error: ' . $e->getMessage(),
            ], 400);
        }
    }


    //Archive invoices
    private function archiveInvoice(Request $request)
    {
        $rules = ['orders' => 'required|array|min:1', 'user_id'   => 'required'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));


        $user = User::find($data["user_id"]);
        $order_ids = $data["orders"];

        if ($user && $order_ids) {
            $orders = NewOrder::whereIn('id', $order_ids)->pluck('id')->toArray();
            if (count($orders)) {
                InvoiceArchiveJob::dispatch($orders, $user)->onQueue('file-archive');
                return response()->json([
                    'success' => true,
                    'message' => 'Archive processing! After completing the process, we sent you notification!',
                ]);
            } else {
                throw new Exception("Invalid order selection. Please select only valid invoices.");
            }
        } else {
            throw new Exception("Error Processing Request");
        }
    }


    private function mpOrderPlace(Request $request)
    {
        $rules = ['order_id' => 'required'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        $order_id = $data['order_id'] ?? null;
        if(empty($order_id))
        {
            throw new \Exception('Invalid order!');
        }

        return app('App\Http\Controllers\AdminDrmAllOrdersController')->getOrderSupplier($order_id);
    }

    private function mpOrderPlaceSupplierApi(Request $request)
    {
        $rules = ['order_id' => 'required'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        $order_id = $data['order_id'] ?? null;
        if(empty($order_id))
        {
            throw new \Exception('Invalid order!');
        }

        return app('App\Http\Controllers\AdminDrmAllOrdersController')->sendApiOrder($order_id);
    }

    //Update product rq
    private function updateProductRq(Request $request)
    {
        $rules = ['order_id' => 'required', 'user_id'   => 'required'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        $order = NewOrder::where('cms_user_id', $data['user_id'])
            ->where('id', $data['order_id'])
            ->whereNotNull('shop_id')
            ->select('cart', 'shop_id', 'cms_user_id')
            ->first();

        if($order && $order->id)
        {
            $carts = json_decode($order->cart);
            UpdateProductRq::dispatch($carts, $order->cms_user_id, $order->shop_id);

            return response()->json([
                'success' => true,
                'message' => 'RQ request send successfully',
            ]);
        }

        throw new Exception('Order not found!');
    }


    private function orderCancelApiSend(Request $request)
    {
        $request->validate(['order_id' => 'required']);
        app(OrderCancel::class)->cancel($request->order_id);
    }


    //Order tracking
    private function orderTrackingNumberSend(Request $request)
    {
        $request->validate([
            'order_id' => 'required',
            'parcel_name' => 'required',
            'tracking_number' => 'required',
        ]);

        $payload = $request->only(['parcel_name', 'tracking_number']);         
        app(OrderTracking::class)->tracking($request->order_id, $payload);
    }


    //Campaign request
    private function sendCampaign(Request $request)
    {
        $rules = ['tags' => 'required|array|min:1'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        app('App\Http\Controllers\NewShopSyncController')->sendCampaignMailJobAfterInserting($data['tags']);

        return response()->json([
            'success' => true,
            'message' => 'Campaign request send successfully',
        ]);
    }


    // Tariff purchased
    private function tariffPurchased(Request $request)
    {
        $rules = ['user_id' => 'required'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        //unlock user & reactivate campus
		app('App\Http\Controllers\AdminDrmAllCustomersController')->lockUnlockCustomer($data['user_id']);
        $this->dropcampusReactivate($data['user_id']);

        return response()->json([
            'success' => true,
            'message' => 'Tariff purchased request send successfully',
        ]);
    }


    // Tariff blocked
    private function tariffBlockUnlock(Request $request)
    {
        $rules = ['user_id' => 'required', 'status' => 'required'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        //unlock user & reactivate campus
        app('App\Http\Controllers\AdminDrmAllCustomersController')->lockUnlockCustomer($data['user_id'], $data['status']);

        return response()->json([
            'success' => true,
            'message' => 'Tariff block request send successfully',
        ]);
    }

    // Tariff purchased
    private function tariffDowngradeResolved(Request $request)
    {
        $rules = ['user_id' => 'required'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        // TODO:: Tariff downgrade resolve logic
        app('App\Http\Controllers\tariffController')->tariffAllDataDelete($data['user_id']);

        return response()->json([
            'success' => true,
            'message' => 'Tariff downgrade request send successfully',
        ]);
    }

    // User account delete
    private function userAccountDelete(Request $request)
    {
        $rules = ['user_id' => 'required'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));
        //$data['user_id']
        app(\App\Services\DeactiveUser\UserRemove::class)->remove($data['user_id']); // remove user
        
        return response()->json([
            'success' => true,
            'message' => 'Account delete request send successfully',
        ]);
    }


    




    //Mp category access modify
    private function mpCategoryAccessModify(Request $request)
    {
        $rules = ['customer_id' => 'required', 'parent_category' => 'required|array|min:1'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        app(CategoryAccessModify::class)->categoryModifyAction($data);

        return response()->json([
            'success' => true,
            'message' => 'MP category modify event run successfully',
        ]);
    }

    //MP Product transfer to core
    public function productTransferToCore(Request $request){
        $rules = ['product_id' => 'required|array|min:1', 'user_id' => 'required'];
        $request->validate($rules);
        $product_id = $request->product_id;
        $user_id    = $request->user_id;

        app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferTarifLimitedProductsToDrm($product_id, $user_id);

        return response()->json([
            'success' => true,
            'message' => 'Product transfer to core successfully',
        ]);
    }    



    // Auto transfer product to shop
    public function autoTransferProductToShop(Request $request){
        $rules = ['shop_id' => 'required'];
        $request->validate($rules);
        $shop_id = $request->shop_id;


        $shop = DB::table('shops')->where('id', $shop_id)->select('user_id', 'id', 'lang')->first();
        if(empty($shop)) throw new Exception("Invalid shop");;

        DB::table('drm_products')
        ->where('drm_products.user_id', $shop->user_id)
        ->leftJoin('channel_products', function ($join) use ($shop) {
            $join->on('drm_products.id', '=', 'channel_products.drm_product_id')
                 ->where('channel_products.shop_id', $shop->id)
                 ->where('channel_products.user_id', $shop->user_id);
        })
        ->whereNull('channel_products.id')
        ->select('drm_products.id')
        ->orderBy('drm_products.id')
        ->chunk(200, function ($chunks) use ($shop) {
            AutoTransfer::dispatch($chunks->pluck('id')->toArray(), $shop->user_id, $shop->lang ?? 'de',[$shop->id]);
        });

        return response()->json([
            'success' => true,
            'message' => 'Product transfer to shop in progress',
        ]);
    }

    private function serviceFn()
    {
        $service = request()->header('service');
        if(isset($this->services[$service])) return $this->services[$service];

        throw new Exception("Invalid service");
    }

    private function verify()
    {
        $token = request()->header('token');
        if($token != config('app.drm_background_token')) {
            throw new Exception("Token mismatch");
        }
    }

    //Mp return label
    private function mpReturnLabel(Request $request)
    {
        $rules = [
            'id' => 'required|array|min:1',
            'weight' => 'required|numeric',
            'height' => 'required|numeric',
            'length' => 'required|numeric',
            'width' => 'required|numeric',
        ];

        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        $returnRest = app(PlushReturnLabelService::class)->returnlabel($data['id'], $data);

        return response()->json([
            'success' => true,
            'message' => $returnRest['message'],
        ]);
    }

    private function dropcampusReactivate($user_id){
        $user = User::with('billing_detail')->find($user_id);
        $drmCampusService = new DropmatixCampus();
        $drmCampusService->reactivateDropmatixCampusUser($user);
        $dtCampusService = new DroptiendaCampus();
        $dtCampusService->reactivateDroptiendaCampusUser($user);
        $erCampusService = new ExpertiseCampus();
        $erCampusService->reactivateExpertiseCampusUser($user);
    }

}
