<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\User;
use CRUDBooster;
use Carbon\Carbon;
use DB;
use ServiceKey;

class TakeAppointment extends Controller
{
    public function index(){
        return view('admin.drm_appointment.take_appointment');
    }
    
    
        public function sofort_appointment(){

            $req=json_decode(request()->data);
            $select_type = $req->select_type;
            $payment_source=request()->source;
            if($select_type == 'fiveday'){
                $price = 445.00;
                $day = 5;
            }
            elseif($select_type == 'tenday'){
                $price = 890.00;
                $day = 10;
            }
            elseif($select_type = 'twentyfiveday'){
                $price = 2225.00;
                $day = 25;
            }
            elseif($select_type = 'fiftyday'){
                $price = 8900.00;
                $day = 50;
            }
            else{
                $price = 0.00;
                $day = 0;
            }
            \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));  
            try{
            $charge = \Stripe\Charge::create([
                'amount' => $price*100,
                'currency' => 'eur',
                'source' => $payment_source,
            ]);
            
            if($charge->status != "succeeded"){
                // return CRUDBooster::redirect(url("https://drm_v7.test/admin"),"Sorry, the payment you made is failed","info");
                CRUDBooster::redirect(CRUDBooster::adminPath(),"Sorry, the payment you made is failed!","info");
            }
            }catch(\Exception $e){
            return "Sorry, the payment you made is failed";
            }
            
            $charge_id = $charge->id;
            $payment_type = "Sofort";
            $this->MentorAppointmentUpdate($price,$day,$charge_id,$payment_type);
    
            // return CRUDBooster::redirect(url("https://drm_v7.test/admin"),"Successfully Active Mentor Appointment Plan.","success");
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-plan'),"Successfully Active Mentor Appointment Plan.","success");
            
        }
    
        public function AppointmentSofort(Request $request){
          $type = $_POST['select_type'];
          $user=User::where('id',CRUDBooster::myId())->first();
    
                if($type == 'fiveday'){
                    $price = 445.00;
                    $day = 5;
                }
                elseif($type == 'tenday'){
                    $price = 890.00;
                    $day = 10;
                }
                elseif($type = 'twentyfiveday'){
                    $price = 2225.00;
                    $day = 25;
                }
                elseif($type = 'fiftyday'){
                    $price = 8900.00;
                    $day = 50;
                }
                else{
                    $price = 0.00;
                    $day = 0;
                }
    
                \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
                $source = \Stripe\Source::create([
                    "type" => "sofort",
                    "amount" => $price*100,
                    "currency" => "eur",
                    "redirect" => [
                    "return_url" => CRUDBooster::adminPath('')."/sofort_appointment?data=".json_encode(request()->post()),
                    ],
                    "sofort" => [
                    "country" => "DE",
                    ],
                    "owner" => [
                    "email" => $user->email,
                    "name" => $user->name,
                    ]
                ]);
                return redirect($source['redirect']['url']);
        }
    
        
    
        public function giropay_appointment(){

            $req=json_decode(request()->data);
            $select_type = $req->select_type;
            $payment_source=request()->source;
            if($select_type == 'fiveday'){
                $price = 445.00;
                $day = 5;
            }
            elseif($select_type == 'tenday'){
                $price = 890.00;
                $day = 10;
            }
            elseif($select_type = 'twentyfiveday'){
                $price = 2225.00;
                $day = 25;
            }
            elseif($select_type = 'fiftyday'){
                $price = 8900.00;
                $day = 50;
            }
            else{
                $price = 0.00;
                $day = 0;
            }
            \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));  
            try{
            $charge = \Stripe\Charge::create([
                'amount' => $price*100,
                'currency' => 'eur',
                'source' => $payment_source,
            ]);
            if($charge->status != "succeeded"){
                // return CRUDBooster::redirect(url("https://drm_v7.test/admin"),"Sorry, the payment you made is failed","info");
                CRUDBooster::redirect(CRUDBooster::adminPath(),"Sorry, the payment you made is failed!","info");
            }
            }catch(\Exception $e){
            return "Sorry, the payment you made is failed";
            }
            
            $charge_id = $charge->id;
            $payment_type = "Giropay";
            $this->MentorAppointmentUpdate($price,$day,$charge_id,$payment_type);
    
            // return CRUDBooster::redirect(url("https://drm_v7.test/admin"),"Successfully Active Mentor Appointment Plan.","success");
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-plan'),"Successfully Active Mentor Appointment Plan.","success");
            
        }
    
        public function AppointmentGiropay(Request $request){
            // dd($request->all());
          $type = $_POST['select_type'];
          $user=User::where('id',CRUDBooster::myId())->first();
    
                if($type == 'fiveday'){
                    $price = 445.00;
                    $day = 5;
                }
                elseif($type == 'tenday'){
                    $price = 890.00;
                    $day = 10;
                }
                elseif($type = 'twentyfiveday'){
                    $price = 2225.00;
                    $day = 25;
                }
                elseif($type = 'fiftyday'){
                    $price = 8900.00;
                    $day = 50;
                }
                else{
                    $price = 0.00;
                    $day = 0;
                }
    
                \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
    
                $source = \Stripe\Source::create([
                    "type" => "giropay",
                    "amount" => $price*100,
                    "currency" => "eur",
                    "redirect" => [
                        "return_url" => CRUDBooster::adminPath('')."/giropay_appointment?data=".json_encode(request()->post()),
                    ],
                    "owner" => [
                    "email" => $user->email,
                    "name" => $user->name,
                    ]
                ]);
    
                return redirect($source['redirect']['url']);
        }
    
        public function MentorAppointmentUpdate($price,$day,$charge_id,$payment_type){
            
            // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
            // ->join('countries','countries.id','=','billing_details.country_id')
            // ->first();
    
            // $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";
        
            
             $appointment = DB::table('takeappointment')->where('user_id',CRUDBooster::myId())->first();
             if($appointment == null){
                return CRUDBooster::redirectBack("You are not accessible to use this feature, Please Contact with DRM Customer Care!", "warning");
            }
            else{
                    $paymentAmount = $appointment->payment_amount;
                    $userID = $appointment->user_id;
                    $totalDay = $appointment->payment_date_for;
                    $totalDayDue = $appointment->payment_date_remaining;
    
                    $go = DB::table('takeappointment')->where('user_id',$userID)->update([
                        'payment_id' => $charge_id,
                        'payment_amount' => $paymentAmount + $price,
                        'payment_date' => date('Y-m-d'),
                        'payment_date_for' => $totalDay + $day,
                        'payment_date_remaining' => $totalDayDue + $day
                    ]);
                
                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($price * $taxShow) /100;
                $order_info = [
                    'user_id' => 98,
                    'cms_client'  => $userID,
                    'order_date'    => date('Y-m-d H:i:s'),
                    'total' => round(($price),2),
                    'sub_total' => round($price-$total_tax,2),
                    'total_tax' => round($total_tax,2),
                    'payment_type'  => $payment_type,
                    'status'    => "Succeeded",
                    'currency'  => "EUR",
                    'adjustment'    => 0,
                    'insert_type'   => 3,
                    'shop_id'       => 8,
                    'billing'   => userToBillingJson(CRUDBooster::myId()),
                    'order_id_api'  => $charge_id,
                ];

                  $carts = [];
                  $cart_item = [];
                  $cart_item['id'] = 1;
                  $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT','Mentor Appointment for '.$day.' Dates');
                  $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT',"Mentor Appointment");
                  $cart_item['qty'] = 1;
                  $cart_item['rate'] = round($price,2);
                  $cart_item['tax'] = $taxShow;
                  $cart_item['product_discount'] = 0;
                  $cart_item['amount'] = round($price,2);
                  $carts[] = $cart_item;
                  $order_info['cart'] = json_encode($carts);

                app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
            }
        }


    public function Appointment(Request $request){
        // dd($request->all());
        
    // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
    // ->join('countries','countries.id','=','billing_details.country_id')
    // ->first();

    // $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";   

      $type = $_POST['select_type'];
      $user=User::where('id',CRUDBooster::myId())->first();
      $appointment = DB::table('takeappointment')->where('user_id',CRUDBooster::myId())->get();
      if($appointment[0] == null){
            return CRUDBooster::redirectBack("You are not accessible to use this feature, Please Contact with DRM Customer Care!", "warning");
      }
      else{
            $paymentAmount = $appointment[0]->payment_amount;
            $userID = $appointment[0]->user_id;
            $totalDay = $appointment[0]->payment_date_for;
            $totalDayDue = $appointment[0]->payment_date_remaining;
    
            if($type == 'fiveday'){
                $price = 445.00;
                $day = 5;
            }
            elseif($type == 'tenday'){
                $price = 890.00;
                $day = 10;
            }
            elseif($type = 'twentyfiveday'){
                $price = 2225.00;
                $day = 25;
            }
            elseif($type = 'fiftyday'){
                $price = 8900.00;
                $day = 50;
            }
            else{
                $price = 0.00;
                $day = 0;
            }
            
            
              \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
        
              $charge = \Stripe\Charge::create([
                'amount' => $price * 100,
                'currency' => 'eur',
                'description' => 'Mentor Appointment Package Active by "'. $user->name.'", & Plan Active for '. $day.' Dates',
                'source' => $_POST['stripeToken']
              ]);
              

                if($charge->status != "succeeded"){
                    return CRUDBooster::redirectBack("Something wrong with you payment!", "success");
                }
                else{

                DB::table('takeappointment')->where('user_id',$userID)->update([
                    'payment_id' => $charge->id,
                    'payment_amount' => $paymentAmount + $price,
                    'payment_date' => date('Y-m-d'),
                    'payment_date_for' => $totalDay + $day,
                    'payment_date_remaining' => $totalDayDue + $day
                ]);
                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($price * $taxShow) /100;
                $order_info = [
                    'user_id' => 98,
                    'cms_client'  => $userID,
                    'order_date'    => date('Y-m-d H:i:s'),
                    'total' => round(($price),2),
                    'sub_total' => round($price-$total_tax,2),
                    'total_tax' => round($total_tax,2),
                    'payment_type'  => 'Stripe Card',
                    'status'    => $charge->status,
                    'currency'  => "EUR",
                    'adjustment'    => 0,
                    'insert_type'   => 3,
                    'shop_id'       => 8,
                    'billing'   => userToBillingJson(CRUDBooster::myId()),
                    'order_id_api'  => $charge->id,
                    
                ];
                
                  $carts = [];
                  $cart_item = [];
                  $cart_item['id'] = 1;
                  $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT','Mentor Appointment for '.$day.' Dates');
                  $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT',"Mentor Appointment");
                  $cart_item['qty'] = 1;
                  $cart_item['rate'] = round($price,2);
                  $cart_item['tax'] = $taxShow;
                  $cart_item['product_discount'] = 0;
                  $cart_item['amount'] = round($price,2);
                  $carts[] = $cart_item;
                  $order_info['cart'] = json_encode($carts);

                app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);

                // return CRUDBooster::redirectBack("Your Mentor Appointment package is Confirm. Now you can get an appointment with mentor.!", "success");
                CRUDBooster::redirect(CRUDBooster::adminPath('appointment-plan'),"Your Mentor Appointment package is Confirm. Now you can get an appointment with mentor.!","success");
            }
        }
    }


}
