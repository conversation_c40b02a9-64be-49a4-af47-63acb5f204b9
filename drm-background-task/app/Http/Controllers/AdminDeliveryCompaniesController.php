<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use CB;
	use App\AppStore;
	use Illuminate\Support\Facades\App;
	use Illuminate\Support\Facades\Cache;
	use Illuminate\Support\Facades\Hash;
	use Illuminate\Support\Facades\PDF;
	use Illuminate\Support\Facades\Route;
	use Illuminate\Support\Facades\Storage;
	use Illuminate\Support\Facades\Validator;
    use Maatwebsite\Excel\Facades\Excel;
    use Illuminate\Support\Str;
	use App\Http\Controllers\AdminDrmImportsController;
	use Illuminate\Validation\Rule;

	class AdminDeliveryCompaniesController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {
			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "name";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = false;
			$this->button_filter = true;
			$this->button_import = true;
			$this->button_export = false;
			$this->table = "delivery_companies";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"User","name"=>"user_id","join"=>"cms_users,name"];
			$this->col[] = ["label"=>"Name","name"=>"name"];
			$this->col[] = ["label"=>"Website","name"=>"url"];
			$this->col[] = ["label"=>"Category","name"=>"category_id","join"=>"drm_supplier_categories,category_name"];
			$this->col[] = ["label"=>"Address","name"=>"address"];
			$this->col[] = ["label"=>"Zip","name"=>"zip"];
			$this->col[] = ["label"=>"State","name"=>"state"];
			$this->col[] = ["label"=>"Country Id","name"=>"country_id","join"=>"countries,name"];
			$this->col[] = ["label"=>"Email","name"=>"email"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Name','name'=>'name','type'=>'text','validation'=>'required|string|min:3|max:70','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
			$this->form[] = ['label'=>'Website','name'=>'url','type'=>'text','validation'=>'required|string|min:3|max:70','width'=>'col-sm-10','placeholder'=>'Enter URL'];
			$this->form[] = ['label'=>'Address','name'=>'address','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Zip','name'=>'zip','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'State','name'=>'state','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Country','name'=>'country_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'countries,name'];
			$this->form[] = ['label'=>'Email','name'=>'email','type'=>'email','validation'=>'required|min:1|max:255|email','width'=>'col-sm-10','placeholder'=>'Please enter a valid email address'];
			$this->form[] = ['label'=>'Phone','name'=>'phone','type'=>'number','validation'=>'required|numeric','width'=>'col-sm-10','placeholder'=>'You can only enter the number only'];
			$this->form[] = ['label'=>'Contact Name','name'=>'contact_name','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Customer Number','name'=>'customer_number','type'=>'text','validation'=>'nullable|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Category','name'=>'category_id','type'=>'select2','validation'=>'nullable|integer|min:0','width'=>'col-sm-10','datatable'=>'drm_supplier_categories,category_name', 'datatable_where'=>'drm_supplier_categories.user_id = '.CRUDBooster::myId()];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
			//$this->form[] = ["label"=>"Name","name"=>"name","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
			//$this->form[] = ["label"=>"Address","name"=>"address","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Zip","name"=>"zip","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"State","name"=>"state","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Country Id","name"=>"country_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"country,id"];
			//$this->form[] = ["label"=>"Email","name"=>"email","type"=>"email","required"=>TRUE,"validation"=>"required|min:1|max:255|email|unique:delivery_companies","placeholder"=>"Please enter a valid email address"];
			//$this->form[] = ["label"=>"Phone","name"=>"phone","type"=>"number","required"=>TRUE,"validation"=>"required|numeric","placeholder"=>"You can only enter the number only"];
			//$this->form[] = ["label"=>"Contact Name","name"=>"contact_name","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = "
	            #table_dashboard tr td:nth-child(4) {
                    width: 214px;
                }

	        ";



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
				$user_id = CRUDBooster::myId();
				if(!CRUDBooster::isSuperadmin()){
					$query->where('delivery_companies.user_id',$user_id);
				}

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value){
				if($column_index == 3){
					if(!pathIsUrl($column_value)){
						$column_value = "http://".$column_value;
					}
					$column_value = "<a href='$column_value' target='_blank'>$column_value</a>";
				}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {

			$postdata['user_id'] = CRUDBooster::myId();
			//custom email validation
			Validator::make($postdata, [
				'email'      => [
					'required',
					Rule::unique('delivery_companies')->where(function ($query) {
					   return $query->where('user_id',CRUDBooster::myId());
					})],
				'name' => ['required'],
				'contact_name' => ['required'],
				'phone' => ['required'],
				'url' => ['required'],

				'address' => ['required'],
				'zip' => ['required'],
				'state' => ['required'],
				'country_id' => ['required'],
			],[
				'address.required' => 'Street field is required',
			]

			)->validate();
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
			$postdata['user_id'] = CRUDBooster::myId();

			//custom email validation
			Validator::make($postdata, [
				'email'      => [
					'required',
					Rule::unique('delivery_companies')->where(function ($query) {
					   return $query->where('user_id',CRUDBooster::myId());
					})->ignore($id)], //ignoring supplier that is being eidted
			])->validate();

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }

	    public function getAddressManagement(){

	    }

	    public function getChangeBaseCalculation() {

	    }


	    public function getAssortment() {

	    }


public function getImportData()
{
	$this->cbLoader();
	$data['page_menu']  = \Route::getCurrentRoute()->getActionName();
	$data['page_title'] = 'Import Data '.$module->name;

	if (Request::get('file') && ! Request::get('import')) {
		$file = base64_decode(Request::get('file'));
		$file = storage_path('app/'.$file);
		// $rows = Excel::load($file, function ($reader) {
		// })->get();
		$type = pathinfo($file, PATHINFO_EXTENSION);

		$import = new AdminDrmImportsController;
		$rows = $import->csvToArray($file,$type,';',false);

		$countRows = ($rows)?count($rows):0;
		//dd($countRows );
		Session::put('total_data_import', $countRows);

		$data_import_column = [];
		foreach ($rows as $value) {
			$a = [];
			foreach ($value as $k => $v) {
				$a[] = $k;
			}
			if ($a && count($a)) {
				$data_import_column = $a;
			}
			break;
		}
	   $table_columns = DB::getSchemaBuilder()->getColumnListing($this->table);
		  unset($table_columns[1]);

		$labels = [
			2=>'Name Lieferant',
			3=>'Straße',
			4=>'PLZ',
			5=>'Ort/Stadt',
			6=>'Land',
			7=>'E-Mail-Adresse',
			8=>'Telefon',
			9=>'Name Ansprechpartner',
			10=>'Kategorie',
			11=>'Kundennummer',
			14=>'Hersteller-Logo',
			15=>'Doc',
			16=>'Notitz',
			17=>'Webseite'
		];

		$data['table_columns'] = $table_columns;
		$data['data_import_column'] = $data_import_column;
		$data['labels'] = $labels;

	}
	return view('admin.drm_delivery_companies.import', $data);
}


public function postDoImportChunk()
{
		$this->cbLoader();
		$file_md5 = md5(Request::get('file'));

		if (Request::get('file') && Request::get('resume') == 1) {
				$total = Session::get('total_data_import');
				$prog = intval(Cache::get('success_'.$file_md5)) / $total * 100;
				$prog = round($prog, 2);
				if ($prog >= 100) {
						Cache::forget('success_'.$file_md5);
				}

				return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_'.$file_md5)]);
		}

		$select_column = Session::get('select_column');
		$select_column = array_filter($select_column);
		$table_columns = DB::getSchemaBuilder()->getColumnListing($this->table);

		$file = base64_decode(Request::get('file'));
		$file = storage_path('app/'.$file);

		// $rows = Excel::load($file, function ($reader) {
		// })->get();
		$type = pathinfo($file, PATHINFO_EXTENSION);
		$import = new AdminDrmImportsController;
		$rows = $import->csvToArray($file,$type,';',false);

		$has_created_at = false;
		if (CRUDBooster::isColumnExists($this->table, 'created_at')) {
				$has_created_at = true;
		}

		$data_import_column = [];
		foreach ($rows as $value) {
			$value = (object)$value;
				$a = [];
				foreach ($select_column as $sk => $s) {
						$a['user_id'] = CRUDBooster::myId();
						$colname = $table_columns[$sk];
						if (CRUDBooster::isForeignKey($colname)) {

								//Skip if value is empty
								if ($value->$s == '') {
										continue;
								}

								if (intval($value->$s)) {
										$a[$colname] = $value->$s;
								} else {
										$relation_table = CRUDBooster::getTableForeignKey($colname);
										$relation_moduls = DB::table('cms_moduls')->where('table_name', $relation_table)->first();

										$relation_class = __NAMESPACE__.'\\'.$relation_moduls->controller;
										if (! class_exists($relation_class)) {
												$relation_class = '\App\Http\Controllers\\'.$relation_moduls->controller;
										}
										$relation_class = new $relation_class;
										$relation_class->cbLoader();

										$title_field = $relation_class->title_field;

										$relation_insert_data = [];
										$relation_insert_data[$title_field] = $value->$s;

										if (CRUDBooster::isColumnExists($relation_table, 'created_at')) {
												$relation_insert_data['created_at'] = date('Y-m-d H:i:s');
										}

										try {
												$relation_exists = DB::table($relation_table)->where($title_field, $value->$s)->first();
												if ($relation_exists) {
														$relation_primary_key = $relation_class->primary_key;
														$relation_id = $relation_exists->$relation_primary_key;
												} else {
														$relation_id = DB::table($relation_table)->insertGetId($relation_insert_data);
												}

												$a[$colname] = $relation_id;
										} catch (\Exception $e) {
												exit($e);
										}
								} //END IS INT

						} else {
								$a[$colname] = $value->$s;
						}
				}

				$has_title_field = true;
				foreach ($a as $k => $v) {
						if ($k == $this->title_field && $v == '') {
								$has_title_field = false;
								break;
						}
				}

				if ($has_title_field == false) {
						continue;
				}

				try {

						if ($has_created_at) {
								$a['created_at'] = date('Y-m-d H:i:s');
						}

						DB::table($this->table)->insert($a);
						Cache::increment('success_'.$file_md5);
				} catch (\Exception $e) {
						$e = (string) $e;
						Cache::put('error_'.$file_md5, $e, 500);
				}
		}

		return response()->json(['status' => true]);
}


public function getIndex()
{
		$this->cbLoader();

		$module = CRUDBooster::getCurrentModule();

		if (! CRUDBooster::isView() && $this->global_privilege == false) {
				CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
				CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
		}

		if (Request::get('parent_table')) {
				$parentTablePK = CB::pk(g('parent_table'));
				$data['parent_table'] = DB::table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
				if (Request::get('foreign_key')) {
						$data['parent_field'] = Request::get('foreign_key');
				} else {
						$data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
				}

				if ($parent_field) {
						foreach ($this->columns_table as $i => $col) {
								if ($col['name'] == $parent_field) {
										unset($this->columns_table[$i]);
								}
						}
				}
		}

		$data['table'] = $this->table;
		$data['table_pk'] = CB::pk($this->table);
		$data['page_title'] = $module->name;
		$data['iframe_url'] = 'https://player.vimeo.com/video/388360305';
		$data['page_description'] = trans('crudbooster.default_module_description');
		$data['date_candidate'] = $this->date_candidate;
		$data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

		$tablePK = $data['table_pk'];
		$table_columns = CB::getTableColumns($this->table);
		$result = DB::table($this->table)->select(DB::raw($this->table.".".$this->primary_key));

		if (Request::get('parent_id')) {
				$table_parent = $this->table;
				$table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
				$result->where($table_parent.'.'.Request::get('foreign_key'), Request::get('parent_id'));
		}

		$this->hook_query_index($result);

		if (in_array('deleted_at', $table_columns)) {
				$result->where($this->table.'.deleted_at', null);
		}

		$alias = [];
		$join_alias_count = 0;
		$join_table_temp = [];
		$table = $this->table;
		$columns_table = $this->columns_table;
		foreach ($columns_table as $index => $coltab) {

				$join = @$coltab['join'];
				$join_where = @$coltab['join_where'];
				$join_id = @$coltab['join_id'];
				$field = @$coltab['name'];
				$join_table_temp[] = $table;

				if (! $field) {
						continue;
				}

				if (strpos($field, ' as ') !== false) {
						$field = substr($field, strpos($field, ' as ') + 4);
						$field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
						$result->addselect(DB::raw($coltab['name']));
						$columns_table[$index]['type_data'] = 'varchar';
						$columns_table[$index]['field'] = $field;
						$columns_table[$index]['field_raw'] = $field;
						$columns_table[$index]['field_with'] = $field_with;
						$columns_table[$index]['is_subquery'] = true;
						continue;
				}

				if (strpos($field, '.') !== false) {
						$result->addselect($field);
				} else {
						$result->addselect($table.'.'.$field);
				}

				$field_array = explode('.', $field);

				if (isset($field_array[1])) {
						$field = $field_array[1];
						$table = $field_array[0];
				} else {
						$table = $this->table;
				}

				if ($join) {

						$join_exp = explode(',', $join);

						$join_table = $join_exp[0];
						$joinTablePK = CB::pk($join_table);
						$join_column = $join_exp[1];
						$join_alias = str_replace(".", "_", $join_table);

						if (in_array($join_table, $join_table_temp)) {
								$join_alias_count += 1;
								$join_alias = $join_table.$join_alias_count;
						}
						$join_table_temp[] = $join_table;

						$result->leftjoin($join_table.' as '.$join_alias, $join_alias.(($join_id) ? '.'.$join_id : '.'.$joinTablePK), '=', DB::raw($table.'.'.$field.(($join_where) ? ' AND '.$join_where.' ' : '')));
						$result->addselect($join_alias.'.'.$join_column.' as '.$join_alias.'_'.$join_column);

						$join_table_columns = CRUDBooster::getTableColumns($join_table);
						if ($join_table_columns) {
								foreach ($join_table_columns as $jtc) {
										$result->addselect($join_alias.'.'.$jtc.' as '.$join_alias.'_'.$jtc);
								}
						}

						$alias[] = $join_alias;
						$columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
						$columns_table[$index]['field'] = $join_alias.'_'.$join_column;
						$columns_table[$index]['field_with'] = $join_alias.'.'.$join_column;
						$columns_table[$index]['field_raw'] = $join_column;

						@$join_table1 = $join_exp[2];
						@$joinTable1PK = CB::pk($join_table1);
						@$join_column1 = $join_exp[3];
						@$join_alias1 = $join_table1;

						if ($join_table1 && $join_column1) {

								if (in_array($join_table1, $join_table_temp)) {
										$join_alias_count += 1;
										$join_alias1 = $join_table1.$join_alias_count;
								}

								$join_table_temp[] = $join_table1;

								$result->leftjoin($join_table1.' as '.$join_alias1, $join_alias1.'.'.$joinTable1PK, '=', $join_alias.'.'.$join_column);
								$result->addselect($join_alias1.'.'.$join_column1.' as '.$join_column1.'_'.$join_alias1);
								$alias[] = $join_alias1;
								$columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
								$columns_table[$index]['field'] = $join_column1.'_'.$join_alias1;
								$columns_table[$index]['field_with'] = $join_alias1.'.'.$join_column1;
								$columns_table[$index]['field_raw'] = $join_column1;
						}
				} else {

						if(isset($field_array[1])) {
								$result->addselect($table.'.'.$field.' as '.$table.'_'.$field);
								$columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
								$columns_table[$index]['field'] = $table.'_'.$field;
								$columns_table[$index]['field_raw'] = $table.'.'.$field;
						}else{
								$result->addselect($table.'.'.$field);
								$columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
								$columns_table[$index]['field'] = $field;
								$columns_table[$index]['field_raw'] = $field;
						}

						$columns_table[$index]['field_with'] = $table.'.'.$field;
				}
		}

		if (Request::get('q')) {
				$result->where(function ($w) use ($columns_table, $request) {
						foreach ($columns_table as $col) {
								if (! $col['field_with']) {
										continue;
								}
								if ($col['is_subquery']) {
										continue;
								}
								$w->orwhere($col['field_with'], "like", "%".Request::get("q")."%");
						}
				});
		}

		if (Request::get('where')) {
				foreach (Request::get('where') as $k => $v) {
						$result->where($table.'.'.$k, $v);
				}
		}

		$filter_is_orderby = false;
		if (Request::get('filter_column')) {

				$filter_column = Request::get('filter_column');
				$result->where(function ($w) use ($filter_column, $fc) {
						foreach ($filter_column as $key => $fc) {

								$value = @$fc['value'];
								$type = @$fc['type'];

								if ($type == 'empty') {
										$w->whereNull($key)->orWhere($key, '');
										continue;
								}

								if ($value == '' || $type == '') {
										continue;
								}

								if ($type == 'between') {
										continue;
								}

								switch ($type) {
										default:
												if ($key && $type && $value) {
														$w->where($key, $type, $value);
												}
												break;
										case 'like':
										case 'not like':
												$value = '%'.$value.'%';
												if ($key && $type && $value) {
														$w->where($key, $type, $value);
												}
												break;
										case 'in':
										case 'not in':
												if ($value) {
														$value = explode(',', $value);
														if ($key && $value) {
																$w->whereIn($key, $value);
														}
												}
												break;
								}
						}
				});

				foreach ($filter_column as $key => $fc) {
						$value = @$fc['value'];
						$type = @$fc['type'];
						$sorting = @$fc['sorting'];

						if ($sorting != '') {
								if ($key) {
										$result->orderby($key, $sorting);
										$filter_is_orderby = true;
								}
						}

						if ($type == 'between') {
								if ($key && $value) {
										$result->whereBetween($key, $value);
								}
						} else {
								continue;
						}
				}
		}

		if ($filter_is_orderby == true) {
				$data['result'] = $result->paginate($limit);
		} else {
				if ($this->orderby) {
						if (is_array($this->orderby)) {
								foreach ($this->orderby as $k => $v) {
										if (strpos($k, '.') !== false) {
												$orderby_table = explode(".", $k)[0];
												$k = explode(".", $k)[1];
										} else {
												$orderby_table = $this->table;
										}
										$result->orderby($orderby_table.'.'.$k, $v);
								}
						} else {
								$this->orderby = explode(";", $this->orderby);
								foreach ($this->orderby as $o) {
										$o = explode(",", $o);
										$k = $o[0];
										$v = $o[1];
										if (strpos($k, '.') !== false) {
												$orderby_table = explode(".", $k)[0];
										} else {
												$orderby_table = $this->table;
										}
										$result->orderby($orderby_table.'.'.$k, $v);
								}
						}
						$data['result'] = $result->paginate($limit);
				} else {
						$data['result'] = $result->orderby($this->table.'.'.$this->primary_key, 'desc')->paginate($limit);
				}
		}

		$data['columns'] = $columns_table;

		if ($this->index_return) {
				return $data;
		}

		//LISTING INDEX HTML
		$addaction = $this->data['addaction'];

		if ($this->sub_module) {
				foreach ($this->sub_module as $s) {
						$table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
						$addaction[] = [
								'label' => $s['label'],
								'icon' => $s['button_icon'],
								'url' => CRUDBooster::adminPath($s['path']).'?return_url='.urlencode(Request::fullUrl()).'&parent_table='.$table_parent.'&parent_columns='.$s['parent_columns'].'&parent_columns_alias='.$s['parent_columns_alias'].'&parent_id=['.(! isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']).']&foreign_key='.$s['foreign_key'].'&label='.urlencode($s['label']),
								'color' => $s['button_color'],
								'showIf' => $s['showIf'],
						];
				}
		}

		$mainpath = CRUDBooster::mainpath();
		$orig_mainpath = $this->data['mainpath'];
		$title_field = $this->title_field;
		$html_contents = [];
		$page = (Request::get('page')) ? Request::get('page') : 1;
		$number = ($page - 1) * $limit + 1;
		foreach ($data['result'] as $row) {
				$html_content = [];

				if ($this->button_bulk_action) {

						$html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='".$row->{$tablePK}."'/>";
				}

				if ($this->show_numbering) {
						$html_content[] = $number.'. ';
						$number++;
				}

				foreach ($columns_table as $col) {
						if ($col['visible'] === false) {
								continue;
						}

						$value = @$row->{$col['field']};
						$title = @$row->{$this->title_field};
						$label = $col['label'];

						if (isset($col['image'])) {
								if ($value == '') {
										$value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='".asset('vendor/crudbooster/avatar.jpg')."'><img width='40px' height='40px' src='".asset('vendor/crudbooster/avatar.jpg')."'/></a>";
								} else {
										$pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
										$value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='".$pic."'><img width='40px' height='40px' src='".$pic."'/></a>";
								}
						}

						if (@$col['download']) {
								$url = (strpos($value, 'http://') !== false) ? $value : asset($value).'?download=1';
								if ($value) {
										$value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
								} else {
										$value = " - ";
								}
						}

						if ($col['str_limit']) {
								$value = trim(strip_tags($value));
								$value = Str::limit($value, $col['str_limit']);
						}

						if ($col['nl2br']) {
								$value = nl2br($value);
						}

						if ($col['callback_php']) {
								foreach ($row as $k => $v) {
										$col['callback_php'] = str_replace("[".$k."]", $v, $col['callback_php']);
								}
								@eval("\$value = ".$col['callback_php'].";");
						}

						//New method for callback
						if (isset($col['callback'])) {
								$value = call_user_func($col['callback'], $row);
						}

						$datavalue = @unserialize($value);
						if ($datavalue !== false) {
								if ($datavalue) {
										$prevalue = [];
										foreach ($datavalue as $d) {
												if ($d['label']) {
														$prevalue[] = $d['label'];
												}
										}
										if ($prevalue && count($prevalue)) {
												$value = implode(", ", $prevalue);
										}
								}
						}

						$html_content[] = $value;
				} //end foreach columns_table

				if ($this->button_table_action):

						$button_action_style = $this->button_action_style;
					$html_content[] = "<div class='button_action' style='text-align:right'>".view('admin.crudbooster.supplier_action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render()."</div>";

				endif;//button_table_action

				foreach ($html_content as $i => $v) {
						$this->hook_row_index($i, $v);
						$html_content[$i] = $v;
				}

				$html_contents[] = $html_content;
		} //end foreach data[result]

		$html_contents = ['html' => $html_contents, 'data' => $data['result']];

		$data['html_contents'] = $html_contents;
		// $data['magazineAppResults']=AppStore::Leftjoin('app_categories','app_categories.id','=','app_stores.app_category_id')
		// 			->select('app_stores.*')->where('app_stores.feature_product',"Active")->get();


		$subs=DB::table('purchase_apps')
		->where('cms_user_id',CRUDBooster::myId())
		->where('type','!=','Free Trail')
		->where(function($q) {
				$q->where('subscription_date_end','>', date('Y-m-d'))
					->orwhere('subscription_life_time',1);
		})
		->pluck('app_id')->toArray();
		$query=AppStore::Leftjoin('app_categories','app_categories.id','=','app_stores.app_category_id')
		->select('app_stores.*');
		if(!empty($subs)){
			$query->whereNotIn('app_stores.id',$subs);
		}
		$data['magazineAppResults']=$query->where('app_stores.feature_product',"Active")->get();

		// $data['results'] = AppStore::where('feature_product',"Active")->get();
		// dd($data['results']);
		// die();
		return view('admin.drm_delivery_companies.index',$data);
  }

	    // get details
		public function getDetail($id)
		{

			$data['page_title'] = 'Details';

			$data['d_companies'] = DB::table('delivery_companies')->find($id);

			$data['docs'] = json_decode($data['d_companies']->doc);

			return view('admin.drm_delivery_companies.details',$data);
		}

		// upload-profile-doc
		public function postUploadProfileDoc()
		{
			// return $_FILES;
			// return $_POST;
			// $type = array_key_first($_FILES);
			// return $_FILES[$type];
			// return array_key_first($_FILES);
			// return response()->json($process);

			if($_FILES['profile_picture'])
			{
				$ext  = end(explode(".",$_FILES['profile_picture']['name']));
				$name = 'pp'.time().'.'.$ext;

				if(! move_uploaded_file($_FILES['profile_picture']['tmp_name'], './storage/delivery_companies/profile_picture/'.$name))
				{
					return response()->json('File not moved', 500);
				}

				DB::table('delivery_companies')->where('id',$_POST['id'])->update(['profile_picture' => $name]);

			}
			else if($_FILES['upload_doc'])
			{

				$docs = json_decode(DB::table('delivery_companies')->where('id',$_POST['id'])->first()->doc, true);
				if(!$docs)
				{
					$count =1;
				}
				else
				{
					$count = count($docs)+1;
				}

				if($count > 5)
				{
					return response()->json('File is maxed out.', 500);
				}

				$ext  = end(explode(".",$_FILES['upload_doc']['name']));
				$name = 'doc'.time().'.'.$ext;

				if(! move_uploaded_file($_FILES['upload_doc']['tmp_name'], './storage/delivery_companies/doc/'.$name))
				{
					return response()->json('File not moved', 500);
				}

				// return $count;

				$docs['doc'.$count] = $name;

				DB::table('delivery_companies')->where('id',$_POST['id'])->update([
					// 'file'.$_POST["file_no"] => $name,
					'doc' => json_encode($docs),
				]);

				// DB::table('delivery_companies')->where('id',$_POST['id'])->update(['doc' => $name]);
			}
			else
			{
				return response()->json('Picture not found', 500);
			}

			$data['file'] = $name;
			$data['type'] = array_key_first($_FILES);
			$data['count'] = $count;
			$data['docs'] = $docs;
			return response()->json($data);
			// return response()->json($_REQUEST);
		}

	    public function postUpdateNote()
		{
			// return $_REQUEST;

			DB::table('delivery_companies')->where('id',$_REQUEST['id'])->update([
				'note' => $_REQUEST['note'],
			]);
			$data = $_REQUEST;
			return response()->json($data, 200);
		}

		public function getSupplierListJson(){
			$email = isset($_REQUEST['email'])? $_REQUEST['email'] : null;
			$suppliers = \App\DeliveryCompany::select('id', 'name', 'email')->where('user_id', CRUDBooster::myId())->get()->map(function($item) use($email){
				$check = ($item->email == $email)? 'selected' : '';
                return ['id' => $item->id, 'name' => $item->name, 'selected' => $check ];
            })->toArray();
            if(count($suppliers)){
            	return response()->json([
            		'success' => true,
            		'data' => $suppliers
            	]);
            }
            return response()->json([
        		'success' => true,
        		'message' => 'Something went wrong!'
            ]);
		}


	}
