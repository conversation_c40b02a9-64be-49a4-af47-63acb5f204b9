<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Request;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use PDF;
use App\NewOrder;
use App\User;

class AdminCustomPaywallChargesController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = true;
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = true;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "custom_paywall_charges";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "User Name", "name" => "user_id", "join" => "cms_users,name"];
        $this->col[] = ["label" => "Charge", "name" => "charge"];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'User Name', 'name' => 'user_id', 'type' => 'select', 'validation' => 'required|integer|min:0', 'width' => 'col-sm-10', 'datatable' => 'cms_users,name'];
        // $this->form[] = ['label'=>'Charge','name'=>'charge','type'=>'text','validation'=>'required|text|min:0','width'=>'col-sm-10'];
        $this->form[] = ['label' => 'Charge(%)', 'name' => 'charge', 'type' => 'number', 'validation' => 'required|min:0', 'step' => 'any', 'width' => 'col-sm-10'];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'cms_users,name'];
        //$this->form[] = ['label'=>'Charge','name'=>'charge','type'=>'text','validation'=>'required|text|min:0','width'=>'col-sm-10'];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }


    //By the way, you can still create your own method in here... :)

    public function getChargedAmountList()
    {

        $request = $_REQUEST;

        $date_data = isset($request['date']) ? $request['date'] : date('Y-m');

        $date_data = explode('-', $date_data);

        $year = isset($date_data[0]) ? $date_data[0] : date('Y');
        $month = isset($date_data[1]) ? $date_data[1] : date('m');

        $data = [];
        $data['page_title'] = 'User List';
        $data['user_list'] = User::has('new_orders')->where('id_cms_privileges', 3)->select('id', 'name')->get();
        $data['users'] = User::with(['new_orders' => function ($q) use ($month, $year) {
            return $q->whereYear('new_orders.created_at', '=', $year)->whereMonth('new_orders.created_at', '=', $month)->where('new_orders.status', '!=', 'Canceled')->where('new_orders.invoice_number', '>', 0);
        }, 'paywall'])->whereHas('new_orders', function ($qq) use ($month, $year) {
            return $qq->whereYear('new_orders.created_at', '=', $year)->whereMonth('new_orders.created_at', '=', $month);
        })->where('id_cms_privileges', 3)->get();


// $data = User::join('new_orders')->get();

        // dd($data);


        return view('admin.new_order.charged_amount_list', $data);
    }

    public function getChargeInvoice($user_id)
    {

        $data['page_title'] = 'Invoice Details';

        if (isset($_REQUEST['date']) && $_REQUEST['date']) {
            $date_data = explode('-', $_REQUEST['date']);
            $year = isset($date_data[0]) ? $date_data[0] : null;
            $month = isset($date_data[1]) ? $date_data[1] : null;
            if ($year && $month) {
                $data['user'] = User::with(['new_orders' => function ($q) use ($month, $year) {
                    return $q->whereYear('new_orders.created_at', '=', $year)->whereMonth('new_orders.created_at', '=', $month);
                }, 'paywall'])->whereHas('new_orders', function ($qq) use ($month, $year) {
                    return $qq->whereYear('new_orders.created_at', '=', $year)->whereMonth('new_orders.created_at', '=', $month)->where('new_orders.status', '!=', 'Canceled')->where('new_orders.invoice_number', '>', 0);
                })->where('id_cms_privileges', 3)->find($user_id);
            } else {
                $data['user'] = User::with('new_orders')->find($user_id);
            }
        }

        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $id)->orderBy('id', 'desc')->first();
        $pdf = PDF::loadView('admin.invoice.charge', $data);
        return $pdf->stream();
    }

    // View charged amount
    public function getChargeAmount($user_id)
    {
        $data['page_title'] = 'Charged Amount';
        $user = User::with('new_orders')->find($user_id);
        $data = array(
            'charged_due_amount' => $user->total_payable_amount,
            'charged_paid_amount' => $user->total_paid_amount,
            'total_order' => $user->total_order,
            'total_order_price' => $user->total_order_amount,
            'user_name' => $user->name,
            'user_id' => $user_id,

        );
        return view('admin.new_order.charged_amount', $data);
    }

    // Creating for sending mail with charged amount for a All User
    public function getEmailCharge($user_id)
    {
        $data = [];
        $data['page_title'] = 'Invoice Details';
        $data['user'] = User::with('new_orders')->find($user_id);
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();

        $pdf = PDF::loadView('admin.invoice.charge', $data);

        try {
            if (filter_var($data['user']->email, FILTER_VALIDATE_EMAIL)) {
                app('drm.mailer')->getMailer()->send('admin.drm_order.charged_mailD', $data, function ($message) use ($data, $pdf) {
                    $message->to($data['user']->email, $data['user']->name)
                        ->subject('Charged Amount this month from DRM')
                        ->attachData($pdf->output(), "charged_invoice.pdf");
                });
                if (CRUDBooster::myId()) CRUDBooster::redirectBack(trans('Mail sent!'), 'success');
            } else {
                if (CRUDBooster::myId()) CRUDBooster::redirectBack(trans('Invalid Email!' . $data['user']->email . 'kk'), 'error');
            }
        } catch (JWTException $exception) {
            $this->serverstatuscode = "0";
            $this->serverstatusdes = $exception->getMessage();
            if (CRUDBooster::myId()) CRUDBooster::redirectBack($exception->getMessage(), 'error');
        }
    }


}
