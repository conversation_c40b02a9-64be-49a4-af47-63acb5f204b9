<?php

namespace App\Http\Controllers;

use App\HandlingTimeEmailHistory;
use App\Models\DeliveryNote;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Request;
use Illuminate\Support\Facades\DB;
use PDF;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Helper\LengowApi;
use crocodicstudio\crudbooster\helpers\CB;
use DateTime;
use DateInterval;
use Illuminate\Support\Facades\Validator;
use ZipArchive;
use Illuminate\Support\Facades\Storage;
use App\NewOrder;
use App\InvoiceProduct;
use App\DrmProduct;
use App\NewCustomer;
use App\MonthlyPaywall;
use App\User;
use Exception;
use AppStore;
use App\CustomerTag;
use App\DeliveryCompany;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Collection;
use App\Notifications\DRMNotification;
use App\Jobs\InvoiceArchiveJob;
use Illuminate\Support\Facades\Cache;
use App\BillingDetail;
use App\Models\Product\ProfitCalculation;
use App\Models\ChannelProduct;
use App\OrderTrackings;
use App\OrderTrackingEmail;
use App\Services\Marketplace\MPSupplierOrder;
use App\DropfunnelCustomerTag;
use App\Services\Payment\PaymentTax;
use App\Models\Marketplace\MpVirtualCredit;
use App\Services\Notification\PushNotifyDeliveryProblem;

class AdminDrmAllOrdersController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "order_date,desc;invoice_number,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_img";
        $this->button_add = false;
        $this->button_edit = true;
        if (CRUDBooster::isSuperadmin() || (CRUDBooster::myId() == 98))
            $this->button_delete = true;
        else
            $this->button_delete = false;
        $this->button_detail = false;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "new_orders";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "ID", "name" => "id"];
        $this->col[] = ["label" => __('order.ORDER_ID'), "name" => "order_id_api"];
        if (CRUDBooster::isSuperadmin())
            $this->col[] = ["label" => "User", "name" => "cms_user_id", "join" => "cms_users,name"];
        $this->col[] = ["label" => __('order.SHOP_NAME'), "name" => "shop_id", "join" => "shops,shop_name"];
        $this->col[] = ["label" => __('order.CUSTOMER_NAME'), "name" => "drm_customer_id", "join" => "new_customers,full_name"];
        $this->col[] = ["label" => __('order.Invoice'), "name" => "invoice_number"];
        $this->col[] = ["label" => __('order.ORDER_DATE'), "name" => "order_date"];
        $this->col[] = ["label" => __('order.DELIVERY_STATUS'), "name" => "status"];
        $this->col[] = ["label" => __('order.Amount'), "name" => "total"];
        $this->col[] = ["label" => __('order.MAIL_SENT'), "name" => "mail_sent"];
        $this->col[] = ["label" => __('order.Supplier'), "name" => "supplier"];
        $this->col[] = ["label" => "Insert Type", "name" => "insert_type"];
        $this->col[] = ["label" => __('order.TAX_NUMBER'), "name" => "drm_customer_id", "join" => "new_customers,vat_number"];
        $this->col[] = ["label" => "Test Order", "name" => "test_order", "visible" => false];
        $this->col[] = ["label" => "Supplier Id", "name" => "supplier_id", "join" => "delivery_companies,name", "visible" => false];
        $this->col[] = ["label" => "Supplier timestamp", "name" => "supplier_time", "visible" => false];


        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'Cms Client', 'name' => 'cms_client', 'type' => 'textarea', 'width' => 'col-sm-10'];
        # END FORM DO NOT REMOVE THIS LINE

        /*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
        $this->sub_module = array();


        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
        $this->addaction = array();
        if (CRUDBooster::isSuperadmin() || (CRUDBooster::myId() == 98)) {
            $this->addaction[] = ['label' => 'API Response', 'url' => url('api/response/new-invoice/[id]'), 'color' => 'info', "icon" => "fa fa-list", 'showIf' => "[insert_type] == 1 && isShopExist([shop_id])"];
        }
        if (test_order_btn()) {
            $this->addaction[] = ['title' => 'Test Invoice', 'url' => CRUDBooster::mainpath('status-test/[id]'), 'color' => 'darkgray', 'confirmation_confirmButtonText' => testOrderYesBtn(), 'confirmation_btn_show' => testOrderYesBtnShow(), 'confirmation_text' => 'Do you want to mark this order as Test Order?', "img-icon" => asset('images/icons/test-order.svg'), 'showIf' => "[test_order] != 1", 'confirmation' => true];
        }
        // $this->addaction[] = ['label'=>'Shop not exist','url'=>'javascript:void(0)','icon'=>'fa fa-exclamation-triangle','color'=>'warning' , 'showIf'=>"[insert_type] == 1 && !isShopExist([shop_id])"];
        $this->addaction[] = ['title' => 'Customer', 'url' => CRUDBooster::adminPath('drm_all_customers/detail/[drm_customer_id]'), 'icon' => 'fa fa-user', "img-icon" => asset('images/icons/customer.svg'), 'color' => 'darkgray', 'showIf' => "[drm_customer_id] != null"];

        $this->addaction[] = ['title' => 'Products', 'url' => 'javascript:show_order_products_popup([id])', "img-icon" => asset('images/icons/product.svg'), 'color' => 'darkgray', 'showIf' => "[test_order] != 1"];
        $this->addaction[] = ['title' => 'Products - Test Order', 'url' => 'javascript:show_order_products_popup([id])', "img-icon" => asset('images/icons/product.svg'), 'color' => 'warning', 'showIf' => "[test_order] == 1"];

        $this->addaction[] = ['title' => 'Send Email', 'url' => CRUDBooster::mainpath('send-email/[id]'), "img-icon" => asset('images/icons/email.svg'), 'color' => 'darkgray', 'showIf' => "[mail_sent] == null"];
        $this->addaction[] = ['title' => 'Resend Email', 'url' => CRUDBooster::mainpath('send-email/[id]'), 'color' => 'warning', "img-icon" => asset('images/icons/email.svg'), 'showIf' => "[mail_sent] != null"];

        $this->addaction[] = ['title' => 'Delivery Note', 'url' => CRUDBooster::mainpath('delivery-notes/[id]'), 'target' => '_blank', "img-icon" => asset('images/icons/delivary-note.svg'), 'color' => 'darkgray', "icon" => "fa fa-list"];
        $this->addaction[] = ['title' => 'Invoice', 'url' => CRUDBooster::mainpath('detail/[id]'), 'target' => '_blank', "img-icon" => asset('images/icons/invoice.svg'), 'color' => 'darkgray', "icon" => "fa fa-list"];


        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
        $this->button_selected = array();
        $this->button_selected[] = ['label' => '', 'icon' => 'fa fa-arrow-down', 'name' => ''];


        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
        $this->alert = array();


        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
        $this->index_button = array();
        // $this->index_button[] = ['label'=>'Neue Rechnung','url'=>CRUDBooster::mainpath('add'),'icon'=>'fa fa-plus-circle','color'=>'warning'];
        //    $this->index_button[] = ['label'=> __('order.INVOICE_SETTING'),'url'=>CRUDBooster::mainpath('invoice-setting'),'icon'=>'fa fa-cogs','color'=>'warning'];
        //    $this->index_button[] = ['label'=> __('order.EMAIL_SETTING'),'url'=>CRUDBooster::mainpath('email-setting'),'icon'=>'fa fa-cogs','color'=>'warning'];
        $this->index_button[] = ['label' => 'Import', 'color' => 'btn btn-success import_btn', "icon" => "fa fa-amazon"];
        // $this->index_button[] = ['label'=>'Filter','url'=>"javascript:void(0)",'color'=>'info',"icon"=>"fa fa-filter"];
        if (CRUDBooster::isSuperadmin()) {
            $this->index_button[] = ['label' => 'Paywall Invoice', 'url' => CRUDBooster::adminPath('custom_paywall_charges/charged-amount-list'), 'color' => 'info', "icon" => "fa fa-list"];
        }


        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
        $this->table_row_color = array();


        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */

        $order_statt = orderStatisticsData();
        $total_order = (isset($order_statt->total_order)) ? $order_statt->total_order : 0;
        $shipped_order = (isset($order_statt->shipped_order)) ? $order_statt->shipped_order : 0;
        $others_order = (isset($order_statt->others_order)) ? $order_statt->others_order : 0;

        $order_currency = (isset($order_statt->currency)) ? $order_statt->currency : null;
        $performa_statt = (isset($order_statt->performa)) ? $order_statt->performa : null;

        $this->index_statistic = array();
        $this->index_statistic[] = ['label' => __('order.TOTAL_ORDERS'), 'count' => $total_order, 'icon' => 'fa fa-area-chart', 'color' => 'success btn-success total-order', 'width' => 'col-md-2'];
        $this->index_statistic[] = ['label' => __('order.Shipped'), 'count' => $shipped_order, 'icon' => 'fa fa-google-wallet', 'color' => 'success btn-waning shipped-order', 'width' => 'col-md-2'];
        $this->index_statistic[] = ['label' => __('order.Processed'), 'count' => $others_order, 'icon' => 'fa fa-google-wallet', 'color' => 'success btn-danger other-order', 'width' => 'col-md-2'];

        $this->index_statistic[] = ['label' => __('order.TOTAL_SALES'), 'count' => '', 'amount' => $order_currency, 'icon' => 'fa fa-eur', 'color' => 'success btn-primary', 'width' => 'col-md-3'];
        $this->index_statistic[] = ['label' => __('order.PROFORMA_INVOICE'), 'count' => '', 'performa' => $performa_statt, 'icon' => 'fa fa-google-wallet', 'color' => 'success btn-warning proforma-invoice', 'width' => 'col-md-3'];


        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
        $this->script_js = '$(".total-order").click(function () {
                window.location.href = window.location.origin+ "/admin/drm_all_orders" ;
              });

              $(".shipped-order").click(function () {
                window.location.href = window.location.origin+ "/admin/drm_all_orders?filter=shipped" ;
              });

              $(".other-order").click(function () {
                window.location.href = window.location.origin+ "/admin/drm_all_orders?filter=other" ;
              });

              $(".proforma-invoice").click(function () {
                window.location.href = window.location.origin+ "/admin/drm_all_orders?filter=proforma-invoice" ;
              });

              $("#filter").click(function(e){
                e.preventDefault();

                $("#filter_modal").modal("show");
              });

              ';


        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
        $this->pre_index_html = null;


        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
        $this->post_index_html = '

            <!-- Modal -->
            <div id="import_modal" class="modal fade" role="dialog">
              <div class="modal-dialog">

                <!-- Modal content-->
                <form action="' . CRUDBooster::mainpath("import-order") . '" method="POST" enctype="multipart/form-data" >
                <input type="hidden" name="_token" value="' . csrf_token() . '">
                <div class="modal-content">
                  <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Amazon Import</h4>
                  </div>
                  <div class="modal-body">
                    <p>Amazon order reports upload in txt format.</p>
                    <input type="file" name="order_file" id="order_import" accept=".txt">
                  </div>
                  <div class="modal-footer">
                    <button type="submit" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                  </div>
                </div>
                </form>

              </div>
            </div>

            <div id="filter_modal" class="modal fade" role="dialog">
              <div class="modal-dialog">

                <!-- Modal content-->
                <form action="' . CRUDBooster::mainpath("") . '" method="get"  >

                <div class="modal-content">
                  <div class="modal-header" style="background: #f39c12; color: #fff;">
                    <button type="button" class="close" data-dismiss="modal" style="position: relative;top: -3px;border: 3px solid #fff;padding: 4px 10px;color: #fff;">&times;</button>
                    <h4 class="modal-title">Advance Filter</h4>
                  </div>
                  <div class="modal-body">

                    <div class="row">
                        <div class="col-md-12">
                            <h4>Search by Date range</h4>
                            <div class="form-group col-md-6 pl-0">
                                <strong>Start date</strong>
                                <input class="datepicker form-control" type="text" autocomplete="off" name="date_from" value="' . $_REQUEST['date_from'] . '">
                            </div>
                            <div class="form-group col-md-6 pl-0">
                                <strong>End date</strong>
                                <input class="datepicker form-control" type="text" autocomplete="off" name="date_to" value="' . $_REQUEST['date_to'] . '">
                            </div>
                        </div>
                    </div>
                  </div>

                  <input type="hidden" name="limit" value="200">

                  <div class="modal-footer">
                    <button type="submit" class="btn btn-orange">Submit</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                  </div>
                </div>
                </form>

              </div>
            </div>

            <!-- Modal -->
			<div class="modal fade view_modal" id="orderPopupModal" tabindex="-1" role="dialog"></div>

			<div class="modal fade view_modal" id="orderBulckSupplierModal" role="dialog">
              <div class="modal-dialog">

                <!-- Modal content-->
                <form action="' . CRUDBooster::mainpath("place-order") . '" method="post"  >
                ' . csrf_field() . '
                <div class="modal-content">
                  <div class="modal-header" style="background: #f39c12; color: #fff;">
                    <button type="button" class="close" data-dismiss="modal" style="position: relative;top: -3px;border: 3px solid #fff;padding: 4px 10px;color: #fff;">&times;</button>
                    <h4 class="modal-title">BULK Supplier orders</h4>
                  </div>
                  <div class="modal-body"></div>
                  <div class="modal-footer">
                    <button type="submit" class="btn btn-orange">Submit</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                  </div>
                </div>
                </form>

              </div>


				</div>
            ';


        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
        $this->load_js = array();
        $this->load_js[] = asset('js/orders.js');
        $this->load_js[] = asset('vendor/crudbooster/assets/select2/dist/js/select2.full.min.js');


        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
        $this->style_css = "";

        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
        $this->load_css = array();
        $this->load_css[] = asset('css/exports-btns.css');
        $this->load_css[] = asset('vendor/crudbooster/assets/select2/dist/css/select2.min.css');

        // $this->futureInoiceNumber();
    }


    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }

    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
    public function hook_query_index(&$query)
    {
        if (!CRUDBooster::isSuperadmin()) {
            $query->where('cms_user_id', CRUDBooster::myId());
        }

        // $query->where('new_orders.status', '!=', 'Canceled');
        $query->whereNotIn('new_orders.status', ['Storniert', 'Canceled']);

        if (isset($_REQUEST['filter'])) {
            if ($_REQUEST['filter'] === "shipped") {
                $query->where('new_orders.status', 'Shipped');
            } else if ($_REQUEST['filter'] === "other") {
                $query->where('new_orders.status', '!=', 'Shipped');
            } else if ($_REQUEST['filter'] === "proforma-invoice") {
                $query->where('new_orders.invoice_number', '-1');
            }
        }

        if ($_REQUEST['date_from']) {
            $date_from = $_REQUEST['date_from'] . ' 00:00:00';
            $query->whereRaw('cast(order_date as datetime) >= \'' . $date_from . '\'');
        }
        if ($_REQUEST['date_to']) {
            $date_to = $_REQUEST['date_to'] . ' 23:00:00';
            $query->whereRaw('cast(order_date as datetime) <= \'' . $date_to . '\'');
        }
        // $query->orderby($this->table.'.invoice_number','desc');
    }

    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
    public $order = [];

    public function hook_row_index($column_index, &$column_value)
    {
        $shop_col = 3;
        $customer_col = 4;
        $invoice_col = 5;
        $order_date_col = 6;
        $supplier_col = 10;
        $amount_col = 8;
        $insert_type_col = 11;
        $tax_col = 12;
        if (CRUDBooster::isSuperadmin()) {
            $shop_col += 1;
            $customer_col += 1;
            $invoice_col += 1;
            $order_date_col += 1;
            $supplier_col += 1;
            $amount_col += 1;
            $insert_type_col += 1;
            $tax_col += 1;
        }


        if ($column_index == 1) {
            $this->order['id'] = $column_value;
            $this->order['order_data'] = DB::table('new_orders')->find($column_value, ['insert_type', 'cms_client', 'supplier_id', 'supplier_time']);
            $this->order['supplier_name'] = DB::table('delivery_companies')->find($this->order['order_data']->supplier_id, ['name'])->name;
        }

        if ($column_index == $shop_col) {
            if ($column_value == null)
                $column_value = "DRM Manual";
            $this->order['shop_name'] = $column_value;
        }

        if ($column_index == $customer_col) {
            if (in_array($this->order['order_data']->insert_type, [3, 4])) {
                $column_value = DB::table('cms_users')->find($this->order['order_data']->cms_client, ['name'])->name;
            }
        }

        if ($column_index == $invoice_col) {
            // dd($this->order['id']);
            if ($column_value == -1)
                $column_value = "<span class ='label label-warning' style='font-size: 90%'> -1 (" . DB::table('new_orders')->find($this->order['id'])->invoice_date . ")</span>  ";
        }

        if ($column_index == $order_date_col) {
            $column_value = date('Y-m-d', strtotime($column_value));
        }

        if ($column_index == $supplier_col) {
            $supplier_time = $this->order['order_data']->supplier_time;
            // $supplier_time = ($supplier_time && ($column_value == 'checked'))? $supplier_time : null;
            $column_value = '<input data-id="' . $this->order['id'] . '" class="supplier_toggle" type="checkbox" ' . $column_value . ' data-toggle="toggle" data-onstyle="success" data-size="mini" style="display:none"> <span class ="label label-info label-xsm" style="font-size: 90%">' . $supplier_time . '</span><br><span class ="label label-info label-sm" style="font-size: 90%">' . $this->order['supplier_name'] . '</span>';
            //  $column_value = date('Y-m-d', strtotime($column_value));
        }
        if ($column_index == $amount_col) {
            $column_value = number_format((float)$column_value, 2, ',', '.');
        }

        if ($column_index == $insert_type_col) {
            $column_value = getInsertTypeName($column_value);
        }
        if ($column_index == $tax_col) {
            if ($this->order['order_data']->insert_type == 3) {
                $column_value = DB::table('billing_details')->select('billing_details.vat_id as user_vat_id')->where('billing_details.user_id', $this->order['order_data']->cms_client)->first()->user_vat_id;
            }
        }
    }

    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
    public function hook_after_add($id)
    {
        $this->ordersCacheClear();
    }

    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
    public function hook_after_edit($id)
    {
        $this->ordersCacheClear();
    }

    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    public function getStatusTest($id)
    {
        if (userTestOrderCount() >= 5) CRUDBooster::redirectBack('Test Order limit finished!', 'warning');
        $order = NewOrder::where('id', $id)->where('cms_user_id', CRUDBooster::myId())->first();
        if ($order && ($order->test_order != 1)) {
            if ($order->update(['test_order' => 1])) {
                Cache::forget('test_order_count_' . CRUDBooster::myId());
                $this->ordersCacheClear();
                CRUDBooster::redirectBack('Status update', 'success');
            }
        }
        CRUDBooster::redirectBack('Something went wrong', 'error');
    }

    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
    public function hook_after_delete($id)
    {
        $this->ordersCacheClear();
    }

    private function ordersCacheClear($user_id = null)
    {
        $userId = $user_id ? $user_id : CRUDBooster::myParentId();
        Cache::forget('order_statt_' . $userId);
        Cache::forget('best_8_products_' . $userId);
    }

    private function updateInvoiceProduct($cart, $user_id = null)
    {
        if (!is_null($cart)) {
            InvoiceProduct::updateOrCreate(
                [
                    'name' => $cart['product_name'],
                    'cms_user_id' => $user_id ?? CRUDBooster::myId()
                ],
                [
                    'rate' => removeCommaPrice($cart['rate']),
                    'description' => $cart['description'],
                    'unit' => $cart['unit'],
                    'tax' => $cart['tax'],
                ]
            );
        }
    }

    public function postInvoiceProduct()
    {
        $request = $_REQUEST;
        $output = [];
        try {
            if (isset($request['search']) && $request['search']) {
                $product_find = $request['search'];
                $products = [];
                $products['manual'] = InvoiceProduct::where('name', 'like', '%' . $product_find . '%')->where('cms_user_id', CRUDBooster::myId())->select('name', 'id', 'description', 'tax', 'rate', 'unit')->get()->toArray();

                $languages = ['_de', '_en', '_es', '_fr', '_it', '_nl', '_pl', '_ru', '_sv'];
                foreach ($languages as $lang) {
                    $table = 'drm_translation' . $lang;
                    $value = $request['value'];
                    $products[$table] = DB::table($table)->join('drm_products', 'drm_products.id', '=', $table . '.product_id')->select($table . '.title as name', $table . '.id', $table . '.description', 'drm_products.image', 'drm_products.vk_price as rate')->whereNull('drm_products.deleted_at')->whereNotNull($table . '.title')->where('drm_products.user_id', CRUDBooster::myId())->where(function ($q) use ($product_find, $table) {
                        $q->where($table . '.ean', 'LIKE', '%' . $product_find . '%')->orWhere($table . '.title', 'LIKE', '%' . $product_find . '%');
                    })->get()->toArray();
                }

                $item_array = collect($products)->filter()->all();
                if ($item_array) {
                    foreach ($item_array as $key => $items) {
                        foreach ($items as $p) {
                            $p = (array)$p;
                            $image = null;
                            if (isset($p['image']) && !is_null($p['image'])) {
                                $img_data = $p['image'];
                                $image = (drmIsJSON($img_data)) ? reset(json_decode($img_data, true))['src'] : $img_data;
                            }
                            $i = [];
                            $i['name'] = $p['name'];
                            $i['id'] = $p['id'];
                            $i['description'] = $p['description'];
                            $i['tax'] = $p['tax'] ?? null;
                            $i['rate'] = $p['rate'] ?? null;
                            $i['unit'] = $p['unit'] ?? null;
                            $i['image'] = $image;
                            $i['table'] = $key;
                            $output[] = $i;
                        }
                    }
                }
                $output = collect($output)->take(10);
            }
        } catch (Exception $e) {
            return $e->getMessage();
        }
        return response()->json($output);
    }

    public function getIndex()
    {
        $this->cbLoader();

        $module = CRUDBooster::getCurrentModule();

        if (!CRUDBooster::isView() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        if (Request::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
            if (Request::get('foreign_key')) {
                $data['parent_field'] = Request::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($parent_field) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $parent_field) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        $data['page_title'] = $module->name;
        $data['page_description'] = trans('crudbooster.default_module_description');
        $data['date_candidate'] = $this->date_candidate;
        $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::table($this->table)->select(DB::raw($this->table . "." . $this->primary_key), DB::raw('cast(order_date as datetime) as order_datetime'));

        if (Request::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . Request::get('foreign_key'), Request::get('parent_id'));
        }

        $this->hook_query_index($result);

        if (in_array('deleted_at', $table_columns)) {
            $result->where($this->table . '.deleted_at', null);
        }

        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;
        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {

                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if (Request::get('q')) {
            $result->where(function ($w) use ($columns_table, $request) {
                foreach ($columns_table as $col) {
                    if (!$col['field_with']) {
                        continue;
                    }
                    if ($col['is_subquery']) {
                        continue;
                    }
                    $w->orwhere($col['field_with'], "like", "%" . Request::get("q") . "%");
                }
            });
        }

        if (Request::get('where')) {
            foreach (Request::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }

        $filter_is_orderby = false;
        if (Request::get('filter_column')) {

            $filter_column = Request::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {
                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];

                if ($sorting != '') {
                    if ($key) {
                        $key = ($key == 'new_orders.order_date') ? 'order_datetime' : $key;
                        if ($key == 'new_orders.invoice_number') {
                            $result->orderByRaw('CAST(invoice_number AS SIGNED) ' . $sorting);
                        } else {
                            $result->orderby($key, $sorting);
                        }
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            $data['result'] = $result->orderByRaw('CAST(invoice_number AS SIGNED) desc')->orderBy('order_datetime', 'desc')->paginate($limit);
            // if ($this->orderby) {
            //     if (is_array($this->orderby)) {
            //         foreach ($this->orderby as $k => $v) {
            //             if (strpos($k, '.') !== false) {
            //                 $orderby_table = explode(".", $k)[0];
            //                 $k = explode(".", $k)[1];
            //             } else {
            //                 $orderby_table = $this->table;
            //             }
            //             $result->orderby($orderby_table.'.'.$k, $v);
            //         }
            //     } else {
            //         $this->orderby = explode(";", $this->orderby);
            //         foreach ($this->orderby as $o) {
            //             $o = explode(",", $o);
            //             $k = $o[0];
            //             $v = $o[1];
            //             if (strpos($k, '.') !== false) {
            //                 $orderby_table = explode(".", $k)[0];
            //             } else {
            //                 $orderby_table = $this->table;
            //             }
            //             $result->orderby($orderby_table.'.'.$k, $v);
            //         }
            //     }
            //     $data['result'] = $result->paginate($limit);
            // } else {
            //     $data['result'] = $result->orderby($this->table.'.'.$this->primary_key, 'desc')->paginate($limit);
            // }
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];

        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(Request::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (Request::get('page')) ? Request::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            if ($this->button_bulk_action) {

                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];

                if (isset($col['image'])) {
                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('vendor/crudbooster/avatar.jpg') . "'><img width='40px' height='40px' src='" . asset('vendor/crudbooster/avatar.jpg') . "'/></a>";
                    } else {
                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='" . $pic . "'><img width='40px' height='40px' src='" . $pic . "'/></a>";
                    }
                }

                if (@$col['download']) {
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action) :

                $button_action_style = $this->button_action_style;
                $html_content[] = "<div class='button_action' style='text-align:right'>" . view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render() . "</div>";

            endif; //button_table_action

            foreach ($html_content as $i => $v) {
                $this->hook_row_index($i, $v);
                $html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

        $data['html_contents'] = $html_contents;

        return view("admin.new_order.index", $data);
    }

    public function getDetail($id = null)
    {
        //Create an Auth

        if (!CRUDBooster::isRead() && $this->global_privilege == FALSE || $this->button_edit == FALSE || !$id) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = 'Invoice Details';
        $data['order'] = DB::table('new_orders')->where('id', $id)->first();

        if (!$data['order']->id) {
            return CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        return $this->generate_invoice_pdf($id);
    }

    public function getOrderProducts($order_id)
    {
        $data['products'] = $data['product_list'] = json_decode(DB::table('new_orders')->find($order_id)->cart);
        return response()->json($data);
    }

    public function postToggleSupplier()
    {
        // return $_REQUEST['id'];
        return DB::table('new_orders')->where('id', $_REQUEST['id'])->update(['supplier' => $_REQUEST['status']]);
    }


    public function getAdd()
    {


        // $date = new DateTime('2000-01-01');
        // $date->add(new DateInterval('PT'.date("H\Hi\Ms\S")));
        // echo $date->format('Y-m-d H:i:s') . "\n";
        // dd("");

        //Create an Auth
        if (!CRUDBooster::isCreate() && $this->global_privilege == FALSE || $this->button_add == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = 'Add Order';

        // $data['columns'] = DB::getSchemaBuilder()->getColumnListing('drm_products');
        // $columns = DB::getSchemaBuilder()->getColumnListing('drm_products');

        // dd($columns) ;

        $data['columns'] = [
            "ean",
            "name",
            // "id",

            // "drm_import_id",
            // "delivery_company_id",
            // "country_id",
            // "language_id",
            // "item_number",
            // "description",
            // "image",
            // "ek_price",
            // "vk_price",
            // "vat",
            // "stock",
            // "category",
            // "item_weight",
            // "update_enabled",
        ];

        if (CRUDBooster::isSuperadmin()) {
            $customer_list = DB::table('new_customers')->orderBy('full_name')->orderBy('email')->get();
            $products_list = DB::table('drm_products')->select('id', 'drm_products.name', 'description')->whereNotNull('vk_price')->limit(150)->get();
        } else {
            $customer_list = DB::table('new_customers')->where('user_id', CRUDBooster::myId())->orderBy('full_name')->orderBy('email')->get();

            $products_list = DB::table('drm_products')
                ->join('drm_imports', 'drm_products.drm_import_id', '=', 'drm_imports.id')
                ->select('drm_products.id', 'drm_products.name', 'drm_products.description')
                ->where('drm_imports.user_id', CRUDBooster::myId())->whereNotNull('vk_price')->limit(150)->get();
        }

        // dd($products_list);

        /* --------------- invoice number --------------------- */

        $inv1 = DB::table('new_orders')->where('cms_user_id', CRUDBooster::myId())->where('invoice_number', '!=', -1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;

        $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id', CRUDBooster::myId())->first()->start_invoice_number;

        // dd($inv1,$inv2);
        $invoice_number = ($inv1 > $inv2) ? $inv1 : $inv2;
        // dd($inv1 , $inv2);

        $data['languages'] = DB::table('countries')->where('is_active', 1)->orderBy('language_shortcode')->get();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();
        $data['users'] = DB::table('cms_users')->orderBy('name')->get();

        $data['customer_list'] = $customer_list;
        $data['products_list'] = $products_list;
        $data['invoice_number'] = $invoice_number;

        return view('admin.new_order.add', $data);
    }

    public function postAddSave()
    {
        // dd($_REQUEST);

        $validator = Validator::make($_REQUEST, [
            'drm_customer_id' => 'required',
            // 'invoice_number' => 'required',
            // 'invoice_date' => 'required|date',
            // 'due_date' => 'required|date',
            // 'payment_methods' => 'required|string',
            // 'currency' => 'required|string',
            // 'shipping' => 'required|string',
            // 'billing' => 'required|string',
            // 'customer_info' => 'required|string',
            // 'product_id' => 'required|array|min:1',

            // 'description' => 'required|array|min:1',
            // 'description.*' => 'required|string',

            'product_name' => 'required|array|min:1',
            'qty' => 'required|array|min:1',
            'qty.*' => 'required|numeric',

            'rate' => 'required|array|min:1',
            'rate.*' => 'required|numeric',

            'tax' => 'required|array|min:1',
            'tax.*' => 'required|numeric',

            // 'discount' => 'required|numeric',
            // 'discount_type' => 'required|string',
            // 'adjustment' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
            // return 0;
        }

        $customer_id = $_REQUEST['drm_customer_id'];
        $customer_info = [
            'customer_full_name' => $_REQUEST['customer_full_name'],
            'company_name' => $_REQUEST['customer_company_name'],
            'city' => $_REQUEST['customer_city'],
            'zip_code' => $_REQUEST['customer_zip_code'],
            'state' => $_REQUEST['customer_state'],
            'country' => $_REQUEST['customer_country'],
            'address' => $_REQUEST['customer_address'],
            'insert_type' => 6,

            // shipping
            'street_shipping' => $_REQUEST['shipping_street'],
            'city_shipping' => $_REQUEST['shipping_city'],
            'state_shipping' => $_REQUEST['shipping_state'],
            'zipcode_shipping' => $_REQUEST['shipping_zip_code'],
            'country_shipping' => $_REQUEST['shipping_country'],

            //billing
            'street_billing' => $_REQUEST['billing_street'],
            'city_billing' => $_REQUEST['billing_city'],
            'state_billing' => $_REQUEST['billing_state'],
            'zipcode_billing' => $_REQUEST['billing_zip_code'],
            'country_billing' => $_REQUEST['billing_country'],
        ];


        // $customer = DB::table('new_customers')->find($_REQUEST['drm_customer_id']);
        $_REQUEST['insert_type'] = 6;
        $_REQUEST['user_id'] = CRUDBooster::myId();

        // date seconds fixing
        $date1 = new DateTime($_REQUEST['order_date']);
        $date1->add(new DateInterval('PT' . date("H\Hi\Ms\S")));
        $_REQUEST['order_date'] = $date1->format('Y-m-d H:i:s');

        //customer info
        $_REQUEST['customer_info'] = customerInfoJson($customer_info);

        //billing
        $_REQUEST['billing'] = billingInfoJson($customer_info);

        //shipping
        $_REQUEST['shipping'] = shippingInfoJson($customer_info);

        if (isset($_REQUEST['is_same_address'])) {
            $customer_info['is_same_address'] = true;
            $_REQUEST['billing'] = $_REQUEST['shipping'];
        }

        app('App\Http\Controllers\AdminDrmAllCustomersController')->update_customer($customer_info, $customer_id);

        $carts = [];
        foreach ((array)$_REQUEST['product_name'] as $key => $product) {
            $cart_item = [];
            $cart_item['id'] = $key;
            $cart_item['product_name'] = strip_tags(preg_replace_array('/"/', [' '], $product));
            $cart_item['description'] = strip_tags(preg_replace_array('/"/', [' '], $_REQUEST['description'][$key]));
            $cart_item['qty'] = $_REQUEST['qty'][$key];
            $cart_item['rate'] = removeCommaPrice($_REQUEST['rate'][$key]);
            $cart_item['tax'] = $_REQUEST['tax'][$key];
            $cart_item['image'] = null;
            $cart_item['product_discount'] = $_REQUEST['product_discount'][$key] ?? 0;
            $cart_item['amount'] = $_REQUEST['amount'][$key];
            $carts[] = $cart_item;

            $this->updateInvoiceProduct($cart_item);
        }
        $_REQUEST['cart'] = json_encode($carts);

        $order = $this->insert_order($_REQUEST);
        if ($order == null || $order == [] || $order->id == null) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Please enter input correctly'), 'error');
        }

        // return redirect('admin/drm_invoice')->with('success','Invoice added');
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), trans('Order added'), 'success');
    }

    //Add order
    public function add_order($order_info)
    {
        $validator = Validator::make($order_info, [
            'user_id' => 'required',
        ]);
        if ($validator->fails()) {
            return null;
        }

        /* ----------------- End Calculation ------------------ */
        $check['cms_user_id'] = $order_info['user_id'];
        // $check['drm_customer_id']   = $order_info['drm_customer_id'];
        $check['order_date'] = $order_info['order_date'];
        $check['insert_type'] = $order_info['insert_type'];
        $check['shop_id'] = $order_info['shop_id'];
        $check['order_id_api'] = $order_info['order_id_api'];

        /* --------------- invoice number --------------------- */
        if (DB::table('new_orders')->where($check)->count() > 1) return false;

        $order_inv = DB::table('new_orders')->where($check)->first();


        if (!($order_inv == null || $order_inv == [] || $order_inv->id == null)) {
            return false;
            $invoice_number = $order_inv->invoice_number;
        }

        // Invoice number generator start
        $inv_setting = DB::table('drm_invoice_setting')->where('cms_user_id', $check['cms_user_id'])->orderBy('id', 'desc')->select('start_from_1', 'start_invoice_number', 'suffix')->first();
        $start_from_1 = (bool)$inv_setting->start_from_1;
        $inv_suffix = $inv_setting->suffix;

        $last_order_item = DB::table('new_orders')->where('cms_user_id', $check['cms_user_id'])->where('invoice_number', '!=', -1)->where('credit_number', 0);

        if ($start_from_1) {
            $last_order_item->whereYear('created_at', date('Y'));
        }

        $inv1 = $last_order_item->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
        $inv2 = $inv_setting->start_invoice_number;
        $invoice_number = ($start_from_1) ? $inv1 : (($inv1 > $inv2) ? $inv1 : $inv2);


        //Extra layer of duplication check
        $inv_used_count = DB::table('new_orders')->where(['cms_user_id' => $cms_user_id, 'invoice_number' => $invoice_number])->where('credit_number', 0);
        if ($start_from_1) {
            $inv_used_count->whereYear('created_at', date('Y'));
        }
        $inv_used_count = $inv_used_count->count();

        if ($inv_used_count > 0) {
            User::find(71)->notify(new DRMNotification('DUPLICATE INV NUBMER TRY: ' . inv_number_string($invoice_number, $inv_suffix) . ' USER ID: ' . $cms_user_id . ' SHOP_ID: ' . $shop_id . ' API_ID: ' . $order_id_api, 'DUPLICATE_INV_TRY'));
            return false;
        }
        //Invoice number generator end


        /* -------------- future invoice ------------------- */
        $status = $order_info['status'] ?? "nicht_bezahlt";

        if ($order_info['invoice_date'] != "" || $order_info['invoice_date'] != null) {
            $now = new DateTime();
            $due = new DateTime($order_info['invoice_date']);

            if ($due > $now) {
                $invoice_number = -1;
            }
        }

        /* ------------------ insert order ----------------- */
        $row['invoice_number'] = $invoice_number;
        $row['invoice_date'] = $order_info['invoice_date'];

        if (strpos($order_info['total'], ",")) {
            $have = [".", ","];
            $will_be = ["", "."];
            $order_info['total'] = str_replace($have, $will_be, $order_info['total']);
        }
        $row['total'] = $order_info['total'];
        $row['sub_total'] = $order_info['sub_total'];
        $row['total_tax'] = $order_info['total_tax'];

        $row['eur_total'] = isset($order_info['eur_total']) ? $order_info['eur_total'] : $order_info['total'];

        $row['drm_customer_id'] = $order_info['drm_customer_id'];

        $row['discount'] = $order_info['discount'];
        $row['discount_type'] = $order_info['discount_type'];
        $row['adjustment'] = $order_info['adjustment'];
        $row['payment_type'] = $order_info['payment_type'];
        $row['currency'] = $order_info['currency'];
        $row['shipping_cost'] = $order_info['shipping_cost'];
        $row['customer_info'] = $order_info['customer_info'];
        $row['billing'] = $order_info['billing'];
        $row['shipping'] = $order_info['shipping'];
        $row['client_note'] = $order_info['client_note'];
        $row['status'] = ucfirst($status);
        $row['cms_client'] = $order_info['cms_client'];
        $row['cart'] = $order_info['cart'];
        $row['inv_pattern'] = drm_invoice_number_format($invoice_number, $inv_suffix);

        if (isset($order_info['insert_type']) && ($order_info['insert_type'] == 3)) {
            $row['customer_info'] = $row['shipping'] = $row['billing'];
        }

        if (isset($order_info['dropmatix_sub_total'])) {
            $row['dropmatix_sub_total'] = $order_info['dropmatix_sub_total'];
            $row['dropmatix_total_tax'] = $order_info['dropmatix_total_tax'];
            $row['dropmatix_discount'] = $order_info['dropmatix_discount'];
            $row['dropmatix_shipping_cost'] = $order_info['dropmatix_shipping_cost'];
            $row['dropmatix_tax_rate'] = $order_info['dropmatix_tax_rate'];
        }

        $order = NewOrder::updateOrCreate($check, $row);

        if (DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'])->first()->auto_mail) {
            app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
        }

        return $order;
    }

    //Insert manual order
    public function insert_order($order_info)
    {
        // //DB::beginTransaction();
        try {

            $validator = Validator::make($order_info, [
                'user_id' => 'required',
            ]);
            if ($validator->fails()) {
                throw new Exception("User empty!");
            }

            $row['cms_user_id'] = $check['cms_user_id'] = $order_info['user_id'];
            $row['order_date'] = $check['order_date'] = $order_info['order_date'];
            $row['insert_type'] = $check['insert_type'] = $order_info['insert_type'];
            $row['shop_id'] = $check['shop_id'] = $order_info['shop_id'];

            /* --------------- invoice number --------------------- */
            $inv1 = DB::table('new_orders')->where('cms_user_id', $check['cms_user_id'])->where('invoice_number', '!=', -1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
            $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id', $check['cms_user_id'])->orderBy('id', 'desc')->first()->start_invoice_number;

            $invoice_number = ($inv1 > $inv2) ? $inv1 : $inv2;

            /* -------------- future invoice ------------------- */
            $status = "Shipped";
            if ($order_info['invoice_date'] != "" || $order_info['invoice_date'] != null) {
                $now = new DateTime();
                $due = new DateTime($order_info['invoice_date']);

                if ($due > $now) {
                    $status = "In Progress";
                    $invoice_number = -1;
                }
            }

            /* ------------------ insert order ----------------- */
            $row['invoice_number'] = $invoice_number;
            $row['invoice_date'] = $order_info['invoice_date'];

            if (strpos($order_info['total'], ",")) {
                $have = [".", ","];
                $will_be = ["", "."];
                $order_info['total'] = str_replace($have, $will_be, $order_info['total']);
            }

            $row['total'] = $order_info['total'];
            $row['sub_total'] = $order_info['sub_total'];
            $row['total_tax'] = $order_info['total_tax'];

            $row['drm_customer_id'] = $order_info['drm_customer_id'];

            $row['discount'] = $order_info['discount'];
            $row['discount_type'] = $order_info['discount_type'];
            $row['adjustment'] = $order_info['adjustment'];
            $row['payment_type'] = $order_info['payment_type'];
            $row['currency'] = $order_info['currency'];
            $row['shipping_cost'] = $order_info['shipping_cost'];
            $row['customer_info'] = $order_info['customer_info'];
            $row['billing'] = $order_info['billing'];
            $row['shipping'] = $order_info['shipping'];
            $row['client_note'] = $order_info['client_note'];
            $row['status'] = ucfirst($order_info['status'] ?? $status);
            $row['cms_client'] = $order_info['cms_client'];
            $row['cart'] = $order_info['cart'];
            $row['char_status'] = $order_info['char_status'] ?? 0;

            $row['order_id_api'] = $order_info['order_id_api'] ?? null;
            $row['remainder_date'] = $order_info['remainder_date'] ?? null;
            $row['eur_total'] = $order_info['eur_total'] ?? 0;
            $row['dropmatix_sub_total'] = $order_info['dropmatix_sub_total'] ?? 0;

            // Store VAT number
            $row['vat_number'] = $order_info['vat_number'] ?? null;

            if(isset($order_info['dropmatix_total_tax']))
            {
              $row['dropmatix_total_tax'] = $order_info['dropmatix_total_tax'] ?? 0;
            }

            if(isset($order_info['dropmatix_tax_rate']))
            {
              $row['dropmatix_tax_rate'] = $order_info['dropmatix_tax_rate'] ?? 0;
            }

            if(!empty($row['mp_payment_agreement_at']))
            {
              $row['mp_payment_agreement_at'] = $row['mp_payment_agreement_at'];
            }

            $row['marketplace_order_ref'] = $order_info['marketplace_order_ref'] ?? null;

            $order = NewOrder::create($row);

            //Auto email
            if (DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'])->first()->auto_mail) {
                app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
            }

            // If KlickTpp is active tags will be inserted
            // $purchase_check  = AppStore::CheckAppPurchaseBoolean('40');

            // if($purchase_check[0]){
            $decodedCartTag = json_decode($order_info['cart'], true);


            foreach ($decodedCartTag as $productNameasTag) {

                $tag_label = strip_tags(preg_replace_array('/"/', [' '], $productNameasTag['product_name']));
                $productNameAsTag = ltrim($tag_label, '"'); // removing double quote from product name

                // If product name already exist as tag ignore
                $productNameExistOrNot = CustomerTag::where('customer_id', $order_info['drm_customer_id'])
                    ->where('user_id', $check['cms_user_id'])
                    ->whereRaw('JSON_CONTAINS(tags, \'{"label": "' . $productNameAsTag . '"}\')')
                    ->first();

                $tagsArray = array();

                //push data to array
                if (!isset($productNameExistOrNot->id)) {
                    array_push($tagsArray, ['id' => '', 'label' => $productNameAsTag]);
                }
            }

            //get shop name
            $shopNameTag = \App\Shop::where('id', $check['shop_id'])
                ->first();

            // If order channel already exist as tag ignore
            $channelExistOrNot = CustomerTag::where('customer_id', $order_info['drm_customer_id'])
                ->where('user_id', $check['cms_user_id'])
                ->whereRaw('JSON_CONTAINS(tags, \'{"label": "' . $shopNameTag->shop_name . '"}\')')
                ->first();


            if (!isset($channelExistOrNot->id)) {
                array_push($tagsArray, ['id' => '', 'label' => $shopNameTag->shop_name]);
            }
            // do not continue if tags array is empty
            if (isset($tagsArray[0])) {

                // encode to json
                $tag_json = json_encode($tagsArray, true);

                // Insert Tags
                CustomerTag::insert([
                    'customer_id' => $order_info['drm_customer_id'],
                    'user_id' => $check['cms_user_id'],
                    'tags' => $tag_json,
                    'type' => 'manual_tags',
                    'input_type' => 'Manual'
                ]);
            }

            // }
            // KlickTipp tag Insertion code end

            // //DB::commit();
            return $order;
        } catch (Exception $e) {
            // //DB::rollBack();
            return null;
        }
    }


    // --------------------- Edit -------------------------------

    public function getEdit($id)
    {
        if (!CRUDBooster::isUpdate() && $this->global_privilege == FALSE || $this->button_edit == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = 'Edit Data';

        $data['columns'] = [
            "ean",
            "name",
            // "id",

            // "drm_import_id",
            // "delivery_company_id",
            // "country_id",
            // "language_id",
            // "item_number",
            // "description",
            // "image",
            // "ek_price",
            // "vk_price",
            // "vat",
            // "stock",
            // "category",
            // "item_weight",
            // "update_enabled",
        ];

        // dd();
        if (CRUDBooster::isSuperadmin()) {
            $customer_list = DB::table('new_customers')->orderBy('full_name')->orderBy('email')->get();
            // $products_list = DB::table('drm_products')->select('id','drm_products.name','description')->whereNotNull('vk_price')->limit(150)->get();
        } else {
            $customer_list = DB::table('new_customers')->where('user_id', CRUDBooster::myId())->orderBy('full_name')->orderBy('email')->get();

            // $products_list = DB::table('drm_products')
            // ->join('drm_imports', 'drm_products.drm_import_id', '=', 'drm_imports.id')
            // ->select('drm_products.id','drm_products.name','drm_products.description')
            // ->where('drm_imports.user_id', CRUDBooster::myId())->whereNotNull('vk_price')->limit(150)->get();

        }

        $data['customer_list'] = $customer_list;
        // $data['products_list'] = $products_list;

        $data['order'] = DB::table('new_orders')->find($id);

        $customer_info = json_decode($data['order']->customer_info);
        $billing = json_decode($data['order']->billing);
        $shipping = json_decode($data['order']->shipping);

        $shipping_default = [
            'name' => null,
            'company' => null,
            'street' => null,
            'zip_code' => null,
            'city' => null,
            // 'state' => null,
            'country' => null,
        ];
        $customer_info_default = [
            'name' => null,
            'company' => null,
            'address' => null,
            'zip_code' => null,
            'city' => null,
            // 'state' => null,
            'country' => null,
        ];

        if ($billing) {
            unset($billing->address);
            unset($billing->state);
        }
        if ($shipping) {
            unset($shipping->address); //remove extra field
            unset($shipping->state); //remove extra field
        }
        if ($customer_info) {
            unset($customer_info->state); //remove extra field
        }

        $data['customer_info'] = $customer_info ?? $customer_info_default;
        $data['billing'] = $billing ?? $shipping_default;
        $data['shipping'] = $shipping ?? $shipping_default;
        $data['languages'] = DB::table('countries')->where('is_active', 1)->orderBy('language_shortcode')->get();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();

        $data['products'] = json_decode($data['order']->cart);
        return view("admin.new_order.edit", $data);
    }

    // --------------------- Edit Save --------------------------

    public function postEditSave($id)
    {
        return;

        // dd($_REQUEST);

        $validator = Validator::make($_REQUEST, [
            // 'drm_customer_id' => 'required', // hide now
            // 'invoice_number' => 'required',
            // 'invoice_date' => 'required|date',
            // 'due_date' => 'required|date',
            // 'payment_methods' => 'required|string',

            'currency' => 'required|string',
            // 'customer_info' => 'required|string',
            // 'billing' => 'required|string',

            // 'product_id' => 'required|array|min:1',
            'product_name' => 'required|array|min:1',

            // 'description' => 'required|array|min:1',
            // 'description.*' => 'required|string',

            'qty' => 'array|min:1',
            'qty.*' => 'numeric',

            'rate' => 'array|min:1',
            'rate.*' => 'numeric',

            'tax' => 'array|min:1',
            'tax.*' => 'numeric',

            // 'discount' => 'numeric',
            // 'discount_type' => 'required|string',
            // 'adjustment' => 'numeric',
        ]);

        if ($validator->fails()) {
            return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
            // return 0;
        }

        $order_info = $_REQUEST;

        $customer_info = [
            'customer_full_name' => $_REQUEST['customer_name'],
            'company_name' => $_REQUEST['customer_company'],
            'city' => $_REQUEST['customer_city'],
            'zip_code' => $_REQUEST['customer_zip_code'],
            'state' => $_REQUEST['customer_state'],
            'country' => $_REQUEST['customer_country'],
            'address' => $_REQUEST['customer_address'],
            // 'insert_type' => 6,

            // shipping
            'shipping_name' => $_REQUEST['shipping_name'],
            'shipping_company' => $_REQUEST['shipping_company'],
            'street_shipping' => $_REQUEST['shipping_street'],
            'city_shipping' => $_REQUEST['shipping_city'],
            'state_shipping' => $_REQUEST['shipping_state'],
            'zipcode_shipping' => $_REQUEST['shipping_zip_code'],
            'country_shipping' => $_REQUEST['shipping_country'],

            //billing
            'billing_name' => $_REQUEST['billing_name'],
            'billing_company' => $_REQUEST['billing_company'],
            'street_billing' => $_REQUEST['billing_street'],
            'city_billing' => $_REQUEST['billing_city'],
            'state_billing' => $_REQUEST['billing_state'],
            'zipcode_billing' => $_REQUEST['billing_zip_code'],
            'country_billing' => $_REQUEST['billing_country'],
        ];

        /* ----------------- End Calculation ------------------ */
        $order = DB::table('new_orders')->where('id', $id)->first();

        /* -------------- future invoice ------------------- */
        $invoice_number = $order->invoice_number;
        $status = "Shipped";

        if ($order_info['invoice_date'] != "" || $order_info['invoice_date'] != null) {
            $now = new DateTime();
            $due = new DateTime($order_info['invoice_date']);

            if ($due > $now) {
                $status = "In Progress";
                $invoice_number = -1;
            }
        }

        $row['invoice_number'] = $invoice_number;
        $row['order_date'] = $order_info['order_date'];
        $row['invoice_date'] = $order_info['invoice_date'];

        $row['total'] = $order_info['total'];
        $row['sub_total'] = $order_info['sub_total'];
        $row['total_tax'] = $order_info['total_tax'];
        $row['shipping_cost'] = $order_info['shipping_cost'];

        $row['discount'] = $order_info['discount'];
        $row['discount_type'] = $order_info['discount_type'];
        $row['adjustment'] = $order_info['adjustment'];
        $row['payment_type'] = $order_info['payment_type'];
        $row['currency'] = $order_info['currency'];
        $row['customer_info'] = customerInfoJson($customer_info);
        $row['billing'] = billingInfoJson($customer_info);
        $row['shipping'] = shippingInfoJson($customer_info);
        $row['client_note'] = $order_info['client_note'];
        $row['status'] = ucfirst($order_info['status'] ?? $status);
        // $row['insert_type']     = 6;
        //Billing address update
        if (isset($_REQUEST['is_same_address'])) {
            $customer_info['is_same_address'] = true;
            $row['billing'] = $row['shipping'];
        }

        // ---------- updating order ----------
        $order = NewOrder::find($id);
        if ($order) {
            $carts = [];
            foreach ((array)$_REQUEST['product_name'] as $key => $product) {
                $cart_item = [];
                $cart_item['id'] = $key;
                $cart_item['product_name'] = strip_tags(preg_replace_array('/"/', [' '], $product));
                $cart_item['description'] = strip_tags(preg_replace_array('/"/', [' '], $_REQUEST['description'][$key]));
                $cart_item['qty'] = $_REQUEST['qty'][$key];
                $cart_item['rate'] = removeCommaPrice($_REQUEST['rate'][$key]);
                $cart_item['tax'] = $_REQUEST['tax'][$key];
                $cart_item['image'] = null;
                $cart_item['product_discount'] = $_REQUEST['product_discount'][$key] ?? 0;
                $cart_item['amount'] = $_REQUEST['amount'][$key];
                $carts[] = $cart_item;

                if ($order->insert_type == 6) {
                    $this->updateInvoiceProduct($cart_item);
                }
            }

            $row['cart'] = json_encode($carts);

            $order->update($row);
            $customer_id = $order->drm_customer_id;
            if ($customer_id) app('App\Http\Controllers\AdminDrmAllCustomersController')->update_customer($customer_info, $customer_id);
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), trans('Order Edited'), 'success');
        }
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), trans('Order Edited Failed'), 'error');
    }

    // --------------------- product-search-by-field --------------
    public function getProductSearchByField()
    {
        $request = $_REQUEST;
        $output = [];
        if (isset($request['col']) && isset($request['value'])) {
            try {
                $products = [];
                $languages = ['_de', '_en', '_es', '_fr', '_it', '_nl', '_pl', '_ru', '_sv'];
                foreach ($languages as $lang) {
                    $table = 'drm_translation' . $lang;

                    $col = $request['col'];
                    $col = ($col == 'name') ? 'title' : $col;
                    $col = $table . '.' . $col;

                    $value = $request['value'];
                    $products[$table] = DB::table($table)->join('drm_products', 'drm_products.id', '=', $table . '.product_id')->select($table . '.title as name', $table . '.id')->whereNull('drm_products.deleted_at')->whereNotNull($table . '.title')->where('drm_products.user_id', CRUDBooster::myId())->where(function ($q) use ($col, $value) {
                        $q->where($col, 'LIKE', '%' . $value . '%');
                    })->get()->toArray();
                }

                $item_array = collect($products)->filter()->all();
                if ($item_array) {
                    foreach ($item_array as $key => $items) {
                        foreach ($items as $p) {
                            $p = (array)$p;
                            $image = null;
                            if (isset($p['image']) && !is_null($p['image'])) {
                                $img_data = $p['image'];
                                $image = (drmIsJSON($img_data)) ? reset(json_decode($img_data, true))['src'] : $img_data;
                            }
                            $i = [];
                            $i['text'] = $p['name'];
                            $i['id'] = $key . '_' . $p['id'];
                            $output[] = $i;
                        }
                    }
                }
                $output = collect($output);
            } catch (Exception $e) {
            }
        }
        return response()->json($output);
    }

    // product details by translation table id
    public function getProductByTableId()
    {
        if (isset($_REQUEST['id'])) {
            $data = $_REQUEST['id'];
            try {
                $table = implode('_', explode('_', $data, -1));
                $id = substr($data, (strlen($table) + 1));
                return response()->json(DB::table($table)->join('drm_products', 'drm_products.id', '=', $table . '.product_id')->select($table . '.title as name', $table . '.id', $table . '.description', 'drm_products.image', 'drm_products.vk_price as rate')->where($table . '.id', $id)->where('drm_products.user_id', CRUDBooster::myId())->first());
            } catch (Exception $e) {
            }
        }
    }

    public function futureInoiceNumber()
    {
        $inv1 = DB::table('new_orders')->where('cms_user_id', CRUDBooster::myId())->where('invoice_number', '!=', -1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
        $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id', CRUDBooster::myId())->first()->start_invoice_number;

        // dd($inv1,$inv2);
        $invoice_number = ($inv1 > $inv2) ? $inv1 : $inv2;

        $f_invoice = DB::table('new_orders')->where('invoice_number', -1)->get();

        foreach ((object)$f_invoice as $item) {

            $now = new DateTime();
            $due = new DateTime($item->invoice_date);

            if ($due > $now) {
                continue;
            }

            DB::table('new_orders')->where('id', $item->id)->update([
                'invoice_number' => $invoice_number++,
                'status' => "shipped",
            ]);
        }
    }


    // customer invoice number
    // get-customer-invoice-number
    // ----------------------------also used for getting customer details
    public function getGetCustomerInvoiceNumber()
    {
        $customer_id = $_REQUEST['id'];
        $customer = DB::table('new_customers')->where('id', $customer_id)->first();
        $data['customer'] = $customer;

        $data['shipping'] = json_decode($customer->shipping);
        $data['billing'] = json_decode($customer->billing);
        $data['invoice_number'] = DB::table('drm_invoices')->where('user_id', CRUDBooster::myId())->count() + 1;
        return response()->json($data);
    }

    public function getShowInvoicePreview()
    {
        $order = $this->generate_fake_order();

        $data['page_title'] = 'Invoice Details';
        // $data['order'] = DB::table('new_orders')->inRandomOrder()->first();


        // $data['product_list'] = DB::table('drm_order_products')->where('drm_order_id',$data['order']->id)->get();


        // $pdf_path = 'storage/order_invoice/order'.$data['order']->id.'.pdf';


        \Storage::disk('public')->makeDirectory('order_invoice_new');
        // $order = NewOrder::find($order_id);
        $data['order'] = $order;
        $data['product_list'] = json_decode($order->cart);
        $data['customer'] = (object)$order->customer;
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', CRUDBooster::myId())->first();

        // $pdf_path = ($local)? 'storage/order_invoice_new/order'.$order->id.'.pdf' : 'order_invoice_new/order'.$order->id.'.pdf';

        $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.invoice.daily' : 'admin.invoice.general';

        //          if($local)
        //          {
        //              $pdf=PDF::loadView($pdf_view, $data)->setWarnings(false)->save($pdf_path);
        //              return $pdf_path;
        //          }

        //          $pdf=PDF::loadView($pdf_view, $data)->setWarnings(false)->stream();
        //          Storage::disk('spaces')->put($pdf_path, $pdf, 'public');

        //          if(Storage::disk('spaces')->exists($pdf_path)){
        // 	return Storage::disk('spaces')->url($pdf_path);
        // }
        //          return null;


        // return view($pdf_view, $data)->render();


        $pdf = PDF::loadView($pdf_view, $data)->setWarnings(false);
        return $pdf->stream('invoice.pdf');
    }

    public function getInvoiceSetting()
    {

        $data['page_title'] = "Invoice Setting";

        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', CRUDBooster::myId())->first();
        return view("admin.drm_order.invoice_setting", $data);
    }

    public function postSaveInvoiceSetting()
    {
        // dd($_REQUEST);
        // ffffffffffffffffffffffffffffffffffffffffffffffff

        // $temp = explode(".",$_FILES['shop_logo']['name']);

        $row = [];

        // if($_FILES['shop_logo']['name'] != null)
        // {
        //     $ext  = end(explode(".",$_FILES['shop_logo']['name']));
        //     $name = 'sl'.time().'.'.$ext;

        //     dd($name);
        //     Storage::disk('public')->put('shop_logo/'.$name, );

        //     if(! move_uploaded_file($_FILES['shop_logo']['tmp_name'], './storage/shop_logo/'.$name))
        //     {
        //         CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders/invoice-setting'), trans('Picture not uploaded'), 'error');
        //     }

        //     $row['logo'] = $name;
        // }

        if (request()->hasFile('shop_logo')) {
            $file = request()->file('shop_logo');
            $name = 'sl' . time() . '.' . $file->getClientOriginalExtension();
            if (Storage::disk('public')->put('shop_logo/' . $name, file_get_contents($file))) {
                $row['logo'] = $name;
            }
        } else if ($_REQUEST['is_delete']) {
            $row['logo'] = '';
        }
        // 'logo' => $name,
        $row += [
            'logo_position' => $_REQUEST['logo_position'],
            'head_text' => $_REQUEST['head_text'],
            'store_name' => $_REQUEST['store_name'],
            'email' => $_REQUEST['email'],
            'start_invoice_number' => $_REQUEST['start_invoice_number'],
            // 'currency' => $_REQUEST['currency'],
            'company_address' => $_REQUEST['company_address'],
            'color' => $_REQUEST['color'],
            // 'current_month' => $_REQUEST['current_month'],
            // 'url' => $_REQUEST['url'],
            // 'note' => $_REQUEST['note'],
            'small_business' => $_REQUEST['small_business'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'col1_text' => $_REQUEST['col1_text'],
            'col2_text' => $_REQUEST['col2_text'],
            'col3_text' => $_REQUEST['col3_text'],
            'col4_text' => $_REQUEST['col4_text'],
        ];

        DB::table('drm_invoice_setting')->updateOrInsert([
            'cms_user_id' => CRUDBooster::myId()
        ], $row);

        CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders/invoice-setting'), trans('Setting Changed'), 'success');
    }

    public function postSaveSmallBusiness()
    {
        // //DB::beginTransaction();
        try {
            $row = [];
            $row['small_business'] = $_REQUEST['small_business'];
            $data = DB::table('drm_invoice_setting')->updateOrInsert([
                'cms_user_id' => CRUDBooster::myId()
            ], $row);
            if (is_null($data)) throw new Exception("Something went wrong!");
            // //DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Update success!',
                'checkbox' => $row['small_business'],
            ]);
        } catch (Exception $e) {
            // //DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function generate_invoice_pdf($order_id, $local = false)
    {
        $data = [];
        $data['page_title'] = 'Invoice Details';
        // if($drm_order_new->insert_type == "Stripe" || $drm_order_new->insert_type == "Charge"){
        //     $data['order'] = DB::table('new_orders')
        //                 ->select('*','new_orders.id')
        //                 ->join('cms_users','new_orders.cms_client','=','cms_users.id')
        //                 ->where('new_orders.id',$order_id)
        //                 ->first();
        // }
        // elseif($drm_order_new->insert_type == "Stripe API"){
        //     $data['order'] = DB::table('new_orders')->where('id',$order_id)->first();
        // }
        // else{
        // $data['order'] = DB::table('new_orders')
        //                 ->select('*','new_orders.id', 'new_orders.status')
        //                 ->join('new_customers','new_orders.drm_customer_id','=','new_customers.id')
        //                 ->where('new_orders.id',$order_id)
        //                 ->first();
        // }
        \Storage::disk('public')->makeDirectory('order_invoice_new');
        $order = NewOrder::find($order_id);
        $data['order'] = $order;
        $data['product_list'] = json_decode($order->cart);
        $data['customer'] = $order->customer;
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->first();
        $pdf_path = ($local) ? 'storage/order_invoice_new/order' . $order->id . '.pdf' : 'order_invoice_new/order' . $order->id . '.pdf';

        $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.invoice.daily' : 'admin.invoice.general';
        $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

        // if(CRUDBooster::isSuperadmin()){
        // 	$customer = $data['customer'];
        // 	$setting = $data['setting'];
        // 	$order_date = strtotime($order->order_date);
        // 	$date = strtotime("07/01/2020");
        // 	if(($customer->country == "Deutschland" || $customer->country == "Germany" || $customer->country == "DE") && $order_date<$date){
        // 	  $tax_rate = 19;
        // 	}
        // 	else{
        // 	  $tax_rate = ( ($setting->small_business == 1) && (countryCodeTax($customer->country, true) == 4) )? 0 : countryCodeTax($customer->country);
        // 	}
        //
        // 	dd($order_date,$date,$tax_rate);
        // }
        if ($local) {

            // TODO:: DROPMATIX
            $pdf = (new \App\Services\Order\InvoiceDoc($order->id))->save($pdf_path);
            return $pdf_path;
        }

        // TODO:: DROPMATIX
        // $pdf = (new \App\Services\Order\InvoiceDoc($order->id))->save($pdf_path);
        // return $pdf_path;

        // TODO:: DROPMATIX
        return (new \App\Services\Order\InvoiceDoc($order->id))->stream();
    }


    /*
        ======================================================
        ===============Export order Invoice - Queue ==========
        ======================================================
        */
    public function postArchiveInvoice()
    {
        try {
            $user = User::find(CRUDBooster::myId());
            $order_ids = $_REQUEST["orders"];
            if ($user && $order_ids) {
                InvoiceArchiveJob::dispatch($order_ids, $user)->onQueue('long-running-queue');
                return response()->json([
                    'success' => true,
                    'message' => 'Archive processing! After completing the process, we sent you notification!',
                ]);
            } else {
                throw new Exception("Error Processing Request");
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Archive failed. Error: ' . $e->getMessage(),
            ]);
        }
    }

    public function archiveInvoices($order_ids, $user)
    {
        try {
            $zip = new ZipArchive(); // Load zip library

            $date = Carbon::now()->format('Y_m_d');
            $unix_time = Carbon::now()->unix();
            $filename = $date . "_" . $user->id . '_' . $unix_time;

            $zip_name = "storage\order_invoice_new\orders_" . $filename . ".zip"; // Zip name
            Storage::disk('public')->makeDirectory('order_invoice_new');
            $orders = NewOrder::whereIn('id', $order_ids)->where('is_locked', '<>', 1)->orderByRaw('CAST(invoice_number AS SIGNED) desc')->orderByRaw('CAST(order_date AS datetime) desc')->get();
            //where('invoice_number', '>', 0)->orderBy('invoice_number', 'desc')->find($order_ids);
            if (is_null($orders) || (!count($orders))) throw new Exception('Invoice selection invalid!');
            if ($orders && ($zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE))) {

                foreach ($orders as $order) {
                    $order_date = date('Y-m-d', strtotime($order->order_date));

                    $fil_name = "inv_" . $order_date . "_" . inv_number_string($order->invoice_number, $order->inv_pattern) . ".pdf";
                    // $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
                    // $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

                    // $data = [];

                    // if($order->marketplace_order_ref && $order->cms_user_id == 2455) {
                    //   $data['shipping_details'] = DB::table("new_orders")
                    //   ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                    //   ->select("new_orders.id as mp_order_id", "new_customers.*")
                    //   ->where('new_orders.id', $order->marketplace_order_ref)->first();
                    // }else if($order->credit_number && $order->cms_user_id == 2455) {
                    //     $marketplace_credit = DB::table('new_orders')->where('credit_ref', '=', $order->id)->value('marketplace_order_ref');

                    //     if($marketplace_credit->marketplace_order_ref)
                    //     {
                    //         $data['shipping_details'] = DB::table("new_orders")
                    //       ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                    //       ->select("new_orders.id as mp_order_id", "new_customers.*")
                    //       ->where('new_orders.id', $marketplace_credit->marketplace_order_ref)->first();
                    //     }
                    // }
                    //Marketplace credit end

                    //Credit
                    if ($order->credit_number > 0) {
                        $fil_name = "credit_" . $order_date . "_" . $order->credit_number . ".pdf";

                        // $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.credit_note.daily' : 'admin.credit_note.general';
                        // $pdf_view = ($order->insert_type == 4) ? 'admin.credit_note.charge_inv' : $pdf_view;

                        // $data['ref_invoice'] = null;

                        // $api_number = $order->order_id_api;
                        // if (strpos($api_number, 'inv_id_') !== false) {
                        //     $api_number = (int)str_replace('inv_id_', '', $api_number);
                        //     $data['ref_invoice'] = DB::table('new_orders')->find($api_number, ['invoice_number'])->invoice_number;
                        // }
                    } else if ($order->invoice_number == -1) {
                        $fil_name = "proforma_" . date('Y-m-d', strtotime($order->invoice_date)) . "_" . $order->id . ".pdf";
                    }

                    // $data['order'] = $order;
                    // $data['product_list'] = json_decode($order->cart);
                    // $data['customer'] = $order->customer;

                    // if($order->invoice_layout_id)
                    // {
                    //     $data['setting'] = DB::table('drm_invoice_setting')
                    //         ->where('cms_user_id', $order->cms_user_id)
                    //         ->where('id', $order->invoice_layout_id)
                    //         ->first();
                    // }else{
                    //     $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
                    // }

                    if (!empty($order->offer_number)) {

                        // if($order->offer_layout_id)
                        // {
                        //     $data['setting'] = DB::table('drm_offer_setting')
                        //         ->where('cms_user_id', $order->cms_user_id)
                        //         ->where('id', $order->offer_layout_id)
                        //         ->first();
                        // }else{
                        //     $data['setting'] = DB::table('drm_offer_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
                        // }

                        $fil_name = "offer_" . $order_date . "_" . $order->id . ".pdf";
                    }

                    // TODO:: DROPMATIX
                    $pdf = (new \App\Services\Order\InvoiceDoc($order->id));
                    $zip->addFromString($fil_name, $pdf->stream());
                }

                $zip->close();
                $invoice_file_path = 'archive/invoice_' . $filename;
                Storage::disk('spaces')->put($invoice_file_path . ".zip", file_get_contents(realpath($zip_name)), 'public');
                @unlink(realpath($zip_name));

                // $download_url = url('admin/drm_all_orders/download-archive-order/'.$invoice_file_path);
                $download_url = Storage::disk('spaces')->url($invoice_file_path . ".zip");
                $user->notify(new DRMNotification('Invoice Archived successfully! Download it.', 'BACKGROUND_ARCHIVE_JOB', $download_url));
            } else {
                throw new Exception('Invoice Archive failed. Please try again!');
            }
        } catch (Exception $e) {
            $user->notify(new DRMNotification('Invoice Archive Error: ' . $e->getMessage(), 'BACKGROUND_ARCHIVE_JOB_ERROR', '#'));
        }
    }

    public function getDownloadArchiveOrder($folder, $filename)
    {
        $url = Storage::disk('spaces')->url($folder . '/' . $filename . ".zip");
        header('Content-type: application/zip');
        header('Content-Disposition: attachment; filename="Orders.zip"');
        readfile($url);
        //remove zip file logic write hear..
    }

    // ------------- export-order-pdf ------------------
    public function postExportOrderPdf()
    {
        // fffffffffffffffffffffffffffffffffffffffff
        // return response()->json($_REQUEST);

        $zip = new ZipArchive(); // Load zip library

        $zip_name = "storage\order_invoice_new\Orders_pdf" . CRUDBooster::myId() . ".zip"; // Zip name

        if ($zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE)) {

            foreach ($_REQUEST["orders"] as $order_id) {
                // $pdf_path = 'storage/order_invoice_new/order'.$order_id.'.pdf';
                $order = NewOrder::find($order_id, ['order_date', 'invoice_number']);
                $order_date = date('Y-m-d', strtotime($order->order_date));
                $invoice_name = "inv_" . $order_date . "_" . inv_number_string($order->invoice_number, $order->inv_pattern) . ".pdf";

                // if(! $zip->addFile($pdf_path, $invoice_name))
                // {
                $pdf_path = $this->generate_invoice_pdf($order_id, true);
                $zip->addFile($pdf_path, $invoice_name);
                // }

                // unlink(realpath($pdf_path));
            }

            $zip->close();
            session(['order_pdf_url' => $zip_name]);
            return response()->json([
                'success' => true,
                'message' => 'Archived',
                'url' => $zip_name,
            ]);
        }
        return response()->json([
            'success' => false,
            'message' => 'Archive failed',
        ]);
    }

    public function getDownloadOrderPdf()
    {

        header('Content-type: application/zip');
        header('Content-Disposition: attachment; filename="Orders.zip"');
        readfile(realpath(session('order_pdf_url')));
        // unlink(realpath(session('order_pdf_url')));
    }


    // ------------------ generate delivery note pdf --------------

    public function generate_delivery_note($order_id)
    {
        $data['page_title'] = 'Delivery Note';
        $order = NewOrder::find($order_id);
        $data['order'] = $order;
        $data['product_list'] = json_decode($order->cart);
        $data['customer'] = $order->customer;
        $data['setting']  = $this->getDelveriNoteTemplate($order, $order->cms_user_id);

        if ($order->credit_number && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
            $order->marketplace_order_ref = DB::table('new_orders')->where('credit_ref', '=', $order->id)->value('marketplace_order_ref');
        }

        if ($order->marketplace_order_ref) {
            $data['shipping_details'] = DB::table("new_orders")
                ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                ->select("new_orders.id as mp_order_id", "new_customers.*")
                ->where('new_orders.id', $order->marketplace_order_ref)->first();
        }
        $pdf_path = 'storage/order_delivery_note/delivery_note' . $order->id . '.pdf';

        $pdf_view = 'admin.invoice.delivery_note';
        $pdf = PDF::loadView($pdf_view, $data)->setWarnings(false)->save($pdf_path);
        return $pdf_path;
    }


    // ---------------- download delivery note -----------
    public function getDeliveryNotes($order_id)
    {

        $pdf_path = $this->generate_delivery_note($order_id);

        header("Content-Type: application/octet-stream");
        header('Content-Disposition: attachment; filename="delivery_note' . $order_id . '.pdf"');
        readfile(realpath($pdf_path));
    }


    // --------------------- mass delivery note generate ---------------
    public function postExportDeleveryNotePdf()
    {
        // dddddddddddddddddddddddddddddddddddddd
        // return response()->json($_REQUEST);

        $zip = new ZipArchive(); // Load zip library

        $zip_name = "storage\order_delivery_note\delivery_notes_pdf" . CRUDBooster::myId() . ".zip"; // Zip name


        if ($zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE)) {

            foreach ($_REQUEST["orders"] as $order_id) {
                // $pdf_path = 'storage/order_delivery_note/delivery_note'.$order_id.'.pdf';

                // if(! $zip->addFile($pdf_path,"delivery_note".$order_id.".pdf"))
                // {
                $pdf_path = $this->generate_delivery_note($order_id);
                $zip->addFile($pdf_path, "delivery_note" . $order_id . ".pdf");
                // }
            }

            $zip->close();
            session(['delivery_note_pdf_url' => $zip_name]);
            return response()->json([
                'success' => true,
                'message' => 'Archived',
                'url' => $zip_name,
            ]);
        }
        return response()->json([
            'success' => false,
            'message' => 'Archive failed',
        ]);
    }


    public function getDownloadDeliveryNotePdf()
    {
        header('Content-type: application/zip');
        header('Content-Disposition: attachment; filename="DeliveryNotes.zip"');
        readfile(realpath(session('delivery_note_pdf_url')));
        // unlink(realpath(session('delivery_note_pdf_url')));
    }


    public function getTrash()
    {

        $data['orders'] = DB::table('new_orders')
            ->where('cms_user_id', CRUDBooster::myId())
            ->where('trash', 1)
            ->get();

        // dd($data['orders']);

        return view('admin.drm_order.trash', $data);
    }

    public function getSetTrash($id)
    {
        // dd($id);
        DB::table('new_orders')->where('id', $id)->update([
            'trash' => 1
        ]);

        CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), trans('Order sent to trash.'), 'success');
    }

    public function getRemoveTrash($id)
    {
        // dd($id);
        DB::table('new_orders')->where('id', $id)->update([
            'trash' => 0
        ]);

        CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), trans('Order sent to trash.'), 'success');
    }

    public function getSendEmail($id)
    {
        //Create an Auth
        if (!CRUDBooster::isRead() && $this->global_privilege == FALSE || $this->button_edit == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        if ($this->send_email($id)) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Sent'), 'success');
        } else {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!'), 'error');
        }
    }

    //Send offer remainder email
    public function offerRemainderEmail($order_id)
    {
        try {

            $is_charge_email = false;
            $pay_url = null;
            $paywall_id = null;

            $is_offer = false;

            $tags = $template = [];

            $data = [];
            $invoice_data = [];
            $order = NewOrder::find($order_id);
            if (empty($order)) return false;


            if( !(DB::table('drm_offer_remainder_mail')->where('cms_user_id', $order->cms_user_id)->where('auto_mail', 1)->exists()) ) return false;

            $invoice_data['page_title'] = 'Offer document';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;

            if ($order->offer_layout_id) {
                $invoice_data['setting'] = DB::table('drm_offer_setting')
                    ->where('cms_user_id', $order->cms_user_id)
                    ->where('id', $order->offer_layout_id)
                    ->first();
            } else {
                $invoice_data['setting'] = DB::table('drm_offer_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
            }

            if ($order->marketplace_order_ref && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                $invoice_data['shipping_details'] = DB::table("new_orders")
                    ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                    ->select("new_orders.id as mp_order_id", "new_customers.*")
                    ->where('new_orders.id', $order->marketplace_order_ref)->first();
            }

            $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);
            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';
            $logo = $invoice_data['setting']->logo ?? '';

            //Offer
            $is_offer = true;
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'offer_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'offer_number' => $order->offer_number,
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){

            if ($order->marketplace_order_ref) {
                $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
            } else {
                $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
            }

            $tags['shipping_address'] = $shipping;
            $tags['credit_note'] = $order->credit_number;

            $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $order->cms_user_id)->pluck('signature', 'id')->toArray();

            if ($email_signatures) {
                foreach ($email_signatures as $key => $signature) {
                    $tags['drm-sign-' . $key] = $signature;
                }
            }
            // }

            // if(isLocal() || in_array($order->cms_user_id, [212, 2592])){

            $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

            $channel_offer_remainder_mail = DB::table('drm_offer_remainder_mail_by_channels')
                ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                ->exists();

            if ($channel_offer_remainder_mail) {
                $template = DRMParseOfferEmailTemplate($tags, $order->cms_user_id, 'drm_offer_remainder_mail_by_channels', $channel);
            } else {
                $template = DRMParseOfferEmailTemplate($tags, $order->cms_user_id, 'drm_offer_remainder_mail');
            }

            // }else{
            // $template = DRMParseOfferEmailTemplate($tags, $order->cms_user_id, 'drm_offer_remainder_mail');
            // }

            $file_name = 'offer.pdf';

            $data['email_to'] = $email_to = $invoice_data['customer']->email;
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            // TODO:: DROPMATIX
            $pdf_stream = (new \App\Services\Order\InvoiceDoc($order->id))->output();

            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $file_name) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }

                $messages->subject($data['subject']);
                $messages->attachData($pdf_stream, $file_name, [
                    'mime' => 'application/pdf',
                ]);
            });

            $order->update(['offer_remainder' => null]); //offer remainder

            try {
                $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
                updateOrderHistory($order, 'send_offer_remainder_email', 'Offer remainder email sent to: ' . $email_to . '' . $is_bcc);
            } catch (Exception $ee) {
            }


            try {
                // Update deal tags
                $deal_id = DB::table('offer_deal')->where('offer_id', $order->id)->value('id');
                if (!empty($deal_id)) {
                    DropfunnelCustomerTag::insertTag('Offer Followed Up', $deal_id, $order->drm_customer_id, 27);
                    DB::table('offer_deal')->where('id', $deal_id)->where('stage_id', '<', 4)->update(['stage_id' => 4, 'updated_at' => now()]);
                    DB::table('deal_histories')->insert([
                        'deal_id' => $deal_id,
                        'message' => 'Offer remainder email sent.',
                        'stage' => 4,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            } catch (Exception $ee) {
            }

            return true;
        } catch (Exception $e) {
            User::find(71)->notify(new DRMNotification('Send customer offer error ' . $e->getMessage() . ' Line:' . $e->getLine(), '', '#'));
            return false;
        }
    }

    //Send invoice to customer email
    public function send_email($order_id, $channel = null)
    {
        $already_send = DB::connection('mysql::write')->table('new_orders')->where('id', $order_id)->whereNotNull('mail_sent')->count();
        if ($already_send > 0) return false;

        $already_send = DB::connection('mysql::write')->table('new_orders')->where('id', $order_id)->whereNotNull('mail_sent')->exists();
        if ($already_send) return false;

        //insert log
        DB::table('proforma_restore_mail_log')->updateOrInsert(
            ['order_id' => $order_id],
            ['created_at' => now()]
        );

        // //DB::beginTransaction();
        try {
            $data = [];
            $invoice_data = [];
            $order = NewOrder::find($order_id);
            $invoice_data['page_title'] = 'Order Invoice';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;

            if ($order->invoice_layout_id) {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')
                    ->where('cms_user_id', $order->cms_user_id)
                    ->where('id', $order->invoice_layout_id)
                    ->first();
            } else {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
            }

            if (!empty($order->offer_number)) {
                if ($order->offer_layout_id) {
                    $invoice_data['setting'] = DB::table('drm_offer_setting')
                        ->where('cms_user_id', $order->cms_user_id)
                        ->where('id', $order->offer_layout_id)
                        ->first();
                } else {
                    $invoice_data['setting'] = DB::table('drm_offer_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
                }
            }

            if ($order->marketplace_order_ref && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                $invoice_data['shipping_details'] = DB::table("new_orders")
                    ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                    ->select("new_orders.id as mp_order_id", "new_customers.*")
                    ->where('new_orders.id', $order->marketplace_order_ref)->first();
            }

            $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $product_list = json_decode($order->cart);

            $is_charge_email = false;
            $pay_url = null;

            //Check if stripe keys exists
            $has_stripe_key = DB::table('stripe_keys')->where('user_id', $order->cms_user_id)->exists();

            $admin_url = config('global.admin_url');
            if ($order->insert_type == 4) {
                $paywall = MonthlyPaywall::where('order_id', $order->id)->whereNull('paid_at')->first();
                if ($paywall) {
                    $is_charge_email = true;
                    $pay_url = $admin_url . '/paywall-payment/' . $paywall->id;
                }
            } elseif ($has_stripe_key && ($order->total > 0)) {
                $due_status = ['nicht_bezahlt'];
                if (in_array($order->status, $due_status)) {
                    $is_charge_email = true;
                    $pay_url = $admin_url . '/invoice-payment/' . $order->id;
                }
            }

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => $pay_url,
                'PAYWALL' => $is_charge_email,
            ];

            // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){

            if ($order->marketplace_order_ref) {
                $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
            } else {
                $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
            }

            $tags['shipping_address'] = $shipping;
            $tags['credit_note'] = $order->credit_number;

            $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $order->cms_user_id)->pluck('signature', 'id')->toArray();

            if ($email_signatures) {
                foreach ($email_signatures as $key => $signature) {
                    $tags['drm-sign-' . $key] = $signature;
                }
            }
            // }

            $template = DRMParseOrderEmailTemplate($tags, $order->cms_user_id, $channel);
            $data['email_to'] = $email_to = $invoice_data['customer']->email;
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            // TODO:: DROPMATIX
            $pdf_stream = (new \App\Services\Order\InvoiceDoc($order->id))->output();

            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
                $messages->subject($data['subject']);
                $messages->attachData($pdf_stream, 'invoice.pdf', [
                    'mime' => 'application/pdf',
                ]);
            });


            try {
                $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
                updateOrderHistory($order, 'send_email', 'Invoice sent to: ' . $email_to . '' . $is_bcc);
            } catch (Exception $ee) {
            }

            DB::table('new_orders')->where('id', $order_id)->update(['mail_sent' => date('Y-m-d H:i:s')]);

            //update after mail sent
            DB::table('proforma_restore_mail_log')->where('order_id',$order_id)->update(
                ['mail_sent' => 1 , 'updated_at' => now()]
            );
            ////DB::commit(); //All ok
            return true;
        } catch (Exception $e) {

            //save the exception in log
            DB::table('proforma_restore_mail_log')->where('order_id',$order_id)->update(
                ['error' => $e->getMessage()]
            );
            // //DB::rollBack();
            return false;
        }
    }

    //Droptienda send mail
    public function droptienda_send_email($order_id)
    {
        // //DB::beginTransaction();
        try {
            $data = [];
            $invoice_data = [];
            $order = NewOrder::find($order_id);
            $invoice_data['page_title'] = 'Order Invoice';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;

            if ($order->invoice_layout_id) {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')
                    ->where('cms_user_id', $order->cms_user_id)
                    ->where('id', $order->invoice_layout_id)
                    ->first();
            } else {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
            }

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'payment_method' => $order->payment_type ? ucfirst($order->payment_type) : '',
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            $template = DRMParseDroptiendaOrderEmailTemplate($tags, $order->cms_user_id);
            $data['email_to'] = $email_to = $invoice_data['customer']->email;
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $pdf_stream = null; //PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream();
            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
                $messages->subject($data['subject']);
                // $messages->attachData($pdf_stream, 'invoice.pdf', [
                //     'mime' => 'application/pdf',
                // ]);
            });

            try {
                $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
                updateOrderHistory($order, 'send_email', 'Droptienda order confirmation email sent to: ' . $email_to . '' . $is_bcc);
            } catch (Exception $ee) {
            }

            // DB::table('new_orders')->where('id', $order_id)->update(['mail_sent' => date('Y-m-d H:i:s')]);
            ////DB::commit(); //All ok
            return true;
        } catch (Exception $e) {
            ////DB::rollBack();
            return false;
        }
    }

    //Get product list
    public function getProductList($order_id)
    {
        $data = [];
        $data['order'] = $order = NewOrder::find($order_id);
        $data['product_list'] = json_decode($order->cart);
        $data['customer'] = $customer = $order->customer;
        $data['setting'] = $setting = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->first();

        $data['tax_rate'] = (($setting->small_business == 1) && (countryCodeTax($customer->country, true) == 4)) ? 0 : (in_array($order->cms_user_id, [98, 2454, 2455, 2439]) ? 21 : countryCodeTax($customer->country));
        return view('admin.new_order.product_list', $data)->render();
    }

    public function postImportOrder()
    {
        $spreadsheet = IOFactory::load($_FILES["order_file"]["tmp_name"]);
        $data_arr = $spreadsheet->getActiveSheet()->toArray();

        unset($data_arr[0]);
        $count = 0;
        foreach ($data_arr as $value) {

            $customer_info = $order_info = [];
            $country = $value[23];
            $customer_info = [
                "customer_full_name" => $value[5],
                "currency" => $value[10],
                'email' => $value[4],
                'address' => $value[17],
                'city' => $value[20],
                'country' => $country,
                'phone' => $value[24],
                'zip_code' => $value[22],
                'insert_type' => 5,

                //shipping
                // 'street_shipping' => $value[] ,
                'city_shipping' => $value[20],
                'state_shipping' => $value[21],
                'zipcode_shipping' => $value[22],
                'country_shipping' => $country,
            ];

            $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);

            $order_info['drm_customer_id'] = $customer_id;
            $order_info['order_date'] = $value[2];
            $order_info['insert_type'] = 5;
            // $order_info['total'] = ;
            // $order_info['shop_id'] = ;
            $order_info['order_id_api'] = $value[0];

            // $order_info['sub_total'] = $total_sum;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $value[15];
            $order_info['currency'] = $customer_info["currency"];

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $order_info['client_note'] = $value[7];
            $order_info['status'] = "Shipped";
            $order_info['user_id'] = CRUDBooster::myId();

            //Pricing
            $qty = (float)($value[9]);
            $total = (float)($value[11]);
            $rate = ($qty > 0) ? ($total / $qty) : 0;
            $shipping_charge = (float)($value[13]);
            // $order_price = $total + $shipping_charge;

            $order_info['total'] = $total;
            $order_info['sub_total'] = $total;
            $order_info['total_tax'] = $value[12];
            $order_info['shipping_cost'] = $shipping_charge;

            $carts = [];
            $cart_item = [];
            $cart_item['id'] = 1;
            $cart_item['product_name'] = strip_tags(preg_replace_array('/"/', [' '], $value[8]));
            $cart_item['description'] = null;
            $cart_item['qty'] = $value[9];
            $cart_item['rate'] = $rate;
            $cart_item['unit'] = null;
            $cart_item['tax'] = 0;
            $cart_item['product_discount'] = 0;
            $cart_item['amount'] = $total;
            $carts[] = $cart_item;
            $order_info['cart'] = json_encode($carts);
            if ($this->add_order($order_info)) {
                $count++;
            }
        }
        if ($count) CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), $count . ' ' . trans('Orders Import') . ' ', 'success');
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), trans('No Order Imported') . ' ', 'warning');
    }

    public function getEmailSetting()
    {
        $data['page_title'] = 'Email setting';
        $data['mail'] = DB::table('drm_order_mail')->where('cms_user_id', CRUDBooster::myId())->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', CRUDBooster::myId())->first();
        return view("admin.new_order.email_setting", $data);
    }

    public function postSaveEmailSetting()
    {
        \Cache::forget('drm_order_mail_' . CRUDBooster::myId());

        DB::table('drm_order_mail')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myId()
            ],
            [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => $_REQUEST['auto_mail'],
                'email_template' => $_REQUEST['email_template']
            ]
        );

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postTestEmail()
    {
        try {
            $order = $this->generate_fake_order();
            $invoice_data['page_title'] = 'Test order email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->first();
            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => $order->invoice_number,
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            $template = DRMParseOrderEmailTemplate($tags, $order->cms_user_id);
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $pdf_stream = PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream();
            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
                $messages->attachData($pdf_stream, 'invoice.pdf', [
                    'mime' => 'application/pdf',
                ]);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function getFixInvoiceNumber()
    {
        \App\Shop::orderBy('id')->each(function ($item) {

            $d_in = DB::table('new_orders')
                ->select(DB::raw('invoice_number, COUNT(invoice_number) as count'))
                ->where([
                    'cms_user_id' => $item->user_id,
                    // 'shop_id' => $item->id
                ])
                ->groupBy('invoice_number')
                ->having('count', '>', 1)
                ->first();

            echo $d_in->count . "<br>";

            if ($d_in->count > 1) {
                // dd($d_in->invoice_number);
                $all_dup = DB::table('new_orders')->where([
                    'cms_user_id' => $item->user_id,
                    'invoice_number' => $d_in->invoice_number,
                ])->get();

                $total_inv = DB::table('new_orders')->where('cms_user_id', $item->user_id)->where('invoice_number', '!=', -1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;

                foreach ((object)$all_dup as $value) {
                    // dd($value->id);
                    DB::table('new_orders')->where('id', $value->id)
                        ->update([
                            'invoice_number' => $total_inv++
                        ]);
                }
            }
        });

        dd('Successfull');
        // dd($all_shop);

        // SELECT  cms_user_id,invoice_number,COUNT(invoice_number) FROM new_orders
        // WHERE new_orders.cms_user_id = 1 AND new_orders.shop_id = 4
        // GROUP BY invoice_number
        // HAVING COUNT(invoice_number) >1;

    }

    //product details
    public function getProductDetails($order_id, $product_id)
    {
        $order = NewOrder::find($order_id);
        $products = $order->products;
        $products_collection = new Collection($products);
        $product = $products_collection->where('id', $product_id)->first();
        $product->currency = formatCurrency($order->currency);
        return response()->json([
            'success' => true,
            'data' => $product,
        ]);
    }


    // public function getKarinaInsert(){
    // 	$old_orders = DB::table('drm_orders_new')->where('insert_type', 'Manual')->where('cms_user_id', 61)->get();
    // 	try{
    //  	$count = 0;
    //  	foreach ($old_orders as $order) {

    //             $customer_full_name = $company_name = $email = $city = $zip_code = $address = $state = $country = $currency = null;
    //             $street_shipping = $city_shipping = $state_shipping = $zipcode_shipping = $country_shipping = null;
    //             $street_billing = $city_billing = $state_billing = $zipcode_billing = $country_billing = null;
    //             $website  = $default_language = null;

    // $customer = DB::table('drm_customers')->find($order->drm_customer_id);
    // $customer_full_name = $customer->full_name;
    //          $company_name  = $customer->company_name;
    //          $email  = $customer->email;
    //          $city  = $customer->city;
    //          $zip_code  = $customer->zip_code;
    //          $state  = $customer->state;
    //          $country  = $customer->country;
    //          $phone  = $customer->phone;
    //          $currency  = $customer->currency;
    //          $address  = $customer->address;

    //          $website  = $customer->website;
    //          $default_language  = $customer->default_language;

    //          $customer_billing = DB::table('drm_customer_address')->where('drm_customer_id', $customer->id)->where('type', 'billing')->first();
    //          if ($customer_billing) {
    //              $street_billing = $customer_billing->street;
    //              $city_billing = $customer_billing->city;
    //              $state_billing = $customer_billing->state;
    //              $zipcode_billing = $customer_billing->zipcode;
    //              $country_billing = $customer_billing->country;
    //          }
    //          $customer_shipping = DB::table('drm_customer_address')->where('drm_customer_id', $customer->id)->where('type', 'shipping')->first();
    //          if ($customer_shipping) {
    //              $street_shipping = $customer_shipping->street;
    //              $city_shipping = $customer_shipping->city;
    //              $state_shipping = $customer_shipping->state;
    //              $zipcode_shipping = $customer_shipping->zipcode;
    //              $country_shipping = $customer_shipping->country;
    //          }


    //          $customer_info = [
    //              'customer_full_name' => $customer_full_name,
    //              'company_name' => $company_name,
    //              'email' =>  $email,
    //              'city' => $city,

    //              'zip_code' => $zip_code,
    //              'state' =>  $state,
    //              'country' => $country,
    //              'phone' =>  $phone,
    //              'website' => $website,
    //              'currency' => $currency,
    //              'default_language' => $default_language,
    //              'address' => $address,
    //              'insert_type' => 6,
    //              'user_id' => $order->cms_user_id,
    //              // 	'vat_number' => ,

    //              // shipping
    //              'street_shipping' => $street_shipping,
    //              'city_shipping' => $city_shipping,
    //              'state_shipping' => $state_shipping,
    //              'zipcode_shipping' => $zipcode_shipping,
    //              'country_shipping' => $country_shipping,

    //              //billing
    //              'street_billing' => $street_billing,
    //              'city_billing' => $city_billing,
    //              'state_billing' => $state_billing,
    //              'zipcode_billing' => $zipcode_billing,
    //              'country_billing' =>  $country_billing,
    //          ];

    //          //Get customer id


    // $shipping = $order->shipping;
    // if ($shipping) {
    // 	$shipping = explode("<br>", $shipping);

    // 	$street_shipping = isset($shipping[1])? $shipping[1] : null;
    // 	$city_shipping = isset($shipping[2])? $shipping[2] : null;

    // 	if($city_shipping){
    // 		preg_match("/\b\d{4,6}(-\d{4,6})?\b/", $city_shipping, $matches);
    // 		$zip = $matches[0];
    // 		if ($zip) {
    // 			$zipcode_shipping = $zip;
    // 			$city_shipping = preg_replace('/\b'.$zip.'\b/i', '', $city_shipping);
    // 		}
    // 	}
    // 	$country_shipping = $shipping[3];
    // }

    // $customer_data = $order->customer_info;
    // if ($customer_data) {
    // 	$customer_data = explode("<br>", $customer_data);

    // 	$customer_full_name = isset($customer_data[0])? $customer_data[0] : null;
    // 	$address = isset($customer_data[2])? $customer_data[2] : null;
    // 	$city = isset($customer_data[3])? $customer_data[3] : null;

    // 	if($city){
    // 		preg_match("/\b\d{4,6}(-\d{4,6})?\b/", $city, $matches);
    // 		$zip = $matches[0];
    // 		if ($zip) {
    // 			$zip_code = $zip;
    // 			$city = preg_replace('/\b'.$zip.'\b/i', '', $city);
    // 		}
    // 	}
    // 	$country = $customer_data[5];
    // }


    //       $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);

    //      	$customer_info = [
    //              'customer_full_name' => $customer_full_name,
    //              'company_name' => $company_name,
    //              'email' =>  $email,
    //              'city' => $city,

    //              'zip_code' => $zip_code,
    //              'state' =>  $state,
    //              'country' => $country,
    //              'phone' =>  $phone,
    //              'website' => $website,
    //              'currency' => $currency,
    //              'default_language' => $default_language,
    //              'address' => $address,
    //              'insert_type' => 6,
    //              'user_id' => $order->cms_user_id,
    //              // 	'vat_number' => ,

    //              // shipping
    //              'street_shipping' => $street_shipping,
    //              'city_shipping' => $city_shipping,
    //              'state_shipping' => $state_shipping,
    //              'zipcode_shipping' => $zipcode_shipping,
    //              'country_shipping' => $country_shipping,

    //              //billing
    //              'street_billing' => $street_billing,
    //              'city_billing' => $city_billing,
    //              'state_billing' => $state_billing,
    //              'zipcode_billing' => $zipcode_billing,
    //              'country_billing' =>  $country_billing,
    //          ];

    //       // order
    //       $order_info['user_id'] = $order->cms_user_id;
    //       $order_info['drm_customer_id'] = $customer_id;
    //       $order_info['order_date'] = $order->order_date;
    //       $order_info['insert_type'] = 6;
    //       $order_info['total'] = $order->total;
    //       $order_info['shop_id'] = $order->shop_id;
    //       $order_info['order_id_api'] = $order->order_id_api;

    //       $order_info['sub_total'] = $order->sub_total;
    //       $order_info['discount'] = $order->discount;
    //       $order_info['discount_type'] = $order->discount_type;
    //       $order_info['adjustment'] = $order->adjustment;
    //       $order_info['payment_type'] = $order->payment_type;
    //       $order_info['currency'] = $order->currency;

    //       //customer info
    //       $order_info['customer_info'] = customerInfoJson($customer_info);

    //       //billing
    //       $order_info['billing'] = shippingInfoJson($customer_info); //billingInfoJson($customer_info);

    //       //shipping
    //       $order_info['shipping'] = $order_info['billing'];

    // $carts = [];
    // $products = DB::table('drm_order_products')->where('drm_order_id', $order->id)->get();
    // if ($products) {
    // 	foreach ($products as $key => $product) {
    // 		$cart_item = [];
    //            $cart_item['id'] = $key;
    //            $cart_item['product_name'] = trim(iconv('UTF-8', 'ASCII//TRANSLIT', $product->name),"\"");
    //            $cart_item['description'] =  trim(iconv('UTF-8', 'ASCII//TRANSLIT', $product->description),"\"");
    //            $cart_item['qty'] = $product->quantity;
    //            $cart_item['rate'] = $product->rate;
    //            $cart_item['tax'] = $product->tax;
    //            $cart_item['unit'] = $product->unit;
    //            $cart_item['product_discount'] = $product->discount;
    //            $cart_item['amount'] = $product->amount;
    //            $cart_item['image'] = $product->image;
    //            $carts[] = $cart_item;
    // 	}
    // }
    //  		$order_info['cart'] = json_encode($carts);

    //  		$this->add_import_order($order_info);

    //  		$count ++;

    //  	}

    //  	dd($count);

    // 	}catch(Exception $e){
    // 		dd($e->getMessage());
    // 	}
    // }


    //Add order import
    public function add_import_order($order_info)
    {
        $validator = Validator::make($order_info, [
            'user_id' => 'required',
        ]);
        if ($validator->fails()) {
            return null;
        }

        $product = $order_info['product_id'];
        $product_name = $order_info['product_name'];
        $description = $order_info['description'];
        $quantity = $order_info['qty'];
        $unit = $order_info['unit'];
        $rate = $order_info['rate'];
        $tax = $order_info['tax'];
        $product_discount = $order_info['product_discount'];
        // $final_price = $order_info['final_price'];
        $amount = $order_info['amount'];
        /* ----------------- End Calculation ------------------ */
        $check['cms_user_id'] = $order_info['user_id'];
        // $check['drm_customer_id']   = $order_info['drm_customer_id'];
        $check['order_date'] = $order_info['order_date'];
        $check['insert_type'] = $order_info['insert_type'];
        $check['shop_id'] = $order_info['shop_id'];
        $check['order_id_api'] = $order_info['order_id_api'];

        /* --------------- invoice number --------------------- */
        if (DB::table('new_orders')->where($check)->count() > 1) return false;

        $order_inv = DB::table('new_orders')->where($check)->first();


        if (!($order_inv == null || $order_inv == [] || $order_inv->id == null)) {
            $invoice_number = $order_inv->invoice_number;
        } else {
            $inv1 = DB::table('new_orders')->where('cms_user_id', $check['cms_user_id'])->where('invoice_number', '!=', -1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
            $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id', $check['cms_user_id'])->orderBy('id', 'desc')->first()->start_invoice_number;
            $invoice_number = ($inv1 > $inv2) ? $inv1 : $inv2;
        }

        /* -------------- future invoice ------------------- */
        $status = "Shipped";

        if ($order_info['invoice_date'] != "" || $order_info['invoice_date'] != null) {
            $now = new DateTime();
            $due = new DateTime($order_info['invoice_date']);

            if ($due > $now) {
                $status = "In Progress";
                $invoice_number = -1;
            }
        }

        /* ------------------ insert order ----------------- */
        $row['invoice_number'] = $invoice_number;
        $row['invoice_date'] = $order_info['invoice_date'];

        if (strpos($order_info['total'], ",")) {
            $have = [".", ","];
            $will_be = ["", "."];
            $order_info['total'] = str_replace($have, $will_be, $order_info['total']);
        }
        $row['total'] = $order_info['total'];
        $row['sub_total'] = $order_info['sub_total'];
        $row['total_tax'] = $order_info['total_tax'];

        $row['drm_customer_id'] = $order_info['drm_customer_id'];

        $row['discount'] = $order_info['discount'];
        $row['discount_type'] = $order_info['discount_type'];
        $row['adjustment'] = $order_info['adjustment'];
        $row['payment_type'] = $order_info['payment_type'];
        $row['currency'] = $order_info['currency'];
        $row['shipping_cost'] = $order_info['shipping_cost'];
        $row['customer_info'] = $order_info['customer_info'];
        $row['billing'] = $order_info['billing'];
        $row['shipping'] = $order_info['shipping'];
        $row['client_note'] = $order_info['client_note'];
        $row['status'] = ucfirst($order_info['status'] ?? $status);
        $row['cms_client'] = $order_info['cms_client'];
        $row['cart'] = $order_info['cart'];

        $order = NewOrder::updateOrCreate($check, $row);

        return $order;
    }

    /*-------------------------------------------
    	----------------- Fix Invoice number --------
    	--------------------------------------------*/
    // public function getFixInvoice(){
    // 	// return 'Invalid operation!';
    // 	$start = 508; // Fabian fix invoice number
    //       	$orders = NewOrder::orderByRaw('CAST(order_date AS datetime) asc')->whereRaw('cast(invoice_number as SIGNED) > 507')->where('cms_user_id', 98)->where('invoice_number', '!=', -1)->get();

    //       	// dd($orders);

    //       	// $orders = NewOrder::orderBy('order_date', 'asc')->where('cms_user_id', 52)->get();
    //       	foreach ($orders as $order) {
    //       		$order->update(['invoice_number' => $start]);
    //       		$start++;
    //       	}

    //       	// orderBy('order_date', 'asc')
    //        return $start;
    // }

    private function generate_fake_order()
    {

        $faker = \Faker\Factory::create();
        $country = $faker->country;
        $currency = array_rand(['EUR', 'GBP '])[0];

        $data = [
            "id" => 7,
            // "order_id_api" => "400217",
            "cms_user_id" => CRUDBooster::myId(),
            "shop_id" => rand(100, 150),
            "drm_customer_id" => rand(100, 150),
            "invoice_number" => rand(1, 150),
            "order_date" => Carbon::today(),
            "invoice_date" => null,

            "total_tax" => null,
            "discount" => 0,
            "discount_type" => "fixed",
            "adjustment" => 0,
            "shipping_cost" => null,
            "payment_type" => "amazon",
            "currency" => $currency,
            // "customer_info" => "{"name":"Melanie M\u00fcller","company":"","address":"","zip_code":"51107","city":null,"state":"Nordrhein-Westfalen","country":"Germany"}"
            // "billing" => "{"name":"Melanie M\u00fcller","company":"","street":"He\u00dfhofstr. 4 ","address":null,"zip_code":"51107","city":"K\u00f6ln","state":"Nordrhein-Westfalen","cou â–¶"
            // "shipping" => "{"name":"Melanie M\u00fcller","company":"","street":"He\u00dfhofstr. 4 ","address":null,"zip_code":"51107","city":"K\u00f6ln","state":"Nordrhein-Westfalen","cou â–¶"
            "client_note" => $faker->sentence,
            "mail_sent" => null,
            "status" => "Versendet",
            "insert_type" => rand(1, 6),
            "trash" => null,
            "supplier" => null,

            "char_status" => 0,
            "payment_status" => null,
            "cms_client" => null,
            "created_at" => Carbon::today(),
            "updated_at" => Carbon::today(),
        ];

        $total = 0;
        $carts = [];
        $item = (int)rand(3, 10);
        for ($i = 1; $i <= $item; $i++) {
            $qty = (int)rand(1, 5);
            $rate = (float)rand(10, 200);
            $amount = $qty * $rate;

            $cart_item = [];
            $cart_item['id'] = $i;
            $cart_item['product_name'] = $faker->word;
            $cart_item['description'] = $faker->sentence;
            $cart_item['qty'] = $qty;
            $cart_item['rate'] = $rate;
            $cart_item['tax'] = 0;
            $cart_item['product_discount'] = 0;
            $cart_item['amount'] = $amount;

            $total += $amount;
            $carts[] = $cart_item;
        }

        $data['cart'] = json_encode($carts);
        $data['sub_total'] = (float)($total);
        $data['total'] = (float)(rand($total, $total + rand(0, 20)));

        $customer_name = $faker->name;

        $data['billing'] = json_encode([
            'name' => $customer_name,
            'company' => $faker->company,
            'street' => $faker->streetAddress,
            'address' => $faker->address,
            'zip_code' => $faker->postcode,
            'city' => $faker->city,
            'state' => $faker->state,
            'country' => $country,
        ]);

        $data['customer'] = [
            'country' => $country,
            'name' => $customer_name
        ];

        return (object)$data;
    }


    public function postSupplierView()
    {
        $order_ids = $_REQUEST["orders"];
        if ($order_ids) {
            $suppliers = \App\DeliveryCompany::where('user_id', \CRUDBooster::myId())->select('id', 'name', 'email')->get();
            if ($suppliers) {
                return response()->json([
                    'success' => true,
                    'html' => view('admin.new_order.supplier-view', compact('suppliers', 'order_ids'))->render()
                ]);
            }
        }
    }


    //Manual product insert to invoice_products table
    // function getInsertInvoiceProduct(){
    // 	$orders = NewOrder::where('insert_type', 6)->get();
    // 	foreach ($orders as $order) {
    // 		$prodeucts = $order->products;
    // 		foreach ($prodeucts as $cart) {
    // 			$this->updateInvoiceProduct((array)$cart, $order->cms_user_id);
    // 		}
    // 	}
    // }


    /*==============================
    	=============Place order========
    	===============================*/
    public function getSupplierEmailSetting()
    {
        $data['page_title'] = 'Supplier Email Template Setting';
        $data['mail'] = DB::table('drm_supplier_mail')->where('cms_user_id', CRUDBooster::myId())->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', CRUDBooster::myId())->first();
        return view("admin.new_order.supplier_email_setting", $data);
    }

    public function postSaveSupplierEmailSetting()
    {
        \Cache::forget('drm_supplier_mail_' . CRUDBooster::myId());
        DB::table('drm_supplier_mail')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myId()
            ],
            [
                'mail_subject' => $_REQUEST['mail_subject'],
                'email_template' => $_REQUEST['email_template']
            ]
        );

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Supplier Email Setting Changed'), 'success');
    }


    public function postTestSupplierEmail()
    {
    }


    public function postPlaceOrder()
    {
        try {
            $supplider_id = request()->drm_order_supplier_id;
            $order_ids = request()->order_ids;

            if ($supplider_id && $order_ids) {

                $user = User::find(CRUDBooster::myId());
                $data['supplider_id'] = $supplider_id;
                $data['order_ids'] = $order_ids;

                dispatch(new \App\Jobs\SendBulkSupplierEmailJob($user, $data));
                CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), count($order_ids) . ' Order ready to send. Once completed, we send you notification!', 'success');
            } else {
                throw new Exception("Invalid action!");
            }
        } catch (Exception $e) {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), trans('Order placed failed!') . ' ' . $e->getMessage(), 'success');
        }
    }


    public function SendBulkSupplierEmail($user, $order_data)
    {
        try {

            $count = 0;
            if (isset($order_data['order_ids']) && $order_data['order_ids']) {

                $count = 0;
                $supplier_id = $order_data['supplider_id'];

                //Dropmatix carnot supplied replace with Wohlauf carnot supplier
                $supplier_id = (int) $supplier_id === 14383 ? 13040 : $supplier_id;
                $supplier = DeliveryCompany::find($supplier_id);

                if (is_null($supplier)) throw new Exception('Invalid supplier!');
                $supplier_name = $supplier->name;

                foreach ($order_data['order_ids'] as $order_id) {
                    $data = [];
                    $data['page_title'] = 'Delivery Note';
                    $order = NewOrder::find($order_id);
                    $data['order'] = $order;
                    $data['product_list'] = json_decode($order->cart);
                    $data['customer'] = $customer = $order->customer;

                    $supplier_user_id = (int)$supplier_id === 13040 ? $supplier->user_id : $order->cms_user_id;
                    $user_id = $order->cms_user_id;


                    if ($order->marketplace_order_ref && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                        $data['shipping_details'] = DB::table("new_orders")
                            ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                            ->select("new_orders.id as mp_order_id", "new_customers.*")
                            ->where('new_orders.id', $order->marketplace_order_ref)->first();
                    } else if ($order->credit_number && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                        $marketplace_credit = DB::table('new_orders')->where('credit_ref', '=', $order->id)->value('marketplace_order_ref');

                        if ($marketplace_credit->marketplace_order_ref) {
                            $data['shipping_details'] = DB::table("new_orders")
                                ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                                ->select("new_orders.id as mp_order_id", "new_customers.*")
                                ->where('new_orders.id', $marketplace_credit->marketplace_order_ref)->first();
                        }
                    }
                    //Marketplace credit end
                    $data['setting'] = $invoice_data = $this->getDelveriNoteTemplate($order, $supplier_user_id);

                    $pdf_stream = PDF::loadView('admin.invoice.delivery_note', $data)->setWarnings(false)->stream();

                    $product_list = json_decode($order->cart);
                    $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

                    if (isset($data['shipping_details']) && $data['shipping_details']) {
                        $shipping = '<p>' . formatBillingAddress($data['shipping_details']->shipping) . '</p>';
                    } else {
                        $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                    }

                    $logo = ($invoice_data->logo) ? $invoice_data->logo : '';
                    $tags = [
                        'customer_name' => $customer->full_name,
                        'company_name' => $customer->company_name,
                        'billing_address' => $billing,
                        'shipping_address' => $shipping,
                        'order_items' => view('admin.new_order.email_supplier_order_items', compact('product_list', 'order'))->render(),
                        'order_items_n_article' => view('admin.new_order.email_supplier_order_items_n_article', compact('product_list', 'order'))->render(),
                        'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                        'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data->store_name . '" >',
                        'order_number' => $order->id,
                        'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                        'contact_name' => $supplier_name,
                        'pay_url' => null,
                        'PAYWALL' => false,
                        'IS_DROPSHIPPING'   => true,
                        'IS_FULFILLMENT'    => false,
                    ];

                    // if( (isLocal() || in_array($user_id, [212, 2592])) ){

                    $tags['credit_note'] = $order->credit_number;

                    $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $user_id)->pluck('signature', 'id')->toArray();

                    if ($email_signatures) {
                        foreach ($email_signatures as $key => $signature) {
                            $tags['drm-sign-' . $key] = $signature;
                        }
                    }
                    // }

                    // if(isLocal() || in_array($user_id, [212, 2592])){

                    $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                    $channel_supplier_mail = DB::table('drm_supplier_mail')
                        ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                        ->exists();

                    if ($channel_supplier_mail) {
                        $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id, $channel);
                    } else {
                        $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id);
                    }

                    // }else{
                    // $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id);
                    // }

                    $data['email_to'] = $email_to = $supplier->order_email ?? $supplier->email;
                    $data['email_from'] = $template['senderEmail'];;
                    $data['subject'] = $template['subject'];
                    $data['bcc'] = $template['bcc'];

                    if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                        throw new Exception("Something Wrong! Email Not Sent!.");
                    }

                    // $note_name = 'delivery_note_' . $supplier_name . '_' . $order->invoice_number;
                    // $note_name = preg_replace('/\s+/', '_', $note_name);
                    // $note_name = preg_replace('/[_]+/', '_', $note_name);
                    $note_name = $order->id . '.pdf';

                    app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_supplier', $template, function ($messages) use ($data, $pdf_stream, $note_name) {
                        // $messages->from($data['email_from']);
                        $messages->to($data['email_to']);
                        $messages->subject($data['subject']);

                        if ($data['bcc']) {
                            $messages->bcc($data['bcc']);
                        }

                        $messages->attachData($pdf_stream, $note_name, [
                            'mime' => 'application/pdf',
                        ]);
                    });

                    $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
                    $log_message = 'Order placed Successfully! Email: ' . $email_to . ' (' . $supplier_name . ')' . $is_bcc;

                    updateOrderHistory($order, 'place_order', $log_message);
                    DB::table('new_orders')->where('id', $order_id)->update(['supplier_id' => $supplier_id, 'supplier_time' => now(), 'supplier' => 'checked']);

                    $log_data = [];
                    $log_data['product_list'] = $product_list;
                    $log_data['tags'] = $tags;
                    $log_data['order_id'] = $order->id;
                    $supplier->logs()->create([
                        'type' => 1,
                        'data' => $log_data,
                        'message' => $log_message,
                    ]);

                    //send FTP file
                    try {
                        if ((int) $supplier_id === 13040) {
                            app(\App\Services\FTP\OrderFTPService::class)->sendOrders([$order->id], $supplier_user_id);
                        }
                    } catch (\Exception $ex) {
                    }

                    $count++;
                }

                $user->notify(new DRMNotification($count . ' Order sent to supplier.', 'ORDER_MODULE', '#'));
            }
        } catch (Exception $e) {
            $user->notify(new DRMNotification('Order sent failed to supplier. ' . $e->getMessage(), 'ORDER_MODULE_ERROR', '#'));
        }
    }


    //User billing address to update customer profile
    public function insertStripeOrder($order_info, $user_id = null, $order_return = false)
    {
        $customer = NewCustomer::where('cc_user_id', $user_id)->where('user_id', $order_info['user_id'])->first();
        if ($customer) {
            $order_info['drm_customer_id'] = $customer->id;
            $order_info['customer_info'] = customerToCustomerInfoJson($customer);
            $order_info['billing'] = $customer->billing;
            $order_info['shipping'] = $customer->shipping;

            $new_order = $this->add_order($order_info);
            if ($new_order) {
                return ($order_return) ? $new_order : true;
            }
        }
        return false;
    }

    public function decrementHandlingTime()
    {
        $statuses = array_map('strtolower', ['Shipped', 'order_placed', 'mp_order_placed', 'Canceled', 'Completed']);
        $result = '';
        foreach ($statuses as $value) {
            $result .= "'" . $value . "',";
        }
        $result = substr($result, 0, -1);
        NewOrder::whereRaw('LOWER(status) NOT IN(' . $result . ')')
            ->whereNotNull('delivery_days')
            ->decrement('delivery_days');

        DB::table('new_orders')
            ->whereRaw('LOWER(status) NOT IN(' . $result . ')')
            ->whereNotNull('delivery_days')
            ->where('delivery_days', 0)
            ->update(['delivery_day_date' => Carbon::now()]);
        // DB::table('new_orders')
        //     ->whereNotIn('status', ['Shipped','order_placed','Canceled'])
        //     // ->where('status', '!=', 'Shipped')
        //     ->whereNotNull('delivery_days')
        //     ->decrement('delivery_days'); // decrement by one

        $this->getNegativeDataAndSendEmail();
    }

    public function getNegativeDataAndSendEmail()
    {
        $orders = NewOrder::with('customer')
            // ->whereNotIn('status', ['Shipped','order_placed','Canceled'])
            ->whereNotNull('delivery_days')
            ->where('delivery_days', -1)
            ->get();

        //New status
        $new_status = 'auslieferung_verzogert';

        if ($orders->isNotEmpty()) {
            foreach ($orders as $order) {


                $alreadySent = HandlingTimeEmailHistory::where('user_id', $order->cms_user_id)
                    ->where('customer_id', $order->drm_customer_id)
                    ->where('order_id', $order->id)
                    ->exists();

                if ($alreadySent) {
                    continue;
                }

                $isMailOn = DB::table('marketing_email_settings')
                    ->where('auto_mail', 1)
                    ->where('cms_user_id', $order->cms_user_id)
                    ->exists();

                if ($isMailOn) {
                    // email sending here
                    try {
                        $invoice_data['customer'] = (object)$order->customer;

                        if ($order->invoice_layout_id) {
                            $invoice_data['setting'] = DB::table('drm_invoice_setting')
                                ->where('cms_user_id', $order->cms_user_id)
                                ->where('id', $order->invoice_layout_id)
                                ->first();
                        } else {
                            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
                        }

                        $product_list = json_decode($order->cart);

                        $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

                        $logo = ($invoice_data['setting'] && $invoice_data['setting']->logo) ? $invoice_data['setting']->logo : '';
                        $tags = [
                            'customer_name' => $invoice_data['customer']->full_name,
                            'company_name' => $invoice_data['customer']->company_name,
                            'billing_address' => $billing,
                            'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                            'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                            'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                            'order_number' => $order->id,
                            'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                            'pay_url' => null,
                            'PAYWALL' => false,
                        ];

                        // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){

                        if ($order->marketplace_order_ref) {
                            $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                            $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                        } else {
                            $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                        }

                        $tags['shipping_address'] = $shipping;
                        $tags['credit_note'] = $order->credit_number;

                        $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $order->cms_user_id)->pluck('signature', 'id')->toArray();

                        if ($email_signatures) {
                            foreach ($email_signatures as $key => $signature) {
                                $tags['drm-sign-' . $key] = $signature;
                            }
                        }
                        // }

                        // if(isLocal() || in_array($order->cms_user_id, [212, 2592])){

                        $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                        $channel_handling_time_mail = DB::table('marketing_email_setting_by_channels')
                            ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                            ->exists();

                        if ($channel_handling_time_mail) {
                            $template = DRMParseHandlingTimeEmailTemplate($tags, $order->cms_user_id, $channel);
                        } else {
                            $template = DRMParseHandlingTimeEmailTemplate($tags, $order->cms_user_id);
                        }

                        // }else{
                        // $template = DRMParseHandlingTimeEmailTemplate($tags, $order->cms_user_id);
                        // }

                        $data['email_to'] = $invoice_data['customer']->email;
                        $data['email_from'] = $template['senderEmail'];

                        if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                            throw new Exception("Something Wrong! Email Not Sent!.");
                        }

                        $data['subject'] = $template['subject'];
                        $data['bcc'] = $template['bcc'];

                        app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                            // $messages->from($data['email_from']);
                            $messages->to($data['email_to']);
                            $messages->subject($data['subject']);

                            if ($data['bcc']) {
                                $messages->bcc($data['bcc']);
                            }
                        });

                        //Update order status
                        $order->update(['status' => $new_status]);

                        $history = new HandlingTimeEmailHistory();
                        $history->user_id = $order->cms_user_id;
                        $history->customer_id = $order->drm_customer_id;
                        $history->order_id = $order->id;
                        $history->save();

                        try {
                            $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
                            $message_text = 'Handling time email sent to ' . $data['email_to'] . '' . $is_bcc . '. Status change to ' . drmHistoryLabel($new_status);
                            updateOrderHistory($order, 'handlingtime_email', $message_text);
                        } catch (Exception $ex) {
                        }
                    } catch (Exception $exception) {
                        Log::error('Handling Time Mail Send Error. Message => ' . $exception->getMessage());
                    }
                }
            }
        }
    }


    //Fix invalid customer_id_issue
    public function getFixInvalidEmailOrder()
    {

        $count = 0;


        $invalid_customers = [265, 271, 8801, 9071, 9549, 11237, 12006, 12084, 12288, 12552];


        $invalid_orders = NewOrder::whereIn('drm_customer_id', $invalid_customers)->get();

        foreach ($invalid_orders as $order) {

            $customer_info = json_decode($order->billing, true);

            $fake_email = $customer_info['name'];
            $email = preg_replace('/[^a-zA-Z0-9]/', '', $fake_email);
            $email = preg_replace('/\s+/', '_', $email);
            $email = $email . '4' . $zipcode_shipping . '@drmebay.com';
            $email = strtolower(trim(preg_replace('/\s+/', ' ', $email)));

            $customer_data = [
                'full_name' => $customer_info['name'],
                'company_name' => $customer_info['company'],
                'country' => $customer_info['country'],
                'phone' => null,
                'website' => null,
                'city' => $customer_info['city'],
                'zip_code' => $customer_info['zip_code'],
                'state' => $customer_info['state'],
                'currency' => 'EUR',
                'default_language' => 'de',
                'address' => $customer_info['street'],
                'insert_type' => 1,
                'billing' => $order->billing,
                'shipping' => $order->shipping,
            ];

            //customer_check
            $customer_check = [
                'email' => $email,
                'user_id' => $order->cms_user_id,
            ];

            //customer_data
            $customer_data = array_filter($customer_data);

            // customer add
            $customer = NewCustomer::updateOrCreate($customer_check, $customer_data);

            if ($customer && $customer->id) {
                // dd($order->customer, $customer);
                $order->update([
                    'drm_customer_id' => $customer->id,
                ]);

                $count++;
            }

            // $ref_customer = NewOrder::where(['cms_user_id' => $order->cms_user_id, 'order_id_api' => $order->order_id_api] )->whereNotIn('drm_customer_id', $invalid_customers)->select('drm_customer_id')->pluck('drm_customer_id')->toArray();


            // $customer = NewCustomer::whereIn('id', $ref_customer)->where('email', 'LIKE', '%@%')->first();
            // if($customer && $customer->id){
            // 	// dd($order->customer, $customer);
            // 	$order->update([
            // 		'drm_customer_id' => $customer->id,
            //         'customer_info' => customerToCustomerInfoJson($customer),
            //         'billing' => $customer->billing,
            //         'shipping' => $customer->shipping
            //     ]);

            //     $count++;

            // }
        }


        // $invalid_customers = [271, 8801, 9071, 9549, 11237, 12006, 12084, 12288, 12552];


        // $invalid_orders = NewOrder::whereIn('drm_customer_id', $invalid_customers )->select('id', 'shop_id', 'order_id_api', 'cms_user_id', 'drm_customer_id')->get();

        // foreach ($invalid_orders as $order) {
        // 	$ref_customer = NewOrder::where(['cms_user_id' => $order->cms_user_id, 'order_id_api' => $order->order_id_api] )->whereNotIn('drm_customer_id', $invalid_customers)->select('drm_customer_id')->pluck('drm_customer_id')->toArray();


        // 	$customer = NewCustomer::whereIn('id', $ref_customer)->where('email', 'LIKE', '%@%')->first();
        // 	if($customer && $customer->id){
        // 		// dd($order->customer, $customer);
        //  	$order->update([
        //  		'drm_customer_id' => $customer->id,
        //          'customer_info' => customerToCustomerInfoJson($customer),
        //          'billing' => $customer->billing,
        //          'shipping' => $customer->shipping
        //      ]);

        //      $count++;

        // 	}
        // }

        dd($count);


        //    	$success = $failed = 0;

        //    	foreach ($orders_group as $shop_id => $orders) {

        // 		$order_collection = collect($orders);
        // 		$order_ids = $order_collection->pluck('order_id_api')->toArray();

        // 		try{
        // app('App\Http\Controllers\ApiInvoiceResponseCntroller')->EbaySyncRESTAPI($shop_id, $order_ids);
        // 		}catch(Exception $e){
        // 			$failed++;
        // 		}
        //    	}
    }

    public function FunctionName()
    {
        $orders = DB::table('new_orders')
            ->select('id')
            ->whereNotNull('remainder_date')
            ->whereRaw('remainder_date + interval 7 day = remainder_date')
            ->get()
            ->toArray();

        if (!empty($orders)) {
            NewOrder::whereIn('id', $orders)
                ->update([
                    'status' => 'inkasso'
                ]);
        }
    }

    //Mp agreement charge
    public function chargeMpAgreement($order_id)
    {
        try {
            $marketplaceStripe = new \App\Services\Stripe\MarketplaceStripe('stripe_key_2455');
            $marketplaceStripe->payment($order_id);
        } catch (Exception $pex) {
        }

        $this->sendRemainderAgreement($order_id);
    }


    // Remainder agreement payment due
    private function sendRemainderAgreement($order_id)
    {
          $hasPaymentAction = DB::connection('mysql::write')
          ->table('new_orders')
          ->where('id', $order_id)
          ->whereNull('intend_id')
          ->exists();

        if ($hasPaymentAction) {
          $this->send_remainder_email($order_id);

            // create new invoice with 15 surcharge- status: payment reminder, credit_ref: prev_invoice
            $this->create_surcharge_invoice($order_id);
        }
    }




    //Send remainder
    public function send_remainder_email($order_id, $channel = null)
    {
        try {
            $is_charge_email = false;
            $pay_url = null;
            $paywall = null;

            $data = [];
            $invoice_data = [];
            $order = NewOrder::find($order_id);
            $invoice_data['page_title'] = 'Order Invoice';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;

            if ($order->invoice_layout_id) {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')
                    ->where('cms_user_id', $order->cms_user_id)
                    ->where('id', $order->invoice_layout_id)
                    ->first();
            } else {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
            }

            if ($order->marketplace_order_ref && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                $invoice_data['shipping_details'] = DB::table("new_orders")
                    ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                    ->select("new_orders.id as mp_order_id", "new_customers.*")
                    ->where('new_orders.id', $order->marketplace_order_ref)->first();
            }

            $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            //Check if stripe keys exists
            $has_stripe_key = DB::table('stripe_keys')->where('user_id', $order->cms_user_id)->exists();

            $admin_url = config('global.admin_url');
            if ($order->insert_type == 4) {
                $paywall = MonthlyPaywall::where('order_id', $order->id)->whereNull('paid_at')->first();
                if ($paywall) {
                    $is_charge_email = true;
                    $pay_url = $admin_url . '/paywall-payment/' . $paywall->id;
                }
            } elseif ($has_stripe_key && ($order->total > 0)) {
                $due_status = ['nicht_bezahlt'];
                if (in_array($order->status, $due_status)) {
                    $is_charge_email = true;
                    $pay_url = $admin_url . '/invoice-payment/' . $order->id;
                }
            }

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => $pay_url,
                'PAYWALL' => $is_charge_email,
            ];

            // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){

            if ($order->marketplace_order_ref) {
                $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
            } else {
                $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
            }

            $tags['shipping_address'] = $shipping;
            $tags['credit_note'] = $order->credit_number;

            $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $order->cms_user_id)->pluck('signature', 'id')->toArray();

            if ($email_signatures) {
                foreach ($email_signatures as $key => $signature) {
                    $tags['drm-sign-' . $key] = $signature;
                }
            }
            // }

            $template = DRMParseRemainderEmailTemplate($tags, $order->cms_user_id, $channel);
            $data['email_to'] = $email_to = $invoice_data['customer']->email;
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            // TODO:: DROPMATIX
            $pdf_stream = (new \App\Services\Order\InvoiceDoc($order->id))->output();

            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }

                $messages->subject($data['subject']);
                $messages->attachData($pdf_stream, 'invoice.pdf', [
                    'mime' => 'application/pdf',
                ]);
            });

            //Unpaid order product delete warning email

            // Action only allow for Moritz(<EMAIL>) and Özgür (<EMAIL>) and Betül1(<EMAIL>) Account
            if(in_array($order->cms_user_id, [2956, 3445, 3624])){
            $this->productDeleteWarningEmail($order->id, 'mahnung');
            }

            if ($paywall) {
                MonthlyPaywall::whereNull('paid_at')->whereNull('start_at')->where('id', $paywall->id)->update(['start_at' => now()->addDays(7)]);
            }

            $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
            $log_message = 'Remainder email sent to: ' . $email_to . '' . $is_bcc . '. Status changed from ' . drmHistoryLabel($order->status) . ' to ' . drmHistoryLabel('mahnung') . ' successfully!';

            $order->update(['remainder_date' => now(), 'status' => 'mahnung']);
            updateOrderHistory($order, 'mahnung', $log_message);
        } catch (Exception $e) {
            Log::channel('command')->info($e->getMessage());
        }
    }


    //Get order supplier && Send to email
    public function getOrderSupplier($order_id)
    {

        $order = NewOrder::with('shop:id,lang,channel,password')->find($order_id);
        if (is_null($order)) return false;

        if(hasV2Access($order->cms_user_id))
        {
          $this->orderTransferToDropmatixV9($order_id);
          return;
        }

        if($order->credit_number > 0 || !empty($order->credit_ref)) return;

        if(in_array(strtolower($order->status), ['storniert', 'canceled', 'cancelled', 'shipped'])) return false;

        // Order already sent
        if(!empty($order->marketplace_order_ref)) return false;

        $can_send = $order->invoice_number && ($order->credit_number < 1) &&  $order->test_order != 1;
        if (!$can_send) return false;
        //Products
        $products = $order->products;
        if (is_null($products)) return false;

        //Channel
        $channel = collect($order->shop)->toArray();

        //Is automail active
        $supplier_mail_setting = DB::table('drm_supplier_mail')->where('cms_user_id', $order->cms_user_id)->select('auto_mail', 'auto_send_ftp')->first();
        $is_automail_active = (bool)$supplier_mail_setting->auto_mail;
        //send FTP file
        try {
            $user_has_ftp = DB::table('ftp_credentials')->where(['user_id' => $order->cms_user_id, 'is_i7o' => 1])->exists();
            $user_has_ftp_send = $user_has_ftp ? $supplier_mail_setting->auto_send_ftp : 0;
            if ($user_has_ftp_send) {
                app(\App\Services\FTP\OrderFTPService::class)->sendOrders([$order->id], $order->cms_user_id);
            }
        } catch (\Exception $ex) {
        }

        if(isset($_GET['test_v'])){
          dump('after ftp');
        }

        //Find supplier
        $product_collection = collect($products);

        $supplier_products = [];
        $carts = [];

        foreach ($product_collection as $product) {

            //Possible finding entry
            $find_args = [
                'ean' => $product->ean,
                'item_number' => $product->item_number,
                'title' => $product->product_name,
            ];
            $find_args = array_filter($find_args);

            /*
            Finding Database product
            EAN, Item number, title
            */
            $db_product = $this->findDbProduct($find_args, $order->cms_user_id, $channel);

            if (!empty($db_product)) {

                $supplier_id            = $db_product['supplier_id'];
                $db_product_id          = $db_product['product_id'];
                $marketplace_product_id = $db_product['marketplace_product_id'];

                //set product id
                $product->product_id                = $db_product_id;
                $product->marketplace_product_id    = $marketplace_product_id;
                $product->supplier_id               = $supplier_id;
                $product->item_number               = $db_product['item_number'];
                $product->delivery_days             = intval($db_product['delivery_days'] ?? 0);
                $product->ean                       = $db_product['ean'];
                $product->shipping_cost             = $db_product['shipping_cost'] ?? null;
                $product->product_name              = !empty($db_product['product_title']) ? $db_product['product_title'] : $product->product_name;
                $product->image                     = $db_product['image'] ?? null;

                //assign product
                if (!empty($supplier_id)) {
                    $supplier_products[$supplier_id][] = $product;
                }
            }

            $carts[] = $product;
        }

        $forcePaid = false;
        // check for Colizey order
        if ($order->shop->channel == 20) {
        //   if (!empty($carts)) {
        //     $forcePaid = true;

        //     app(\App\Services\OrderTracking\ColizeyTracking::class)->acceptOrder($order->shop, $order);
        //   } else { // no stock, reject order
        //     app(\App\Services\OrderTracking\ColizeyTracking::class)->rejectOrder($order->shop, $order);
        //   }
            if (!empty($carts)) {
                $forcePaid = true;

                try {
                    app(\App\Services\OrderTracking\ColizeyTracking::class)->acceptOrder($order->shop, $order);
                } catch (\Exception $e) {
                    // Handle the exception here, such as logging or displaying an error message
                    // For example:
                    // Log::error($e->getMessage());
                    // return response()->json(['error' => 'An error occurred while accepting the order'], 500);
                }
            } else { // no stock, reject order
                try {
                    app(\App\Services\OrderTracking\ColizeyTracking::class)->rejectOrder($order->shop, $order);
                } catch (\Exception $e) {
                    // Handle the exception here, such as logging or displaying an error message
                    // For example:
                    // Log::error($e->getMessage());
                    // return response()->json(['error' => 'An error occurred while rejecting the order'], 500);
                }
            }

        }

        //Update order cart & handling time
        if (!empty($carts)) {
            //Min delivery days
            $min_delivery_days = collect($carts)->min('delivery_days');
            $order_update = ['cart' => json_encode($carts)];

            if ($min_delivery_days) {
                $order_update['delivery_days'] = $min_delivery_days;
            }

            if($forcePaid)
            {
              $order_update['status'] = 'paid';
            }

            $order->update($order_update); //Update order
        }


        if(isset($_GET['test_v'])){
          dump('before paid status');
        }

        //Unpaid manual order
        if ($order->status === 'nicht_bezahlt') {
            return false;
        }

        if ($this->isDtUnpaidOrder($order)) {
            return false;
        }

        if ($this->isEbayUnpaidOrder($order)) {
            return false;
        }

        if(isset($_GET['test_v'])){
          dump('after paid status');
        }

        //Send order to suppliers
        $supplier_products = array_filter($supplier_products);
        if (is_array($supplier_products) && $supplier_products) {
            try {
                return $this->sendSupplierProduct($order, $supplier_products, $is_automail_active);
            } catch (Exception $ee) {
                return false;
            }
        }
    }

    private function getMarketplaceShippingCost($marketplace_product_id): float
    {
        return \App\Models\Marketplace\Product::where([
            'id' => $marketplace_product_id
        ])->value('shipping_cost') ?? 0;
    }

    //find datbase product
    private function findDbProduct($find_args, $user_id, $channel)
    {
        // Default values
        $lang = 'de';

        if (!empty($channel)) {
            $lang = $channel['lang'];
        }

        $lang = empty($lang) ? 'de' : $lang;
        $title_column = 'title->' . $lang;

        $query = DB::table('drm_products')
            ->where('user_id', '=', $user_id)
            ->select('marketplace_supplier_id', 'id', 'image', 'delivery_company_id', 'item_number', 'marketplace_product_id', 'ean', 'delivery_days', $title_column . ' as product_title', 'shipping_cost');

        // Find by ean, item number, or title
        if (!empty($find_args['ean'])) {
            $ean = $find_args['ean'];
            $query->where(function ($q) use ($ean) {
                $q->where('ean', $ean)->orWhereJsonContains('additional_eans', $ean);
            });
        } elseif (!empty($find_args['item_number'])) {
            $item_number = $find_args['item_number'];
            $query->where(function ($q) use ($item_number) {
                $q->where('item_number', $item_number)->orWhere('ean', $item_number)->orWhereJsonContains('additional_eans', $item_number);
            });
        } elseif (!empty($find_args['title'])) {
            $title = $find_args['title'];
            $utf8_title = strtolower(mb_convert_encoding($title, 'UTF-8', mb_list_encodings()));

            $query->where(function ($q) use ($title, $lang, $title_column, $utf8_title) {
                $q->where($title_column, $title)
                    ->orWhereRaw("TRIM(CONVERT(JSON_UNQUOTE(JSON_EXTRACT(`title` , '$.{$lang}')) using utf8)) LIKE '{$utf8_title}'");
            });
        } else {
            return [];
        }

        $product = $query->first();

        if (!empty($product)) {
            // $shipping_cost = $this->getMarketplaceShippingCost($product->marketplace_product_id);
            if(($product->image))
            return [
                'product_id' => $product->id,
                'supplier_id' => $product->delivery_company_id,
                'marketplace_product_id' => $product->marketplace_product_id,
                'ean' => $product->ean,
                'delivery_days' => $product->delivery_days,
                'shipping_cost' => $product->shipping_cost,
                'product_title' => trim($product->product_title),
                'item_number' => $product->item_number,


                'easy_price' => $this->checkEasyPrice($product->id),


                'image' => $this->checkEasyImage($product->image),
            ];
        }

        return [];
    }

    private function checkEasyImage($image) {
        $jsonArray = (is_array($image))? $image : json_decode($image, true);

        if($jsonArray && is_array($jsonArray)){
            $returnImage = reset($jsonArray);
            if(is_array($returnImage)) return $returnImage['src'];
            return $returnImage;
        }else return null;
    }


    private function findDbProduct1($find_args, $user_id, $channel)
    {
        //Shop
        $lang =  'de';
        $channel_id = $channel_type_id = null;

        if (!empty($channel)) {
            $lang = $channel['lang'];
            $channel_id = $channel['id'];
            $channel_type_id = $channel['channel'];
        }

        $lang = empty($lang) ? 'de' : $lang;
        $title_column = 'title->' . $lang;

        //Find by ean
        if (!empty($find_args['ean'])) {
            $ean = $find_args['ean'];

            $product = DB::table('drm_products')
                ->where('user_id', '=', $user_id)
                ->where(function ($q) use ($ean) {
                    return $q->where('ean', $ean)->orWhereJsonContains('additional_eans', $ean);
                })
                // ->whereNotNull('delivery_company_id')
                ->select('marketplace_supplier_id', 'id', 'image', 'item_number', 'delivery_company_id', 'marketplace_product_id', 'ean', 'delivery_days', $title_column . ' as product_title')
                ->first();

            if (!empty($product)) {
                // $shipping_cost = @channel_product_shipping_cost($product->id, $channel_type_id) ?? null;
                $shipping_cost = $this->getMarketplaceShippingCost($product->marketplace_product_id);
                return [
                    'product_id' => $product->id,
                    'supplier_id' => $product->delivery_company_id,
                    'marketplace_product_id' => $product->marketplace_product_id,
                    'ean' => $product->ean,
                    'delivery_days' => $product->delivery_days,
                    'shipping_cost' => $shipping_cost,
                    'product_title' => trim($product->product_title),
                    'easy_price'    => $this->checkEasyPrice($product->id),
                    'image' => $this->checkEasyImage($product->image),
                    'item_number' => $product->item_number,
                ];
            }
        }


        //find by item number
        if (!empty($find_args['item_number'])) {
            $item_number = $find_args['item_number'];
            $product = DB::table('drm_products')
                ->where('user_id', '=', $user_id)
                ->where(function ($q) use ($item_number) {
                    return $q->where('item_number', $item_number)->orWhere('ean', $item_number)->orWhereJsonContains('additional_eans', $item_number);
                })
                // ->whereNotNull('delivery_company_id')
                ->select('marketplace_supplier_id', 'id', 'image', 'item_number', 'delivery_company_id', 'marketplace_product_id', 'ean', 'delivery_days', $title_column . ' as product_title')
                ->first();

            if (!empty($product)) {
                // $shipping_cost = @channel_product_shipping_cost($product->id, $channel_type_id) ?? null;
                $shipping_cost = $this->getMarketplaceShippingCost($product->marketplace_product_id);

                return [
                    'product_id' => $product->id,
                    'supplier_id' => $product->delivery_company_id,
                    'marketplace_product_id' => $product->marketplace_product_id,
                    'ean' => $product->ean,
                    'delivery_days' => $product->delivery_days,
                    'shipping_cost' => $shipping_cost,
                    'product_title' => trim($product->product_title),
                    'easy_price'    => $this->checkEasyPrice($product->id),
                    'image' => $this->checkEasyImage($product->image),
                    'item_number' => $product->item_number,
                ];
            }
        }

        //Find by title
        if (!empty($find_args['title'])) {
            $title = $find_args['title'];
            $lang = empty($lang) ? 'de' : $lang;

            $product = DB::table('drm_products')
                ->where('user_id', '=', $user_id)
                ->where(function ($query) use ($title, $lang) {
                    $column = 'title->' . $lang; //Search column name
                    $utf8_title = strtolower(mb_convert_encoding($title, 'UTF-8', mb_list_encodings()));

                    $query->where($column, $title)
                        ->orWhereRaw("TRIM(CONVERT(JSON_UNQUOTE(JSON_EXTRACT(`title` , '$.{$lang}')) using utf8)) LIKE '{$utf8_title}'");
                })
                // ->whereNotNull('delivery_company_id')
                ->select('marketplace_supplier_id', 'id', 'image', 'item_number', 'delivery_company_id', 'marketplace_product_id', $title_column . ' as product_title', 'ean', 'delivery_days')
                ->first();

            if (!empty($product)) {
                // $shipping_cost = @channel_product_shipping_cost($product->id, $channel_type_id) ?? null;
                $shipping_cost = $this->getMarketplaceShippingCost($product->marketplace_product_id);

                return [
                    'product_id' => $product->id,
                    'supplier_id' => $product->delivery_company_id,
                    'marketplace_product_id' => $product->marketplace_product_id,
                    'ean' => $product->ean,
                    'delivery_days' => $product->delivery_days,
                    'shipping_cost' => $shipping_cost,
                    'product_title' => trim($product->product_title),
                    'easy_price'    => $this->checkEasyPrice($product->id),
                    'image' => $this->checkEasyImage($product->image),
                    'item_number' => $product->item_number,
                ];
            }
        }

        return [];
    }


    private function checkEasyPrice($product_id): bool
    {
        $calculation_id = ChannelProduct::where('drm_product_id', $product_id)->value('calculation_id') ?? 0;
        if ($calculation_id) {
            return (bool)ProfitCalculation::find($calculation_id)->default;
        }

        return false;
    }

    //Send delivery note with supplier note
    private function sendSupplierProduct(NewOrder $order, $products, $is_automail_active = false)
    {

      $mpCarts = collect($products)->flatten()->whereNotNull('marketplace_product_id')->toArray();
      $dropmatixSupplierCarts = collect($products)->flatten()->whereNull('marketplace_product_id')->whereIn('supplier_id', [50120,14392])->toArray();

      // Send order on email
      foreach ($products as $supplier_id => $carts) {
        $nonMpCart = collect($carts)->whereNull('marketplace_product_id')->toArray();

        //place order
        if (!empty($nonMpCart) && $is_automail_active) {
          $this->placeOrderProduct($order, $nonMpCart, $supplier_id);
        }
      }

      if(isset($_GET['test_v'])){
          dump('after nn mp place');
          dump($mpCarts);
      }

      //Create marketplace invoice
      if (!empty($mpCarts)) {
          $this->createMarketplaceSellinvoice($order, $mpCarts);
      }

      //Create marketplace invoice - Dropmatix
      if (!empty($dropmatixSupplierCarts)) {
          $this->createMarketplaceSellinvoiceDropmatix($order, $dropmatixSupplierCarts);
      }

      //Cache clear
      $this->ordersCacheClear($order->cms_user_id);

    }

    private function isDtUnpaidOrder(NewOrder $order)
    {
        $status = $order->status;
        $shoptype = $order->shop && $order->shop->channel ? $order->shop->channel : null;
        return (int)$shoptype === 10 && $status === 'unpaid';
    }

    private function isEbayUnpaidOrder(NewOrder $order)
    {
        $status = $order->status;
        $shoptype = $order->shop && $order->shop->channel ? $order->shop->channel : null;
        return (int)$shoptype === 4 && in_array($status, ['nicht_bezahlt', 'Active']);
    }

    private function mpAllowedCountry($mpProductId, $country, $shopId)
    {
        $countryShortCode = DB::table('tax_rates')
            ->where('country_code', 'like', $country)
            ->orWhere('country', 'like', $country)
            ->orWhere('country_de', 'like', $country)
            ->orWhere('country_es', 'like', $country)
            ->value('country_code');

        $countryId = DB::table('countries')->where('country_shortcut', $countryShortCode)->value('id');

        if (empty($countryId) || in_array($countryId, [11, 81, 83, 84])) return false;
        if (empty($shopId)) return true;

        $shop = \App\Shop::where('id', $shopId)->select('channel', 'user_id')->first();
        $channel = $shop->channel;
        $user_id = $shop->user_id;

        $flags = \App\Models\Marketplace\MarketplaceAllowedChannel::select('country_id', 'channel_id')
            ->where('product_id', $mpProductId)
            ->get();

        if (empty($flags) || blank($flags)) {
            $disable_channel = \App\Enums\Channel::MP_BLACKLIST;
            $channels = collect(config('channel.list'))->filter(function ($ch) use ($disable_channel, $user_id) {
                return !in_array($ch['type'], $disable_channel) || in_array($user_id, \App\Enums\Channel::MP_USER_WHITELIST[$ch['type']]);
            })
                ->pluck('type')
                ->toArray();
        } else {
            $channels = $flags->firstWhere('country_id', $countryId);
            if (empty($channels)) return false;
            $channels = $channels->channel_id;
        }

        return in_array($channel, $channels);
    }



    //Create Invoice on dropmatix for marketplace order - Dropmatix
    private function createMarketplaceSellinvoiceDropmatix(NewOrder $order, $marketplace_carts)
    {
        $activeUser = DB::table('cms_users')->where('id', $order->cms_user_id)->where('is_tester', '<>', 1)->exists();
        if (!$activeUser) return false;

        $is_amazon = $order->shop && (int)$order->shop->channel === 5;
        $order_id_api = $is_amazon ? $order->order_id_api : 'marketplace_' . $order->shop_id . 'u' . $order->cms_user_id . 'o' . $order->id;

        if(DB::connection('mysql::write')->table('new_orders')->where('cms_user_id', 2455)->where('order_id_api', $order_id_api)->where('shop_id', 440)->exists()) {
          return false;
        }

        if (empty($marketplace_carts)) return false;

        $shippingArr = json_decode($order->shipping, true);
        // Validate address

        // Previous invalid address, user need to change address
        if($order->address_confirm && (int)$order->address_confirm === 2)
        {
          return false;
        }

        // Address validation
        if(empty($order->address_confirm))
        {
          try {
            app(\App\Services\UiValidation\UiValidation::class)->validateAddress($shippingArr);

            // STORE_ORDER_STATUS_ON_DATABASE
            DB::table('new_orders')->where('id', $order->id)->update(['address_confirm' => 1]);

          } catch (\Exception $e) {
            DB::table('new_orders')->where('id', $order->id)->update(['status' => 'zustellproblem', 'address_confirm' => 2]);
            updateOrderHistory($order, 'invalid_shipping_address', $e->getMessage());
            $this->sendDeliveryProblemNotification($order, $e->getMessage());
            return false;
          }
        }

        $marketplace_ids = collect($marketplace_carts)->pluck('product_id')->toArray();
        $marketplace = DB::table('drm_products')->whereIntegerInRaw('id', $marketplace_ids)
            ->select('ek_price', 'id', 'image', 'delivery_company_id', 'shipping_cost', 'marketplace_shipping_method')
            ->get();
        if (empty($marketplace)) return false;

        $marketplace_price_info = $marketplace->pluck('ek_price', 'id')->toArray();
        $marketplace_shipping_cost = $marketplace->pluck('shipping_cost', 'id')->toArray();
        $marketplace_supplier_info = $marketplace->pluck('delivery_company_id', 'id')->toArray();
        $marketplace_shipping_info = $marketplace->pluck('marketplace_shipping_method', 'id')->toArray();

        $dropmatix_id = 2455;
        $client_id = (int)$order->cms_user_id;

        $paymentTax = new PaymentTax($client_id, $dropmatix_id);
        $vat_number = $paymentTax->vatNumber();
        $tax_rate = $paymentTax->taxRate();

        $shipping_cost = 0;

        $carts = [];
        foreach ($marketplace_carts as $cart) {

            $product_id = $cart->product_id;
            if (!isset($marketplace_price_info[$product_id])) continue;

            $rate = $marketplace_price_info[$product_id];
            $qty = $cart->qty ?? 1;
            $amount = $rate * $qty;

            $cart->supplier_id = $marketplace_supplier_info[$product_id] ?? null;
            $cart->shipping_method = $marketplace_shipping_info[$product_id] ?? null;

            $cart->tax = $tax_rate;
            $cart->rate = $rate;
            $cart->amount = $amount;

            if (isset($marketplace_shipping_cost[$product_id]) && $item_shipping_cost = $marketplace_shipping_cost[$product_id] ) {
                $shipping_cost += (($item_shipping_cost ?? 0) * $qty);
            }

            $carts[] = (array)$cart;
        }


        if (empty($carts)) return false;

        $total_amount = collect($carts)->sum('amount');

        $shipping_discount = 0;
        // $mp_carts = collect($carts)->whereNotNull('mp_supplier_id');
        // if(!blank($mp_carts)){
        //     $adminDrmImportClass = app(\App\Http\Controllers\AdminDrmImportsController::class);
        //     $checkUserTariff = $adminDrmImportClass->checkIsProfessionalOrEnterprice($order->cms_user_id);
        //     if (!blank($checkUserTariff)) {
        //         $is_professional = $checkUserTariff->import_plan_id == 26 ? true : false;
        //         $shipping_discount = $this->shippingCostDiscountCalculation($mp_carts, $is_professional);
        //     } else {
        //         $trial_remain_days = $adminDrmImportClass->checkImportTrial($order->cms_user_id);
        //         if ($trial_remain_days > 0) {
        //             $shipping_discount = $this->shippingCostDiscountCalculation($mp_carts, false);
        //         }
        //     }
        // }

        //Price calculation
        $total = $total_amount + $shipping_cost - $shipping_discount;
        $sub_price = $total_amount;
        $discount = 0;
        $total_tax = 0;

        if ($tax_rate) {
            $total_tax = (($total * $tax_rate) / 100);
            $total = $total + $total_tax;
        }

        //Generate customer info
        $order_info = [
            'user_id' => $dropmatix_id,
            'cms_client' => $client_id,
            'order_date' => $order->order_date,
            'total' => round(($total), 2),
            'sub_total' => round($sub_price, 2),
            'discount' => round($discount, 2),
            'discount_type' => 'fixed',
            'total_tax' => round($total_tax, 2),
            'payment_type' => null,
            'status' => "nicht_bezahlt",
            'currency' => "EUR",
            'adjustment' => 0,
            'insert_type' => 8,
            'shop_id' => 440,
            'order_id_api' => $order_id_api,
            'invoice_date' => $order->invoice_date,
            'carts' => $carts,
            'marketplace_order_ref' => $order->id,
            'shipping' => $order->shipping,

            'tax_rate' => $tax_rate,
            'vat_number' => $vat_number ?? null,
            'tax_version' => 1,
            'shipping_discount' => $shipping_discount,
        ];

        if ($shipping_cost) {
            $order_info['shipping_cost'] = $shipping_cost;
        }

        return $this->insertMarketplaceOrder($order_info, $client_id);
    }


    //Create Invoice on dropmatix for marketplace order
    private function createMarketplaceSellinvoice(NewOrder $order, $marketplace_carts)
    {
        $activeUser = DB::table('cms_users')->where('id', $order->cms_user_id)->where('is_tester', '<>', 1)->exists();
        if (!$activeUser) return false;

        $is_amazon = $order->shop && (int)$order->shop->channel === 5;
        $order_id_api = $is_amazon ? $order->order_id_api : 'marketplace_' . $order->shop_id . 'u' . $order->cms_user_id . 'o' . $order->id;

        $client_id = (int)$order->cms_user_id;
        $dropmatix_id = in_array($client_id, User::PATRICK_SPECIAL_USERS) ? User::LAYAN_ACCOUNT_ID : User::DROPMATIX_ACCOUNT_ID;

        if(DB::connection('mysql::write')->table('new_orders')->where('cms_user_id', $dropmatix_id)->where('order_id_api', $order_id_api)->where('shop_id', $dropmatix_id == 2455 ? 440 : 1477)->exists()) {
          return false;
        }

        if (empty($marketplace_carts)) return false;

        $shippingArr = json_decode($order->shipping, true);
        // Validate address

        // Previous invalid address, user need to change address
        if($order->address_confirm && (int)$order->address_confirm === 2)
        {
          return false;
        }

        // Address validation
        if(empty($order->address_confirm))
        {
          try {
            app(\App\Services\UiValidation\UiValidation::class)->validateAddress($shippingArr);

            // STORE_ORDER_STATUS_ON_DATABASE
            DB::table('new_orders')->where('id', $order->id)->update(['address_confirm' => 1]);

          } catch (\Exception $e) {
            DB::table('new_orders')->where('id', $order->id)->update(['status' => 'zustellproblem', 'address_confirm' => 2]);
            updateOrderHistory($order, 'invalid_shipping_address', $e->getMessage());
            $this->sendDeliveryProblemNotification($order, $e->getMessage());
            return false;
          }
        }

        $marketplace_ids = collect($marketplace_carts)->pluck('marketplace_product_id')->toArray();

        $skippedCountryCheck = isset($_GET['stdfgyttr46565']);

        // Product prohibited country
        try {
          $shippingCountry = $shippingArr['country'] ?? 'de';
          foreach($marketplace_ids as $mpProductId)
          {
            if(!$skippedCountryCheck && !$this->mpAllowedCountry($mpProductId, $shippingCountry, $order->shop_id) ) throw new \Exception('Address outside the agreed delivery areas. Please change to a permitted destination country');
          }
        } catch (Exception $e) {
          DB::table('new_orders')->where('id', $order->id)->update(['status' => 'zustellproblem']);
          createNewOrderLog($order, 'mp_prohibited', $e->getMessage());
          $this->sendDeliveryProblemNotification($order, $e->getMessage());
          return false;
        }

        $marketplace = DB::connection('marketplace')->table('marketplace_products')->whereIntegerInRaw('id', $marketplace_ids)
            ->select('vk_price', 'id', 'delivery_company_id', 'ek_price')
            ->get();
        if (empty($marketplace)) return false;

        $products_ids = collect($marketplace_carts)->pluck('product_id')->toArray();
        $coreProducts = DB::table('drm_products')
        ->whereIntegerInRaw('id', $products_ids)
        ->select('ek_price', 'id', 'marketplace_shipping_method')
        ->get();

        if (empty($coreProducts)) return false;


        $coreProductsPrice = $coreProducts->pluck('ek_price', 'id')->toArray();
        $marketplace_shipping_info = $coreProducts->pluck('marketplace_shipping_method', 'id')->toArray();

        if (empty($coreProductsPrice)) return false;

        $marketplace_price_info = $marketplace->pluck('vk_price', 'id')->toArray();
        $marketplace_supplier_info = $marketplace->pluck('delivery_company_id', 'id')->toArray();

        // $client_id = (int)$order->cms_user_id;
        // $dropmatix_id = in_array($client_id, User::PATRICK_SPECIAL_USERS) ? 4219 : 2455;

        $paymentTax = new PaymentTax($client_id, User::DROPMATIX_ACCOUNT_ID);
        $vat_number = $paymentTax->vatNumber();
        $tax_rate = $paymentTax->taxRate();

        $shipping_cost = 0;

        $carts = [];
        foreach ($marketplace_carts as $cart) {

            $core_product_id = $cart->product_id;
            if (!isset($coreProductsPrice[$core_product_id])) continue;

            $marketplace_product_id = $cart->marketplace_product_id;
            if (!isset($marketplace_price_info[$marketplace_product_id])) continue;

            $rate = $coreProductsPrice[$core_product_id];
            $qty = $cart->qty ?? 1;
            $amount = $rate * $qty;

            $cart->mp_supplier_id = $marketplace_supplier_info[$marketplace_product_id] ?? null;

            if($client_id == 2698 && $cart->mp_supplier_id == 50131)
            {

              $cart->rate = 0;
              $cart->amount = 0;

            } else {

              $cart->rate = $rate;
              $cart->amount = $amount;

            }

            $cart->tax = $tax_rate;

            $cart->shipping_method = $marketplace_shipping_info[$core_product_id] ?? null;

            if (isset($cart->shipping_cost)) {
                $item_shipping_cost = $cart->shipping_cost ?? 0;
                $shipping_cost += ($item_shipping_cost * $qty);
            }

            $carts[] = (array)$cart;
        }


        if (empty($carts)) return false;

        $total_amount = collect($carts)->sum('amount');

        $shipping_discount = 0;
        // $mp_carts = collect($carts)->whereNotNull('mp_supplier_id');
        // if(!blank($mp_carts)){
        //     $adminDrmImportClass = app(\App\Http\Controllers\AdminDrmImportsController::class);
        //     $checkUserTariff = $adminDrmImportClass->checkIsProfessionalOrEnterprice($order->cms_user_id);
        //     if (!blank($checkUserTariff)) {
        //         $is_professional = $checkUserTariff->import_plan_id == 26 ? true : false;
        //         $shipping_discount = $this->shippingCostDiscountCalculation($mp_carts, $is_professional);
        //     } else {
        //         $trial_remain_days = $adminDrmImportClass->checkImportTrial($order->cms_user_id);
        //         if ($trial_remain_days > 0) {
        //             $shipping_discount = $this->shippingCostDiscountCalculation($mp_carts, false);
        //         }
        //     }
        // }

        //Price calculation
        $total = $total_amount + $shipping_cost - $shipping_discount;
        $sub_price = $total_amount;
        $discount = 0;
        $total_tax = 0;

        if ($tax_rate) {
            $total_tax = (($total * $tax_rate) / 100);
            $total = $total + $total_tax;
        }

        //Generate customer info
        $order_info = [
            'user_id' => $dropmatix_id,
            'cms_client' => $client_id,
            'order_date' => $order->order_date,
            'total' => round(($total), 2),
            'sub_total' => round($sub_price, 2),
            'discount' => round($discount, 2),
            'discount_type' => 'fixed',
            'total_tax' => round($total_tax, 2),
            'payment_type' => null,
            'status' => "nicht_bezahlt",
            'currency' => "EUR",
            'adjustment' => 0,
            'insert_type' => 8,
            'shop_id' => $dropmatix_id == User::LAYAN_ACCOUNT_ID ? 1477 : 440,
            'order_id_api' => $order_id_api,
            'invoice_date' => $order->invoice_date,
            'carts' => $carts,
            'marketplace_order_ref' => $order->id,
            'shipping' => $order->shipping,

            'tax_rate' => $tax_rate,
            'vat_number' => $vat_number ?? null,
            'tax_version' => 1,
            'shipping_discount' => $shipping_discount,
        ];

        if ($shipping_cost) {
            $order_info['shipping_cost'] = $shipping_cost;
        }

        return $this->insertMarketplaceOrder($order_info, $client_id);
    }

    private function sendDropmatixOrderToSupplier($order)
    {
      if(!($order->insert_type == 8 && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID]))) return;

      if(!($order->marketplace_paid_status == 1 || !empty($order->mp_payment_agreement_at) )) return;

      if(DB::connection('mysql::write')->table('order_logs')->where('order_id', $order->id)->where('payload->status', 'Shipped')->exists()) return;

        collect($order->products)->whereNotNull('mp_supplier_id')
            ->groupBy('mp_supplier_id')
            ->each(function ($carts, $supplier_id) use ($order) {

                $mp_supplier_list = config('global.mp_api_suppliers', []);
                if (in_array($supplier_id, $mp_supplier_list)) {
                    //Send internel order to drm
                    send_order_drm_to_internel($order->id);
                } else {
                    if($order->cms_client != 4280){
                        $this->placeOrderProduct($order, $carts, $supplier_id);
    
                        //Fulfilment product order transfer
                        if(Product::whereIn('id',collect($carts)->pluck('marketplace_product_id')->toArray())->where('shipping_method',2)->exists()){
                            send_order_drm_to_internel($order->id);
                        }
                    }
                }
            });

      // Send MP order product - Supplier
      collect($order->products)->whereNotNull('supplier_id')->whereIn('supplier_id', [50120,14392])
        ->groupBy('supplier_id')
        ->each(function ($carts, $supplier_id) use ($order) {
          $this->placeOrderProduct($order, $carts, $supplier_id);
        });
    }

    //User billing address to update customer profile
    private function insertMarketplaceOrder($order_info, $user_id, $order_return = false)
    {
        $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->userBillingToCustomerProfile($user_id, $order_info['user_id']);

        if (!is_null($customer_id)) {
            $customer = NewCustomer::find($customer_id);

            if ($customer) {
                $order_info['drm_customer_id'] = $customer_id;

                $order_info['customer_info'] = customerToCustomerInfoJson($customer);
                $order_info['billing'] = $customer->billing;
                $order_info['tax_number'] = $customer->tax_number;

                // TODO:: DROPMATIX
                // $order_info['shipping'] = $customer->shipping;

                $new_order = $this->insertMarketplaceOrderToDatabase($order_info);
                if ($new_order) {
                    return ($order_return) ? $new_order : true;
                }
            }
        }
        return false;
    }

    private function sendMpOrderSMS($order_id)
    {
        // try {

        //     $message = "We have a new Marketplace order. Dropmatix ID: {$order_id}";
        //     $query = http_build_query([
        //         'message_content' => $message,
        //     ]);
        //     $url = 'http://***************/sms-send?' . $query;

        //     $curl = curl_init();
        //     curl_setopt_array($curl, array(
        //         CURLOPT_URL => $url,
        //         CURLOPT_RETURNTRANSFER => true,
        //         CURLOPT_ENCODING => '',
        //         CURLOPT_MAXREDIRS => 10,
        //         CURLOPT_TIMEOUT => 0,
        //         CURLOPT_FOLLOWLOCATION => true,
        //         CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        //         CURLOPT_CUSTOMREQUEST => 'POST',
        //     ));

        //     $response = curl_exec($curl);
        //     curl_close($curl);
        // } catch (\Exception $e) {
        // }
    }

    //insert marketplace order to database
    private function insertMarketplaceOrderToDatabase($order_info)
    {
        $validator = Validator::make($order_info, [
            'user_id' => 'required',
            'shop_id' => 'required',
            'order_id_api' => 'required',
            'drm_customer_id' => 'required',
            'marketplace_order_ref' => 'required'
        ]);
        if ($validator->fails()) {
            return null;
        }


        /* ----------------- Check duplication ------------------ */
        $check['cms_user_id'] = $cms_user_id = $order_info['user_id'];
        $check['order_id_api'] = $order_id_api = $order_info['order_id_api'];
        if (DB::connection('mysql::write')->table('new_orders')->where($check)->count() > 0) return false;

        $check['shop_id'] = $shop_id = $order_info['shop_id'];
        if (DB::connection('mysql::write')->table('new_orders')->where($check)->count() > 0) return false;

        //Extra layer of duplication check
        if (DB::connection('mysql::write')->table('new_orders')->where(['order_id_api' => $order_id_api, 'cms_user_id' => $cms_user_id, 'shop_id' => $shop_id])->count() > 0) {
            return false;
        }

        // Invoice number generator start
        $inv_setting = DB::connection('mysql::write')->table('drm_invoice_setting')->where('cms_user_id', $check['cms_user_id'])->orderBy('id', 'desc')->select('start_from_1', 'start_invoice_number', 'suffix')->first();
        $start_from_1 = (bool)$inv_setting->start_from_1;
        $inv_suffix = $inv_setting->suffix;

        $last_order_item = DB::connection('mysql::write')->table('new_orders')->where('cms_user_id', $check['cms_user_id'])->where('invoice_number', '!=', -1)->where('credit_number', 0);

        if ($start_from_1) {
            $last_order_item->whereYear('created_at', date('Y'));
        }

        $inv1 = $last_order_item->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
        $inv2 = $inv_setting->start_invoice_number;
        $invoice_number = ($start_from_1) ? $inv1 : (($inv1 > $inv2) ? $inv1 : $inv2);


        //Extra layer of duplication check
        $inv_used_count = DB::connection('mysql::write')->table('new_orders')->where(['cms_user_id' => $cms_user_id, 'invoice_number' => $invoice_number])->where('credit_number', 0);
        if ($start_from_1) {
            $inv_used_count->whereYear('created_at', date('Y'));
        }
        $inv_used_count = $inv_used_count->count();

        if ($inv_used_count > 0) {
            User::find(71)->notify(new DRMNotification('DUPLICATE INV NUBMER TRY: ' . inv_number_string($invoice_number, $inv_suffix) . ' USER ID: ' . $cms_user_id . ' SHOP_ID: ' . $shop_id . ' API_ID: ' . $order_id_api, 'DUPLICATE_INV_TRY'));
            return false;
        }
        //Invoice number generator end


        /* -------------- Status ------------------- */
        $status = $order_info['status'] ?? "nicht_bezahlt";

        /* ------------------ insert order ----------------- */
        $row['invoice_number'] = $invoice_number;
        $row['invoice_date'] = $order_info['invoice_date'];

        $row['order_date'] = $order_info['order_date'];
        $row['insert_type'] = $order_info['insert_type'];

        $status = drmOrderLabelByGroupId($status);

        if (strpos($order_info['total'], ",")) {
            $have = [".", ","];
            $will_be = ["", "."];
            $order_info['total'] = str_replace($have, $will_be, $order_info['total']);
        }
        $row['total'] = $total = abs($order_info['total']);
        $row['sub_total'] = abs($order_info['sub_total']);
        $row['total_tax'] = abs($order_info['total_tax']);

        $row['drm_customer_id'] = $drm_customer_id = $order_info['drm_customer_id'];

        $row['discount'] = abs($order_info['discount']);
        $row['discount_type'] = $order_info['discount_type'];
        $row['adjustment'] = $order_info['adjustment'];
        $row['payment_type'] = $order_info['payment_type'];
        $row['currency'] = $order_info['currency'];
        $row['shipping_cost'] = abs($order_info['shipping_cost']);
        $row['customer_info'] = $order_info['customer_info'];
        $row['billing'] = $order_info['billing'];
        $row['shipping'] = $order_info['shipping'];

        $row['client_note'] = $order_info['client_note'];
        $row['status'] = $status;
        $row['cms_client'] = $order_info['cms_client'];
        $row['payment_status'] = $order_info['payment_status'] ?? null;

        $row['eur_total'] = $order_info['eur_total'] ?? $total;
        $row['currency_rate'] = $order_info['currency_rate'] ?? 1;

        $row['cart'] = $carts_json = json_encode($order_info['carts']);
        $row['shipping_discount'] = $order_info['shipping_discount'];




        $row['inv_pattern'] = drm_invoice_number_format($invoice_number, $inv_suffix);
        $row['marketplace_order_ref'] = $order_info['marketplace_order_ref'];

        //TAX NUMBER
        $row['tax_rate'] = $order_info['tax_rate'];
        $row['vat_number'] = $order_info['vat_number'];
        $row['tax_version'] = $order_info['tax_version'];

        $row['tax_number'] = $order_info['tax_number'] ?? null;

        if (DB::table('new_orders')->where(['cms_user_id' => $cms_user_id, 'order_id_api' => $order_id_api, 'total' => $total, 'drm_customer_id' => $drm_customer_id])->exists()) {
            User::find(71)->notify(new DRMNotification('DUPLICATE ORDER TRY USER ID: ' . $cms_user_id . ' SHOP_ID: ' . $shop_id . ' API_ID: ' . $order_id_api, 'DUPLICATE_ORDER_TRY'));
            return false;
        }

        // Calculate dropmatix amount
        $row['dropmatix_sub_total'] = $row['sub_total'];
        $row['dropmatix_total_tax'] = $row['total_tax'];
        $row['dropmatix_discount'] = $row['discount'];
        $row['dropmatix_shipping_cost'] = $row['shipping_cost'];
        $row['dropmatix_tax_rate'] = $row['tax_rate'];
        // Calculate dropmatix amount end

        $clientOrderData = [];

        if($row['eur_total'] > 0)
        {
          $mpAgreement = new \App\Services\DRM\MpAgreement($row['cms_client']);
          $chargeLater = $mpAgreement->canSendImmediately($row['eur_total']);
          if ($chargeLater) {
            $row['mp_payment_agreement_at'] = now();
            $row['status'] = 'unpaid_payment_target';
          }

        } else {

          $clientOrderData['marketplace_paid_status'] = 1;
          $row['marketplace_paid_status'] = 1;
          $row['status'] = 'paid';
          $row['payment_type'] = 'DRM';
          $row['payment_date'] = now();
          $chargeLater = true;
        }

        // Duplicate check again
        if (DB::connection('mysql::write')->table('new_orders')->where($check)->count() > 0) return false;

        $order = NewOrder::updateOrCreate($check, $row); //Insert order
        if (empty($order)) return false;

        $chargeAmount = $row['eur_total'];
        // MP wallet payment
        $total_credit_amount = MpVirtualCredit::where('user_id', $row['cms_client'])->sum('amount') ?? 0;
        if(!$chargeLater && $row['marketplace_paid_status'] != 1 && $chargeAmount > 0 && $total_credit_amount > $chargeAmount &&
            $this->virtualPayment($order->cms_client, $order->id, $chargeAmount, $total_credit_amount - $chargeAmount))
        {
            $clientOrderData['marketplace_paid_status'] = 1;
            $chargeLater = true;
        }

        $clientOrderData['marketplace_order_ref'] = $order->id;
        $clientOrderData['supplier_id'] = $this->getSupplierByCustomerId($order_info['cms_client'])->id;
        $clientOrderData['mp_payment_agreement_at'] = $order->mp_payment_agreement_at;
        DB::table('new_orders')->where('id', $order_info['marketplace_order_ref'])->update($clientOrderData);

        //Log History
        try {
            drmOrderFirstHistory($order);
        } catch (Exception $eee) {
        }

        //Marketplace order payment
        if (!$chargeLater) {
            try {
                $marketplaceStripe = new \App\Services\Stripe\MarketplaceStripe($order->cms_user_id == 4219 ? 'stripe_key_4219' : 'stripe_key_2455');
                $marketplaceStripe->payment($order->id);


            } catch (Exception $pex) {
            }
        }

        $order = NewOrder::find($order->id); //Query again for history
        $this->sendDropmatixOrderToSupplier($order);

        $send_status_mail = true;
        $send_order_email = true;

        //Send order mail
        try {
            // if(isLocal() || in_array($check['cms_user_id'], [212, 2592])){

            $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

            $channel_order_auto_mail = DB::table('drm_order_mail')
                ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                ->first();

            if ($send_order_email && $channel_order_auto_mail) {

                if ($channel_order_auto_mail->auto_mail) {
                    app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id, $channel);
                    $send_status_mail = false;
                }
            } else if ($send_order_email && DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'])->whereNull('channel')->value('auto_mail')) {
                app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
                $send_status_mail = false;
            }

            // }else{
            // if ($send_order_email && DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'])->value('auto_mail')) {
            //     app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
            //     $send_status_mail = false;
            // }
            // }
        } catch (Exception $exv) {
        }


        //Send status mail
        if ($send_status_mail && $order->cms_client != 4280) {
            send_order_email($order->id);
        }


        // Send push notification
        (new \App\Services\Notification\PushNotifySell)->send($order);

        app('App\Http\Controllers\NewShopSyncController')->insertCustomerTag($order, $drm_customer_id);

        try {
            $this->sendMpOrderSMS($order->id);
            if($order->cms_client != 4280) app(MPSupplierOrder::class)->createSuppliersOrders($order);
        } catch (\Exception $e) {
        }


        return true;
    }

    private function placeOrderProduct(NewOrder $order, $carts, $supplier_id)
    {
        try {
            $status = 'Order übertragen';
            $new_val = 'order_placed';


            if ($supplier_id) {

                //Dropmatix carnot supplied replace with Wohlauf carnot supplier
                $supplier_id = (int) $supplier_id === 14383 ? 13040 : $supplier_id;
                if ((int)$supplier_id === 13040 && (int)$order->cms_user_id === 2455) return false;

                $supplier = DeliveryCompany::find($supplier_id);
                if (is_null($supplier)) throw new Exception('Invalid supplier!');
                $supplier_name = $supplier->name;

                $supplier_user_id = (int)$supplier_id === 13040 ? $supplier->user_id : $order->cms_user_id;

                $data = [];
                $data['page_title'] = 'Delivery Note';
                $data['order'] = $order;
                $data['product_list'] = $product_list = $carts;
                $data['customer'] = $customer = $order->customer;

                //                $data['setting'] = $invoice_data = DB::table('delivery_note_settings')->where('cms_user_id', $supplier_user_id)->orderBy('id', 'desc')->first();
                $data['setting'] = $invoice_data = $this->getDelveriNoteTemplate($order, $supplier_user_id);


                // if (is_null($invoice_data)) throw new Exception("Invoice setting required for sending Email.");
                $item_numbers = collect($product_list)->pluck('item_number')->toArray();

                if ($order->marketplace_order_ref) {
                    $data['shipping_details'] = DB::table("new_orders")
                        ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                        ->select("new_orders.id as mp_order_id", "new_customers.*")
                        ->where('new_orders.id', $order->marketplace_order_ref)->first();
                } else if ($order->credit_number && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                    $marketplace_credit = DB::table('new_orders')->where('credit_ref', '=', $order->id)->value('marketplace_order_ref');

                    if ($marketplace_credit->marketplace_order_ref) {
                        $data['shipping_details'] = DB::table("new_orders")
                            ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                            ->select("new_orders.id as mp_order_id", "new_customers.*")
                            ->where('new_orders.id', $marketplace_credit->marketplace_order_ref)->first();
                    }
                }
                //Marketplace credit end

                //Generate product send table
                $html_table = '<div class="table-responsive"><table class="table table-bordered table-striped"><thead><tr><th>ID</th><th>Name</th><th>Item Number</th></tr></thead><tbody>';
                foreach ($carts as $cart_single) {
                    $product_url = 'https://drm.software/admin/drm_products/detail/' . $cart_single->product_id;
                    $html_table .= '<tr><td>' . $cart_single->product_id . '</td><td>' . $cart_single->product_name . '</td><td><a href="' . $product_url . '">' . $cart_single->item_number . '</a></td></tr>';
                }
                $html_table .= '</tbody></table></div>';

                $pdf_stream = PDF::loadView('admin.invoice.delivery_note', $data)->setWarnings(false)->stream();

                $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

                if (isset($data['shipping_details'])) {
                    $shipping = '<p>' . formatBillingAddress($data['shipping_details']->shipping) . '</p>';
                } else {
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $logo = ($invoice_data->logo) ? $invoice_data->logo : '';
                $tags = [
                    'customer_name' => $customer->full_name,
                    'company_name' => $customer->company_name,
                    'billing_address' => $billing,
                    'shipping_address' => $shipping,
                    'order_items' => view('admin.new_order.email_supplier_order_items', compact('product_list', 'order'))->render(),
                    'order_items_n_article' => view('admin.new_order.email_supplier_order_items_n_article', compact('product_list', 'order'))->render(),
                    'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                    'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data->store_name . '" >',
                    'order_number' => $order->id,
                    'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                    'contact_name' => $supplier_name,
                    'pay_url' => null,
                    'PAYWALL' => false,
                    'IS_DROPSHIPPING'   => true,
                    'IS_FULFILLMENT'    => false,
                ];

                // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){

                $tags['credit_note'] = $order->credit_number;

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $order->cms_user_id)->pluck('signature', 'id')->toArray();

                if ($email_signatures) {
                    foreach ($email_signatures as $key => $signature) {
                        $tags['drm-sign-' . $key] = $signature;
                    }
                }
                // }

                if (strpos($order->order_id_api, "mfull_")) {
                    $tags['IS_DROPSHIPPING']    = false;
                    $tags['IS_FULFILLMENT']     = true;
                    $tags['shipping_address']   = 'XXXXXXXXXX';
                }

                // if(isLocal() || in_array($order->cms_user_id, [212, 2592])){

                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                $channel_supplier_mail = DB::table('drm_supplier_mail')
                    ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                    ->exists();

                if ($channel_supplier_mail) {
                    $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id, $channel);
                } else {
                    $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id);
                }

                // }else{
                // $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id);
                // }

                $data['email_to'] = $email_to = $supplier->order_email ?? $supplier->email;

                $data['email_from'] = $template['senderEmail'];
                $data['subject'] = $template['subject'];
                $data['bcc'] = $template['bcc'];

                if($supplier->id == 49997) $data['bcc'] = '<EMAIL>';

                if (is_null($template['body'])) throw new Exception('Supplier email setting required for sending Delivery Note.');

                if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                    throw new Exception("Something Wrong! Email Not Sent!.");
                }

                // $note_name = 'delivery_note_' . $supplier_name . '_' . $order->invoice_number;
                // $note_name = preg_replace('/\s+/', '_', $note_name);
                // $note_name = preg_replace('/[_]+/', '_', $note_name);
                $note_name = $order->id . '.pdf';

                app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_supplier', $template, function ($messages) use ($data, $pdf_stream, $note_name) {
                    // $messages->from($data['email_from']);
                    $messages->to($data['email_to']);
                    $messages->subject($data['subject']);

                    if ($data['bcc']) {
                        $messages->bcc($data['bcc']);
                    }

                    $messages->attachData($pdf_stream, $note_name, [
                        'mime' => 'application/pdf',
                    ]);
                });

                $is_bcc = $data['bcc'] ? ' BCC: ' . $data['bcc'] : '';
                $order->update(['supplier_id' => $supplier_id, 'supplier_time' => now(), 'supplier' => 'checked', 'status' => $new_val]);

                $log_message = 'Order placed Successfully! Email: ' . $email_to . ' (' . $supplier_name . ')' . $is_bcc . ' Items: ' . implode(', ', $item_numbers) . $html_table;
                updateOrderHistory($order, $new_val, $log_message);

                //send FTP file
                try {
                    if ((int) $supplier_id === 13040) {
                        app(\App\Services\FTP\OrderFTPService::class)->sendOrders([$order->id], $supplier_user_id);
                    }
                } catch (\Exception $ex) {
                }

                $log_data = [];
                $log_data['product_list'] = $product_list;
                $log_data['tags'] = $tags;
                $log_data['order_id'] = $order->id;
                $supplier->logs()->create([
                    'type' => 1,
                    'data' => $log_data,
                    'message' => $log_message,
                ]);

                return [
                    'success' => true,
                    'message' => 'Order placed Successfully! Email: ' . $email_to
                ];
            } else {
                throw new Exception("Supplier can not be empty!");
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Order placed failed! Error: ' . $e->getMessage()
            ];
        }
    }
    //Auto order delivery end


    public function getMarketplaceOrderSupplier($order_id)
    {
        // $order_id = null; //15046;
        $DROPMATRIX_ID = 2455;

        $order = NewOrder::find($order_id);
        if (is_null($order)) return false;

        //Products
        $products = $order->products;
        if (is_null($products)) return false;

        //Find supplier
        $product_collection = collect($products); //->whereNotNull('item_number');

        $supplier_products = [];

        foreach ($product_collection as $product) {
            $item_number = $product->ean;

            $db_product = DB::table('drm_products')->where('user_id', '=', $order->cms_user_id)->where('ean', $item_number)->whereNotNull('marketplace_product_id')->select('marketplace_supplier_id', 'id')->first();

            if ($db_product && $db_product->marketplace_supplier_id) {
                $supplier_id = $db_product->marketplace_supplier_id;

                if ($supplier_id == null) {
                    $supplier_id = $DROPMATRIX_ID;
                }

                //set product id
                $product->product_id = $db_product->id;

                //assign product
                $supplier_products[$supplier_id][] = $product;
            }
        }

        $supplier_products = array_filter($supplier_products);

        if (is_array($supplier_products) && $supplier_products) {
            try {
                return $this->sendmarketplaceSupplierProduct($order, $supplier_products);
            } catch (Exception $ee) {
                return false;
            }
        }
    }

    //Send delivery note with supplier note
    private function sendmarketplaceSupplierProduct(NewOrder $order, $products)
    {
        foreach ($products as $supplier_id => $carts) {
            $this->placeMarketplaceOrderProduct($order, $carts, $supplier_id);
            $this->ordersCacheClear($order->cms_user_id);
        }
    }

    private function placeMarketplaceOrderProduct(NewOrder $order, $carts, $supplier_id)
    {
        try {
            $status = 'Order übertragen';
            $new_val = 'order_placed';

            if ($order->cms_user_id != 2494) return false;

            if ($supplier_id) {

                $supplier_id = DeliveryCompany::where('supplier_id', $supplier_id)->value('id');
                if (is_null($supplier_id)) throw new Exception('Invalid supplier!');

                //Dropmatix carnot supplied replace with Wohlauf carnot supplier
                $supplier_id = (int) $supplier_id === 14383 ? 13040 : $supplier_id;
                if ((int)$supplier_id === 13040 && (int)$order->cms_user_id === 2455) return false;

                $supplier = DeliveryCompany::where('id', '=', $supplier_id)->first();
                $supplier_user_id = (int)$supplier_id === 13040 ? $supplier->user_id : $order->cms_user_id;

                if (is_null($supplier)) throw new Exception('Invalid supplier!');
                $supplier_name = $supplier->name;

                $data = [];
                $data['page_title'] = 'Delivery Note';
                $data['order'] = $order;
                $data['product_list'] = $product_list = $carts;
                $data['customer'] = $customer = $order->customer;

                //                $data['setting'] = $invoice_data = DB::table('delivery_note_settings')->where('cms_user_id', $supplier_user_id)->orderBy('id', 'desc')->first();
                $data['setting'] = $invoice_data = $this->getDelveriNoteTemplate($order, $supplier_user_id);


                // if (is_null($invoice_data)) throw new Exception("Invoice setting required for sending Email.");


                if ($order->marketplace_order_ref) {
                    $data['shipping_details'] = DB::table("new_orders")
                        ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                        ->select("new_orders.id as mp_order_id", "new_customers.*")
                        ->where('new_orders.id', $order->marketplace_order_ref)->first();
                } else if ($order->credit_number && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                    $marketplace_credit = DB::table('new_orders')->where('credit_ref', '=', $order->id)->value('marketplace_order_ref');

                    if ($marketplace_credit->marketplace_order_ref) {
                        $data['shipping_details'] = DB::table("new_orders")
                            ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                            ->select("new_orders.id as mp_order_id", "new_customers.*")
                            ->where('new_orders.id', $marketplace_credit->marketplace_order_ref)->first();
                    }
                }
                //Marketplace credit end

                $item_numbers = array_column($product_list, 'item_number');

                //Generate product send table
                $html_table = '<div class="table-responsive"><table class="table table-bordered table-striped"><thead><tr><th>ID</th><th>Name</th><th>Item Number</th></tr></thead><tbody>';
                foreach ($carts as $cart_single) {
                    $product_url = 'https://drm.software/admin/drm_products/detail/' . $cart_single->product_id;
                    $html_table .= '<tr><td>' . $cart_single->product_id . '</td><td>' . $cart_single->product_name . '</td><td><a href="' . $product_url . '">' . $cart_single->item_number . '</a></td></tr>';
                }
                $html_table .= '</tbody></table></div>';

                $pdf_stream = PDF::loadView('admin.invoice.delivery_note', $data)->setWarnings(false)->stream();

                $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

                if (isset($data['shipping_details']) && $data['shipping_details']) {
                    $shipping = '<p>' . formatBillingAddress($data['shipping_details']->shipping) . '</p>';
                } else {
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $logo = ($invoice_data->logo) ? $invoice_data->logo : '';
                $tags = [
                    'customer_name' => $customer->full_name,
                    'company_name' => $customer->company_name,
                    'billing_address' => $billing,
                    'shipping_address' => $shipping,
                    'order_items' => view('admin.new_order.email_supplier_order_items', compact('product_list', 'order'))->render(),
                    'order_items_n_article' => view('admin.new_order.email_supplier_order_items_n_article', compact('product_list', 'order'))->render(),
                    'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                    'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data->store_name . '" >',
                    'order_number' => $order->id,
                    'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                    'contact_name' => $supplier_name,
                    'pay_url' => null,
                    'PAYWALL' => false,
                    'IS_DROPSHIPPING'   => true,
                    'IS_FULFILLMENT'    => false,
                ];

                // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){

                $tags['credit_note'] = $order->credit_number;

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $order->cms_user_id)->pluck('signature', 'id')->toArray();

                if ($email_signatures) {
                    foreach ($email_signatures as $key => $signature) {
                        $tags['drm-sign-' . $key] = $signature;
                    }
                }
                // }

                // if(isLocal() || in_array($order->cms_user_id, [212, 2592])){

                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                $channel_supplier_mail = DB::table('drm_supplier_mail')
                    ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                    ->exists();

                if ($channel_supplier_mail) {
                    $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id, $channel);
                } else {
                    $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id);
                }

                // }else{
                // $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id);
                // }

                $data['email_to'] = $email_to = $supplier->order_email ?? $supplier->email;
                $data['email_from'] = $template['senderEmail'];
                $data['subject'] = $template['subject'];
                $data['bcc'] = $template['bcc'];

                if (is_null($template['body'])) throw new Exception('Supplier email setting required for sending Delivery Note.');

                if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                    throw new Exception("Something Wrong! Email Not Sent!.");
                }

                // $note_name = 'delivery_note_' . $supplier_name . '_' . $order->invoice_number;
                // $note_name = preg_replace('/\s+/', '_', $note_name);
                // $note_name = preg_replace('/[_]+/', '_', $note_name);
                $note_name = $order->id . '.pdf';

                app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_supplier', $template, function ($messages) use ($data, $pdf_stream, $note_name) {
                    // $messages->from($data['email_from']);
                    $messages->to($data['email_to']);
                    $messages->subject($data['subject']);

                    if ($data['bcc']) {
                        $messages->bcc($data['bcc']);
                    }

                    $messages->attachData($pdf_stream, $note_name, [
                        'mime' => 'application/pdf',
                    ]);
                });

                $is_bcc = $data['bcc'] ? ' BCC: ' . $data['bcc'] : '';
                $order->update(['supplier_id' => $supplier_id, 'supplier_time' => now(), 'supplier' => 'checked', 'status' => $new_val]);

                $log_message = 'Order placed Successfully! Email: ' . $email_to . ' (' . $supplier_name . ')' . $is_bcc . ' Items: ' . implode(', ', $item_numbers) . $html_table;
                updateOrderHistory($order, $new_val, $log_message);

                $log_data = [];
                $log_data['product_list'] = $product_list;
                $log_data['tags'] = $tags;
                $log_data['order_id'] = $order->id;
                DB::table('supplier_logs')->create([
                    'supplier_id' => $supplier_id,
                    'type' => 1,
                    'data' => $log_data,
                    'message' => $log_message,
                ]);

                //send FTP file
                try {
                    if ((int) $supplier_id === 13040) {
                        app(\App\Services\FTP\OrderFTPService::class)->sendOrders([$order->id], $supplier_user_id);
                    }
                } catch (\Exception $ex) {
                }

                return [
                    'success' => true,
                    'message' => 'Order placed Successfully! Email: ' . $email_to
                ];
            } else {
                throw new Exception("Supplier can not be empty!");
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Order placed failed! Error: ' . $e->getMessage()
            ];
        }
    }


    /*------------------------------
    ---------- Order status mail----
    -------------------------------*/
    public function orderStatusMailSend($order_id)
    {
        // //DB::beginTransaction();
        try {
            //Check validity
            if (empty($order_id)) return false;

            usleep(5);
            $order = NewOrder::with('customer')->where('id', $order_id)->first();
            if (empty($order)) return false;

            $order_status = $order->status;
            if (empty($order_status)) return false;

            //Check user has active status mail
            $user_id = $order->cms_user_id;

            $is_charge_email = false;
            $pay_url = null;
            $paywall = null;

            $data = [];
            $invoice_data = [];
            $invoice_data['page_title'] = 'Order mail';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = $product_list = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;

            if ($order->invoice_layout_id) {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')
                    ->where('cms_user_id', $order->cms_user_id)
                    ->where('id', $order->invoice_layout_id)
                    ->first();
            } else {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
            }

            if ($order->marketplace_order_ref && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                $invoice_data['shipping_details'] = DB::table("new_orders")
                    ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                    ->select("new_orders.id as mp_order_id", "new_customers.*")
                    ->where('new_orders.id', $order->marketplace_order_ref)->first();
            }

            $pdf_view = '';
            $pdf_name = '';
            //Select blade file
            if ($order->credit_number > 0) {
                $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.credit_note.daily' : 'admin.credit_note.general';
                $pdf_view = ($order->insert_type == 4) ? 'admin.credit_note.charge_inv' : $pdf_view;

                $pdf_name = 'credit_' . $order->credit_number;
            } else {
                $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.invoice.daily' : 'admin.invoice.general';
                $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;
                $pdf_name = 'invoice_' . $order->invoice_number;
            }

            $customer_name = $order->customer ? $order->customer->full_name : '';
            $note_name = $pdf_name . '_' . $customer_name;
            $note_name = preg_replace('/\s+/', '_', $note_name);
            $note_name = preg_replace('/[_]+/', '_', $note_name);
            $note_name = strtolower($note_name . '.pdf');
            //Note name end

            //Email content
            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';
            $logo = $invoice_data['setting']->logo ?? '';

            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
                // 'pay_url' => $pay_url,
                // 'PAYWALL' => $is_charge_email,
            ];

            // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){

            if ($order->marketplace_order_ref) {
                $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
            } else {
                $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
            }

            $tags['shipping_address'] = $shipping;
            $tags['credit_note'] = $order->credit_number;

            $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $order->cms_user_id)->pluck('signature', 'id')->toArray();

            if ($email_signatures) {
                foreach ($email_signatures as $key => $signature) {
                    $tags['drm-sign-' . $key] = $signature;
                }
            }
            // }

            // (isLocal() || in_array($order->cms_user_id, [212, 2592])) &&
            if (($order_status == 'Shipped')) {

                $tags['package_number'] = '';
                $tags['parcel_service'] = '';

                $tracking = OrderTrackings::with('parcel')->where('order_id', '=', $order->id)->latest()->first();
                if ($tracking && $tracking->id) {
                    $parcel_name = ($tracking->parcel) ? $tracking->parcel->parcel_name : '';
                    $package_number = $tracking->package_number;

                    if (strtolower($parcel_name) == 'dhl') {
                        $package_number = '<a href="https://www.dhl.de/de/privatkunden/pakete-empfangen/verfolgen.html?piececode=' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    } else if (strtolower($parcel_name) == 'dpd') {
                        $package_number = '<a href="https://www.dpd.com/de/de/empfangen/sendungsverfolgung-und-live-tracking" target="_blank">' . $package_number . '</a>';
                    } else if (strtolower($parcel_name) == 'hermes') {
                        $package_number = '<a href="https://www.myhermes.de/empfangen/sendungsverfolgung" target="_blank">' . $package_number . '</a>';
                    } else if (strtolower($parcel_name) == 'ups') {
                        $package_number = '<a href="https://www.ups.com/track?loc=de_DE&requester=ST&trackNums=' . $package_number . '/trackdetails" target="_blank">' . $package_number . '</a>';
                    } else if (strtolower($parcel_name) == 'tnt') {
                        $package_number = '<a href="https://www.tnt.com/express/de_de/site/shipping-tools/tracking.html?searchType=con&cons=' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    } else if (strtolower($parcel_name) == 'gls') {
                        $package_number = '<a href="https://www.gls-pakete.de/sendungsverfolgung?trackingNumber=' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'dachser') {
                        $package_number = '<a href="https://elogistics.dachser.com/shp2s/?search=' . $package_number . '&javalocale=de_DE" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'swisspost') {
                        $package_number = '<a href="https://service.post.ch/ekp-web/ui/entry/search/' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }

                    $tags['package_number'] = $package_number;
                    $tags['parcel_service'] = $parcel_name;
                }
            }

            // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){
            // $combine_status = ['Erstattet', 'credit_note_created', 'refund_initiated', 'refund_completed'];
            $combine_status = config('order_status.combine_status');

            $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

            if (in_array($order_status, $combine_status)) {

                if ($order->cms_user_id > 2990) {
                    $channel_combine_status_mail = DB::table('combine_status_email_settings')
                        ->where(['cms_user_id' => $user_id, 'channel' => $channel])
                        ->exists();

                    if ($channel_combine_status_mail) {
                        $template = $this->combineOrderStatusMailData($tags, $user_id, $channel);
                    } else {
                        $template = $this->combineOrderStatusMailData($tags, $user_id);
                    }
                } else {
                    $channel_order_status_mail = DB::table('order_mail_templates')
                        ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                        ->exists();

                    if ($channel_order_status_mail) {
                        //Get appropriate template
                        $template = $this->orderStatusMailData($tags, $user_id, $order_status, $channel);
                    } else {
                        //Get appropriate template
                        $template = $this->orderStatusMailData($tags, $user_id, $order_status);
                    }
                }
            } else if ($order_status == 'payment_in_progress') {
                $channel_payment_progress_mail = DB::table('payment_progress_email_settings')
                    ->where(['cms_user_id' => $user_id, 'channel' => $channel])
                    ->exists();

                if ($channel_payment_progress_mail) {
                    $template = $this->paymentProgressMailData($tags, $order->cms_user_id, $channel);
                } else {
                    $template = $this->paymentProgressMailData($tags, $order->cms_user_id);
                }
            } else {

                $channel_order_status_mail = DB::table('order_mail_templates')
                    ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                    ->exists();

                if ($channel_order_status_mail) {
                    //Get appropriate template
                    $template = $this->orderStatusMailData($tags, $user_id, $order_status, $channel);
                } else {
                    //Get appropriate template
                    $template = $this->orderStatusMailData($tags, $user_id, $order_status);
                }
            }

            // }else{
            // //Get appropriate template
            // $template = $this->orderStatusMailData($tags, $user_id, $order_status);
            // }

            if (empty($template)) return false;

            $data['email_to'] = $email_to = $invoice_data['customer']->email;
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];
            $data['subject'] = $template['subject'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $is_send_invoice = $template['send_invoice'] ?? 0;

            // TODO:: DROPMATIX
            $pdf_stream = $is_send_invoice ? (new \App\Services\Order\InvoiceDoc($order->id))->output() : '';

            //Send mail
            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $is_send_invoice, $note_name) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                //Send invoice
                if ($is_send_invoice) {
                    $messages->attachData($pdf_stream, $note_name, [
                        'mime' => 'application/pdf',
                    ]);
                }

                //BCC
                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }
            });

            if ($order_status == 'inkasso'){
                //Unpaid order product delete warning email

                // Action only allow for Moritz(<EMAIL>) and Özgür (<EMAIL>) and Betül1(<EMAIL>) Account
                if(in_array($order->cms_user_id, [2956, 3445, 3624])){
                $this->productDeleteWarningEmail($order->id, $order_status);
                }
            }

            // //DB::commit();
            try {
                $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
                $message_text = ucfirst(drmHistoryLabel($order_status)) . ' - Email sent to: ' . $email_to . '' . $is_bcc;
                updateOrderHistory($order, 'send_email_' . $order_status, $message_text);
            } catch (Exception $ee) {
            }

            return true;
        } catch (Exception $e) {
            // //DB::rollBack();
            User::find(71)->notify(new DRMNotification('Send customer invoice error ' . $e->getMessage() . ' Line:' . $e->getLine(), '', '#'));
            return false;
        }
    }


    //Get email conent, subject from email page
    private function orderStatusMailData($tags, $user_id, $order_status, $channel = null)
    {
        if ($channel) {
            $page = DB::table('order_mail_templates')
                ->where('order_status', $order_status)
                ->where('user_id', $user_id)
                ->where('channel', $channel)
                ->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')
                ->first();
        } else {
            $page = DB::table('order_mail_templates')->where('order_status', $order_status)->where('user_id', $user_id)->whereNull('channel')->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')->first();
        }

        if (empty($page) || ($page->auto_mail != 1)) return false;

        $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
        $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

        $content = null;
        $subject = null;
        $bcc = null;
        $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
        $send_invoice = 0;

        if ($page) {
            $content = empty($page->email_template) ? config('system_email_settings.email_settings') : $page->email_template;
            $subject = $page->mail_subject;
            $bcc = $page->bcc_email;
            $send_invoice = $page->send_invoice;

            if (!empty($page->sender_email)) {
                $senderEmail = $page->sender_email;
            }

            foreach ($tags as $k => $value) {
                $find = '[' . $k . ']';
                $tag = $k;
                if (strpos($content, $find) !== false) {
                    $content = str_replace($find, $value, $content);
                }
                if ($tags[$tag] == 'true') {
                    $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
                } else {
                    $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                    // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
                }

                if (strpos($subject, $find) !== false) {
                    $subject = str_replace($find, $value, $subject);
                }
                if ($tags[$tag] == 'true') {
                    $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
                } else {
                    $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                    // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
                }
            }
        }

        return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'send_invoice' => $send_invoice, 'senderEmail' => $senderEmail];
    }



    //test delivery note
    public function sendApiOrder($id)
    {
        $item = NewOrder::find($id);
        $order = $this->sendDropmatixOrderToSupplier($item);
    }

    //Insert order trackings data
    public function insertOrderTrackings($order_id, $trackings_data)
    {
        if (empty($trackings_data)) return;
        if (empty($order_id)) return;

        foreach ($trackings_data as $parcel) {
            $order = NewOrder::has('customer')->with('customer')->find($order_id);
            if (empty($order)) return;

            $user_id = $order->cms_user_id;

            $parcel_name = $parcel['parcel_name'];
            $tracking_number = $parcel['tracking_number'];

            $parcel_id = DB::table('user_parcel_services')
                ->where('parcel_name', $parcel_name)
                ->where(function ($q) use ($user_id) {
                    $q->where('cms_user_id', '=', $user_id)
                        ->orWhereNull('cms_user_id');
                })
                ->orderBy('cms_user_id', 'DESC')
                ->value('id');

            if (empty($parcel_id)) {
                $parcel_id = DB::table('user_parcel_services')->insertGetId(['parcel_name' => $parcel_name]);
            }
            $this->saveOrderTracking($order, $parcel_id, $tracking_number);
        }
    }


    private function saveOrderTracking(NewOrder $order, $parcel_id, $tracking_number)
    {
        try {

            $order->package_number = $tracking_number;
            $order->parcel_id = $parcel_id;
            $order->status = 'Shipped';
            $order->save();

            $tracking_id_val = DB::table('order_trackings')->insertGetId([
                'order_id' => $order->id,
                'user_id' => $order->cms_user_id,
                'package_number' => $tracking_number,
                'parcel_id' => $parcel_id,
            ]);

            $tracking_email = OrderTrackingEmail::where(['cms_user_id' => $order->cms_user_id, 'auto_mail' => 1])->select('auto_mail')->first();
            if ($tracking_email) {
                $this->trackingEmailSend($order, $tracking_id_val);
            }

            updateOrderHistory($order, 'Shipped', 'Order tracking successfully. Tracking number: ' . $tracking_number);
        } catch (Exception $ee) {
        }
    }


    private function getDelveriNoteTemplate($order, $user_id)
    {
        if ($order->delivery_layout_id) {
            $setting = DeliveryNote::where('cms_user_id', $user_id)->where('id', $order->delivery_layout_id)->first();
        } else {
            try {
                $shop_type = \App\Shop::where('id', $order->shop_id)->value('channel');
                $setting = DB::table('delivery_note_assign')
                    ->join('delivery_note_settings', '.delivery_note_settings.id', '=', 'delivery_note_assign.note_id')
                    ->where('delivery_note_assign.user_id', $user_id)
                    ->where('delivery_note_assign.channel_type', $shop_type)
                    ->orderBy('delivery_note_assign.id', 'desc')
                    ->first();
            } catch (Exception $exception) {
            }
        }
        if (empty($setting)) {
            $setting = DeliveryNote::DoesntHave('channels')
                ->DoesntHave('order')
                ->where('cms_user_id', $user_id)
                ->orderBy('id', 'desc')
                ->first();
        }
        return $setting;
    }


    //trancking email send function
    private function trackingEmailSend(NewOrder $order, $tracking_id)
    {
        $invoice_data['page_title'] = 'Test email tracking order email template';
        $invoice_data['order'] = $order;
        $invoice_data['product_list'] = json_decode($order->cart);
        $invoice_data['customer'] = (object)$order->customer;

        if ($order->invoice_layout_id) {
            $invoice_data['setting'] = DB::table('drm_invoice_setting')
                ->where('cms_user_id', $order->cms_user_id)
                ->where('id', $order->invoice_layout_id)
                ->first();
        } else {
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
        }

        $product_list = json_decode($order->cart);

        $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

        $logo = $invoice_data['setting']->logo ?? '';
        $tracking = OrderTrackings::with('parcel')->where('order_id', '=', $order->id)->find($tracking_id);
        if (empty($tracking)) throw new \Exception('Sorry, Invalid shipment label!');

        $parcel_name = ($tracking->parcel) ? $tracking->parcel->parcel_name : '';
        $package_number = $tracking->package_number;

        if (strtolower($parcel_name) == 'dhl') {
            $package_number = '<a href="https://www.dhl.de/de/privatkunden/pakete-empfangen/verfolgen.html?piececode=' . $package_number . '" target="_blank">' . $package_number . '</a>';
        } else if (strtolower($parcel_name) == 'dpd') {
            $package_number = '<a href="https://www.dpd.com/de/de/empfangen/sendungsverfolgung-und-live-tracking" target="_blank">' . $package_number . '</a>';
        } else if (strtolower($parcel_name) == 'hermes') {
            $package_number = '<a href="https://www.myhermes.de/empfangen/sendungsverfolgung/sendungsinformation#' . $package_number . '" target="_blank">' . $package_number . '</a>';
        } else if (strtolower($parcel_name) == 'ups') {
            $package_number = '<a href="https://www.ups.com/track?loc=de_DE&requester=ST&trackNums=' . $package_number . '/trackdetails" target="_blank">' . $package_number . '</a>';
        } else if (strtolower($parcel_name) == 'tnt') {
            $package_number = '<a href="https://www.tnt.com/express/de_de/site/shipping-tools/tracking.html?searchType=con&cons=' . $package_number . '" target="_blank">' . $package_number . '</a>';
        } else if (strtolower($parcel_name) == 'gls') {
            $package_number = '<a href="https://www.gls-pakete.de/sendungsverfolgung?trackingNumber=' . $package_number . '" target="_blank">' . $package_number . '</a>';
        }else if (strtolower($parcel_name) == 'dachser') {
            $package_number = '<a href="https://elogistics.dachser.com/shp2s/?search=' . $package_number . '&javalocale=de_DE" target="_blank">' . $package_number . '</a>';
        }else if (strtolower($parcel_name) == 'swisspost') {
            $package_number = '<a href="https://service.post.ch/ekp-web/ui/entry/search/' . $package_number . '" target="_blank">' . $package_number . '</a>';
        }

        $tags = [
            'customer_name' => $invoice_data['customer']->full_name,
            'company_name' => $invoice_data['customer']->company_name,
            'billing_address' => $billing,
            'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
            'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
            'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
            'order_number' => $order->id,
            'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
            'package_number' => $package_number,
            'parcel_service' => $parcel_name,
            'pay_url' => null,
            'PAYWALL' => false,
        ];

        if ($order->marketplace_order_ref) {
            $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

            $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
        } else {
            $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
        }

        $tags['shipping_address'] = $shipping;

        $template = $this->parseTrackingEmailTemplate($tags, $order->cms_user_id);
        $data['email_to'] = ($order->customer) ? $order->customer->email : '';
        $data['email_from'] = $template['senderEmail'];

        if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
            throw new Exception("Something Wrong! Email Not Sent!.");
        }

        $data['subject'] = $template['subject'];
        $data['bcc'] = (isset($template['bcc']) && $template['bcc']) ? $template['bcc'] : null;

        app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
            // $messages->from($data['email_from']);
            $messages->to($data['email_to']);
            $messages->subject($data['subject']);

            if ($data['bcc']) {
                $messages->bcc($data['bcc']);
            }
        });
    }

    //Tracking email template
    private function parseTrackingEmailTemplate($tags, $user_id)
    {
        $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
        $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

        $content = null;
        $subject = null;
        $bcc = null;
        $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
        $page = DB::table('order_tracking_emails')->where('cms_user_id', $user_id)->first();

        if ($page) {
            $content = empty($page->email_template) ? config('system_email_settings.tracking_email_settings') : $page->email_template;
            $subject = $page->mail_subject;
            $bcc = $page->bcc_email;

            if (!empty($page->sender_email)) {
                $senderEmail = $page->sender_email;
            }

            foreach ($tags as $k => $value) {
                $find = '[' . $k . ']';
                $tag = $k;
                if (strpos($content, $find) !== false) {
                    $content = str_replace($find, $value, $content);
                }
                if ($tags[$tag] == 'true') {
                    $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
                } else {
                    $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                    // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
                }

                if (strpos($subject, $find) !== false) {
                    $subject = str_replace($find, $value, $subject);
                }
                if ($tags[$tag] == 'true') {
                    $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
                } else {
                    $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                    // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
                }
            }
        }

        return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
    }

    //Get email conent, subject from email page
    private function combineOrderStatusMailData($tags, $user_id, $channel = null)
    {
        if ($channel) {
            $page = DB::table('combine_status_email_settings')
                ->where('cms_user_id', $user_id)
                ->where('channel', $channel)
                ->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')
                ->first();
        } else {
            $page = DB::table('combine_status_email_settings')
                ->where('cms_user_id', $user_id)
                ->whereNull('channel')
                ->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')
                ->first();
        }
        if (empty($page) || ($page->auto_mail != 1)) return false;

        $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
        $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

        $content = null;
        $subject = null;
        $bcc = null;
        $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
        $send_invoice = 0;

        if ($page) {
            $content = empty($page->email_template) ? config('system_email_settings.email_settings') : $page->email_template;
            $subject = $page->mail_subject;
            $bcc = $page->bcc_email;
            $send_invoice = $page->send_invoice;

            if (!empty($page->sender_email)) {
                $senderEmail = $page->sender_email;
            }

            foreach ($tags as $k => $value) {
                $find = '[' . $k . ']';
                $tag = $k;
                if (strpos($content, $find) !== false) {
                    $content = str_replace($find, $value, $content);
                }
                if ($tags[$tag] == 'true') {
                    $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
                } else {
                    $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                    // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
                }

                if (strpos($subject, $find) !== false) {
                    $subject = str_replace($find, $value, $subject);
                }
                if ($tags[$tag] == 'true') {
                    $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
                } else {
                    $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                    // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
                }
            }
        }

        return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'send_invoice' => $send_invoice, 'senderEmail' => $senderEmail];
    }

    //Get email conent, subject from email page
    private function paymentProgressMailData($tags, $user_id, $channel = null)
    {
        if ($channel) {
            $page = DB::table('payment_progress_email_settings')
                ->where('cms_user_id', $user_id)
                ->where('channel', $channel)
                ->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')
                ->first();
        } else {
            $page = DB::table('payment_progress_email_settings')
                ->where('cms_user_id', $user_id)
                ->whereNull('channel')
                ->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')
                ->first();
        }
        if (empty($page) || ($page->auto_mail != 1)) return false;

        $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
        $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

        $content = null;
        $subject = null;
        $bcc = null;
        $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
        $send_invoice = 0;

        if ($page) {
            $content = empty($page->email_template) ? config('system_email_settings.payment_progress_body') : $page->email_template;
            $subject = $page->mail_subject;
            $bcc = $page->bcc_email;
            $send_invoice = $page->send_invoice;

            if (!empty($page->sender_email)) {
                $senderEmail = $page->sender_email;
            }

            foreach ($tags as $k => $value) {
                $find = '[' . $k . ']';
                $tag = $k;
                if (strpos($content, $find) !== false) {
                    $content = str_replace($find, $value, $content);
                }
                if ($tags[$tag] == 'true') {
                    $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
                } else {
                    $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                    // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
                }

                if (strpos($subject, $find) !== false) {
                    $subject = str_replace($find, $value, $subject);
                }
                if ($tags[$tag] == 'true') {
                    $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
                } else {
                    $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                    // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
                }
            }
        }

        return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'send_invoice' => $send_invoice, 'senderEmail' => $senderEmail];
    }

    //Order From Supplier when product is available
    function preOrderSupplier($id){
        try{
            $this->getOrderSupplier($id);
        }catch(Exception $e){

        }
    }

    public function binoSupplierOrderMailSend($order_id){
        $order = NewOrder::find($order_id);
        collect($order->products)->whereNotNull('mp_supplier_id')
            ->groupBy('mp_supplier_id')
            ->each(function ($carts, $supplier_id) use ($order) {
                if ($supplier_id == 49997) {
                    $this->placeOrderProduct($order, $carts, $supplier_id);
                }
            });
    }

    //Send product delete warning email
    public function productDeleteWarningEmail($order_id, $order_status)
    {
        try {
            $data = [];
            $invoice_data = [];
            $order = NewOrder::find($order_id);
            $invoice_data['page_title'] = 'Order Invoice';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;

            if ($order->invoice_layout_id) {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')
                    ->where('cms_user_id', $order->cms_user_id)
                    ->where('id', $order->invoice_layout_id)
                    ->first();
            } else {
                $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
            }

            if ($order->marketplace_order_ref && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                $invoice_data['shipping_details'] = DB::table("new_orders")
                    ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                    ->select("new_orders.id as mp_order_id", "new_customers.*")
                    ->where('new_orders.id', $order->marketplace_order_ref)->first();
            }

            $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $logo = $invoice_data['setting']->logo ?? 'https://dropmatix.fra1.digitaloceanspaces.com/images/logo_full.png';

            $tags = [
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
            ];

            $template = DRMParseUnpaidInvoiceEmailTemplate($tags, $order->cms_user_id, $order_status);

            $data['email_to'] = $invoice_data['customer']->email;
            $data['email_from'] = $template['senderEmail'];
            $data['subject'] = $template['subject'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            if(empty($template['subject']) || empty($template['body'])) return;

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);

                $messages->subject($data['subject']);
            });

        } catch (Exception $e) {
            Log::channel('command')->info($e->getMessage());
        }
    }

    function shippingCostDiscountCalculation($cart_products, $is_professional_user = false) {
        return 0.0;
        $totalShippingCost = 0;
        $discountedShippingCost = 0.00;
        $groupedCartProducts = $cart_products->groupBy('mp_supplier_id');
        foreach ($groupedCartProducts as $cartProductsForCompany) {
            $cartQuantity = $cartProductsForCompany->sum('qty');
            $shippingCosts = $cartProductsForCompany->pluck('shipping_cost')->toArray();

            if ($cartProductsForCompany->count() >= 2 || $cartQuantity >= 2) {
                $totalShippingCost += $cartProductsForCompany->sum(function ($item) {
                    return $item['qty'] * $item['shipping_cost'];
                });

                $highestShippingCost = max($shippingCosts);
                $discountedShippingCost += $highestShippingCost + ($cartQuantity - 1) * ($is_professional_user ? 2.5 : 1.5);
            } else {
                $totalShippingCost += array_sum($shippingCosts);
            }
        }

        if($discountedShippingCost > $totalShippingCost) return 0;
        return $discountedShippingCost > 0 ? $totalShippingCost - $discountedShippingCost : 0;
    }


    // Virtual payment
    public function virtualPayment($user_id, $item_id, $amount, $totalCreditAmount)
    {
        $credit = MpVirtualCredit::create([
            'user_id' => $user_id,
            'amount' => -1 * abs($amount),
        ]);
        $amountStr = number_format($amount, 2, ',', '.') . formatCurrency('EUR');
        if($credit)
        {
            $credit->virtualCreditLog()->create([
                'order_id' => $item_id,
                'message' => "Marketplace Order payment. ID: {$item_id}. Amount: {$amountStr}",
                'type' => 0,
            ]);

            $this->paidAction($item_id, 'MP virtual credit', $amount, $totalCreditAmount);

            return true;
        }

        return false;
    }

    //Marketplace order paid action
    //1 => status update on DROPMATIX   (paid)
    //2 => Update history
    private function paidAction($item_id, $service = 'Stripe', $amount, $totalCreditAmount){

        $order = NewOrder::where('id', $item_id)->first();
        $message = 'Marketplace order paid successfully using ('.$amount .' '.$service.') and now remain ('.$totalCreditAmount .' '.$service.')';

        if($order){
            $order->update([
                'payment_type' => $service,
                'payment_date' => now(),
                'marketplace_paid_status' => 1,
                'status' => 'paid',
            ]);

            $this->updateOrderHistory($order, 'MARKETPLACE_PAID', $message);
        }

        $client_order = NewOrder::where('marketplace_order_ref', $item_id)->first();
        if($client_order){
            $client_order->update([
                'marketplace_paid_status' => 1,
            ]);

            $this->updateOrderHistory($client_order, 'MARKETPLACE_PAID', $message);
        }
    }

    //Update order history
    private function updateOrderHistory($order, $status, $message)
    {
        $payload = [
            'status' => $status,
            'time' => date('Y-m-d H:i:s'),
            'action_by' => null,
            'user_name' => 'SYSTEM',
            'message' => $message,
        ];

        // TODO:: DROPMATIX
        DB::table('order_logs')->insert([
            'order_id' => $order->id,
            'payload' => json_encode($payload),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }


    // send delivery problem notification
    private function sendDeliveryProblemNotification($order, $messsage)
    {
      try {

        if($order->status == 'zustellproblem') return;

        $url = 'https://drm.software/admin/drm_all_orders?order_status=zustellproblem';

        // Send push notification
        (new PushNotifyDeliveryProblem)->send($order, $messsage, 'Delivery problem');

        User::find($order->cms_user_id)->notify(new DRMNotification('Delivery problem: '. $messsage. ' ('.$order->id.')', null, $url));
      } catch(\Exception $e){}
    }

    public function create_surcharge_invoice($order_id) {
        $order_info = NewOrder::find($order_id);

        $surcharge = 15;
        $prev_invoice_number = $order_info->invoice_number;

        $dropmatix_id = 2455;
        $client_id = (int)$order_info->cms_client;

        $paymentTax = new PaymentTax($client_id, $dropmatix_id);
        $vat_number = $paymentTax->vatNumber();
        $tax_rate = $paymentTax->taxRate();

        $total_tax = 0;
        $total = $surcharge;


        if ($tax_rate) {
            $total_tax = (($total * $tax_rate) / 100);
            $total = $total + $total_tax;
        }

        $cart_arr[] = [
            "product_name" => "Surcharge processing fee of failed invoice " . $prev_invoice_number,
            "description" => "Payment failed because agreed payment term can not be debited in due date.",
            "qty" => 1,
            "rate" => $surcharge,
            "unit" => "",
            "tax" => $tax_rate,
            "product_discount" => 0,
            "amount" => $surcharge,
        ];

        $payload = [
            'order_id_api' => 'surcharge_' . $order_id,
            'user_id' => $order_info->cms_user_id,
            'cms_client' => $order_info->cms_client,
            'drm_customer_id' => $order_info->drm_customer_id,
            'shop_id' => $order_info->shop_id,
            'invoice_date' => date('Y-m-d H:i:s'),
            'order_date' => date('Y-m-d H:i:s'),
            'vat_number' => $vat_number,

            'insert_type' => 6,
            'status' => 'mahnung',
            'remainder_date' => date('Y-m-d H:i:s'),
            'customer_info' => $order_info->customer_info,
            'billing' => $order_info->billing,
            'shipping' => $order_info->shipping,

            'cart' => json_encode($cart_arr, true),
            'total' => round($total, 2),
            'eur_total' => round($total, 2),
            'sub_total' => round($surcharge, 2),
            'dropmatix_sub_total' => $surcharge,
            'dropmatix_total_tax' => $total_tax,
            'dropmatix_tax_rate' => $tax_rate,
            'total_tax' => $total_tax,
            'discount' => 0,
            'shipping_cost' => 0,
            'currency' => 'EUR',
            'tax_rate'=> $tax_rate,
            'client_note' => null,
        ];

        $res = $this->insert_order($payload);
    }

    /**
     * Get customer supplier by customer user id
     * @param $user_id
     */
    private function getSupplierByCustomerId($user_id)
    {
        $data = [
            'name' => 'Dropmatix Systema SL',
            'address' => 'C/ HORTS, 33',
            'zip' => '07200',
            'state' => 'FELANITX',
            'country_id' => 8,
            'contact_name' => 'Dropmatix Systema SL',
            'note' => 'Marketplace Supplier',
            'is_marketplace_supplier' => true,
        ];

        $email = '<EMAIL>'; // Dropmatix Email

        $result = DeliveryCompany::firstOrCreate(
            [
                'user_id' => $user_id,
                'email' => $email
            ],
            $data
        );

        return $result;
    }

    private function orderTransferToDropmatixV9($order_id)
    {
        if(empty($order_id)) return;

        try {
            $queueJob = new \App\Services\MessageBroker\OrderPlaceSyncJob;
            $batchId = Str::uuid()->toString();
            $queueJob->publish([
                'batchId' => $batchId,
                'orderId' => $order_id,
            ]);
        } catch (Exception $e) {
            print("Order MP transfer error");
        }
    }
}
