<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\NewCustomer;
use App\Notifications\DRMNotification;
use Illuminate\Support\Facades\Validator;
use App\User;
use App\CustomerTag;
use AppStore;

class UscreenSyncController extends Controller
{
    //receive customers from uscreen
    public function syncCustomer(){
        try{
            $data = json_decode(request()->getContent(), true);

            $customer_info = [
                "customer_full_name" => $data['name'],
                "company_name" =>  '',
                "currency" => 'EUR',
                'email' => $data['email'],
                'address' =>  '',
                'country' => '',
                'default_language' => 'DE',
                'zip_code' => '',
                'state' => '',
                'insert_type' => 2, //int
            
                //shipping
                'street_shipping' => '',
                'city_shipping' => '',
                'state_shipping' => '',
                'zipcode_shipping' => '',
                'country_shipping' => '',
            
                //billing
                'street_billing' => '',
                'city_billing' => '',
                'state_billing' => '',
                'zipcode_billing' => '',
                'country_billing' => '',
            
                'user_id' => 98,
            ];

            $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);
            User::find(98)->notify(new DRMNotification('A customer is synced from userscreen!' . PHP_EOL .'Email :' .$data['email'], 'CustomerSyncUserscreen'));

        }catch (\Exception $e) {}
    }

    //receive ransactions from uscreen
    public function syncTransaction(){
        try{
            
            $data = json_decode(request()->getContent(), true);

            // $validator = Validator::make($data, [
            //     'customer_email' => 'required',
            //     'total' => 'required',
            // ]);
            if (is_null($data)) {
                throw new \Exception("Invalid VOD data!");
            }

            $customer_info = [
                "full_name" => $data['customer_name'],
                "currency" => 'EUR',
                'default_language' => 'DE',
                'insert_type' => 2, 
            ];

            $customer = NewCustomer::updateOrCreate([
                'email'=> $data['customer_email'],
                'user_id'=> 98,
                ],
                $customer_info
            );

            $order_info['user_id'] = 98;
            
            $total = isset($data['total'])? (float)$data['total'] : 0;
            $product_price = $total;
            $sub_total = isset($data['amount'])? (float)$data['amount'] : 0;
            $discount = isset($data['discount'])? (float)$data['discount'] : 0;
            $total = ($discount > 0)? ($total - $discount) : $total;

            if(isset($customer->id)){
                $order_info['drm_customer_id'] = $customer->id;
            }

            $order_info['order_date'] =  date("Y-m-d h:i:sa");
            $order_info['insert_type'] = 2;
            $order_info['total'] = $total;
            $order_info['shop_id'] = 108;
            $order_info['order_id_api'] = $data['id'];

            $order_info['sub_total'] = $sub_total;
            $order_info['discount'] =  $discount;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = 'Stripe Card';
            $order_info['currency'] = 'EUR';
            $order_info['billing'] = customerToBillingJson($customer);
            $order_info['shipping'] = customerToShippingJson($customer);

            $order_info['status'] = 'Succeeded';

            $cart_item = [[
                "id" => 1,
                "product_name" => iconv('UTF-8', 'ASCII//TRANSLIT', $data['title']),
                'qty' => 1,
                'rate' => $product_price,
                'unit' => 1,
                'offer_id' => $data['offer_id'],
                'tax' => 0,
                'product_discount' => $discount,
                'amount' =>  $product_price,
                'user_id' => 98,
            ]];

            $order_info['cart'] = json_encode($cart_item);

            Log::channel('uscreen')->info($order_info);

            app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
           
            // Notify user that a new order has been added
            User::find(98)->notify(new DRMNotification('A order paymnet received via userscreen!'. PHP_EOL . 'Customer Email :' .$data['customer_email'] . PHP_EOL . 'Product Name:'. $data['title']. PHP_EOL .'Amount: '.$data['total']  , 'CustomerSyncUserscreen'));

        }catch (\Exception $e) {Log::channel('uscreen')->info($e->getMessage());}
    }    

    //receive due transactions from uscreen
    public function syncTransactionDue(){
        try{
            
            $data = json_decode(request()->getContent(), true);
            
            if (is_null($data)) {
                throw new \Exception("Invalid Due Transection VOD data!");
            }

            $customer_info = [
                "full_name" => $data['name'],
                "currency" => 'EUR',
                'default_language' => 'DE',
                'insert_type' => 2, 
            ];

            $customer = NewCustomer::updateOrCreate([
                'email'=> $data['email'],
                'user_id'=> 98,
                ],
                $customer_info
            );

            $order_info['user_id'] = 98;


            if(isset($customer->id)){
                $order_info['drm_customer_id'] = $customer->id;
            }

            $order_info['order_date'] =  date("Y-m-d h:i:sa");
            $order_info['insert_type'] = 2;
            $order_info['total'] = $data['final_price'];
            $order_info['shop_id'] = 108;
            $order_info['order_id_api'] = $data['invoice_id'];

            $order_info['sub_total'] = $data['final_price'];
            $order_info['discount'] =  0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = 'Stripe Card';
            $order_info['currency'] = 'EUR';
            $order_info['billing'] = customerToBillingJson($customer);
            $order_info['shipping'] = customerToShippingJson($customer);

            $order_info['status'] = 'Due';

            $cart_item = [[
                "id" => 1,
                "product_name" => iconv('UTF-8', 'ASCII//TRANSLIT', $data['title']),
                'qty' => 1,
                'rate' => $data['final_price'],
                'unit' => 1,
                'offer_id' => null,
                'tax' => 0,
                'product_discount' => 0,
                'amount' =>  $data['final_price'],
                'user_id' => 98,
            ]];

            $order_info['cart'] = json_encode($cart_item);

            Log::channel('uscreen')->info($order_info);

            app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
           
            // Notify user that a new order has been added
            User::find(98)->notify(new DRMNotification('A order paymnet received via userscreen!'. PHP_EOL . 'Customer Email :' .$data['email'] . PHP_EOL . 'Product Name:'. $data['title']. PHP_EOL .'Amount: '.$data['final_price']  , 'CustomerSyncUserscreen'));

        }catch (\Exception $e) {Log::channel('uscreen')->info($e->getMessage());}
    }

    //receive customers from uscreen
    public function uscreenWebhookOthers(){
        try{

            $data = json_decode(request()->getContent(), true);
            Log::channel('uscreen')->info($data);

        }catch (\Exception $e) {}
    }
}
