<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use ServiceKey;
use App\Http\Resources\StripeOrderFetchResource;
use Illuminate\Support\Collection;



use App\Helper\LengowApi;
use DateTime;
use App\Helper\GambioApi;
use App\Helper\ShopifyApi;
use App\Helper\EbayApi;
use \Hkonnet\LaravelEbay\EbayServices;
use \DTS\eBaySDK\Trading\Services;
use \DTS\eBaySDK\Trading\Types;
use \DTS\eBaySDK\Trading\Enums;
use \DTS\eBaySDK\Constants;
use Ebay;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Notifications\DRMNotification;
use App\User;
use Carbon\Carbon;
use \Illuminate\Support\Arr;

class ApiResponseController extends Controller
{
    public function stripeCharges($timestamp = null){
    	$last_item = DB::table('order_sync_reports')->where('api_type', 'daily_insert')->orderBy('id', 'DESC')->first();
		$last =  ($last_item->timestamp)? $last_item->timestamp : null;
    	
    	\Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET_DAILY', '******************************************'));
    	if($timestamp){
	    	return \Stripe\Charge::all([
				'limit' => 100,
				"paid" => true,
				'created' => [
				'lte' => $timestamp,
				],
			]);
    	}
		return \Stripe\Charge::all([
			'limit' => 100,
			"paid" => true,
			'created' => [
			'gt' => $last,
			],
		]);
    }


    public function stripeCustomers($timestamp = null){    	
    	\Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET_DAILY', '******************************************'));
		$response = \Stripe\Customer::all([
			'limit' => 100,
			'created' => [
			'lte' => $timestamp,
			],
		]);

		return $response;
    }

    public function stripeOrderFetch(){
		return StripeOrderFetchResource::collection( DB::table('order_sync_reports')->where('api_type', 'daily_insert')->get() );
    }


    /*@params
    $user user id
    $shop_id
    */
    public function orderSyncResponse($user_id = null, $shop_id = null ){

            $shops = \App\Shop::where('status', 1);
            if($user_id) $shops->where('user_id', $user_id);
            if($shop_id) $shops->where('id', $shop_id);
            $shops = $shops->get();

            // return $shop_arr;
            $data = [];

            if($shops){
            	foreach ($shops as $shop) {
            		if($shop->channel == 1)
	                {
	                   $data[$shop->id] = $this->syncGambioorder($shop);
	                }
	                if($shop->channel == 2)
	                {
	                   $data[$shop->id] = $this->syncLengowOrder($shop);
	                }
	                if($shop->channel == 3)
	                {
	                   $data[$shop->id] = $this->syncYategoOrder($shop);
	                }
	                if($shop->channel == 4)
	                {
	                   $data[$shop->id] = $this->syncEbayOrder($shop);
	                }
	                if($shop->channel == 6)
	                {
	                   $data[$shop->id] = $this->syncShopifyOrder($shop);
	                }
            	}
            }

        	return $data;  	
    }

    /*@params
    $user user id
    $shop_id
    $option pass 'delete' to delete
    */
    public function removeOrderSync($user_id, $order_id = null, $option = null){
    	if ($order_id && $option == 'delete') {
	    	$order = DB::table('drm_orders_new')->where('cms_user_id', $user_id)->where('order_id_api', $order_id)->first();
	    	if($order){
				DB::table('drm_customers')->where('id', $order->drm_customer_id)->delete();
				DB::table('drm_customer_address')->where('drm_customer_id' , $order->drm_customer_id)->delete();
				DB::table('drm_order_products')->where('drm_order_id', $order->id)->delete();
				DB::table('drm_orders_new')->where('id', $order->id)->delete();
				return 'Order ID: '.$order_id.' Deleted';
	    	}else{
	    		return 'Order ID not found: '.$order_id.' Deleted';
	    	}
    	}

    	$order_query = DB::table('drm_orders_new');
    	$order_query->where('cms_user_id', $user_id);
    	if ($order_id){
    		$order_query->where('order_id_api', $order_id);
    	}else{
    		$order_query->whereNotNull('order_id_api');
    	}
    	$order_query = $order_query->select('order_id_api','shop_id','total')->get();
    	return $order_query;
    }


    public function checkDuplicateOrder($user_id, $option = null){
    	$db_sync_data = $this->removeOrderSync($user_id);
    	$response_data = $this->orderSyncResponse($user_id);
    	$db_sync_data_collection = collect($db_sync_data);
  	
	  	if (count($db_sync_data_collection) && count($response_data) ) {
			print '<table>
				<thead>
					<tr>
						<th>API ID</th>
						<th>Shop ID</th>
						<th>Amount</th>
						<th>Comment</th>
					</tr>		
				</thead>
				<tbody>';
			$sync_filtered = $db_sync_data_collection->filter(function ($item, $key) use($response_data) {
				$api_id = $item->order_id_api;
				$shop_id = $item->shop_id;
				$total = $item->total;

				if( isset($response_data[$shop_id])  && is_array($response_data[$shop_id]) && in_array($api_id, $response_data[$shop_id]) ){
					print "<tr><td>$api_id</td><td>$shop_id</td><td>$total</td><td>Ok</td></tr>";
					return false;
				}elseif( isset($response_data[$shop_id])  && is_array($response_data[$shop_id]) && !in_array($api_id, $response_data[$shop_id]) ){
					print "<tr style='background:red'><td>$api_id</td><td>$shop_id</td><td>$total</td><td>Dirty</td></tr>";
					return true;
				}else{
					print "<tr style='background:yellow'><td>$api_id</td><td>$shop_id</td><td>$total</td><td>Check API </td></tr>";
					return false;
				}
			});

			print '	</tbody>
			</table>';

			// if($option == 'adjust'){

			// 	foreach ($sync_filtered as $item) {
			// 		$this->removeOrderSync($user_id, $item->order_id_api, 'delete');
			// 	}

			// 	if (!response()->json()) {
			// 		return redirect()->back();
			// 	}

			// 	print 'Order adjusted';
			// }
			dd($sync_filtered);
	  	}else{
	  		return 'No Record found';
	  	}


    }

    public function syncGambioorder($shop, $page = 1, &$data = [])
    {
        try{
	        if ($shop) {
	            $user = $shop->username;
	            $pass = $shop->password;
	            $shopId = $shop->id;
	            $base_url = $shop->url;
	            $api_path = "api.php/v2/";
	        } else {
	            throw new \Exception("You have not configured any shop yet!");
	        }

	        $auth = base64_encode("$user:$pass");

	        $per_page = 50;
	        $url  = $base_url . $api_path . "orders/?page=$page&per_page=$per_page";

	        $headers = array("Authorization: Basic " . $auth);

	        $ch = curl_init();
	        curl_setopt($ch, CURLOPT_URL, $url);
	        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
	        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");

	        $response = curl_exec($ch);
	        $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	        $errNo = curl_errno($ch);
	        $errStr = curl_error($ch);
	        curl_close($ch);

	        if ($responseCode != 200) {
	            throw new \Exception("Shop connection problem!");
	        }
	        $allOrder = json_decode($response);
	        if (count((array) $allOrder) != 0) {

	        	foreach ($allOrder as $value){
	        		array_push($data, $value->id);
	        	}
	        	
	            $page++;
	            $this->syncGambioorder($shop, $page, $data);
	        }

			return $data;	


	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }

    public function syncLengowOrder($shop, $page = 1, &$data = [])
    {
    	try{
	        if (!$shop) {
	            throw new \Exception("You have not configured any Lengow shop yet!");
	        }

	        $access_token = $shop->username;
	        $secret   = $shop->password;
	        $lengow = new LengowApi($access_token, $secret);

	        if ($lengow->token == "") {
	            throw new \Exception("$shop->shop_name shop token problem!");
	        }

	        $all = $lengow->getOrder($page);

	        if ($all) {
		        $allorder = $all->results;
		        foreach ((object) $allorder as $key => $value){
            		$name = $value->billing_address->full_name . $value->billing_address->first_name . $value->billing_address->last_name . $value->packages[0]->delivery->city;
            		if (strtolower(substr($name, 0, 3)) == 'xxx') continue;
		        	array_push($data, $value->marketplace_order_id);
		        }
		        if ($all->next) {
		            $page++;
		            $this->syncLengowOrder($shop, $page, $data);
		        }
	        }

	        return $data;

	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }





    function syncYategoOrder($shop)
    {
    	$order = [];
        try{
	        if (!$shop) {
	        	throw new \Exception("You have not configured any Yatego shop yet!");
	        }
	        $order_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_order";

	        $order_content = @file_get_contents($order_url);
	        if (!$order_content) {
	            throw new \Exception("Can not access url or no order found!");
	        }

	        $putted_orders = @file_put_contents(storage_path() . "/tempOrderTest.csv", $order_content);
	        if (!$putted_orders) {
	            throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderTest.csv");
	        }

	        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
	        $reader->setInputEncoding('UTF-8');
	        $reader->setDelimiter(';');
	        $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderTest.csv"));

	        $order_arr = $spreadsheet->getActiveSheet()->toArray();
	        $order_columns = $order_arr[0];
	        unset($order_arr[0]);

	        if (count($order_arr)) {
		        foreach ($order_arr as $k => $item) {
		        	$order_data = @array_combine($order_columns, $item);
		            array_push($order, $order_data['Order_ID']);
		        }
	        }
	        return $order;
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }



    public function syncEbayOrder($shop)
    {
    	$data = [];
    	try{
    		if($shop){
	    		$ebay_service = new EbayServices();
				$service = $ebay_service->createTrading();
				$request = new Types\GetOrdersRequestType();
				$request->RequesterCredentials = new Types\CustomSecurityHeaderType();
				$request->RequesterCredentials->eBayAuthToken = $shop->password;
				$request->CreateTimeFrom = \DateTime::createFromFormat('Y-m-d H:i:s', '2019-02-01 18:33:00');
				$request->CreateTimeTo = \DateTime::createFromFormat('Y-m-d H:i:s', now());
				$request->OrderStatus = 'All';
				$response = $service->getOrders($request);

				if(json_decode($response,true)['Ack'] == 'Success'){
					$response = (object)(json_decode($response,true)['OrderArray']);
					$all_orders = $response->Order;

					if($all_orders){

						foreach ($all_orders as $order){
							array_push($data, $order['OrderID']);
						}
					}
				}
    		}else{
    			throw new \Exception('You have not configured any Ebay shop!');
    		}
    		return $data;
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }


    // Shopify Orders
    public function syncShopifyOrder($shop)
    {
    	$data_arr = [];
    	try{
    		if($shop){
		        $url = $shop->url . 'admin/api/2020-01/orders.json?status=any&fulfillment_status=any';
		        // $url = $shop_details->url . 'admin/api/2020-01/draft_orders.json';
		        $client = new \GuzzleHttp\Client();
		        $response = $client->request('GET', $url, [
		            'auth' => [$shop->username, $shop->password]
		        ]);

		        if($response->getStatusCode() !== 200){
		        	throw new \Exception('Connection problem!');
		        }

				$data = $response->getBody()->getContents();
	        	$all_orders = (json_decode($data))->orders;

		        if($all_orders){
		        	foreach ((object) $all_orders as $value){
		        		array_push($data_arr, $value->id);
		        	}
		        }
	    	}else{
	    		throw new \Exception('You have not configured shop!');
	    	}

	    	return $data_arr;
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }

}
