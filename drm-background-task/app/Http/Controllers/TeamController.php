<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;

	use App\User;
	use Carbon\Carbon;
 	use App\Notifications\DRMNotification;

	use Illuminate\Support\Facades\Validator;
    use Illuminate\Support\Facades\Storage;
    use App;
    use Illuminate\Support\Str;
	use  App\Traits\ProjectShare;

	class TeamController extends Controller {
        public function affiliates(){
            $userId=CRUDBooster::myId();
            // dd($userId);
            if($userId){
                $time= request()->time;
                if($time=="MONTH"){
                    $users=DB::select(DB::raw("select u1.*, count(u2.id) as ref_count from cms_users as u1 join cms_users as u2 on u1.id=u2.referrer_id where MONTH(u2.created_at)=MONTH(CURRENT_DATE()) and YEAR(u2.created_at)=YEAR(CURRENT_DATE()) and u2.referrer_id IS NOT NULL group by u1.id order by ref_count desc"));
                    $time="Current Month";
                }elseif($time=="YEAR"){
                    $users=DB::select(DB::raw("select u1.*, count(u2.id) as ref_count from cms_users as u1 join cms_users as u2 on u1.id=u2.referrer_id where YEAR(u2.created_at)=YEAR(CURRENT_DATE()) and u2.referrer_id IS NOT NULL group by u1.id order by ref_count desc"));
                    $time="Current Year";
                }else{
                    $users=DB::select(DB::raw("select u1.*, count(u2.id) as ref_count from cms_users as u1 join cms_users as u2 on u1.id=u2.referrer_id where u2.referrer_id IS NOT NULL group by u1.id order by ref_count desc"));
                    $time="All time";
                }
                if(DB::table('cms_users')->where('id',$userId)->first()->ref_id==null){
                    DB::table('cms_users')->where('id',$userId)->update(['ref_id'=> Str::random(25)]);
                }
                $user=DB::table('cms_users')->where('id',$userId)->first();
                $usersMonth=DB::select(DB::raw("select u1.*, count(u2.id) as ref_count from cms_users as u1 join cms_users as u2 on u1.id=u2.referrer_id where MONTH(u2.created_at)=MONTH(CURRENT_DATE()) and YEAR(u2.created_at)=YEAR(CURRENT_DATE()) and u2.referrer_id IS NOT NULL group by u1.id order by ref_count desc"));
                $myRefMonth=DB::select(DB::raw("select count(id) as ref_count from cms_users where MONTH(created_at)=MONTH(CURRENT_DATE()) and YEAR(created_at)=YEAR(CURRENT_DATE()) and referrer_id=$userId"))[0];
                $myRef=DB::select(DB::raw("select count(id) as ref_count from cms_users where referrer_id=$userId"))[0];
                // dd($usersMonth);

                // dd($usersAllTime,$usersYear,$usersMonth);
                return view('admin.drm_team.affiliates',compact('user','users','myRef','myRefMonth','usersMonth','time'));
            }else{
                return redirect("/");
            }
        }

        public function sendInvitation(Request $request)
		{


            // $data['have_account'] = DB::table('cms_users')->where( 'email',$_REQUEST['shared_email'])->first();


			$key = \Illuminate\Support\Str::random(32);
			// dd($_REQUEST);


            $data = [];
            $data['key']=$key;
            $data['page_title'] = 'Team Invitation';
            // $data['project'] = DB::table('drm_projects')->where('id',$_REQUEST['project_id'])->first();
			$data['user'] = DB::table('cms_users')->find( CRUDBooster::myId());


			// return view('admin.drm_team.invitation_mail',$data);

            if ( !( filter_var( $_REQUEST['shared_email'], FILTER_VALIDATE_EMAIL)) )
            {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'),"Email is not valid","error");
            }


            app('drm.mailer')->getMailer()->send('admin.drm_team.invitation_mail', $data, function($messages) use ($data){
 	            $messages->from($data['user']->email);
 	            // $messages->from('<EMAIL>');

				$messages->to($_REQUEST['shared_email']);
                // $messages->to('<EMAIL>');

 	            $messages->subject("DRM Projects Invitation");
 	        });


			CRUDBooster::redirect(Request::server('HTTP_REFERER'),"Invitation Send. Project Shared.","success");
		}

    }