<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use Illuminate\Support\Str;
	use Illuminate\Support\Facades\Storage;
	use App\User;

	class AdminAppStoresController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "app_stores";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"App Id","name"=>"id"];
			$this->col[] = ["label"=>"App Name","name"=>"menu_name"];

			$this->col[] = ["label"=>"Category","name"=>"app_category_id","join"=>"app_categories,category_name"];
			$this->col[] = ['label'=>'Subscription Plan', 'name'=>'id'];
			$this->col[] = ["label"=>"Description","name"=>"description"];
			$this->col[] = ["label"=>"Icon","name"=>"icon","image"=>true];
			$this->col[] = ["label"=>"Promotion","name"=>"promotion"];

// 			$this->col[] = ["label"=>"Payment Type","name"=>"payment_type"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'App Name','name'=>'menu_name','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Url','name'=>'url','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Select App Category','name'=>'app_category_id','type'=>'select2','datatable'=>'app_categories,category_name'];


			$this->form[] = ['label'=>'Subscription Plan','name'=>'app_store_id','type'=>'select2','datatable'=>'subscription_plans,name','relationship_table'=>'app_store_plans'];

			$this->form[] = ['label'=>'Icon','name'=>'icon','type'=>'upload','validation'=>'image','width'=>'col-sm-10'];

			$this->form[] = ['label'=>'Description','name'=>'description','type'=>'wysiwyg','validation'=>'required','width'=>'col-sm-10'];
// 			$this->form[] = ['label'=>'Payment Type','name'=>'payment_type','type'=>'text','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();
		    $this->addaction[] = ['label'=>'Assign For Free','url'=>CRUDBooster::mainpath('app-assign/[id]'),'color'=>'info'];
				
			$this->addaction[] = ['label'=>'Promotion On','url'=>CRUDBooster::mainpath('promotion-active/[id]'), 'icon'=>'fa fa-bullhorn', 'showIf' => '[promotion] == Deactive'];
			
			$this->addaction[] = ['label'=>'Promotion Off','url'=>CRUDBooster::mainpath('promotion-inactive/[id]'), 'icon'=>'fa fa-times', 'showIf' => '[promotion] == Active'];


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here
	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here
	        $query->where('type','app');

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    	if($column_index==4){
			    $column_value = DB::table('subscription_plans')
			    ->join('app_store_plans', 'app_store_plans.subscription_plans_id', '=', 'subscription_plans.id')
			    ->where('app_store_plans.app_stores_id', $column_value)
			    ->select('subscription_plans.name as name')->implode('name', ', ');
	    	}
	    	//Your code here
	    	if($column_index==5){
	    		$column_value = Str::words(strip_tags($column_value), 8, '...');
	    	}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here
	        $postdata['type']='app';
	        if(Request::hasFile('icon')){
	        	$file = Request::file('icon');
	        	$postdata['icon'] = $this->fileUpload($file, $postdata['icon'], 'image');
	        }

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here
	        $postdata['type']='app';
	        if(Request::hasFile('icon')){
	        	$file = Request::file('icon');
	        	$postdata['icon'] = $this->fileUpload($file, $postdata['icon'], 'image');
	        }

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here
	    	$this->removeOldFile($id, 'icon');
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here
	        DB::table('app_store_plans')->where('app_store_plans.app_stores_id', $id)->delete();

	    }

	    public function getDeleteFile($id, $col){
	    	$this->removeOldFile($id, $col);
	    	return redirect()->back()->with(["msg" => 'Successfully deleted!']);
	    }

	    public function getEdit($id) {
		  //Create an Auth
		  if(!CRUDBooster::isUpdate() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {    
		    CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
		  }	  
		  $data = [];
		  $data['page_title'] = 'Edit Manage App Store';
		  $data['row'] = DB::table('app_stores')->where('id',$id)->first();
		  $data['app_categories'] = DB::table('app_categories')->get();

		  $data['app_subscrition_list'] = DB::table('subscription_plans')
			    ->join('app_store_plans', 'app_store_plans.subscription_plans_id', '=', 'subscription_plans.id')
			    ->where('app_store_plans.app_stores_id', $id)
			    ->select('subscription_plans.id as id')->pluck('id', 'id')->toArray();

		  $data['subscriptions'] = DB::table('subscription_plans')->get();
// return $data;
		  //Please use cbView method instead view method from laravel
		  $this->cbView('app_store.edit_app',$data);
		}

		public function getDetail($id) {
		  //Create an Auth
		  if(!CRUDBooster::isRead() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {    
		    CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
		  }
		  $data = [];
		  $data['page_title'] = 'Detail Manage App Store';
		  $data['row'] = DB::table('app_stores')->where('id',$id)->first();
		  $data['category'] = DB::table('app_categories')->find($data['row']->app_category_id)->category_name;
		  $data['app_subscritions'] = DB::table('subscription_plans')
			    ->join('app_store_plans', 'app_store_plans.subscription_plans_id', '=', 'subscription_plans.id')
			    ->where('app_store_plans.app_stores_id', $id)
			    ->select('subscription_plans.name as name')->implode('name', ', ');
		  //Please use cbView method instead view method from laravel
		  $this->cbView('app_store.view_app', $data);
		}

	    //By the way, you can still create your own method in here... :)
	   	private function fileUpload($file, $tmp, $folder){
	    	if( Storage::exists($tmp) ) Storage::delete($tmp); //del uploaded file
        	$file_name = $folder.'/'.md5( $file->getClientOriginalName(). microtime() ).'.'.$file->getClientOriginalExtension();
        	Storage::disk('spaces')->put($file_name, file_get_contents($file), 'public');
        	return ( Storage::disk('spaces')->exists($file_name) )? Storage::disk('spaces')->url($file_name) : null;
	    }

	    private function removeOldFile($id, $col){
	    	$item = DB::table('app_stores')->find($id);
	    	if($item && $item->{$col}){

				$url = $item->{$col};
				$search = '.com/';
				$file = substr($url, strpos($url, $search)+ strlen($search) );
	    		if( Storage::disk('spaces')->exists($file) ){
	    			Storage::disk('spaces')->delete($file);
	    		}
	    		DB::table('app_stores')->where('id', $id)->update([$col => null]);
	    	}
	    }

	    //  user assign for  free use
	    public function postIntervalUser(){
	    	try{
		    	$user_id = $_REQUEST['user_id'];
		    	$app_id = $_REQUEST['app_id'];
		    	$user_name = $_REQUEST['user_name'];
		    	
		    	if($user_id && $app_id){
		    		$plans=DB::table('app_store_plans as asp')
			          ->join('subscription_plans as sp','sp.id','=','asp.subscription_plans_id')
			          ->where('asp.app_stores_id', $app_id)
			          ->orderBy('sp.price','asc')
			          ->select('sp.*')
			          ->get();
		    		$assigned_plan = DB::table('app_assigns')->where(['user_id'=>$user_id,'app_id'=>$app_id])->first();
		    		if($assigned_plan){
		    			$plan_id = $assigned_plan->plan_id;
		    			return response()->json([
		    				'success' => true,
		    				'user_id' => $user_id,
		    				'html'	=> view('app_store.assign.user_plan',compact('user_id','user_name','plan_id', 'plans'))->render()
		    			]);
		    		}
		    		return response()->json([
	    				'success' => true,
	    				'user_id' => $user_id,
	    				'html'	=> view('app_store.assign.user_plan_empty',compact('user_id','user_name','plans'))->render()
		    		]);
		    	}	    		
	    	}catch(\Exception $e){
	    		return response()->json([
    				'success' => false,
    				'user_id' => $user_id,
    				'html'	=> '',
    				'message'	=> 'Error: '.$e->getMessage(),
	    		]);
	    	}

	    }
	    public function getAppAssign($id){
	    	if(CRUDBooster::isSuperadmin() || (CRUDBooster::myId() == 98) ){
		    	$app = DB::table('app_stores')->find($id, ['menu_name']);
		    	if($app){
			    	$users=User::where('id_cms_privileges',3)->select('name','id')->get();
			    	$assign_apps=DB::table('app_assigns')->join('cms_users', 'cms_users.id', '=', 'app_assigns.user_id')->where('app_id',$id)->select('app_assigns.*', 'cms_users.name as user_name')->get();

			    	$plans=DB::table('app_store_plans as asp')
				          ->join('subscription_plans as sp','sp.id','=','asp.subscription_plans_id')
				          ->where('asp.app_stores_id', $id)
				          ->orderBy('sp.price','asc')
				          ->select('sp.*')
				          ->get();
				    $app_name = $app->menu_name;     
			    	return view('app_store.app_assign',compact('id','users','assign_apps', 'plans', 'app_name'));
		    	}
		    	return redirect()->back()->with(['message_type' => 'error', 'message' => 'Invalid app!']);
	    	}else{
	    		return CRUDBooster::redirect('admin','You do not have access to this!', 'warning');
	    	}
	    }

	    public function postAppAssign(){

			$data=DB::table('app_assigns')->where('app_id',request()->app_id)->count();

			if($data>0){
				DB::table('app_assigns')->where('app_id',request()->app_id)->delete();
			}

			$app_id = request()->app_id;
			$plan_ids = (isset($_REQUEST['plan_ids']) && $_REQUEST['plan_ids'] )? $_REQUEST['plan_ids'] : null;
			$is_interval_app = (($app_id == config('global.interval_app_id')) || ($app_id == config('global.csv_interval_app_id')) ) ? true : false;

			if($is_interval_app && ($plan_ids == null)){
				return CRUDBooster::redirect('admin/app_stores','There Are No User Assign For This App!', 'info');
			}

			if((isset(request()->user_id)) and  (count(request()->user_id) >0)) {
				$count = 0;
				if($is_interval_app){
					$plan_ids = array_filter($plan_ids);
					foreach ($plan_ids as $user_id => $plan_id) {
						DB::table('app_assigns')->updateOrInsert(['user_id'=>$user_id,'app_id'=>$app_id], ['plan_id'=>$plan_id]);
						$count++;
					}
				}else{
					foreach (request()->user_id as $key => $value) {
						DB::table('app_assigns')->updateOrInsert(['user_id'=>$value,'app_id'=>$app_id]);
						$count++;
			    	}
				}
				$return_message = ($count>0)? "Assigned Success for $count user!" : 'Nothing Changed!';
	    		return CRUDBooster::redirect('admin/app_stores', $return_message, 'success');
			} else{

			return CRUDBooster::redirect('admin/app_stores','There Are No User Assign For This App!', 'info');
			}


	    }
	
	public function getPromotionActive($id){
		DB::table('app_stores')->where('id',$id)->update(['promotion' => 'Active']);
		CRUDBooster::redirect(CRUDBooster::adminPath('app_stores'),"App Promotion Activated!","success");
	}

	public function getPromotionInactive($id){
		DB::table('app_stores')->where('id',$id)->update(['promotion' => 'Deactive']);
		CRUDBooster::redirect(CRUDBooster::adminPath('app_stores'),"App Promotion Deactivated!!","success");
	}

	public function overDueCommandCall(){
		\Artisan::call('overdue:notify');
	}

}