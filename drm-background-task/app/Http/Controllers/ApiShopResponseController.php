<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use ServiceKey;
use App\Http\Resources\StripeOrderFetchResource;
use Illuminate\Support\Collection;



use App\Helper\LengowApi;
use DateTime;
use App\Helper\GambioApi;
use App\Helper\ShopifyApi;
use App\Helper\EbayApi;
use \Hkonnet\LaravelEbay\EbayServices;
use \DTS\eBaySDK\Trading\Services;
use \DTS\eBaySDK\Trading\Types;
use \DTS\eBaySDK\Trading\Enums;
use \DTS\eBaySDK\Constants;
use Ebay;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Notifications\DRMNotification;
use App\User;
use Carbon\Carbon;
use \Illuminate\Support\Arr;

class ApiShopResponseController extends Controller
{
        public function orderSyncResponseByShop($shop_id = null ){

            $shops = \App\Shop::query();
            if($shop_id) $shops->where('id', $shop_id);
            $shops = $shops->get();
            // return $shop_arr;
            $data = [];

            if($shops){
            	foreach ($shops as $shop) {
            		if($shop->channel == 1)
	                {
	                   $data[$shop->id] = $this->syncGambioorder($shop);
	                }
	                if($shop->channel == 2)
	                {
	                   $data[$shop->id] = $this->syncLengowOrder($shop);
	                }
	                if($shop->channel == 3)
	                {
	                   $data[$shop->id] = $this->syncYategoOrder($shop);
	                }
	                if($shop->channel == 4)
	                {
	                   $data[$shop->id] = $this->syncEbayOrder($shop);
	                }
	                if($shop->channel == 6)
	                {
	                   $data[$shop->id] = $this->syncShopifyOrder($shop);
	                }
            	}
            }

        	return $data;  	
    }

    public function orderSyncResponseByUser($user_id = null ){
		$shops = \App\Shop::query();
		if($user_id) $shops->where('user_id', $user_id);
		$shops = $shops->get();

		// return $shop_arr;
		$data = [];

		if($shops){
			foreach ($shops as $shop) {
				if($shop->channel == 1)
				{
					$data[$shop->id] = $this->syncGambioorder($shop);
				}
				if($shop->channel == 2)
				{
					$data[$shop->id] = $this->syncLengowOrder($shop);
				}
				if($shop->channel == 3)
				{
					$data[$shop->id] = $this->syncYategoOrder($shop);
				}
				if($shop->channel == 4)
				{
					$data[$shop->id] = $this->syncEbayOrder($shop);
				}
				if($shop->channel == 6)
				{
					$data[$shop->id] = $this->syncShopifyOrder($shop);
				}
			}
		}
		return $data;  	
    }

    public function syncGambioorder($shop, $page = 1, &$data = [])
    {
        try{
	        if ($shop) {
	            $user = $shop->username;
	            $pass = $shop->password;
	            $shopId = $shop->id;
	            $base_url = $shop->url;
	            $api_path = "api.php/v2/";
	        } else {
	            throw new \Exception("You have not configured any shop yet!");
	        }

	        $auth = base64_encode("$user:$pass");

	        $per_page = 50;
	        $url  = $base_url . $api_path . "orders/?page=$page&per_page=$per_page";

	        $headers = array("Authorization: Basic " . $auth);

	        $ch = curl_init();
	        curl_setopt($ch, CURLOPT_URL, $url);
	        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
	        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");

	        $response = curl_exec($ch);
	        $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	        $errNo = curl_errno($ch);
	        $errStr = curl_error($ch);
	        curl_close($ch);

	        if ($responseCode != 200) {
	            throw new \Exception("Shop connection problem!");
	        }
	        $allOrder = json_decode($response);
	        if (count((array) $allOrder) != 0) {

	        	foreach ($allOrder as $value){
	        		array_push($data, $value);
	        	}
	        	
	            $page++;
	            $this->syncGambioorder($shop, $page, $data);
	        }

			return $data;	


	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }

    public function syncLengowOrder($shop, $page = 1, &$data = [])
    {
    	try{
	        if (!$shop) {
	            throw new \Exception("You have not configured any Lengow shop yet!");
	        }

	        $access_token = $shop->username;
	        $secret   = $shop->password;
	        $lengow = new LengowApi($access_token, $secret);

	        if ($lengow->token == "") {
	            throw new \Exception("$shop->shop_name shop token problem!");
	        }

	        $all = $lengow->getOrder($page);

	        if ($all) {
		        $allorder = $all->results;
		        foreach ((object) $allorder as $key => $value){
            		$name = $value->billing_address->full_name . $value->billing_address->first_name . $value->billing_address->last_name . $value->packages[0]->delivery->city;
            		if (strtolower(substr($name, 0, 3)) == 'xxx') continue;
		        	array_push($data, $value);
		        }
		        if ($all->next) {
		            $page++;
		            $this->syncLengowOrder($shop, $page, $data);
		        }
	        }

	        return $data;

	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }

    function syncYategoOrder($shop)
    {
    	$order = [];
        try{
	        if (!$shop) {
	        	throw new \Exception("You have not configured any Yatego shop yet!");
	        }
	        $order_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_order";

	        $order_content = @file_get_contents($order_url);
	        if (!$order_content) {
	            throw new \Exception("Can not access url or no order found!");
	        }

	        $putted_orders = @file_put_contents(storage_path() . "/tempOrderTest.csv", $order_content);
	        if (!$putted_orders) {
	            throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderTest.csv");
	        }

	        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
	        $reader->setInputEncoding('UTF-8');
	        $reader->setDelimiter(';');
	        $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderTest.csv"));

	        $order_arr = $spreadsheet->getActiveSheet()->toArray();
	        $order_columns = $order_arr[0];
	        unset($order_arr[0]);

	        if (count($order_arr)) {

				$order_id_from = $order_arr[1][1];
		        $order_id_to = $order_arr[count($order_arr)][1];

		        $order_product_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_products&von=" . $order_id_from . "&bis=" . $order_id_to . "&varids=1";
		        $product_content = @file_get_contents($order_product_url);

		        $putted = @file_put_contents(storage_path() . "/tempOrderProduct.csv", $product_content);
		        if (!$putted) {
		            throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderProduct.csv");
		        }


		        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
		        $reader->setInputEncoding('UTF-8');
		        $reader->setDelimiter(';');
		        $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderProduct.csv"));

		        $product_arr = $spreadsheet->getActiveSheet()->toArray();
		        $product_columns = $product_arr[0];
		        unset($product_arr[0]);
		        $product_arr_new = [];
		        foreach ($product_arr as $item) {
		            $product = @array_combine($product_columns, $item);

		            if (!$product) {
		                throw new \Exception('Error in product details. Please contact admin.');
		            }

		            $product_arr_new[] = $product;
		        }

		        $product_collection = collect($product_arr_new);

		        foreach ($order_arr as $k => $item) {
		        	$order_data = @array_combine($order_columns, $item);
		        	$order_data['products'] = $product_collection->where('Bestellnummer', $order_data['Bestellnummer'])->toArray();
		            array_push($order, $order_data);
		        }
	        }
	        return $order;
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }

    public function syncEbayOrder($shop)
    {
    	$data = [];
    	try{
    		if($shop){
	    		$ebay_service = new EbayServices();
				$service = $ebay_service->createTrading();
				$request = new Types\GetOrdersRequestType();
				$request->RequesterCredentials = new Types\CustomSecurityHeaderType();
				$request->RequesterCredentials->eBayAuthToken = $shop->password;
				$request->CreateTimeFrom = \DateTime::createFromFormat('Y-m-d H:i:s', '2019-02-01 18:33:00');
				$request->CreateTimeTo = \DateTime::createFromFormat('Y-m-d H:i:s', now());
				$request->OrderStatus = 'All';
				$response = $service->getOrders($request);

				if(json_decode($response,true)['Ack'] == 'Success'){
					$response = (object)(json_decode($response,true)['OrderArray']);
					$all_orders = $response->Order;

					if($all_orders){

						foreach ($all_orders as $order){
							array_push($data, $order);
						}
					}
				}
    		}else{
    			throw new \Exception('You have not configured any Ebay shop!');
    		}
    		return $data;
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }


    // Shopify Orders
    public function syncShopifyOrder($shop)
    {
    	$data_arr = [];
    	try{
    		if($shop){
		        $url = $shop->url . 'admin/api/2020-01/orders.json?status=any&fulfillment_status=any';
		        // $url = $shop_details->url . 'admin/api/2020-01/draft_orders.json';
		        $client = new \GuzzleHttp\Client();
		        $response = $client->request('GET', $url, [
		            'auth' => [$shop->username, $shop->password]
		        ]);

		        if($response->getStatusCode() !== 200){
		        	throw new \Exception('Connection problem!');
		        }

				$data = $response->getBody()->getContents();
	        	$all_orders = (json_decode($data))->orders;

		        if($all_orders){
		        	foreach ((object) $all_orders as $value){
		        		array_push($data_arr, $value);
		        	}
		        }
	    	}else{
	    		throw new \Exception('You have not configured shop!');
	    	}

	    	return $data_arr;
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }
}
