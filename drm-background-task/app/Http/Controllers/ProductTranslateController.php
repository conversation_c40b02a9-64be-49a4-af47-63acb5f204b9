<?php

namespace App\Http\Controllers;
use Google\Cloud\Translate\V2\TranslateClient;
use Illuminate\Http\Request;
use DB;
use App\User;
use CRUDBooster;
use App\Mail\AppPurchaseConfirmation;
use Illuminate\Support\Facades\Mail;
use ServiceKey;

class ProductTranslateController extends Controller
{
    public function getLanguage(){
      $product_id=request()->product_id;
      if($_POST['lang'] ==''){
        $lang='de';
      }else{
        $lang=$_POST['lang'];
      }
    	$results=DB::table('countries')->where('language_shortcode','!=',$lang)->get();
    	return view('app_store.language_modal',compact('results','lang','product_id')); 
    }


    public function getProduct(){

    $product_id=request()->product_id;
    $language_id=request()->language_id;
    $source=request()->lang;

    $user = User::find(CRUDBooster::myId());

		$table = "drm_translation_".$source;
		$results = DB::table('drm_products')
		->join($table, $table . '.product_id', '=', 'drm_products.id')
		->whereNull('drm_products.deleted_at')
		->select('drm_products.*', 'drm_products.description as descriptionProduct', 'drm_products.name as product_category_nameroduct', 'drm_products.id as product_id', $table . '.*', $table . '.title as name')
		->whereIn('drm_products.id',request()->product_id)->get();


    $languages= DB::table('countries')->whereIn('id',$language_id)->get();

       $title='';
       $cat='';
       $siz='';
       $weight='';
       $brand='';
       $material='';
       $production_year='';
       $des='';
       $color='';
       $total_text='';

       foreach ($results as $key => $result) {
       	    $title .=$result->name;
       	    $des .=$result->description;
       	    $color .=$result->item_color;
            $siz .=$result->item_size;
            $weight .=$result->item_weight;
            $brand .=$result->brand;
            $material .=$result->materials;
            $production_year .=$result->production_year;
       }
       $total_text .=$title .' '. $des .' '. $color.' '.$production_year.' '.$material.' '.$brand.' '.$weight.' ' .$siz.' '.$cat;

       $total_language =count(request()->language_id);
       $count=str_word_count($total_text);

      //User term
      $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
      $term = ($privacy)? $privacy->page_content : '';
      $user_data = '<div id="customer_data_term"></div>';
      if($user->billing_detail){
          $billing = $user->billing_detail;
          $user_data = '<div id="customer_data_term">'.$billing->company_name.'<br>'.$billing->address.'<br>'.$billing->zip.' '.$billing->city.'<br>'.$billing->country->name.'</div>';
      }
      if (strpos($term, '{customer}') !== false) {
          $term = str_replace('{customer}', $user_data, $term);
      }
    	return view('app_store.text_modal',compact('results','count','total_language','language_id','product_id','languages','source', 'user', 'term'));
    }

    // jahidulhasanzahid
    public function sofort_translate(){
      // dd(request());
      $req=json_decode(request()->data);
      // dd($req);
      // dd(\request()->get());
      $lang_id = json_decode($req->language_id);
      $productNew = json_decode($req->product_id);
      $translateProductID=implode(",",$productNew);
      $translateProductLangID = array();
      foreach($lang_id as $langID){
        $toTranslateLangName = DB::table('countries')->where('id',$langID)->select('name')->first();
        $translateProductLangID[] = $toTranslateLangName->name;
      }
      
      $translateProductLangIDshow = implode(",",$translateProductLangID);
      // dd($translateProductLangIDshow);
      $price=$req->price *100;
      $payment_source=request()->source;
      // dd($payment_source);
      \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));

   
    // dd($charge);  
    try{
      $charge = \Stripe\Charge::create([
        'amount' => $price,
        'currency' => 'eur',
        'source' => $payment_source,
      ]);
      if($charge->status != "pending"){
        // return CRUDBooster::redirect(url("https://drm_v7.test/admin/drm_products"),"Sorry, the payment you made is failed","info");
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'),"Sorry, the payment you made is failed!","info");
      }
    }catch(\Exception $e){
      return "Sorry, the payment you made is failed";
    } 
    
      // dd($product_id);
      
      $countryName = DB::table('countries')->where('language_shortcode','=',$req->source)->select('name')->first();
      $countryNameShow = $countryName->name;
      $languages= DB::table('countries')->whereIn('id',$lang_id)->get();

      foreach ($languages as $key => $value) {
    
         $this->appTranslate($productNew,$req->source,$value->language_shortcode);

      }

      // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
      //   ->join('countries','countries.id','=','billing_details.country_id')
      //   ->first();

      //   $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";

        $taxShow = config('global.tax_for_invoice');
        $price = $req->price.'00';
				$total_tax = ($price * $taxShow) /100;
        $order_info = [
            'user_id' => 98,
            'cms_client'  => CRUDBooster::myId(),
            'order_date'    => date('Y-m-d H:i:s'),
            'total' => round(($price),2),
            'sub_total' => round($price-$total_tax,2),
            'total_tax' => round($total_tax,2),
            'payment_type'  => "Sofort",
            'status'    => "Succeeded",
            'currency'  => "EUR",
            'adjustment'    => 0,
            'insert_type'   => 3,
            'shop_id'       => 8,
            'billing'   => userToBillingJson(CRUDBooster::myId()),
            'order_id_api'  => $charge->id,
        ];

          $carts = [];
          $cart_item = [];
          $cart_item['id'] = 1;
          $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT','Product Translate ID  '.$translateProductID.'.');
          $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT',"Product Translate from $countryNameShow to $translateProductLangIDshow.");
          $cart_item['qty'] = 1;
          $cart_item['rate'] = round($price,2);
          $cart_item['tax'] = $taxShow;
          $cart_item['product_discount'] = 0;
          $cart_item['amount'] = round($price,2);
          $carts[] = $cart_item;
          $order_info['cart'] = json_encode($carts);

        app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
        

      // return CRUDBooster::redirect(url("https://drm_v7.test/admin/drm_products"),"Successfully Translated Product","success");
      CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'),"Successfully Translated Product","success");
      

    }

    public function store_translate_product_sofort(){

      if($_POST['price'] < 1.00){
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'),"Amount must convert to at least 1 euro.","warning");
      }
      else{
      $user=User::where('id',CRUDBooster::myId())->first();
      if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);
      \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));

      $source = \Stripe\Source::create([
        "type" => "sofort",
        "amount" => $_POST['price']*100,
        "currency" => "eur",
        "redirect" => [
          "return_url" => CRUDBooster::adminPath('')."/sofort_translate?data=".json_encode(request()->post()),
        ],
        "sofort" => [
          "country" => "DE",
        ],
        "owner" => [
          "email" => $user->email,
          "name" => $user->name,
        ]
      ]);

      return redirect($source['redirect']['url']);
        }
    }


    public function giropay_translate(){
      // dd(request());
      $req=json_decode(request()->data);
      // dd($req);
      // dd(\request()->get());
      $lang_id = json_decode($req->language_id);
      $productNew = json_decode($req->product_id);

      $translateProductID=implode(",",$productNew);
      $translateProductLangID = array();
      foreach($lang_id as $langID){
        $toTranslateLangName = DB::table('countries')->where('id',$langID)->select('name')->first();
        $translateProductLangID[] = $toTranslateLangName->name;
      }
      
      $translateProductLangIDshow = implode(",",$translateProductLangID);
      // dd($translateProductLangIDshow);

      $price=$req->price *100;
      $payment_source=request()->source;
      // dd($payment_source);
      \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));

   
    // dd($charge);  
    try{
      $charge = \Stripe\Charge::create([
        'amount' => $price,
        'currency' => 'eur',
        'source' => $payment_source,
        
      ]);
      if($charge->status!="succeeded"){
        // return CRUDBooster::redirect(url("https://drm_v7.test/admin/drm_products"),"Sorry, the payment you made is failed","info");
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'),"Sorry, the payment you made is failed!","info");
      }
    }catch(\Exception $e){
      return "Sorry, the payment you made is failed";
    } 
    
      // dd($product_id);
      
      $countryName = DB::table('countries')->where('language_shortcode','=',$req->source)->select('name')->first();
      $countryNameShow = $countryName->name;
      $languages= DB::table('countries')->whereIn('id',$lang_id)->get();

      foreach ($languages as $key => $value) {
    
         $this->appTranslate($productNew,$req->source,$value->language_shortcode);

      }

      // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
      //   ->join('countries','countries.id','=','billing_details.country_id')
      //   ->first();

      //   $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";

        $taxShow = config('global.tax_for_invoice');
        $price = $req->price.'00';
				$total_tax = ($price * $taxShow) /100;
        $order_info = [
            'user_id' => 98,
            'cms_client'  => CRUDBooster::myId(),
            'order_date'    => date('Y-m-d H:i:s'),
            'total' => round(($price),2),
            'sub_total' => round($price-$total_tax,2),
            'total_tax' => round($total_tax,2),
            'payment_type'  => "Giropay",
            'status'    => "Succeeded",
            'currency'  => "EUR",
            'adjustment'    => 0,
            'insert_type'   => 3,
            'shop_id'       => 8,
            'billing'   => userToBillingJson(CRUDBooster::myId()),
            'order_id_api'  => $charge->id,
        ];

          $carts = [];
          $cart_item = [];
          $cart_item['id'] = 1;
          $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT','Product Translate ID  '.$translateProductID.'.');
          $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT',"Product Translate from $countryNameShow to $translateProductLangIDshow.");
          $cart_item['qty'] = 1;
          $cart_item['rate'] = round($price,2);
          $cart_item['tax'] = $taxShow;
          $cart_item['product_discount'] = 0;
          $cart_item['amount'] = round($price,2);
          $carts[] = $cart_item;
          $order_info['cart'] = json_encode($carts);

        app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);

      // return CRUDBooster::redirect(url("https://drm_v7.test/admin/drm_products"),"Successfully Translated Product","success");
      CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'),"Successfully Translated Product","success");

    }


    public function store_translate_product_giropay(){
      if($_POST['price'] < 1.00){
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'),"Amount must convert to at least 1 euro.","warning");
      }
      else{
      $user=User::where('id',CRUDBooster::myId())->first();
      if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);
      \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));

      $source = \Stripe\Source::create([
        "type" => "giropay",
        "amount" => $_POST['price']*100,
        "currency" => "eur",
        "redirect" => [
          "return_url" => CRUDBooster::adminPath('')."/giropay_appointment?data=".json_encode(request()->post()),
        ],
        "owner" => [
          "email" => $user->email,
          "name" => $user->name,
        ]
      ]);
      return redirect($source['redirect']['url']);
      }
    }
    // jahidulhasanzaid

    public function store_translate_product(Request $request){
    \Stripe\Stripe::setApiKey( ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91') );
    $user=User::with('billing_detail')->find(CRUDBooster::myId());
    $countryName = DB::table('countries')->where('language_shortcode','=',$request->source)->select('name')->first();
    $countryNameShow = $countryName->name;
    
    $translateProductID=implode(",",$request->product_id);
    $translateProductLangID = array();
    foreach($request->language_id as $langID){
      $toTranslateLangName = DB::table('countries')->where('id',$langID)->select('name')->first();
      $translateProductLangID[] = $toTranslateLangName->name;
    }
      
    $translateProductLangIDshow = implode(",",$translateProductLangID);
    // dd($translateProductLangIDshow);

    if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);


    try{
      // operation  start
        // //DB::beginTransaction();
      $charge = \Stripe\Charge::create([
        'amount' => $request->price * 100,
        'currency' => 'eur',
        'description' => 'Product Translated ',
        'source' => $request->stripeToken
      ]);

      if($charge['status'] == "succeeded"){
        $languages= DB::table('countries')->whereIn('id', $request->language_id)->get();
      foreach ($languages as $key => $value) {

         $this->appTranslate($request->product_id, $request->source, $value->language_shortcode);
      }

      $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
        ->join('countries','countries.id','=','billing_details.country_id')
        ->first();

        $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";
        
        $taxShow = config('global.tax_for_invoice');
        $price = $request->price.'00';
				$total_tax = ($price * $taxShow) /100;
        $order_info = [
            'user_id' => 98,
            'cms_client'  => CRUDBooster::myId(),
            'order_date'    => date('Y-m-d H:i:s'),
            'total' => round(($price),2),
            'sub_total' => round($price-$total_tax,2),
            'total_tax' => round($total_tax,2),
            'payment_type'  => "Stripe Card",
            'status'    => "Succeeded",
            'currency'  => "EUR",
            'adjustment'    => 0,
            'insert_type'   => 3,
            'shop_id'       => 8,
            'billing'   => userToBillingJson(CRUDBooster::myId()),
            'order_id_api'  => $charge->id,
        ];

          $carts = [];
          $cart_item = [];
          $cart_item['id'] = 1;
          $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT','Product Translate ID  '.$translateProductID.'.');
          $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT',"Product Translate from $countryNameShow to $translateProductLangIDshow.");
          $cart_item['qty'] = 1;
          $cart_item['rate'] = round($price,2);
          $cart_item['tax'] = $taxShow;
          $cart_item['product_discount'] = 0;
          $cart_item['amount'] = round($price,2);
          $carts[] = $cart_item;
          $order_info['cart'] = json_encode($carts);

        app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);

      $postdata=[
        'app_name'=>'Product Translate',
        'price'=>$request->price,
        'subject'=>'Product Translate confirmation By DRM',
      ];
      app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new AppPurchaseConfirmation($postdata));

      }

      // operation  end

     // //DB::commit();    // Commiting  ==> There is no problem whatsoever
    return response(['status'=>true, 'message'=>'Successfully Translated Product']);
    } catch (\Exception $e) {
        // //DB::rollBack();   // rollbacking  ==> Something went wrong

        return response(['status'=> false,'message'=>$e->getMessage()]);
    }

    }


    public function appTranslate ($productNew, $source, $user_lang) {

        $language=DB::table('countries')->where('language_shortcode',$source)->first();
        $table = "drm_translation_".$source;

         $products = DB::table('drm_products')
        ->join($table, $table . '.product_id', '=', 'drm_products.id')
        ->whereNull('drm_products.deleted_at')
        ->select('drm_products.*', 'drm_products.description as descriptionProduct', 'drm_products.name as product_category_nameroduct', 'drm_products.id as product_id', $table . '.*', $table . '.title as name')
        ->whereIn('drm_products.id',$productNew)->get();

        $translate = new TranslateClient(['key' =>'AIzaSyCXmhbZQ5pBqXjKgAKwYQVoXF7FHZEE9FA']);
          $charCount = 0;
          $charCount +=$_POST['count'];
            
            foreach($products as $product) {
                
                $names        = ""; 
                $descriptions = "";
                $name        = ($product->name=='')?"":$product->name;
                $description = ($product->description=='')?"":$product->description;
                $item_weight = ($product->item_weight=='')?"":$product->item_weight;
                $item_size = ($product->item_size=='')?"":$product->item_size;
                $item_color = ($product->item_color=='')?"":$product->item_color;
                $brand = ($product->brand=='')?"":$product->brand;
                $production_year = ($product->production_year=='')?"":$product->production_year;
                $material = ($product->materials=='')?"":$product->materials;

              if($charCount > 95000) { sleep(100); $charCount = 0; }
              try {
                $results = $translate->translateBatch([$name, $description,$item_weight,$item_size,$item_color,$brand,$production_year,$material], ['source'=>$source, 'target' =>$user_lang]);

               $names= $results[0]['text'];
               $descriptions= $results[1]['text'];
               $item_weights= $results[2]['text'];
               $item_sizes= $results[3]['text'];
               $item_colors= $results[4]['text'];
               $brands= $results[5]['text'];
               $production_years= $results[6]['text'];
               $materials= $results[7]['text'];

              }catch(Exception $e){
             
                        dd($e);
                        
              }finally {

                      if($names =='' or $descriptions=='' or $item_weights=='' or $item_size=='' or $item_colors=='' or $brands=='' or $production_years=='' or $materials ==''){
                        sleep(2);
                         $results = $translate->translateBatch([$name, $description,$item_weight,$item_size,$item_color,$brand,$production_year,$material], ['source'=>$source, 'target' =>$user_lang]);

                           $names= $results[0]['text'];
                           $descriptions= $results[1]['text'];
                           $item_weights= $results[2]['text'];
                           $item_sizes= $results[3]['text'];
                           $item_colors= $results[4]['text'];
                           $brands= $results[5]['text'];
                           $production_years= $results[6]['text'];
                           $materials= $results[7]['text'];

                      }

                      $rows['title'] = strip_tags($names);
                      $rows['description'] = strip_tags($descriptions);
                      $rows['item_weight'] = strip_tags($item_weights);
                      $rows['item_size'] = strip_tags($item_sizes);
                      $rows['item_color'] = strip_tags($item_colors);
                      $rows['brand'] = strip_tags($brands);
                      $rows['production_year'] = strip_tags($production_years);
                      $rows['materials'] = strip_tags($materials);
                      $rows['source_id'] =$language->id ;
                      $rows['ean'] = $product->ean;
                      DB::table('drm_translation_'.$user_lang)->updateOrInsert(['product_id' => $product->product_id], $rows);
                      
                      $category = DB::table('drm_products')->join('drm_product_categories', 'drm_product_categories.product_id', '=', 'drm_products.id')
                          ->join('drm_category', 'drm_category.id', '=', 'drm_product_categories.category_id')
                          ->select('drm_category.*', 'drm_products.ean')
                          ->where('drm_products.id', $product->product_id)->get();
                          $column='category_name_'.$user_lang;
                          foreach ($category as $value) {
                                          $trans_category = 'category_name_' . $source;//lang = de/en/fr
                                          $category = $value->category_name ?: $value->$trans_category;
                                  $cat= ($category=='')?"":$category;
                                 $data = $translate->translateBatch([$cat], ['source'=>$source, 'target' =>$user_lang]);
                                 $category_names= $data[0]['text'];
                                 DB::table('drm_category')->where('id',$value->id)->update([$column=>$category_names]);
                              }

              }
            
            }
}


public function update_calculation(){

  $product_id=request()->product_id;

    
    $table = "drm_translation_".request()->source;
     $results = DB::table('drm_products')
    ->join($table, $table . '.product_id', '=', 'drm_products.id')
    ->whereNull('drm_products.deleted_at')
    ->select('drm_products.*', 'drm_products.description as descriptionProduct', 'drm_products.name as product_category_nameroduct', 'drm_products.id as product_id', $table . '.*', $table . '.title as name')
    ->whereIn('drm_products.id',request()->product_id)->get();

       $title='';
       $cat='';
       $siz='';
       $weight='';
       $brand='';
       $material='';
       $production_year='';
       $des='';
       $color='';
       $total_text='';

       foreach ($results as $key => $result) {
            $title .=$result->name;
            $des .=$result->description;
            $color .=$result->item_color;
            $siz .=$result->item_size;
            $weight .=$result->item_weight;
            $brand .=$result->brand;
            $material .=$result->materials;
            $production_year .=$result->production_year;
       }
       $total_text .=$title .' '. $des .' '. $color.' '.$production_year.' '.$material.' '.$brand.' '.$weight.' ' .$siz.' '.$cat;
      $count=str_word_count($total_text);
      $total=$count * request()->language;

       $price=$total/100;
       return response(['total'=>$total,'price'=>$price]);
}

public function read_moore(){


    $table = "drm_translation_".request()->source;


$results = DB::table('drm_products')
    ->join($table, $table . '.product_id', '=', 'drm_products.id')
    ->whereNull('drm_products.deleted_at')
    ->select('drm_products.*', 'drm_products.description as descriptionProduct', 'drm_products.name as product_category_nameroduct', 'drm_products.id as product_id', $table . '.*', $table . '.title as name')
    ->where('drm_products.id',request()->product_id)->get();

  return view('app_store.read_moore',compact('results'));
}


}
