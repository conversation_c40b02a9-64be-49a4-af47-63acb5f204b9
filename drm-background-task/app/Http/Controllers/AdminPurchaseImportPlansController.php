<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use App\User;
	use ServiceKey;
	// use App\Mail\AppPurchaseConfirmation;
	use App\Mail\DRMSEndMail;
	use Illuminate\Support\Facades\Mail;
	class AdminPurchaseImportPlansController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = false;
			$this->button_edit = false;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "purchase_import_plans";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Import Plan","name"=>"import_plan_id","join"=>"import_plans,plan"];
			$this->col[] = ["label"=>"Customer","name"=>"cms_user_id","join"=>"cms_users,name"];
			$this->col[] = ["label"=>"Product Amount Import","name"=>"product_amount_import"];
			$this->col[] = ["label"=>"Start Date","name"=>"start_date"];
			$this->col[] = ["label"=>"End Date","name"=>"end_date"];
			$this->col[] = ["label"=>"Status","name"=>"status"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			// $this->form = [];
			// $this->form[] = ['label'=>'Import Plan Id','name'=>'import_plan_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'import_plan,id'];
			// $this->form[] = ['label'=>'Start Date','name'=>'start_date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'End Date','name'=>'end_date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Status','name'=>'status','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Import Plan Id","name"=>"import_plan_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"import_plan,id"];
			//$this->form[] = ["label"=>"Start Date","name"=>"start_date","type"=>"date","required"=>TRUE,"validation"=>"required|date"];
			//$this->form[] = ["label"=>"End Date","name"=>"end_date","type"=>"date","required"=>TRUE,"validation"=>"required|date"];
			//$this->form[] = ["label"=>"Status","name"=>"status","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)

	    public function getImportPayment(){
	    	$plans=DB::table('import_plans')->where('status',1)->get();

	    	$user = User::find(CRUDBooster::myId());

	        //User term
	        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
	        $term = ($privacy)? $privacy->page_content : '';
	        $user_data = '<div id="customer_data_term"></div>';
	        if($user->billing_detail){
	            $billing = $user->billing_detail;
	            $user_data = '<div id="customer_data_term">'.$billing->company_name.'<br>'.$billing->address.'<br>'.$billing->zip.' '.$billing->city.'<br>'.$billing->country->name.'</div>';
	        }
	        if (strpos($term, '{customer}') !== false) {
	            $term = str_replace('{customer}', $user_data, $term);
	        }
	    	return view('app_store.import_payment',compact('plans', 'term'));
	    }

	    public function importPayment(){
	    	$old=DB::table('purchase_import_plans')->where('cms_user_id',CRUDBooster::myId())->first();
	    	$user = User::with('billing_detail')->find(CRUDBooster::myId());
			if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

		    \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
			$database_plan=DB::table('import_plans')->find($_POST['plan_id']);
	    	//craete  method
			//DB::beginTransaction();
    	try{
	    	//stripe  customer
	    	$customers = \Stripe\Customer::all(['email' => $user->email]);
		    $cust=$customers->jsonSerialize();
	    	if(!empty($cust['data'])){
		    	$custId=$cust['data'][0]['id'];
			}else{

			    $customer = \Stripe\Customer::create(array(
			        'email' => $user->email,
			        'source'  => $_POST['stripeToken'],
			    ));
			    $custId=$customer->id;
			}
	    	//stripe  customer
	    	$priceCents = ($database_plan->amount*100);
	    	//stripe  plan
	    	$plans = \Stripe\Plan::all(["amount" => $priceCents,
	        "currency" => 'eur',
	        "interval" => $database_plan->interval]);
		    $plan=$plans->jsonSerialize();
		    if(!empty($plan['data'])) {
		    	$planId=$plan['data'][0]['id'];
		    }else{
		    	$plan = \Stripe\Plan::create(array(
		        "product" => [
		            "name" => $database_plan->plan
		        ],
		        "amount" => $priceCents,
		        "currency" => 'eur',
		        "interval" => $database_plan->interval,
		        "interval_count" => 1
		      ));
		    	$planId=$plan->id;
		    }

		    //Cancel  subscription
	    	if($old !=null){

		    	$old_subscription = \Stripe\Subscription::retrieve($old->stripe_subscription_id);

			    $old_subscription->cancel();

	    	}
	    	//stripe  plan

	    	$subscription = \Stripe\Subscription::create(array(
                "customer" => $custId,
                "items" => array(
                    array(
                        "plan" => $planId,
                    ),
                ),
            ));

        $subsData = $subscription->jsonSerialize();
	    	$current_period_start = date("Y-m-d", $subsData['current_period_start']);
	      $current_period_end = date("Y-m-d", $subsData['current_period_end']);

	    	DB::table('purchase_import_plans')->updateOrInsert(['cms_user_id'=>CRUDBooster::myId()],
	          [
	          'price'=>$database_plan->amount,
	          'import_plan_id'=>request()->plan_id,
	          'stripe_plan_id'=>$subsData['plan']['id'],
	          'stripe_customer_id'=>$subsData['customer'],
	          'payer_email'=>$user->email,
	          'stripe_subscription_id'=> $subsData['id'],
	          'type'=>$subsData['plan']['interval'],
	          'status'=>1,
	          'start_date'=>$current_period_start,
	          'end_date'=>$current_period_end,
						'product_amount_import' => $database_plan->product_amount,
	        ]);

					DB::table('app_trials')->where('user_id',CRUDBooster::myId())->where('app_id',0)->update(['trial_days'=>0]);

	    	//Insert order to daily account
			$taxShow = config('global.tax_for_invoice');
			$price = $database_plan->amount; //$request->fixed_price.'00';
			// $total_tax = ($price * $taxShow) /100;
			$order_info = [
				'user_id' => 98,
            	'cms_client'  => CRUDBooster::myId(),
				'order_date'    => date('Y-m-d H:i:s'),
				'total' => round($price,2),
				'sub_total' => round($price,2),
				'total_tax' => 0,
				'payment_type'  => "Stripe Card",
				'status'    => "Succeeded",
				'currency'  => "EUR",
				'adjustment'    => 0,
				'insert_type'   => 3,
				'shop_id'       => 8,
				'billing'   => userToBillingJson(CRUDBooster::myId()),
				'order_id_api'  => $subsData['id'],
			];

			$carts = [];
			$cart_item = [];
			$cart_item['id'] = 1;
			$cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', $database_plan->plan);
			$cart_item['description'] = iconv('UTF-8', 'ASCII//TRANSLIT','Import Plan Purchase. Plan Name is "'.$database_plan->plan.'". Purchase Date '.date('Y-m-d H:i:s'));
			$cart_item['qty'] = 1;
			$cart_item['rate'] = round($price,2);
			$cart_item['tax'] = $taxShow;
			$cart_item['product_discount'] = 0;
			$cart_item['amount'] = round($price,2);
			$carts[] = $cart_item;
			$order_info['cart'] = json_encode($carts);
	        app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);

			 //DB::commit();    // Commiting  ==> There is no problem whatsoever
             session()->put('url',null);
		    return response(['status'=>true, 'message'=>'Import Plan Purchase success!','url'=>$_POST['url']]);
		    } catch (\Exception $e) {
		        //DB::rollBack();   // rollbacking  ==> Something went wrong

		        return response(['status'=> false,'message'=>$e->getMessage()]);
		    }



	  }

	  public function ImportSubscriptionCancel($id){
	  	\Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
    	$data=DB::table('purchase_import_plans as pip')
    			->join('import_plans','import_plans.id','=','pip.import_plan_id')
    			->where('pip.id',$id)
    			->first();
    	$user=User::with('billing_detail')->find($data->cms_user_id);
    	//DB::beginTransaction();
    	try{
    	    $old=\Stripe\Subscription::retrieve($data->stripe_subscription_id);
    		if($old){
    		\Stripe\Subscription::update($data->stripe_subscription_id, [
			   'cancel_at_period_end' => true
			]);
    		}

    		DB::table('purchase_import_plans')->where('id',$id)->update(['is_renew_cancel'=>1]);

    		$tags = [
			    'app_name' =>  $data->plan,
			    'subscription_interval' =>  ucfirst($data->interval),
			    'period_end' =>  $data->end_date,
			    'period_start' =>  date('Y-m-d'),
			];

			$slug = 'subscription_cancel'; //Page slug
            $lang = getUserSavedLang($user->billing_detail->email);
			$mail_data = DRMParseMailTemplate($tags, $slug, $lang); //Generated html
			app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data)); //Send

    		//DB::commit();    // Commiting  ==> There is no problem whatsoever

	    return response(['status'=>true, 'message'=>'Subscription Cancel Success!']);
	    } catch (\Exception $e) {
	        //DB::rollBack();   // rollbacking  ==> Something went wrong

	        return response(['status'=> false,'message'=>$e->getMessage()]);
	    }
	}


}
