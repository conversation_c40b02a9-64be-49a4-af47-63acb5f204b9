<?php
namespace App\Http\Controllers;

use App\EmailMarketing;
use App\Shop;
use Exception;
use Illuminate\Http\Request;
use Google\Cloud\Translate\V2\TranslateClient;
use Illuminate\Support\Facades\DB;
use App\Traits\EmailCampaignTrait;

class MyTestController extends Controller
{
    use EmailCampaignTrait;
    public function testCalculation($id)
    {
        $order = DB::table('new_orders')
        ->where('id', $id)
        ->select(
            'cms_user_id',
            'billing',
            'order_date',
            'total',
            'cart',

            'dropmatix_sub_total',
            'dropmatix_total_tax',
            'dropmatix_discount',
            'dropmatix_shipping_cost',
            'dropmatix_tax_rate',
        )
        ->first();

        $payload = [
            'dropmatix_sub_total' => $order->dropmatix_sub_total,
            'dropmatix_total_tax' => $order->dropmatix_total_tax,
            'dropmatix_discount' => $order->dropmatix_discount,
            'dropmatix_shipping_cost' => $order->dropmatix_shipping_cost,
            'dropmatix_tax_rate' => $order->dropmatix_tax_rate,
        ];

        $calculate = app(\App\Services\Order\ApiOrderCalculation::class)->calculate((array)$order);

        return [
            'calculat' => $calculate,
            'total' => $order->total,
            'dropmatix' => $payload,
        ];
    }

    public function Testtranslation()
    {
        //$products = DB::table('drm_products')->where('drm_products.drm_import_id',$import_id)->get();

        $translate = new TranslateClient(['key' =>'AIzaSyDRO5pviA1GTWZ5PxVXFMvMC0Cx9SlPDoo']);
        //$charCount = 0;

        // foreach($products as $product) {

        //     $names        = "";
        //     $descriptions = "";

            $name        = strip_tags("Timex Expedition T49900 Herrenuhr Chronograph"); //($product->name=='')?"":$product->name;
            //$description = ($product->description=='')?"":$product->description;

            //$charCount += strlen($name) + strlen($description);
            //if($charCount > 95000) { sleep(100); $charCount = 0; }

            try{

            //foreach($user_lang as $target) {

                //$target_lang = $target;
                $results = $translate->translateBatch([$name], ['source'=>"de", 'target' =>"en"]);
                $names = $results[0]['text'];
                //$descriptions = $results[1]['text'];
            //}

        }catch(Exception $e){

            dd("error found");

        }finally {

            dd(html_entity_decode($names));

            // if($names == "" or $descriptions == "")
            // {
            //     sleep(2);
            //     foreach($user_lang as $target) {

            //         $target_lang = $target;
            //         $results = $translate->translateBatch([$name, $description], ['source'=>$source, 'target' =>$target]);
            //         $names = $results[0]['text'];
            //         $descriptions = $results[1]['text'];
            //     }
            // }


            // $rows['drm_products_id'] = $product->id;
            // $rows['title'] = strip_tags($names);
            // $rows['description'] = strip_tags($descriptions);
            // $convert_lang = DB::table('languages')->select("id")->where("shortcode", $target_lang)->first();
            // $rows['language_id'] = $convert_lang->id;
            // $rows['ean'] = $product->ean;

            // DB::table('drm_translation')->updateOrInsert(['ean' => $product->ean, 'language_id' => $convert_lang->id], $rows);

            }
        }
    //}


    public function dropzone()
    {

        $data['image'] = DB::table('drm_products')->select('drm_products.image')->take(5)->get();
        //dd($data);
        return view("dropzone", $data);
    }

    public function test(){
//        $results = app(NewShopSyncController::class)->syncShopifyOrder(Shop::find(960));

        $client = new \App\Services\Amazon\AZClient([
            'Marketplace_Id' => 'A1PA6795UKMFR9',
            'Seller_Id' => 'A170AC2154JR17', // Username
            'Access_Key_ID' => '********************',
            'Secret_Access_Key' => 'oEIgkGejJYPU9NeTZs3CVacxqNuML2kx1P14ODnw',
            'MWSAuthToken' => 'amzn.mws.70c4ef6a-6b45-f0e8-e0f7-cf27d893079e' //Password
        ]);

        $last_date = '2016-01-01';
        $fromDate = new \DateTime($last_date);
        $res = $client->validateCredentials();

        dd($res);
        if(!$client->validateCredentials()){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Invalid credentials !'), 'error');
        }
    }

    public function stepMails()
    {
        $campaign = EmailMarketing::has('tags')->has('steps')->where('id', 138)->first();
        $this->sendStepEmail($campaign);
    }
}
