<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use ServiceKey;
use App\Http\Resources\StripeOrderFetchResource;
use Illuminate\Support\Collection;

use App\NewOrder;

use App\Helper\LengowApi;
use DateTime;
use App\Helper\GambioApi;
use App\Helper\ShopifyApi;
use App\Helper\EbayApi;
use \Hkonnet\LaravelEbay\EbayServices;
use \DTS\eBaySDK\Trading\Services;
use \DTS\eBaySDK\Trading\Types;
use \DTS\eBaySDK\Trading\Enums;
use \DTS\eBaySDK\Constants;
use Ebay;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Notifications\DRMNotification;
use App\User;
use Carbon\Carbon;
use \Illuminate\Support\Arr;

use Illuminate\Support\Facades\Cache;

use App\Services\OrderSync\EbayOrderAPI;

class ApiInvoiceResponseCntroller extends Controller
{
	//Remove later
    public function getOrderData($order_id){

		$order = DB::table('drm_orders_new')->find($order_id);
    	$data = [];
    	if($order){
            $shop = \App\Shop::find($order->shop_id);
            if($shop){
            	$data['shop_id'] = $shop->id;
            	$data['shop_name'] = $shop->shop_name;
            	$data['order_id'] = $order->id;
            	$data['order_id_api'] = $order->order_id_api;
        		if($shop->channel == 1)
                {
                   $data['data'] = $this->syncGambioorderSearch($shop, $order->order_id_api);
                }
                if($shop->channel == 2)
                {
                   $data['data'] = $this->syncLengowOrderSearch($shop, $order->order_id_api);
                }
                if($shop->channel == 3)
                {
                   $data['data'] = $this->syncYategoOrder($shop, $order->order_id_api);
                }
                if($shop->channel == 4)
                {
                   $data['data'] = $this->syncEbayOrder($shop, $order->order_id_api);
                }
                if($shop->channel == 6)
                {
                   $data['data'] = $this->syncShopifyOrder($shop, $order->order_id_api);
                }
            }
    	}

    	return $data; 
    }

    public function getNewOrderData($order_id){

		$order = DB::table('new_orders')->find($order_id);
    	$data = [];
    	if($order){
            $shop = \App\Shop::find($order->shop_id);
            if($shop){
            	$data['shop_id'] = $shop->id;
            	$data['shop_name'] = $shop->shop_name;
            	$data['order_id'] = $order->id;
            	$data['order_id_api'] = $order->order_id_api;
        		if($shop->channel == 1)
                {
                   $data['data'] = $this->syncGambioorderSearch($shop, $order->order_id_api);
                }
                if($shop->channel == 2)
                {
                   $data['data'] = $this->syncLengowOrderSearch($shop, $order->order_id_api);
                }
                if($shop->channel == 3)
                {
                   $data['data'] = $this->syncYategoOrder($shop, $order->order_id_api);
                }
                if($shop->channel == 4)
                {
                   $data['data'] = $this->syncEbayOrder($shop, $order->order_id_api);
                }
                if($shop->channel == 6)
                {
                   $data['data'] = $this->syncShopifyOrder($shop, $order->order_id_api);
                }
            }
    	}

    	return $data; 
    }

   	public function syncGambioorderSearch($shop, $search)
    {
        try{
	        if ($shop) {
	            $user = $shop->username;
	            $pass = $shop->password;
	            $shopId = $shop->id;
	            $base_url = $shop->url;
	            $api_path = "api.php/v2/";
	        } else {
	            throw new \Exception("You have not configured any shop yet!");
	        }

	        $auth = base64_encode("$user:$pass");

	        $per_page = 50;
	        $url  = $base_url . $api_path . "orders/$search";

	        $headers = array("Authorization: Basic " . $auth);

	        $ch = curl_init();
	        curl_setopt($ch, CURLOPT_URL, $url);
	        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
	        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");

	        $response = curl_exec($ch);
	        $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	        $errNo = curl_errno($ch);
	        $errStr = curl_error($ch);
	        curl_close($ch);

	        if ($responseCode != 200) {
	            throw new \Exception("Shop connection problem!");
	        }

			return json_decode($response);
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }

    public function syncLengowOrderSearch($shop, $search)
    {
    	try{
	        if (!$shop) {
	            throw new \Exception("You have not configured any Lengow shop yet!");
	        }

	        $access_token = $shop->username;
	        $secret   = $shop->password;
	        $lengow = new LengowApi($access_token, $secret);

	        if ($lengow->token == "") {
	            throw new \Exception("$shop->shop_name shop token problem!");
	        }
			return $lengow->getOrderByID($search);
	        return [];
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }


    function syncYategoOrder($shop, $search)
    {
        try{
	        if (!$shop) {
	        	throw new \Exception("You have not configured any Yatego shop yet!");
	        }
	        $order_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_order";

	        $order_content = @file_get_contents($order_url);
	        if (!$order_content) {
	            throw new \Exception("Can not access url or no order found!");
	        }

	        @unlink(storage_path() . "/tempOrderTest.csv");
            @unlink(storage_path() . "/tempOrderProduct.csv");

	        $putted_orders = @file_put_contents(storage_path() . "/tempOrderTest.csv", $order_content);
	        if (!$putted_orders) {
	            throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderTest.csv");
	        }

	        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
	        $reader->setInputEncoding('UTF-8');
	        $reader->setDelimiter(';');
	        $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderTest.csv"));

	        $order_arr = $spreadsheet->getActiveSheet()->toArray();
	        $order_columns = $order_arr[0];
	        unset($order_arr[0]);

	        if (count($order_arr)) {

				$order_id_from = $order_arr[1][1];
		        $order_id_to = $order_arr[count($order_arr)][1];

		        $order_product_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_products&von=" . $order_id_from . "&bis=" . $order_id_to . "&varids=1";
		        $product_content = @file_get_contents($order_product_url);

		        $putted = @file_put_contents(storage_path() . "/tempOrderProduct.csv", $product_content);
		        if (!$putted) {
		            throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderProduct.csv");
		        }


		        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
		        $reader->setInputEncoding('UTF-8');
		        $reader->setDelimiter(';');
		        $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderProduct.csv"));

		        $product_arr = $spreadsheet->getActiveSheet()->toArray();
		        $product_columns = $product_arr[0];
		        unset($product_arr[0]);
		        $product_arr_new = [];
		        foreach ($product_arr as $item) {
		            $product = @array_combine($product_columns, $item);

		            if (!$product) {
		                throw new \Exception('Error in product details. Please contact admin.');
		            }

		            $product_arr_new[] = $product;
		        }

		        $product_collection = collect($product_arr_new);

		        foreach ($order_arr as $k => $item) {
		        	$order_data = @array_combine($order_columns, $item);
		        	$order_data['products'] = $product_collection->where('Bestellnummer', $order_data['Bestellnummer'])->toArray();
		        	if($order_data['Order_ID'] == $search)  return $order_data;
		        }
	        }
	        return [];
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }

    public function syncEbayOrder($shop, $search)
    {
    	try{
    		if($shop){
	    		$ebay_service = new EbayServices();
				$service = $ebay_service->createTrading();
				$request = new Types\GetOrdersRequestType();
				$request->RequesterCredentials = new Types\CustomSecurityHeaderType();
				$request->RequesterCredentials->eBayAuthToken = $shop->password;
				$request->CreateTimeFrom = \DateTime::createFromFormat('Y-m-d H:i:s', '2019-02-01 18:33:00');
				$request->CreateTimeTo = \DateTime::createFromFormat('Y-m-d H:i:s', now());
				$request->OrderStatus = 'All';
				$response = $service->getOrders($request);
				if(json_decode($response,true)['Ack'] == 'Success'){
					$response = (object)(json_decode($response,true)['OrderArray']);
					$all_orders = $response->Order;

					if($all_orders){
						foreach ($all_orders as $order){
							if($order['OrderID'] == $search) return $order;
						}
					}
				}else{
					throw new \Exception('Authentication error!');
				}
    		}else{
    			throw new \Exception('You have not configured any Ebay shop!');
    		}
    		return [];
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }


    // Shopify Orders
    public function syncShopifyOrder($shop, $search)
    {
    	try{
    		if($shop){
		        $url = $shop->url . 'admin/api/2020-01/orders/'.$search.'.json';
		        $client = new \GuzzleHttp\Client();
		        $response = $client->request('GET', $url, [
		            'auth' => [$shop->username, $shop->password]
		        ]);
		        if($response->getStatusCode() !== 200){
		        	throw new \Exception('Connection problem!');
		        }
				$data = $response->getBody()->getContents();
				return (json_decode($data));
	    	}else{
	    		throw new \Exception('You have not configured shop!');
	    	}
	    	return [];
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }



    public function EbayOrderFix($shop_id, $api_ids){


    	try{
    		$shop = \App\Shop::find($shop_id);
    		if($shop){

				$ebay_service = new EbayServices();
				$service = $ebay_service->createTrading();
				$request = new Types\GetOrdersRequestType();
				$request->RequesterCredentials = new Types\CustomSecurityHeaderType();
				$request->RequesterCredentials->eBayAuthToken = $shop->password;
				$request->CreateTimeFrom = \DateTime::createFromFormat('Y-m-d H:i:s', '2019-02-01 18:33:00');
				$request->CreateTimeTo = \DateTime::createFromFormat('Y-m-d H:i:s', Carbon::now());
				$request->OrderStatus = 'All';

				// $request->OrderIDArray = new Types\OrderIDArrayType();

				// foreach($api_ids as $id){
				// 	$request->OrderIDArray->OrderID = $id;
				// }
				

	            $request->Pagination = new \DTS\eBaySDK\Trading\Types\PaginationType();
	            $request->Pagination->EntriesPerPage = 100;

	            $pageNum = 1;
	            $count = 0;

	            do {
	                $request->Pagination->PageNumber = $pageNum;
	                
	                $response = json_decode($service->getOrders($request),true); //Get Response

	                if($response['Ack'] == 'Success'){
	                    $OrderArray = (object)$response['OrderArray'];
	                    if(isset($OrderArray->Order)){   //If response has order
	                        $all_orders = $OrderArray->Order;
	                        if($all_orders){
	                        	foreach ($all_orders as $order){

		                            $order_id_api = $order['OrderID'];
									if( in_array($order_id_api, $api_ids) ) {
										$this->updateInvalidEmailEbayOrder($shop, $order);
									}                  		
	                        	}
							}                       
	                    }
	                }else{
	                    $error_message = 'Authentication error!';
	                    if(isset($response['Errors'])){
	                        $error = reset($response['Errors']);
	                        $error_message = isset($error['LongMessage'])? $error['LongMessage'] : $error_message;
	                    }
	                    throw new \Exception($error_message);
	                }

	                $pageNum += 1;
	            } while (isset($response['HasMoreOrders']) && $response['HasMoreOrders']);

	            return $count;
    		}else{
    			throw new \Exception('You have not configured any Ebay shop!');
    		}
	    }  catch (\Exception $e) {
	    	dd( $e->getMessage() , $e->getLine(), $e->getFile() );
	    }
    }

    public function updateInvalidEmailEbayOrder($shop, $order){
    	$invalid_order = NewOrder::where(['shop_id' => $shop->id, 'order_id_api' => $order['OrderID'] ])->first();
    	if ($invalid_order) {
    		// if(filter_var($invalid_order->customer->email, FILTER_VALIDATE_EMAIL)){
    		// 	return 0;
    		// }
    		
            $customer_info = $order_info = [];
            $customer_id = null;

            if (isset($order['TransactionArray']['Transaction'][0]['Buyer'])) {
                $billing_name       = $order['ShippingAddress']['Name'];
                $street_shipping    = $order['ShippingAddress']['Street1'] ?? $order['ShippingAddress']['Street2'];
                $city_shipping      = $order['ShippingAddress']['CityName'];
                $state_shipping     = $order['ShippingAddress']['StateOrProvince'];
                $zipcode_shipping   = $order['ShippingAddress']['PostalCode'];
                $country_shipping   = $order['ShippingAddress']['CountryName'];

                $email = $order['TransactionArray']['Transaction'][0]['Buyer']['Email'];
                $customer_name = $order['TransactionArray']['Transaction'][0]['Buyer']['UserFirstName'] . ' ' . $order['TransactionArray']['Transaction'][0]['Buyer']['UserLastName'];

				if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
					$fake_email = $order['BuyerUserID'] ?? $customer_name;
					$email = preg_replace('/[^a-zA-Z0-9]/','', $fake_email);
					$email = preg_replace('/\s+/', '_', $email);
					$email = $email.'4'.$zipcode_shipping.'@drmebay.com';
					$email = trim(preg_replace('/\s+/', ' ', $email));


	                $customer_info = [
	                    'customer_full_name' => $customer_name ,
	                    'company_name' => '',
	                    'email' =>  $email,
	                    'city' => $city_shipping,
	                    'zip_code' => $zipcode_shipping,
	                    'state' => $state_shipping,
	                    'country' => $country_shipping,
	                    'phone' =>  $order['ShippingAddress']['Phone'],

	                    'currency' => $order['AmountPaid']['currencyID'],
	                    'address' => $street_shipping,
	                    'insert_type' => 1,
	                    'user_id' => $shop->user_id,

	                    // shipping
	                    'shipping_name' => $billing_name,
	                    'street_shipping' => $street_shipping,
	                    'address_shipping' => $order['ShippingAddress']['Street2'],
	                    'city_shipping' => $city_shipping,
	                    'state_shipping' => $state_shipping,
	                    'zipcode_shipping' => $zipcode_shipping,
	                    'country_shipping' => $country_shipping,

	                    //billing
	                    'billing_name' => $billing_name,
	                    'street_billing' => $street_shipping,
	                    'address_billing' => $order['ShippingAddress']['Street2'],
	                    'city_billing' => $city_shipping,
	                    'state_billing' => $state_shipping,
	                    'zipcode_billing' => $zipcode_shipping,
	                    'country_billing' =>  $country_shipping,
	                ];

	                $customer_id = app('App\Http\Controllers\NewShopSyncController')->insert_customer($customer_info);

	                if($customer_id) {

		                //customer info
			            $order_info['customer_info'] = customerInfoJson($customer_info);
			            $order_info['drm_customer_id'] = $customer_id;
			            //billing
			            $order_info['billing'] = billingInfoJson($customer_info);
			            //shipping
			            $order_info['shipping'] = shippingInfoJson($customer_info);

			            if( $invalid_order->update($order_info) ){
			            	app('App\Http\Controllers\NewShopSyncController')->insertCustomerTag($invalid_order, $customer_id);
			            }

	                }
				}
            }
    	}
    }



    public function EbaySyncRESTAPI($shop_id, $api_ids){

        try{
            $shop = \App\Shop::find($shop_id);
            if(is_null($shop)) throw new \Exception('Invalid shop');
            

            $client = new EbayOrderAPI($shop->username);

            $start = true;
            $next_url = null;


            do {
                $all = ($next_url)? $client->getNextOrders($next_url) : $client->getOrders();
                if ($all) {
                    $orders = $all->orders;

                    if(empty($orders) || is_null($orders) ){
                    	break;
                    }

                    foreach ($orders as $order) {
                        $order_id_api = $order->legacyOrderId;



                        if($order_id_api){
                            $invalid_order = NewOrder::where(['shop_id' => $shop->id, 'order_id_api' => $order_id_api, 'cms_user_id', $shop->user_id ])->first();

                            dd(['shop_id' => $shop->id, 'order_id_api' => $order_id_api, 'cms_user_id' => $shop->user_id ],  $invalid_order);

                            if($invalid_order){

                                $customer_info = $order_info = [];
                                $customer_id = null;

                                if ( isset($order->fulfillmentStartInstructions) && $order->fulfillmentStartInstructions[0] ) {

                                    $fulfillment = $order->fulfillmentStartInstructions[0];
                                    $fulfillment = $fulfillment->shippingStep;
                                    $shipTo = $fulfillment->shipTo;
                                    $contactAddress = $shipTo->contactAddress;

                                    $billing_name       = $shipTo->fullName;
                                    $street_shipping    = $contactAddress->addressLine1;
                                    $city_shipping      = $contactAddress->city;
                                    $state_shipping     = null;
                                    $zipcode_shipping   = $contactAddress->postalCode;
                                    $country_shipping   = $contactAddress->countryCode;

                                    $customer_name = $billing_name;

                                    // if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                                        $fake_email = $order->buyer? $order->buyer->username : $customer_name;
                                        $email = preg_replace('/[^a-zA-Z0-9]/','', $fake_email);
                                        $email = preg_replace('/\s+/', '_', $email);
                                        $email = $email.'4'.$zipcode_shipping.'@drmebay.com';
                                        $email = trim(preg_replace('/\s+/', ' ', $email));

                                        $phone = $shipTo->primaryPhone? $shipTo->primaryPhone->phoneNumber : null;

                                        $customer_info = [
                                            'customer_full_name' => $customer_name ,
                                            'company_name' => '',
                                            'email' =>  $email,
                                            'city' => $city_shipping,
                                            'zip_code' => $zipcode_shipping,
                                            'state' => $state_shipping,
                                            'country' => $country_shipping,
                                            'phone' =>  $phone,

                                            'currency' => 'EUR',
                                            'address' => $street_shipping,
                                            'insert_type' => 1,
                                            'user_id' => $shop->user_id,

                                            // shipping
                                            'shipping_name' => $billing_name,
                                            'street_shipping' => $street_shipping,
                                            'address_shipping' => null,
                                            'city_shipping' => $city_shipping,
                                            'state_shipping' => $state_shipping,
                                            'zipcode_shipping' => $zipcode_shipping,
                                            'country_shipping' => $country_shipping,

                                            //billing
                                            'billing_name' => $billing_name,
                                            'street_billing' => $street_shipping,
                                            'address_billing' => null,
                                            'city_billing' => $city_shipping,
                                            'state_billing' => $state_shipping,
                                            'zipcode_billing' => $zipcode_shipping,
                                            'country_billing' =>  $country_shipping,
                                        ];

                                        $customer_id = app('App\Http\Controllers\NewShopSyncController')->insert_customer($customer_info);

                                        if($customer_id) {

                                            //customer info
                                            $order_info['customer_info'] = customerInfoJson($customer_info);
                                            $order_info['drm_customer_id'] = $customer_id;
                                            //billing
                                            $order_info['billing'] = billingInfoJson($customer_info);
                                            //shipping
                                            $order_info['shipping'] = shippingInfoJson($customer_info);

                                            if( $invalid_order->update($order_info) ){
                                                app('App\Http\Controllers\NewShopSyncController')->insertCustomerTag($invalid_order, $customer_id);
                                            }
                                        }
                                    // }
                                }

                            }
                        }
                    }

                    $next_url = (isset($all->next) && $all->next)? $all->next : null;
                }
            } while($next_url);


        }catch(\Exception $e){

            var_dump( $e->getMessage() , $e->getLine(), $e->getFile() , $shop_id);
        }
    }



    public function clearCache($name){
    	try{
    		Cache::forget($name);
    		return true;
    	}catch(\Exception $ee){
    		return false;
    	}
    	
    }




}
