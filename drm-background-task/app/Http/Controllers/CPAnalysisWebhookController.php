<?php
namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product\AnalysisProduct;
use App\Models\Product\CPAnalysisRequest;
// use App\Models\Product\CPAnalysisResponse;
// use App\Models\Product\CPAnalysisProductsHistory;
use App\Jobs\CPAnalysisWebhook;
use App\Jobs\CpExportJob;
// use App\DrmProduct;
use App\ProductPriceApi;
use App\Services\Keepa\Api as KeepaAPI;
use App\Services\ProductApi\Services\ProductApiInterface;
use Carbon\Carbon;
// use Illuminate\Database\Eloquent\Collection;
// use Illuminate\Support\Facades\Storage;
// use Illuminate\Support\Facades\Log;


use App\Services\ProductApi\Webhook\WebhookInterface;
use Exception;
use Illuminate\Support\Facades\DB;
use App\MarketplaceProducts;

class CPAnalysisWebhookController extends Controller
{
	public function countdownWebhook(Request $request)
	{

		$response_data = json_decode(request()->getContent(), true);
        $analysis = new CPAnalysisWebhook($response_data, 'Countdown');
        $this->dispatch($analysis);

		return response()->json([
			'success' => true,
			'message' => 'Webhook updates successfully',
		]);
	}

	public function rainforestWebhook(Request $request)
	{

		$response_data = json_decode(request()->getContent(), true);
        $analysis = new CPAnalysisWebhook($response_data, 'Rainforest');
        $this->dispatch($analysis);

		return response()->json([
			'success' => true,
			'message' => 'Webhook updates successfully',
		]);
	}

    public function googleshoppingWebhook(Request $request)
	{

		$response_data1 = json_decode(request()->getContent(), true);

        $response_data = [
            "request_info" => $response_data1["request_info"],
            "result_set" => $response_data1["result_set"],
            "collection" => $response_data1["batch"],
        ];
        $analysis = new CPAnalysisWebhook($response_data, 'Googleshopping');
        $this->dispatch($analysis);
        return response()->json([
			'success' => true,
			'message' => 'Webhook updates successfully',
		]);
	}

	//Process webhook
	public function processWebhook(array $response_data, WebhookInterface $driver, ProductApiInterface $service, string $type)
	{
		$collection = $response_data['collection'];
		$collection_id = $collection['id'];
		$collection_name = $collection['name'];
			//id: 1975AB64
			//name: Test collection - DRM Developers

		$request_info = $response_data['request_info'];
			// success: true
			// type: collection_resultset_completed

		$result_set = $response_data['result_set'];
		$download_links = $result_set['download_links'];
		$ended_at = $result_set['ended_at'];
		$json_links = $download_links['json'];
		$json_pages = $json_links['pages']; // all_pages

		$collection_column_name = $driver->collectionColumnName();

        $collection_att = CPAnalysisRequest::select('id', 'user_id', 'forced', 'easy_pricing', 'payload_id')
        ->where($collection_column_name, $collection_id)
        ->first();

        $eanArray = [];
        $count = 0;

        // if(empty($db_request_id)) return;

        // if(empty($json_pages)) return;

        $column_prefix = $driver->columnPrefix();
        $source = $driver->source();

        $db_request_column = $column_prefix.'id';

		foreach($json_pages as $index => $page)
		{
			$data = file_get_contents($page);
			$res_list = json_decode($data, true);

            // $file_name = "ebay/$collection_id/$index.json";
            // _log($file_name, 'countdown.txt');
            // Storage::disk('spaces')->put($file_name, $data, 'public');

            $addPrices = [];
			foreach($res_list as $res)
			{
                $ebay_request_id = $res['id'];

				$result = $res['result'];
				// if(empty($result)) continue;

				$price = $driver->getPrice($result);
                $title = $driver->getTitle($result);
                $rating = $driver->getRating($result);
                $rating_count = $driver->getRatingCount($result);
                $product_number = $driver->getProductNumber($result);
                $shipping_cost = $driver->getShippingCost($result);

				if($type == "Googleshopping"){
                    $request_metadata = $result['search_metadata'];
                    $request_parameters = $result['search_parameters'];
                    $gtin = $request_parameters['q'];
                }
                else{
                    $request_metadata = $result['request_metadata'];
                    $request_parameters = $result['request_parameters'];
                    $gtin = $request_parameters['gtin'];
                }
				$processed_at = $request_metadata['processed_at'];


				if(empty($gtin)) continue;

                $eanArray[] = $gtin;

                // $gtin = str_pad($gtin, 13, '0', STR_PAD_LEFT);

                $product_data = [
					"{$column_prefix}price" => $price,
                    "{$column_prefix}rating" => $rating,
                    "{$column_prefix}rating_count" => $rating_count,
                    "{$column_prefix}product_number" => $product_number,
					"{$column_prefix}last_sync" => $processed_at,
					"{$column_prefix}request_id" => $ebay_request_id,
                    "{$column_prefix}shipping_cost" => $shipping_cost,
				];

                if($type == 'Rainforest'){
                    $is_prime = $driver->getIsPrime($result);
                    $product_data['is_prime'] = $is_prime;
                    $seller_name = $driver->getSellerName($result);
                    $product_data['amazon_seller_name'] = $seller_name;
                    $seller_link = $driver->getSellerLink($result);
                    $product_data['amazon_seller_link'] = $seller_link;
                    $otherSellers = $driver->getOtherSellers($result);
                    $product_data['amazon_other_sellers'] = $otherSellers;
                    $also_bought = $driver->getPeopleAlsoBought($result);
                    $product_data['amazon_also_bought'] = $also_bought;

                    if(strlen($product_number) > 0){
                        DB::table('amazon_asin_collections')->updateOrInsert(
                            [
                                'ean' => $gtin,
                                'domain' => 3
                            ],
                            [
                                'asin' => $product_number
                            ]
                        );
                    }
                }

                if($type == 'Countdown'){
                    $productSold = $driver->getProductSold($result);
                    $product_data['ebay_product_sold'] = $productSold;
                }

                $res1 = AnalysisProduct::where('ean', $gtin)
				->update($product_data);
                // Log::channel('command')->info($res1);
                $count++;

                $addPrices[] = [
                    'ean' => $gtin,
                    'source' => $source,
                    'title' => $title,
                    'price' => $price,
                    'shipping_cost' => $shipping_cost,
                    'rating' => $rating,
                    'rating_count' => $rating_count,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];
			}

            ProductPriceApi::insert($addPrices);
		}

		//Update request
		CPAnalysisRequest::where($collection_column_name, $collection_id)
		->update([
			"{$column_prefix}last_sync" => $ended_at,
		]);
        if($collection_att->forced == 1 || $collection_att->easy_pricing == 1){
            $service = new $service;
            $service->deleteCollection($collection_id);
            $column_name = $column_prefix."synced";
            DB::table('cp_export_request')->where('id', $collection_att->payload_id)->update([
                $column_name => 1,
            ]);

            $sync_status = DB::table('cp_export_request')->where('id', $collection_att->payload_id)->select('ebay_synced', 'amazon_synced', 'google_synced')->first();
            if($sync_status->ebay_synced == 1 && $sync_status->amazon_synced == 1 && $sync_status->google_synced == 1){
                CpExportJob::dispatch($collection_att->payload_id);
            }
        }

        if($type == 'Rainforest' && !empty($eanArray)){
            $fIndex = 0;
            $limit = 100;
            if(sizeof($eanArray)>100){
                for($i = 0; $i <= (sizeof($eanArray)/100); $i++){
                    $products = array_slice($eanArray, $fIndex, $limit);
                    $fIndex += 100;
                    $this->salesRank($products);
                    unset($products);
                }
            }
            else{
                $this->salesRank($eanArray);
            }
        }

        $this->updateTariffBalance($collection_att->user_id, $count);
	}

    public function salesRank($eanArray)
    {
        try {
            $keepa_api = new KeepaAPI();
            $salesRank = $keepa_api->getSelesRankByEAN($eanArray);

            if ($salesRank) {
                foreach ($salesRank as $rank) {
                    AnalysisProduct::where([
                        'ean' => $rank['ean']
                    ])->update($rank);
                }
            }
        } catch (Exception $exception) {

        }
    }

    public function updateTariffBalance($user_id, $count){
        $status = DB::table('drm_tariff_balance')->where('user_id', $user_id)->select('use_top_up', 'balance')->first();
        if($status){
            if($status->use_top_up == 1){
                $balance = $status->balance - ($count * 0.05);
                DB::table('drm_tariff_balance')->where('user_id', $user_id)->update([
                    'balance' => $balance
                ]);
            }
        }
        return true;
    }



}
