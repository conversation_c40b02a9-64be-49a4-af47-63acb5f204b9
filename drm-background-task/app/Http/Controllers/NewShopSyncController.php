<?php

namespace App\Http\Controllers;

use App\Enums\Channel;
use App\Models\ChannelProduct;
use App\Shop;
use Barryvdh\DomPDF\PDF;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Helper\LengowApi;
use App\Helper\KauflandApi;
use DateTime;
use App\Helper\GambioApi;
use App\Helper\ShopifyApi;

// use App\Helper\EbayApi;
// use Ebay;
use Automattic\WooCommerce\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Notifications\DRMNotification;
use Illuminate\Support\Facades\Validator;
use App\User;
use App\NewCustomer;
use App\DropfunnelCustomerTag;
use App\CustomerTag;
use App\ProductTag;
use App\NewOrder;
use App\EmailMarketing;

use Carbon\Carbon;
use App\Jobs\UpdateProductRq;

use ServiceKey;
use App\Jobs\ShopOrderSyncJob;
use App\Jobs\OrderSendToTelegram;
use Illuminate\Support\Facades\Mail;
use Exception;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Services\OrderSync\EtsyOrderAPI;

//Check-24 orders
use League\Flysystem\Adapter\Ftp;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemException;
use League\Flysystem\ConnectionRuntimeException;

use App\Jobs\OttoCustomerSync;
use App\Jobs\SprinterTiendanimalMediamarktCustomerSync;
//Keepa API
use App\Services\Keepa\Api as KeepaAPI;

use App\Services\Notification\PushNotifySell;

//Dropfunnel;
use App\Traits\EmailCampaignTrait;

class NewShopSyncController extends Controller
{
    use EmailCampaignTrait;

    public function getsync($id)
    {
        try {
            ini_set('max_execution_time', -1);
            $sync_shop_types = config('global.sync_shop_types');
            if (empty($sync_shop_types)) throw new Exception('This shop can not be synced!.');

            $sync_shop_types[] = 16; // Treadbyte type

            $shop = \App\Shop::whereIn('channel', $sync_shop_types)->find($id);

            if(isset($_GET['missing_tb_id']))
            {
                return $this->insertMissingTB($shop, $_GET['missing_tb_id']);
            }

            if ($shop) {
                $shoptype = $shop->channel;

                //retry manual sync shop order
                DB::table('new_order_sync_reports')->where('shop_id', $id)->update(['status' => 0]);

                if ($shoptype == 1) {
                    // self::syncGambioorder($shop);
                    ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                } else if ($shoptype == 2) {
                    ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                } else if ($shoptype == 3) {
                    ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                    // self::syncYategoOrder($shop);
                } else if ($shoptype == 4) {
                    ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                    // self::syncEbayOrder($shop);
                    // self::syncEbayOrderLatestAPI($shop);
                } else if ($shoptype == 5) {
                    // self::syncAmazonOrder($shop);
                    ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                } else if ($shoptype == 6) {
                    // self::syncShopifyOrder($shop);
                    ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                } else if ($shoptype == 7) {
                     self::syncWooCommerceOrder($shop);
                    ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                } else if ($shoptype == 11) {
//                     self::syncEtsyOrder($shop);
                    ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                } else if ($shoptype == 12) {
                     self::syncOttoOrder($shop);
//                    ShopOrderSyncJob::dispatchNow($shop, $shoptype);
                    // ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                }
                  else if ($shoptype == 13){
                     self::syncKauflandOrder($shop);
//                    ShopOrderSyncJob::dispatch($shop, $shoptype)->onQueue('ordersync');
                } else if ($shoptype == 14){
                    // self::insertCheck24Order($shop);
                     ShopOrderSyncJob::dispatch($shop, $shoptype);
                } else if($shoptype == 15){
                    self::syncDecathlonOrder($shop);
                    // ShopOrderSyncJob::dispatch($shop, $shoptype);
                } else if($shoptype == 16 || $shoptype == 21){
                    self::insertTradebyteOrder($shop);
                    // ShopOrderSyncJob::dispatch($shop, $shoptype);
                } else if($shoptype == 17){
                    self::syncSprinterOrder($shop);
                } else if($shoptype == 18){
                    self::syncTiendanimalOrder($shop);
                } else if($shoptype == 19){
                    self::syncMediamarktOrder($shop);
                }else if($shoptype == 20){
                    self::syncColizeyOrder($shop);
                    // ShopOrderSyncJob::dispatch($shop, $shoptype);
                } else if($shoptype == 22){
                    self::syncConradOrder($shop);
                } else if($shoptype == 23){
                    self::syncFressnapfOrder($shop);
                } else if($shoptype == 25) {
                    self::syncVolknerOrder($shop);
                } else if($shoptype == 26) {
                    self::syncManorOrder($shop);
                } else if($shoptype == 27) {
                    self::syncXxxlutzOrder($shop);
                } else if($shoptype == 28) {
                    self::syncPerfumesClubOrder($shop);
                } else if($shoptype == 29) {
                    self::syncHome24Order($shop);
                } else if($shoptype == 30) {
                    self::syncAlltricksOrder($shop);
                } else if($shoptype == 32) {
                    self::syncClubeFashionOrder($shop);
                } else if($shoptype == 33) {
                    self::syncZooplusOrder($shop);
                } else if($shoptype == 34) {
                    self::syncPssOrder($shop);
                } else if($shoptype == 35) {
                    self::syncBigbangOrder($shop);
                } else if($shoptype == 36) {
                    self::syncBricodepotOrder($shop);
                } else if($shoptype == 37) {
                    self::syncHornbachOrder($shop);
                } else if($shoptype == 38) {
                    self::syncPlanetahuertoOrder($shop);
                } else if($shoptype == 39) {
                    self::syncCarrefourOrder($shop);
                } else if($shoptype == 40) {
                    self::syncMiraklConnectOrder($shop);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Process running on background!' . phpversion(),
                ]);
            } else {
                throw new Exception('Shop not found!');
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong! Error: ' . $e->getMessage(),
            ]);
        }

    }

    //Gambio order sync
    public function syncGambioorder($shop)
    {
        $last_date = $this->syncStartDate($shop);
        if ($last_date) {
            $last_date = date('Y-m-d', strtotime($last_date . "-1 day")) . ' 00:00:00';
        }
        $this->fetchGambioorder($shop, $last_date);
    }

    private function fetchGambioorder($shop, $last_date = null, $page = 1, &$count = 0)
    {
        try {
            if ($shop) {
                $user = $shop->username;
                $pass = $shop->password;
                $shopId = $shop->id;
                $base_url = $shop->url;
                $api_path = "api.php/v2/";
            } else {
                throw new Exception("You have not configured any shop yet!");
            }

            $auth = 'Basic ' . base64_encode("$user:$pass");
            $per_page = 100;

            $search = '{
                "search": {
                    "greater": {"orders.date_purchased": "' . $last_date . '" }
                }
            }';

            $url = $base_url . $api_path . "orders/search?page=$page&per_page=$per_page";
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $search,
                CURLOPT_HTTPHEADER => array(
                    "accept: application/json",
                    "authorization: " . $auth,
                    "content-type: application/json"
                ),
            ));

            $response = curl_exec($curl);

            $err = curl_error($curl);
            $responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);
            if ($responseCode == 401) {
                throw new Exception("Something went wrong - Credentials!");
            }
            if ($responseCode != 200) {

                throw new Exception();
            }
            $allOrder = json_decode($response);
            $cartsInfo = [];
            foreach ((array)$allOrder as $indexv => $value) {
                $item_url = $base_url . $api_path . "orders/$value->id/items";

                $client = new \GuzzleHttp\Client();
                $response = $client->request('GET', $item_url, [
                    'headers' => [
                        'authorization' => $auth,
                        'content-type' => 'application/json'
                    ],
                ]);

                if ($response->getStatusCode() !== 200) {
                    throw new Exception();
                }

                $product_data = $response->getBody()->getContents();
                $cartsInfo[$value->id] = json_decode($product_data);
            }
            $this->bulkOrderInsertGambio($allOrder, $shop, $cartsInfo);

            if (count((array)$allOrder) != 0) {
                $page++;
                $count += count((array)$allOrder);
                $this->fetchGambioorder($shop, $last_date, $page, $count);
            }

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
    }

    public function bulkOrderInsertGambio($allOrder, $shop, $cartsInfo)
    {
        $shopId = $shop->id;
        $shop_name = $shop->shop_name;
        $userId = $shop->user_id;

        if (count((array)$allOrder) > 0) {

            foreach ($allOrder as $key => $value) {
                $customer_id = null;
                $order_info = [];

                $l_date = $value->purchaseDate;

                //  $exist_shop_id = DB::table('new_orders')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->id)->first();
                // if($exist_shop_id) continue;
                // ---------- customer insert -------------

                list($total_sum, $currency) = explode(" ", $value->totalSum);

                $country = $value->deliveryAddress->country ?? $value->billingAddress->country ?? $value->deliveryAddress->countryIsoCode ?? $value->billingAddress->countryIsoCode;

                $customer_info = $order_info = null;

                $customer_info = [
                    "customer_full_name" => $value->customerName ?? $value->deliveryAddress->firstName . ' ' . $value->deliveryAddress->lastName,
                    "company_name" => $value->deliveryAddress->company ?? $value->billingAddress->company,
                    "currency" => $currency,
                    'email' => $value->customerEmail,
                    'address' => $value->deliveryAddress->additionalAddressInfo ?? $value->billingAddress->additionalAddressInfo,
                    'country' => $country,
                    'default_language' => $value->deliveryAddress->countryIsoCode ?? $value->billingAddress->countryIsoCode,
                    'zip_code' => $value->deliveryAddress->postcode ?? $value->billingAddress->postcode,
                    'state' => $value->deliveryAddress->state ?? $value->billingAddress->state,
                    'insert_type' => 1,

                    //shipping
                    'street_shipping' => $value->deliveryAddress->street . ' ' . $value->deliveryAddress->houseNumber,
                    'city_shipping' => $value->deliveryAddress->city,
                    'state_shipping' => $value->deliveryAddress->state,
                    'zipcode_shipping' => $value->deliveryAddress->postcode,
                    'country_shipping' => $value->deliveryAddress->country ?? $value->billingAddress->countryIsoCode,

                    //billing
                    'street_billing' => $value->billingAddress->street . ' ' . $value->billingAddress->houseNumber,
                    'city_billing' => $value->billingAddress->city,
                    'state_billing' => $value->billingAddress->state,
                    'zipcode_billing' => $value->billingAddress->postcode,
                    'country_billing' => $value->billingAddress->country ?? $value->billingAddress->countryIsoCode,

                    'user_id' => $shop->user_id,
                    'source' => $shop->channel,
                ];

                $customer_id = $this->insert_customer($customer_info);

                $order_info['user_id'] = $shop->user_id;
                $order_info['drm_customer_id'] = $customer_id;
                // $date=date_create("2013-03-15");
                $order_info['order_date'] = $value->purchaseDate;
                $order_info['insert_type'] = 1;
                $order_info['total'] = $total_sum;
                $order_info['shop_id'] = $shop->id;
                $order_info['order_id_api'] = $value->id;
                // $order_info['shipping'] =

                $order_info['sub_total'] = $total_sum;
                $order_info['discount'] = 0;
                $order_info['discount_type'] = "fixed";
                $order_info['adjustment'] = 0;
                $order_info['payment_type'] = $value->paymentType->module;
                $order_info['currency'] = $currency;

                //customer info
                $order_info['customer_info'] = customerInfoJson($customer_info);

                //billing
                $order_info['billing'] = billingInfoJson($customer_info);

                //shipping
                $order_info['shipping'] = shippingInfoJson($customer_info);

                $order_info['status'] = $value->statusName;
                $carts = [];
                foreach ((array)$cartsInfo[$value->id] as $item) {
                    $cart_item = [];
                    $cart_item['id'] = $item->id;
                    $cart_item['product_name'] = preg_replace_array('/"/', [' '], $item->name);
                    $cart_item['description'] = preg_replace_array('/"/', [' '], $item->model);
                    $cart_item['qty'] = $item->quantity;
                    $cart_item['rate'] = $item->price;
                    $cart_item['unit'] = $item->quantityUnitName;
                    $cart_item['tax'] = $item->tax;
                    $cart_item['item_number'] = $item->model ?? null;
                    $cart_item['product_discount'] = $item->discount ?? 0;
                    $cart_item['amount'] = $item->finalPrice;
                    $carts[] = $cart_item;
                }
                $order_info['cart'] = json_encode($carts);

                // ----------------------- Order Insert ---------------------
                $this->insert_order($order_info);
            }/* foreach end */

            $this->insertSyncDate($shop, $l_date);
        }
    }

    //Lengow order sync
    public function syncLengowOrder($shop)
    {
        $last_date = $this->syncStartDate($shop);
        $this->fetchLengowOrder($shop, $last_date);
    }

    private function fetchLengowOrder($shop, $last_date = null, $page = 1, &$count = 0)
    {
        try {
            if (!$shop) {
                throw new Exception("You have not configured any Lengow shop yet!");
            }

            $access_token = $shop->username;
            $secret = $shop->password;
            $lengow = new LengowApi($access_token, $secret);

            if ($lengow->token == "") {
                throw new Exception("$shop->shop_name shop token problem!");
            }

            $all = $lengow->getOrder($page, $last_date);

            if ($all) {
                $allorder = $all->results;
                $this->bulkOrderInsertLengow($allorder, $shop);
                $count += count($allorder);
                if ($all->next) {
                    $page++;
                    $this->fetchLengowOrder($shop, $last_date, $page, $count);
                }
            }

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
    }

    public function insertDroptiendaOrder(Request $request)
    {
        Log::channel('command')->info('Droptienda order');
        Log::channel('command')->info($request);
        Log::channel('command')->info('Droptien order end');


        $shop = Shop::where([
            'username' => $request->header('userToken'),
            'password' => $request->header('userPassToken'),
            'channel' => Channel::DROPTIENDA
        ])->first();

        if (empty($shop)) {
            return response()->json(["success" => false, "message" => "unauthorized"], 401);
        }

        $shopId = $shop->id;
        $shop_name = $shop->shop_name;
        $userId = $shop->user_id;
        $base_url = $shop->url;

        $l_date = null;
        $name = $request->first_name . ' ' . $request->last_name;
        if (strtolower(substr($name, 0, 3)) != 'xxx') {
            $l_date = $request->created_at;

            $customer_info = $order_info = $customer_id = null;


            $email = $request->email;
            if (empty($email)) {
                $email_str = $name . $request->zip . $request->country;
                $email_str = strtolower(preg_replace('/\s+/', '', $email_str));
                $email_str = preg_replace( '/[\W]/', '', $email_str);
                $email = $email_str . '@drmdtfake.com';
                $email = \App\Helper\Encoding::toUTF8($email);
            }

            $customer_info = [
                'customer_full_name' => $name,
//                'company_name' => $value->packages[0]->delivery->company ?? $value->billing_address->company,
                'email' => $email,
                'city' => $request->city,
                'zip_code' => $request->zip,
                'state' => $request->state,
                'country' => $request->country,
                'phone' => $request->phone,
                'currency' => $request->payment_currency,
                'address' => $request->address,
                'insert_type' => 1,
                'user_id' => $shop->user_id,

                // shipping
                'street_shipping' => $request->address,
                'city_shipping' => $request->city,
                'state_shipping' => $request->state,
                'zipcode_shipping' => $request->zip,
                'country_shipping' => $request->country,

                //billing
                'street_billing' => $request->address,
                'city_billing' => $request->city,
                'state_billing' => $request->state,
                'zipcode_billing' => $request->zip,
                'country_billing' => $request->country,

                'source' => $shop->channel,
            ];

            if( isset($request->blilling_info) && $request->blilling_info) {
                $blilling_info = $request->blilling_info;
                $customer_info['street_billing'] = $blilling_info['address'];
                $customer_info['city_billing'] = $blilling_info['city'];
                $customer_info['state_billing'] = $blilling_info['state'];
                $customer_info['zipcode_billing'] = $blilling_info['zip'];
                $customer_info['billing_name'] = $blilling_info['name'];
                $customer_info['country_billing'] = $blilling_info['country'];
            }

            if( isset($request->shipping_info) && $request->shipping_info) {
                $shipping_info = $request->shipping_info;
                $customer_info['street_shipping'] = $shipping_info['address'];
                $customer_info['city_shipping'] = $shipping_info['city'];
                $customer_info['state_shipping'] = $shipping_info['state'];
                $customer_info['zipcode_shipping'] = $shipping_info['zip'];
                $customer_info['shipping_name'] = $shipping_info['name'];
                $customer_info['country_shipping'] = $shipping_info['country'];
            }


            if (!empty($customer_info)) {
                $customer_id = $this->insert_customer($customer_info);
            }

            $orderId = 'drop_t' . $shop->id . '_' . $request->id;
            if (DB::table('new_orders')->where('cms_user_id', $shop->user_id)->where('order_id_api', $orderId)->exists()) {
                return response()->json(['success' => false, 'message' => 'Already exist'], 422);
            }

            // ----------------------- order ----------------------------


            $total_amount = $request->amount;
            $tax_amount = $request->taxes_amount ?? 0;
            $shipping_cost = $request->shipping ?? 0;
            $subtotal = $total_amount;

            $tax_rates = $request->tax_rate;
            // $unit_tax = 0;

            if ($shipping_cost) {
                $subtotal = $total_amount - $shipping_cost;
            }

            //If tax rate defined
            // if(isset($request->tax_rate)) {

            //     $tax_rates = $request->tax_rate;

            // }else{

                $total_cart_price = collect($request->carts ?? [])->map(function($c){
                    $servicePrice = isset($c['servicePrice']) ? $c['servicePrice'] * $c['qty'] : 0;
                    return ['price' => ($c['price'] * $c['qty'])];
                })->sum('price') ?? 0;


                // dd($total_cart_price);

            //     if ($total_cart_price && !empty($tax_amount)) {

            //         $unit_tax = $tax_amount / $total_cart_price;
            //         $tax_rates = $unit_tax * 100;
            //     }
            // }

            $order_info['user_id'] = $shop->user_id;
            $order_info['drm_customer_id'] = $customer_id;
            // $date=date_create("2013-03-15");
            $order_info['order_date'] = $request->created_at;
            $order_info['insert_type'] = 1;
            $order_info['total'] = $total_amount;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $orderId;

            $order_info['sub_total'] = $subtotal;
            $order_info['discount'] = $request->discount_value;
            $order_info['discount_type'] = $request->discount_type;
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $request->payment_type;
            $order_info['currency'] = $order_currency = $request->payment_currency;
            $order_info['shipping_cost'] = $shipping_cost;
            $order_info['total_tax'] = $tax_amount;

            $order_info['tax_rate'] = $tax_rates;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $status = $request->order_status;
            $status_t = strtolower(trim($status));
            $status = ($status_t == 'new') ? 'nicht_bezahlt' : $status;
            $status = ($request->is_paid) ? 'paid' : $status;

            // $order_info['client_note'];
            $order_info['status'] = $status;
            $order_info['coupon'] = $request->promo_code ?? null;

            if(isset($request->test_order)) {
                $test_order = @intval($request->test_order) ?? null;
                if($test_order == 1) {
                    $order_info['test_order'] = 1;
                    $order_info['status'] = 'test_order';
                }
            }

            $carts = [];
            foreach ($request->carts as $j => $item) {

                $cart_item = [];

                $original_amount = $item['price'];
                // $original_amount = isset($item['price_with_tax']) ? $item['price_with_tax'] : $original_amount + ($original_amount * $unit_tax);

                $original_quantity = $item['qty'];

                $serviceItem = [];
                if(isset($item['serviceName']) && isset($item['servicePrice']) && !empty($item['serviceName']))
                {
                    $serviceItem = $this->dtServicePrice($item['serviceName'], $item['servicePrice']);
                }
                if(!empty($serviceItem) && isset($serviceItem['price']) && isset($serviceItem['service']))
                {
                    $cart_item['service_price'] = $original_quantity * $serviceItem['price'];
                    $cart_item['service_item'] = $serviceItem['service'];
                    $cart_item['service_tax_rate'] = $request->tax_rate;
                    $order_info["total_tax"] += orderTaxForNet($cart_item['service_price'], $request->tax_rate);
                }

                $price_amount = isset($item['amount'])? $item['amount'] : (($original_quantity > 1) ? ((float)$original_amount * (float)$original_quantity) : $original_amount);

                $cart_item['id'] = $item['id'];
                $cart_item['product_name'] = preg_replace_array('/"/', [' '], $item['title']);
                $cart_item['description'] = preg_replace_array('/"/', [' '], $item['category']);

                $cart_item['qty'] = $original_quantity;
                $cart_item['rate'] = $original_amount;
                $cart_item['tax'] = $item['tax_rate'];
                $cart_item['ean'] = $item['ean'] ?? $item['product'][0]['ean'];
//                $cart_item['image'] = $item['url_image'];
                $cart_item['item_number'] = null;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = $price_amount;
                $carts[] = $cart_item;


                if(isset($item['amount']))
                {
                    $order_info['dtNew'] = true;
                }
            }

            $upselling_data = $request->upselling_data ?? null;
            if(!empty($upselling_data)) {
                $last_cart_index = count($carts)-1;
                $carts[$last_cart_index]['upselling_data'] = $upselling_data;
            }

            $order_info['cart'] = json_encode($carts);
            // ----------------------- Order Insert ---------------------
            $this->insert_order($order_info);
        }

        $this->insertSyncDate($shop, $l_date);

        return response()->json([
            'success' => true,
            'message' => 'Order synced successfully.',
            'data' => []
        ]);
    }

    public function bulkOrderInsertLengow($allorder, $shop)
    {
        $shopId = $shop->id;
        $shop_name = $shop->shop_name;
        $userId = $shop->user_id;
        $base_url = $shop->url;

        $l_date = null;
        foreach ((object)$allorder as $key => $value) {

            $name = $value->billing_address->full_name . $value->billing_address->first_name . $value->billing_address->last_name . $value->packages[0]->delivery->city;

            if (strtolower(substr($name, 0, 3)) != 'xxx') {

                $l_date = $value->marketplace_order_date;

                $customer_info = $order_info = $customer_id = null;

                $customer_info = [
                    'customer_full_name' => $value->billing_address->full_name ?? $value->billing_address->first_name . " " . $value->billing_address->last_name,
                    'company_name' => $value->packages[0]->delivery->company ?? $value->billing_address->company,
                    'email' => $value->billing_address->email,
                    'city' => $value->packages[0]->delivery->city ?? $value->billing_address->city,
                    'zip_code' => $value->packages[0]->delivery->zipcode ?? $value->billing_address->zipcode,
                    'state' => $value->packages[0]->delivery->state_region ?? $value->billing_address->state_region,
                    'country' => $value->packages[0]->delivery->common_country_iso_a2 ?? $value->billing_address->common_country_iso_a2,
                    'phone' => $value->packages[0]->delivery->phone_mobile ?? $value->billing_address->phone_mobile,
                    // 'website' => ,
                    'currency' => $value->original_currency->iso_a3,
                    // 	'default_language' => ,
                    'address' => $value->contact_address ?? $value->billing_address->full_address ?? $value->packages[0]->delivery->full_address,
                    'insert_type' => 1,
                    'user_id' => $shop->user_id,
                    // 	'vat_number' => ,

                    // shipping
                    'street_shipping' => $value->packages[0]->delivery->first_line ?? $value->packages[0]->delivery->second_line,
                    'city_shipping' => $value->packages[0]->delivery->city,
                    'state_shipping' => $value->packages[0]->delivery->state_region,
                    'zipcode_shipping' => $value->packages[0]->delivery->zipcode,
                    'country_shipping' => $value->packages[0]->delivery->common_country_iso_a2,

                    //billing
                    'street_billing' => $value->billing_address->first_line ?? $value->packages[0]->delivery->second_line,
                    'city_billing' => $value->billing_address->city,
                    'state_billing' => $value->billing_address->state_region,
                    'zipcode_billing' => $value->billing_address->zipcode,
                    'country_billing' => $value->billing_address->common_country_iso_a2,

                    'source' => $shop->channel,

                ];

                $full_country = $full_country_shipping = $full_country_billing = drmCountryNameFull($customer_info['country']);
                if ($customer_info['country'] != $customer_info['country_shipping']) {
                    $full_country_shipping = drmCountryNameFull($customer_info['country_shipping']);
                }
                if ($customer_info['country'] != $customer_info['country_billing']) {
                    $full_country_billing = drmCountryNameFull($customer_info['country_billing']);
                }

                $customer_info['country'] = $full_country;
                $customer_info['country_shipping'] = $full_country_shipping;
                $customer_info['country_billing'] = $full_country_billing;

                if (isset($value->billing_address)) {
                    // insert customer
                    $customer_id = $this->insert_customer($customer_info);
                }
                // dd($customer_id);

                if (DB::table('new_orders')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->marketplace_order_id)->exists()) {
                    continue;
                }

                //If exist on amazon shop
                // $user_amazon_shops = \App\Shop::where('user_id', $shop->user_id)->where('channel', 5)->pluck('id')->toArray();
                // if(DB::table('new_orders')->whereIn('shop_id', $user_amazon_shops)->where(['cms_user_id' => $shop->user_id, 'order_id_api' => $value->marketplace_order_id ])->exists()){
                //     continue;
                // }

                //Skipp amazon order
                // if( strpos($value->marketplace, 'amazon') !== false ){
                //     continue;
                // }

                // ----------------------- order ----------------------------

                $order_info['user_id'] = $shop->user_id;
                $order_info['drm_customer_id'] = $customer_id;
                // $date=date_create("2013-03-15");
                $order_info['order_date'] = $value->marketplace_order_date;
                $order_info['insert_type'] = 1;
                $order_info['total'] = $original_total_order = $value->original_total_order;
                $order_info['shop_id'] = $shop->id;
                $order_info['order_id_api'] = $value->marketplace_order_id;

                $order_info['sub_total'] = $value->original_total_order;
                $order_info['discount'] = $value->original_discount;
                $order_info['discount_type'] = "fixed";
                $order_info['adjustment'] = 0;
                $order_info['payment_type'] = $value->payments[0]->type;
                $order_info['currency'] = $order_currency = $value->original_currency->iso_a3;
                $order_info['shipping_cost'] = $value->original_shipping;
                $order_info['total_tax'] = $value->original_total_tax;

                $order_info['eur_total'] = $total_order = $value->total_order;
                $order_info['currency_rate'] = (strtolower($order_currency) == 'eur') ? 1 : ((float)$original_total_order / (float)$total_order);

                //customer info
                $order_info['customer_info'] = customerInfoJson($customer_info);

                //billing
                $order_info['billing'] = billingInfoJson($customer_info);

                //shipping
                $order_info['shipping'] = shippingInfoJson($customer_info);

                // $order_info['client_note'];
//                $order_info['status'] = $value->lengow_status;
                $order_info['status'] = 'paid';

                $carts = [];
                foreach ((array)$value->packages[0]->cart as $j => $item) {

                    $original_amount = $item->original_amount;
                    $original_quantity = $item->quantity;
                    $price_rate = ($original_quantity > 1) ? ((float)$original_amount / (float)$original_quantity) : $original_amount;

                    $cart_item = [];
                    $cart_item['id'] = $item->id;
                    $cart_item['product_name'] = preg_replace_array('/"/', [' '], $item->title);
                    $cart_item['description'] = preg_replace_array('/"/', [' '], $item->category);
                    $cart_item['qty'] = $original_quantity;
                    $cart_item['rate'] = $price_rate;
                    $cart_item['tax'] = $item->original_tax;
                    $cart_item['image'] = $item->url_image;
                    $cart_item['item_number'] = !empty($item->merchant_product_id) ? $item->merchant_product_id->id ?? null : null;
                    $cart_item['product_discount'] = $item->original_discount ?? 0;
                    $cart_item['amount'] = $original_amount;
                    $carts[] = $cart_item;
                }
                $order_info['cart'] = json_encode($carts);
                // ----------------------- Order Insert ---------------------
                $this->insert_order($order_info);
            }
        }/* end foreach */

        $this->insertSyncDate($shop, $l_date);
    }

    function syncYategoOrder($shop)
    {
        $count = 0;
        try {
            if (!$shop) {
                throw new Exception("You have not configured any Yatego shop yet!");
            }
            $order_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_order";
            // $csv = array_map('str_getcsv', file($url));
            // dd(file($order_url));
            $order_csv = '/tempOrder_' . $shop->id . '.csv';
            $order_product_csv = '/tempOrderProduct_' . $shop->id . '.csv';
            if (file_exists(storage_path() . $order_csv)) {
                @unlink(storage_path() . $order_csv);
            }
            if (file_exists(storage_path() . $order_product_csv)) {
                @unlink(storage_path() . $order_product_csv);
            }
            $order_content = @file_get_contents_utf8($order_url);
            if (!$order_content) {
                throw new Exception("Can not access url or no order found!");
            }
            // dd(json_decode($order_content),$order_content);

            $putted_orders = @file_put_contents(storage_path() . $order_csv, $order_content);
            if (!$putted_orders) {
                throw new Exception("Content can not be putted to file " . storage_path() . $order_csv);
            }
            // dd(realpath('storage/tempOrder.csv'));
            // dd($csv);

            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
            $reader->setInputEncoding('UTF-8');
            $reader->setDelimiter(';');
            $spreadsheet = $reader->load(realpath(storage_path() . $order_csv));

            // dd($spreadsheet->getActiveSheet()->toArray());
            $order_arr = $spreadsheet->getActiveSheet()->toArray();
            $order_columns = $order_arr[0];
            unset($order_arr[0]);
            // dd($order_arr);

            if (count($order_arr)) {

                $order_id_from = $order_arr[1][1];
                $order_id_to = $order_arr[count($order_arr)][1];
                // dd($order_id_from,$order_id_to);

                $order_product_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_products&von=" . $order_id_from . "&bis=" . $order_id_to . "&varids=1";

                $product_content = @file_get_contents_utf8($order_product_url);
                // dd($product_content);

                if (!$product_content) {
                    throw new Exception('Can not access Product url. Please contact admin. ');
                }

                $putted = @file_put_contents(storage_path() . $order_product_csv, $product_content);
                if (!$putted) {
                    throw new Exception("Content can not be putted to file " . storage_path() . $order_product_csv);
                }

                // dd(realpath('storage/tempOrder.csv'));
                // dd($csv);

                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');
                $reader->setDelimiter(';');
                $spreadsheet = $reader->load(realpath(storage_path() . $order_product_csv));

                // dd($spreadsheet->getActiveSheet()->toArray());
                $product_arr = $spreadsheet->getActiveSheet()->toArray();
                // dd($product_arr);
                $product_columns = $product_arr[0];
                unset($product_arr[0]);
                // dd($product_columns, $product_arr);
                $product_arr_new = [];
                foreach ($product_arr as $item) {
                    $product = @array_combine($product_columns, $item);

                    if (!$product) {
                        throw new Exception('Error in product details. Please contact admin.');
                    }

                    $product_arr_new[] = $product;
                }

                // $product_arr_new[] = $product;

                $product_collection = collect($product_arr_new);

                // dd($product_collection);

                foreach ($order_arr as $item) {

                    $order = @array_combine($order_columns, $item);
                    // $order_arr = @array_combine($columns,$product_arr[2]);

                    if ($order) {
                        // dd();
                        $products = $product_collection->where('Bestellnummer', $order['Bestellnummer'])->toArray();

                        if (count($products)) {
                            $this->orderInsertYatego($shop, $order, $products);

                            $count += count($products);
                        }

                    } else {
                        throw new Exception('Shop Setting Changed. Please contact admin.');
                    }
                }

            }

            $this->syncSuccessReport($shop, $count);

        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
        //  dd($shop);

    }

    public function orderInsertYatego($shop, $order, $products)
    {
        $sync_date = $this->syncStartDate($shop);

        // date
        $new = new DateTime($order['Bestelldatum']);

        if(!empty($sync_date)){
            $old = new DateTime($sync_date);
            if($old > $new){
                return;
            }
        }

        $exist_shop_id = DB::table('new_orders')->where('cms_user_id', $shop->user_id)->where('order_id_api', $order['Order_ID'])->exists();
        if($exist_shop_id) return;

        $l_date = $order['Bestelldatum'];

        $customer_info = $order_info = null;


        //Customer info

        $full_name = $order["R_Vorname"] . " " . $order["R_Nachname"];
        $zip_code = $order["R_PLZ"];
        $country = $order["R_Land"];

        $email = $order["E-Mail-Adresse"];
        if (empty($email)) {
            //Generate email address
            $email_str = $full_name . $zip_code . $country;
            $email_str = strtolower(preg_replace('/\s+/', '', $email_str));
            $email_str = preg_replace( '/[\W]/', '', $email_str);
            $email = $email_str . '@drmebayfake.com';
            $email = \App\Helper\Encoding::toUTF8($email);
        }

        $customer_info = [
            'customer_full_name' => $order["R_Vorname"] . " " . $order["R_Nachname"],
            'company_name' => $order["R_Firma"],
            'email' => $email,
            'city' => $order["R_Stadt"],
            'zip_code' => $order["R_PLZ"],
            // 'state' =>  $order["R_Stadt"] ,
            'country' => $order["R_Land"],
            'phone' => $order["R_Telefon"],
            'address' => $order["R_Strasse"],
            // 'website' => ,
            // 'currency' => ,
            // 	'default_language' => ,
            'insert_type' => 1,
            'user_id' => $shop->user_id,
            // 	'vat_number' => ,

            // shipping
            'street_shipping' => $order["L_Strasse"],
            'city_shipping' => $order["L_Stadt"],
            // 'state_shipping' => ,
            'zipcode_shipping' => $order["L_PLZ"],
            'country_shipping' => $order["L_Land"],

            //billing
            'street_billing' => $order["R_Strasse"],
            'city_billing' => $order["R_Stadt"],
            // 'state_billing' =>  ,
            'zipcode_billing' => $order["R_PLZ"],
            'country_billing' => $order["R_Land"],

            'source' => $shop->channel,
        ];

        $customer_id = $this->insert_customer($customer_info);

        // order
        $order_info['user_id'] = $shop->user_id;
        $order_info['drm_customer_id'] = $customer_id;
        $order_info['order_date'] = $order["Bestelldatum"];
        $order_info['insert_type'] = 1;
        $order_info['total'] = $order['Gesamtumsatz'];
        $order_info['currency'] = 'EUR';
        $order_info['shop_id'] = $shop->id;
        $order_info['order_id_api'] = $order['Order_ID'];

        $order_info['sub_total'] = $order['Gesamtumsatz'];
        $order_info['discount'] = $order['Bestellwertrabatt'];
        $order_info['discount_type'] = "fixed";
        $order_info['adjustment'] = 0;
        $order_info['payment_type'] = $order['Zahlart'];
        // $order_info['currency'] = $order['zzzz'];

        $order_info['status'] = 'paid';

        //customer info
        $order_info['customer_info'] = customerInfoJson($customer_info);

        //billing
        $order_info['billing'] = billingInfoJson($customer_info);

        //shipping
        $order_info['shipping'] = shippingInfoJson($customer_info);

        $carts = [];
        foreach ((object)$products as $key => $item) {
            $cart_item = [];
            $cart_item['id'] = $key;
            $cart_item['product_name'] = preg_replace_array('/"/', [' '], $item['Produktname']);
            $cart_item['description'] = preg_replace_array('/"/', [' '], $item['Artikelnummer']);
            $cart_item['item_number'] = preg_replace_array('/"/', [' '], $item['Artikelnummer']);
            $cart_item['qty'] = $item['Anzahl'];
            $cart_item['rate'] = removeCommaFromPrice($item['Einzelpreis']);
            $cart_item['tax'] = removeCommaFromPrice($item['Steuer']);
            $cart_item['product_discount'] = removeCommaFromPrice($item['Mengenrabatt']) ?? 0;
            $cart_item['amount'] = removeCommaFromPrice($item['Gesamtpreis']);
            $carts[] = $cart_item;
        }
        $order_info['cart'] = json_encode($carts);
        // order add
        if($this->insert_order($order_info)){
            $this->insertSyncDate($shop, $l_date);
        }
    }


    /*=======================================================
    ============== Sync Ebay order Latest API ===============
    ========================================================*/
    public function syncEbayOrderLatestAPI($shop)
    {
        $args = [];
        $l_date = $this->syncStartDate($shop);
        if ($l_date) {
            //90 days order check
            $last_sync_date = strtotime(date('Y-m-d', strtotime($l_date)));
            $ninty_days_past = strtotime(date('Y-m-d', strtotime('-90 days')));
            if ($last_sync_date > $ninty_days_past) {
                $args['CreateTimeFrom'] = date('Y-m-d', strtotime($l_date)) . ' 00:00:00';
            }
            $args['limit'] = 200;
        }

        $count = 0;

        $collection = collect([]);
        try {
            if ($shop) {
                $ebay_client = new \App\Services\OrderSync\EbayOrderAPI($shop->username);
                $next_url = null;
                do {
                    $response = ($next_url) ? $ebay_client->getNextOrders($next_url) : $ebay_client->getOrders($args);
                    $next_url = (isset($response['next']) && $response['next']) ? $response['next'] : null;
                    $orders = (isset($response['orders']) && $response['orders']) ? $response['orders'] : [];

                    //Collecting all orders
                    if (is_array($orders) && count($orders)) {
                        $collection = $collection->merge($orders);
                    }

                } while ($next_url);
            } else {
                throw new Exception('You have not configured any Ebay shop!');
            }

            //Insert part
            if ($collection->isNotEmpty()) {
                //Reverse ordering
                $orderCollection = $collection->reverse();

                //Send order array to insertion
                $orderCollection->chunk(100)->each(function ($order_items) use (&$count, $shop) {
                    $this->bulkOrderInsertEbayLatestAPI($shop, $order_items->toArray());
                    $count += $order_items->count();
                });
            }
            //End insert part

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $errom_msg = $e->getMessage() . ' File: ' . $e->getFile() . ' Line: ' . $e->getLine();
            $this->syncErrorReport($shop, $count, $errom_msg);
        }
    }

    private function bulkOrderInsertEbayLatestAPI($shop, $orders)
    {
        $last_sync_date = $this->syncStartDate($shop);

        $last_date = $last_sync_date;

        foreach ($orders as $order) {
            //Check if this order_api_id already exist
            if (DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $order['legacyOrderId']])->exists()) continue;

            $customer_info = $order_info = [];
            $customer_id = null;

            //Price calculation
            $pricingSummary = $order['pricingSummary'];
            $priceSubtotal = isset($pricingSummary['priceSubtotal']) ? $pricingSummary['priceSubtotal'] : [];
            $subtotal = isset($priceSubtotal['convertedFromValue']) ? $priceSubtotal['convertedFromValue'] : 0;

            //deliveryCost
            $deliveryCost = isset($pricingSummary['deliveryCost']) ? $pricingSummary['deliveryCost'] : [];
            $shipping_cost = isset($deliveryCost['convertedFromValue']) ? $deliveryCost['convertedFromValue'] : 0;

            $priceTotal = isset($pricingSummary['total']) ? $pricingSummary['total'] : [];
            $total = isset($priceTotal['convertedFromValue']) ? $priceTotal['convertedFromValue'] : 0;


            //Payment data
            $paymentSummary = $order['paymentSummary'];
            $payment_data = isset($paymentSummary['payments']) ? $paymentSummary['payments'][0] : [];
            $paymentMethod = isset($payment_data['paymentMethod']) ? $payment_data['paymentMethod'] : null;
            $paymentReferenceId = isset($payment_data['paymentReferenceId']) ? $payment_data['paymentReferenceId'] : null;
            $paymentStatus = isset($payment_data['paymentStatus']) ? $payment_data['paymentStatus'] : null;

            $currency = isset($priceSubtotal['convertedFromCurrency']) ? $priceSubtotal['convertedFromCurrency'] : 'EUR';

            // dd($paymentStatus, $paymentMethod, $paymentReferenceId);

            if (isset($order['fulfillmentStartInstructions'][0])) {
                $address_data = $order['fulfillmentStartInstructions'][0];
                $shippingStep = (isset($address_data['shippingStep'])) ? $address_data['shippingStep'] : [];
                $shipTo = (isset($shippingStep['shipTo'])) ? $shippingStep['shipTo'] : [];

                $contactAddress = isset($shipTo['contactAddress']) ? $shipTo['contactAddress'] : [];

                $billing_name = $shipTo['fullName'];
                $street_shipping = $contactAddress['addressLine1'];
                $city_shipping = $contactAddress['city'];
                $state_shipping = null;
                $zipcode_shipping = $contactAddress['postalCode'];
                $country_shipping = $contactAddress['countryCode'];


                $email = $shipTo['email'];
                if (empty($email)) {
                    //Generate email address
                    $email_str = $billing_name . $zipcode_shipping . $country_shipping;
                    $email_str = strtolower(preg_replace('/\s+/', '', $email_str));
                    $email = $email_str . '@drmebayfake.com';
                    $email = \App\Helper\Encoding::toUTF8($email);
                }

                $phone = isset($shipTo['primaryPhone']) ? $shipTo['primaryPhone']['phoneNumber'] : null;

                $customer_info = [
                    'customer_full_name' => $billing_name,
                    'company_name' => '',
                    'email' => $email,
                    'city' => $city_shipping,
                    'zip_code' => $zipcode_shipping,
                    'state' => $state_shipping,
                    'country' => $country_shipping,
                    'phone' => $phone,

                    'currency' => $currency,
                    'address' => $street_shipping,
                    'insert_type' => 1,
                    'user_id' => $shop->user_id,

                    // shipping
                    'shipping_name' => $billing_name,
                    'street_shipping' => $street_shipping,
                    'address_shipping' => null,
                    'city_shipping' => $city_shipping,
                    'state_shipping' => $state_shipping,
                    'zipcode_shipping' => $zipcode_shipping,
                    'country_shipping' => $country_shipping,

                    //billing
                    'billing_name' => $billing_name,
                    'street_billing' => $street_shipping,
                    'address_billing' => null,
                    'city_billing' => $city_shipping,
                    'state_billing' => $state_shipping,
                    'zipcode_billing' => $zipcode_shipping,
                    'country_billing' => $country_shipping,

                    'source' => $shop->channel,
                ];

                $customer_id = $this->insert_customer($customer_info);
            }

            // ----------------------- order ----------------------------


            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $order['legacyOrderId'];
            $order_info['order_date'] = $last_date = $order['creationDate'];
            $order_info['insert_type'] = 1;
            $order_info['total'] = $total;
            $order_info['sub_total'] = $subtotal;
            $order_info['shipping_cost'] = $shipping_cost;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $paymentMethod;
            $order_info['payment_status'] = $paymentStatus;
            $order_info['currency'] = $currency;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $order_info['status'] = $order['orderFulfillmentStatus'];

            $carts = [];
            foreach ($order['lineItems'] as $key => $item) {
                $title = preg_replace_array('/"/', [' '], $item['title']);
                $cart_item = [];
                $cart_item['id'] = $item['lineItemId'];
                $cart_item['product_name'] = $title;
                $cart_item['description'] = null;
                $cart_item['qty'] = $line_qty = $item['quantity'];
                $cart_item['rate'] = $line_rate = $item['lineItemCost']['convertedFromValue'];
                $cart_item['tax'] = 0;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = $line_qty * $line_rate;
                $cart_item['SKU'] = isset($item['sku']) ? $item['sku'] : null;
                $carts[] = $cart_item;
            }

            $order_info['cart'] = json_encode($carts);
            $this->insert_order($order_info);
        }

        $this->insertSyncDate($shop, $last_date);
    }

    /* Ebay Orders	*/

    public function syncEbayOrder($shop)
    {
        $last_date = now()->subDays(80)->format('Y-m-d H:i:s');
        $l_date = $this->syncStartDate($shop);
        if ($l_date) {
            $last_date = date('Y-m-d', strtotime($l_date)) . ' 00:00:00';
        }

        $count = 0;
        try {
            if ($shop) {
                $ebay_client = new \App\Services\OrderSync\EbayOrderAPI($shop->username);
                $secret_key = $ebay_client->getEbaySecretKey();

                $pageNum = 1;

                $args = [
                    'eBayAuthToken' => $secret_key,
                    'CreateTimeFrom' => \DateTime::createFromFormat('Y-m-d H:i:s', $last_date)->format('Y-m-d\TH:i:s.000\Z'),
                    'CreateTimeTo' => \DateTime::createFromFormat('Y-m-d H:i:s', Carbon::now())->format('Y-m-d\TH:i:s.000\Z')
                ];

                do {
                    $args['page'] = $pageNum;
                    $response = resolve('\App\Services\Ebay\Ebay')->getOrders($args);
                    if ($response['Ack'] == 'Success') {
                        $OrderArray = (object)$response['OrderArray'];
                        if (isset($OrderArray->Order)) {   //If response has order
                            $all_orders = $OrderArray->Order;
                            if ($all_orders) {
                                $count += count($all_orders);
                                // Store ebay order logs
                                \Storage::disk('local')->put('ebay_order_data/sync-'.date('Y_m_d_H_i_s').'.txt', json_encode($all_orders));
                                $this->bulkOrderInsertEbay($shop, $all_orders, $ebay_client);
                            }
                        }
                    } else {
                        $error_message = 'Authentication error!';
                        if (isset($response['Errors'])) {
                            $error = reset($response['Errors']);
                            $error_message = isset($error['LongMessage']) ? $error['LongMessage'] : $error_message;
                        }
                        throw new Exception($error_message);
                    }

                    $pageNum += 1;
                } while (isset($response['HasMoreOrders']) && $response['HasMoreOrders']);

                app(\App\Services\OrderSync\EbayDueOrder::class)->dueOrderSync($ebay_client, $shop->id); //Ebay due MP order

            } else {
                throw new Exception('You have not configured any Ebay shop!');
            }

            $this->syncSuccessReport($shop, $count);

        } catch (Exception $e) {
            $errom_msg = $e->getMessage() . ' File: ' . $e->getFile() . ' Line: ' . $e->getLine();
            $this->syncErrorReport($shop, $count, $errom_msg);
        }

    }

    public function bulkOrderInsertEbay($shop, $all_orders, $ebay_client)
    {

        $last_sync_date = $this->syncStartDate($shop);

        $last_date = $last_sync_date;

        foreach ($all_orders as $order) {
            $order_id_api = $order['OrderID'];
            // Check 2 hyphen in order-id-api

            if(empty($order_id_api) || substr_count($order_id_api, '-') < 2) continue;
            //Check if this order_api_id already exist

            $exist_shop_id = DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $order_id_api])->first();
            if ($exist_shop_id) continue;

            $customer_info = $order_info = [];
            $customer_id = null;

            if (isset($order['TransactionArray']['Transaction'][0]['Buyer'])) {
                $billing_name = $order['ShippingAddress']['Name'];
                $street_shipping = $order['ShippingAddress']['Street1'] ?? $order['ShippingAddress']['Street2'];
                $city_shipping = $order['ShippingAddress']['CityName'];
                $state_shipping = $order['ShippingAddress']['StateOrProvince'];
                $zipcode_shipping = $order['ShippingAddress']['PostalCode'];
                $country_shipping = $order['ShippingAddress']['CountryName'];


                $email = $order['TransactionArray']['Transaction'][0]['Buyer']['Email'];
                $customer_name = $order['TransactionArray']['Transaction'][0]['Buyer']['UserFirstName'] . ' ' . $order['TransactionArray']['Transaction'][0]['Buyer']['UserLastName'];
                $phone = $order['ShippingAddress']['Phone'];

                $phone = (strtolower($phone) == strtolower('Invalid Request')) ? null : $phone;

                if (!filter_var($email, FILTER_VALIDATE_EMAIL) || strtolower($email) == strtolower('Invalid Request')) {
                    $fake_email = $order['BuyerUserID'] ?? $customer_name;
                    $email = preg_replace('/[^a-zA-Z0-9]/', '', $fake_email);
                    $email = preg_replace('/\s+/', '_', $email);
                    $email = $email . '4' . $zipcode_shipping . '@drmebay.com';
                    $email = trim(preg_replace('/\s+/', ' ', $email));
                }


                $customer_info = [
                    'customer_full_name' => $customer_name,
                    'company_name' => '',
                    'email' => $email,
                    'city' => $city_shipping,
                    'zip_code' => $zipcode_shipping,
                    'state' => $state_shipping,
                    'country' => $country_shipping,
                    'phone' => $phone,

                    'currency' => $order['AmountPaid']['currencyID'],
                    'address' => $street_shipping,
                    'insert_type' => 1,
                    'user_id' => $shop->user_id,

                    // shipping
                    'shipping_name' => $billing_name,
                    'street_shipping' => $street_shipping,
                    'address_shipping' => $order['ShippingAddress']['Street2'],
                    'city_shipping' => $city_shipping,
                    'state_shipping' => $state_shipping,
                    'zipcode_shipping' => $zipcode_shipping,
                    'country_shipping' => $country_shipping,

                    //billing
                    'billing_name' => $billing_name,
                    'street_billing' => $street_shipping,
                    'address_billing' => $order['ShippingAddress']['Street2'],
                    'city_billing' => $city_shipping,
                    'state_billing' => $state_shipping,
                    'zipcode_billing' => $zipcode_shipping,
                    'country_billing' => $country_shipping,

                    'source' => $shop->channel,
                ];

                $customer_id = $this->insert_customer($customer_info);
            }

            // ----------------------- order ----------------------------


            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $order_id_api;
            $order_info['order_date'] = $order['CreatedTime'];
            $last_date = $order_info['order_date'];
            $order_info['insert_type'] = 1;
            $order_info['total'] = $order['Total']['value'];
            $order_info['sub_total'] = $order['Subtotal']['value'];
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $order['CheckoutStatus']['PaymentInstrument'];
            $order_info['currency'] = $order['Total']['currencyID'];

            if($customer_id){
                $exist_shop_item = DB::table('new_orders')->where([
                    'shop_id' => $shop->id,
                    'drm_customer_id' => $customer_id,
                    'order_date' => $order_info['order_date']
                ])->first();
                if ($exist_shop_item) continue;
            }

            $order_info['status'] = $order['OrderStatus'];

            try{

                $order_r = $ebay_client->getOrder($order_id_api);

                // $pricingSummary = $order_r['pricingSummary'];
                // $priceSubtotal = isset($pricingSummary['priceSubtotal'])? $pricingSummary['priceSubtotal'] : [];
                // $subtotal = isset($priceSubtotal['convertedFromValue'])? $priceSubtotal['convertedFromValue'] : 0;

                // $priceTotal = isset($pricingSummary['total'])? $pricingSummary['total'] : [];
                // $total = isset($priceTotal['convertedFromValue'])? $priceTotal['convertedFromValue'] : 0;

                //Payment data
                $paymentSummary = $order_r['paymentSummary'];
                $payment_data = isset($paymentSummary['payments'])? $paymentSummary['payments'][0]: [];
                $paymentMethod = isset($payment_data['paymentMethod'])? $payment_data['paymentMethod']: null;
                $paymentReferenceId = isset($payment_data['paymentReferenceId'])? $payment_data['paymentReferenceId']: null;
                $paymentStatus = isset($payment_data['paymentStatus'])? $payment_data['paymentStatus']: null;
                $order_info['payment_status'] = $order_info['ebay_payment_status'] = $paymentStatus; //$order_r['orderPaymentStatus'];



                $shippings = $ebay_client->getOrderShippings($order_id_api);
                $shipping_fulfillments = $shippings ? $shippings['fulfillments'] : [];

                $trackings_data = collect($shipping_fulfillments)->filter(function($item) {
                    return isset($item['shipmentTrackingNumber']) && isset($item['shippingCarrierCode']);
                })
                ->map(function($item) {
                    return [
                        'tracking_number' => $item['shipmentTrackingNumber'],
                        'parcel_name' => $item['shippingCarrierCode'],
                    ];
                })
                ->toArray();

                if(!empty($trackings_data))
                {
                    $order_info['trackings_data'] = $trackings_data;
                }

                if(strtolower($paymentStatus) == 'failed')
                {
                    $order_info['status'] = 'canceled';
                }

            }catch(Exception $ee){}
            finally {
                if(empty($paymentStatus) || strtolower($paymentStatus) != 'paid')
                {
                    if($order_info['status'] != 'canceled')
                    {
                        $order_info['status'] = 'nicht_bezahlt';
                    }
                }
            }

            if($order_info['status'] == 'Active')
            {
                $order_info['status'] = 'nicht_bezahlt';
            }

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $carts = [];
            foreach ($order['TransactionArray']['Transaction'] as $key => $item) {
                $item_data = $item['Item'];
                $title = preg_replace_array('/"/', [' '], $item_data['Title']);
                $cart_item = [];
                $cart_item['id'] = $key;
                $cart_item['product_name'] = $title;
                $cart_item['description'] = null;
                $cart_item['qty'] = $item['QuantityPurchased'];
                $cart_item['rate'] = $item['TransactionPrice']['value'];
                $cart_item['tax'] = $item['Taxes']['TotalTaxAmount']['value'];
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = $item['QuantityPurchased'] * $item['TransactionPrice']['value'];
                $cart_item['item_number'] = array_key_exists('SKU', $item_data) ? $item_data['SKU'] : null;
                $carts[] = $cart_item;
            }

            $order_info['cart'] = json_encode($carts);

            $this->insert_order($order_info);
        }

        $this->insertSyncDate($shop, $last_date);

    }


    //Decathlon orders
    public function syncDecathlonOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://marketplace-decathlon-eu.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
    }

    //Sprinter orders
    public function syncSprinterOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://sprinter-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    //Tiendanimal orders
    public function syncTiendanimalOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://tiendanimal.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    //Mediamarkt orders
    public function syncMediamarktOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://mediamarktsaturn.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();

                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    //Conrad orders
    public function syncConradOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://conradb2b-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    //Fressnapf orders
    public function syncFressnapfOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            if ($shop->id == 1156) { // Fressnapf - Frank Averbeck, user_id=3553
                $last_date = '2024-11-06';
            }

            $url = 'https://fressnapfde-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Volkner orders
    public function syncVolknerOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://rein-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Manor orders
    public function syncManorOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://manor-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Xxxlutz orders
    public function syncXxxlutzOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://marketplace.xxxlgroup.com/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Perfume's Club orders
    public function syncPerfumesClubOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://novaclub-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Home24 orders
    public function syncHome24Order($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://home24.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $query['limit'] = 50;
                $query['order_state_codes'] = 'SHIPPING';
                $query['shop_id'] = $shop->api_shop_id;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Alltricks orders
    public function syncAlltricksOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://alltricks-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Clube Fashion orders
    public function syncClubeFashionOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://clubefashion.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Zooplus orders
    public function syncZooplusOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://zooplus-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Pss orders
    public function syncPssOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://mirakl.privatesportshop.com/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Big bang orders
    public function syncBigbangOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://bigbangmarketplace.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Bricodepot orders
    public function syncBricodepotOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://marketplace.bricodepot.es/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Hornbach orders
    public function syncHornbachOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://hornbach-mp.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Planetahuerto orders
    public function syncPlanetahuertoOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://planetahuerto-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Carrefour orders
    public function syncCarrefourOrder($shop)
    {
        $count = 0;

        try {

            $last_date = $this->syncStartDate($shop);
            if ($last_date) {
                $last_date = date('Y-m-d', strtotime($last_date . "-7 day"));
            }

            $url = 'https://carrefoures-prod.mirakl.net/api/orders';
            $params = [
                'headers' => [
                    'Authorization' => $shop->username,
                    'Accept' => 'application/json'
                ],
            ];

            $query = [];
            if($last_date)
            {
                $query['start_date'] = $last_date;
            }

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                $query['offset'] = $offset;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data_json = $response->getBody()->getContents();
                $data = json_decode($data_json);

                $all_orders = $data->orders;
                $count = $data->total_count;

                $order_count = 0;
                if($all_orders)
                {
                    $order_count = count($all_orders);
                    $this->bulkorderInsertDecathlon($shop, $all_orders);

                    $offset += $order_count;
                }

                if ($offset >= $data->total_count) {
                    break;
                }

                $count += $order_count;
                // $offset++;

            } while($order_count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel);
            SprinterTiendanimalMediamarktCustomerSync::dispatch($shop->id, $shop->channel)->delay(now()->addMinutes(40));
        }
    }

    // Bulk insert Decathlon
    public function bulkorderInsertDecathlon($shop, $all_orders)
    {
        $last_date = null;
        $current_req = 0;
        $mirakl_ids = [17, 18, 19, 22, 23, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39];

        foreach ($all_orders as $order) {
            $order_id = $order->order_id;

            $exist_shop_id = DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $order_id])->first();
            if ($exist_shop_id) {
                if (($exist_shop_id->status == 'WAITING_ACCEPTANCE') && ($exist_shop_id->status != $order->order_state) && in_array($shop->channel, $mirakl_ids)) {
                    DB::table('new_orders')->where('id', $exist_shop_id->id)->update(['status' => $order->order_state]);

                    app(\App\Services\OrderSync\SprinterTiendanimalMediamarkt::class)->syncCustomer($order_id, $shop, 0);
                }

                continue;
            }

            if (in_array($shop->channel, $mirakl_ids)) {
                $canInsert = app(\App\Services\OrderSync\SprinterTiendanimalMediamarkt::class)->okToInsertOrder($shop, $order);
                if (!$canInsert) continue;
            }


            if(DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $order_id])->exists())
            {
                continue;
            }

            if (in_array(strtolower($order->order_state), ['closed'])) {
                continue;
            }

            $handling_time = 0;
            if($order->shipping_deadline){
                $handling_time = Carbon::parse($order->shipping_deadline)->diffInDays(Carbon::now(), true);
            }

            $customer_info = $order_info = [];
            $customer_id = null;

            $currency = $order->currency_iso_code ?? 'EUR';
            $total_amount = $order->total_price;

            $customer = $order->customer;
            $billing = $customer->billing_address;
            $shipping = $customer->shipping_address;

            if(empty($billing->country)) continue;

            $customerName = trim($customer->firstname.' '.$customer->lastname);

            $customer_info = [
                'customer_full_name' => $customerName,
                'company_name' => '',
                'email' => $this->_getEmail($order->customer_notification_email ?? '', $customerName),
                'city' => $billing->city,
                'zip_code' => $billing->zip_code,
                'state' => $billing->state,
                'country' => $billing->country,
                'phone' => null,

                'currency' => $currency,
                'address' => $billing->street_1,
                'insert_type' => 1,
                'user_id' => $shop->user_id,

                // shipping
                'shipping_name' => trim($shipping->firstname.' '.$shipping->lastname),
                'street_shipping' => $shipping->street_1,
                'address_shipping' => $shipping->street_1,
                'city_shipping' => $shipping->city,
                'state_shipping' => $shipping->state,
                'zipcode_shipping' => $shipping->zip_code,
                'country_shipping' => $shipping->country,

                //billing
                'billing_name' => trim($billing->firstname.' '.$billing->lastname),
                'street_billing' => $billing->street_1,
                'address_billing' => $billing->street_1,
                'city_billing' => $billing->city,
                'state_billing' => $billing->state,
                'zipcode_billing' => $billing->zip_code,
                'country_billing' => $billing->country,
                'source' => $shop->channel,
            ];

            $customer_id = $this->insert_customer($customer_info);

            // ----------------------- order ----------------------------
            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $order_id;
            $order_info['order_date'] = $last_date = $order->created_date;
            $order_info['insert_type'] = 1;
            $order_info['total'] = $total_amount;
            $order_info['sub_total'] = $total_amount;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $order->payment_type;
            $order_info['currency'] = $currency;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $order_info['status'] = in_array(strtolower($order->order_state), ['shipping', 'shipped']) ? 'paid' : $order->order_state;

            // Handling time
            if($handling_time > 0)
            {
                $order_info['handling_time'] = $handling_time;
            }

            $order_items = $order->order_lines;

            $tax_rate = 0;

            // Carts
            $carts = [];
            if ($order_items) {
                foreach ($order_items as $key => $item) {
                    $tax_rate = round($item->commission_rate_vat, 2);
                    $cart_item = [];
                    $cart_item['id'] = $item->offer_id;
                    $cart_item['product_name'] = $item->product_title;
                    $cart_item['description'] = $item->description;
                    $cart_item['item_number'] = $item->offer_sku;
                    $cart_item['ean'] = $item->offer_sku;
                    $cart_item['qty'] = $item->quantity;
                    $cart_item['rate'] = $item->price_unit;
                    $cart_item['tax'] = $tax_rate;
                    $cart_item['product_discount'] = 0;
                    $cart_item['amount'] = round(($item->price_unit * $item->quantity), 2);
                    $carts[] = $cart_item;
                }
            }
            $carts = collect($carts)
            ->groupBy('ean')
            ->map(function($eanGroup) {
                $product = $eanGroup->first();
                $product['qty'] = $eanGroup->sum('qty');
                $product['rate'] = $eanGroup->sum('rate');
                $product['amount'] = $eanGroup->sum('amount');
                return $product;

            })->values()->toArray();

            $order_info['cart'] = json_encode($carts);
            $this->insert_order($order_info);
        }

        $this->insertSyncDate($shop, $last_date);
    }

    // Shopify Orders
    public function syncShopifyOrder($shop)
    {
        $count = 0;
        try {
            if ($shop) {
                $url = $shop->url . 'admin/api/2023-07/orders.json?status=any&fulfillment_status=any&limit=250';
                // $url = $shop_details->url . 'admin/api/2020-01/draft_orders.json';
                $client = new \GuzzleHttp\Client();
                $response = $client->request('GET', $url, [
                    'auth' => [$shop->username, $shop->password],
                    'headers' => [
                        'X-Shopify-Access-Token' => $shop->password,
                    ],
                ]);



                if ($response->getStatusCode() !== 200) {
                    throw new Exception('Connection problem!');
                }

                $data = $response->getBody()->getContents();

                $all_orders = (json_decode($data))->orders;

                // $response2 = $client->request('GET', $shop->url . 'admin/products/4576050839634.json', [
                //     'auth' => [$shop->username, $shop->password]
                // ]);
                // dd(json_decode($response2->getBody()->getContents(),true),$all_orders[1],$all_orders[6]);
                if ($all_orders) {
                    $count += count((array)$all_orders);
                    $this->bulkorderInsertShopify($shop, $all_orders);
                }

            } else {
                throw new Exception('You have not configured shop!');
            }

            $this->syncSuccessReport($shop, $count);
            app(\App\Services\OrderSync\ShopifyCustomerSync::class)->run($shop);

        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
    }

    public function bulkorderInsertShopify($shop, $all_orders)
    {
        $sync_date = $this->syncStartDate($shop);

        $l_date = $sync_date;


        if ($sync_date) {
            $sync_date = date('Y-m-d', strtotime($sync_date . "-7 day")) . ' 00:00:00';
        }


        // dd($all_orders);
        foreach ((object)$all_orders as $value) {
            //          $exist_shop_id = DB::table('new_orders')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->id)->first();
            // if($exist_shop_id) continue;

            $new = new DateTime($value->created_at);
            $old = new DateTime($sync_date);

            if (($sync_date != null) && ($old >= $new)) {
                echo 'Order Exist!';
            } else {


                $customer_info = $order_info = null;
                $shipping_address = $value->shipping_address;
                $billing_address = $value->billing_address;

                // Billing address empty
                if(empty($billing_address->country)) continue;


                // Duplicate check
                if(DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $value->id])->exists())
                {
                    continue;
                }

                $customer_info = [
                    'customer_full_name' => $value->customer->default_address->name,
                    'company_name' => $value->customer->default_address->company,
                    'email' => $value->customer->email ?? strtolower(Str::random(20)).'@drmfake.com',
                    'city' => $value->customer->default_address->city,
                    'zip_code' => $value->customer->default_address->zip,
                    'state' => $value->customer->default_address->province,
                    'country' => $value->customer->default_address->country_name,
                    'phone' => $value->customer->default_address->phone,
                    // 'website' => ,
                    'currency' => $value->customer->currency,
                    //  'default_language' => ,
                    'address' => $value->customer->default_address->address1,
                    'insert_type' => 1,
                    'user_id' => $shop->user_id,
                    //  'vat_number' => ,

                    // shipping
                    'street_shipping' => $shipping_address->address1,
                    'city_shipping' => $shipping_address->city,
                    'state_shipping' => $shipping_address->province,
                    'zipcode_shipping' => $shipping_address->zip,
                    'country_shipping' => $shipping_address->country,

                    //billing
                    'street_billing' => $billing_address->address1,
                    'city_billing' => $billing_address->city,
                    'state_billing' => $billing_address->province,
                    'zipcode_billing' => $billing_address->zip,
                    'country_billing' => $billing_address->country,

                    'source' => $shop->channel,
                ];


                if (isset($value->customer)) {
                    // insert customer
                    $customer_id = $this->insert_customer($customer_info);
                }

                // ----------------------- order ----------------------------

                $order_info['user_id'] = $shop->user_id;

                $order_info['drm_customer_id'] = $customer_id;
                // $date=date_create("2013-03-15");
                $order_info['order_date'] = $value->created_at;

                $l_date = $order_info['order_date'];

                $order_info['insert_type'] = 1;
                $order_info['total'] = $value->total_price;
                $order_info['shop_id'] = $shop->id;
                $order_info['order_id_api'] = $value->id;

                $order_info['sub_total'] = $value->subtotal_price;
                $order_info['discount'] = 0;
                $order_info['discount_type'] = "fixed";
                $order_info['adjustment'] = 0;
                $order_info['payment_type'] = $value->gateway;
                $order_info['currency'] = $value->currency;

                //customer info
                $order_info['customer_info'] = customerInfoJson($customer_info);

                //billing
                $order_info['billing'] = billingInfoJson($customer_info);

                //shipping
                $order_info['shipping'] = shippingInfoJson($customer_info);

                // $order_info['client_note'];
                $order_info['status'] = $value->financial_status;

                $carts = [];
                foreach ((object)$value->line_items as $item) {
                    $cart_item = [];
                    $cart_item['id'] = $item->id;
                    $cart_item['product_name'] = preg_replace_array('/"/', [' '], $item->name);
                    $cart_item['description'] = preg_replace_array('/"/', [' '], $item->title);
                    $cart_item['qty'] = $item->quantity;
                    $cart_item['rate'] = $item->price;
                    $cart_item['tax'] = $item->tax;
                    $cart_item['item_number'] = $item->sku ?? null;
                    $cart_item['product_discount'] = $item->total_discount ?? 0;
                    $cart_item['amount'] = $item->quantity * $item->price;
                    $carts[] = $cart_item;
                }

                $order_info['cart'] = json_encode($carts);

                // ----------------------- Order Insert ---------------------
                $this->insert_order($order_info);
            }// If end -- order date check
        } //foreach end

        $this->insertSyncDate($shop, $l_date);
    }

    //Woocommerce order sync
    public function syncWooCommerceOrder($shop)
    {
        $last_date = $this->syncStartDate($shop);

        if ($last_date) {
//            $last_date = date('Y-m-d H:i:s', strtotime($last_date) - (7 * 24 * 60 * 60));
            $last_date = date('Y-m-d 00:00:00', strtotime('-7 days', strtotime($last_date)));
        }

        $this->fetchWooCommerceOrder($shop, $last_date);
    }

    //Woocommerce fetch order
    private function fetchWooCommerceOrder($shop, $last_date = null)
    {
        try {
            if ($shop) {
                $url = $shop->url;
                $user = $shop->username;
                $pass = $shop->password;
            } else {
                throw new Exception("You have not configured any WooCommerce shop yet!");
            }

            $woocommerce = new Client($url, $user, $pass);
            $page = 1;
            $count = 0;

            //Fetch woocommerce orders
            do {
                $orders = $woocommerce->get('orders', [
                    'page' => $page,
                    'per_page' => 100,
                    'after' => $last_date
                ]);

                if ($orders) {
                    $count += count((array)$orders);
                    $this->bulkOrderInsertWooCommerce($orders, $shop);
                }

                $page++;
            } while ($orders);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
    }

    private function isPaid($order,$channel)
    {
        $isPaid = false;
        switch ($channel){
            case Channel::WOOCOMMERCE:
            if(!empty($order->transaction_id) or !empty($order->date_paid) or !$order->needs_payment){
                $isPaid = true;
            }
            break;
            //Add other channels
        }
        return $isPaid;
    }

    public function bulkOrderInsertWooCommerce($orders, $shop)
    {
        $l_date = null;
        foreach ((array)$orders as $order) {
            $paymentStatus = $this->isPaid($order,$shop->channel);
            if(!$paymentStatus) continue;

            $billing = $order->billing;
            $billing_value = array_filter((array)$billing);

            // if (count($billing_value) > 1) {

            $l_date = $order->date_created;

            $customer_info = $order_info = $customer_id = null;

            $customer_info = [
                'customer_full_name' => trim($billing->first_name . " " . $billing->last_name),
                'company_name' => $billing->company,
                'email' => $billing->email,
                'city' => $billing->city,
                'zip_code' => $billing->postcode,
                'state' => $billing->state,
                'country' => $billing->country,
                'phone' => $billing->phone,
                // 'website' => ,
                'currency' => $order->currency,
                //  'default_language' => ,
                'address' => trim($billing->address_1 . ' ' . $billing->address_2),
                'insert_type' => 1,
                'user_id' => $shop->user_id,
                //  'vat_number' => ,

                'source' => $shop->channel,
            ];

            //billing
            $customer_info['billing_name'] = $customer_info['customer_full_name'];
            $customer_info['billing_company'] = $customer_info['company_name'];
            $customer_info['street_billing'] = $customer_info['address'];
            $customer_info['city_billing'] = $customer_info['city'];
            $customer_info['state_billing'] = $customer_info['state'];
            $customer_info['zipcode_billing'] = $customer_info['zip_code'];
            $customer_info['country_billing'] = $customer_info['country'];

            // shipping
            $shipping = $order->shipping;
            $customer_info['shipping_name'] = (strlen(trim($shipping->first_name . " " . $shipping->last_name)) > 1) ? trim($shipping->first_name . " " . $shipping->last_name) : $customer_info['customer_full_name'];
            $customer_info['shipping_company'] = (strlen($shipping->company)) ? $shipping->company : $customer_info['company_name'];
            $customer_info['street_shipping'] = (strlen(trim($shipping->address_1 . ' ' . $shipping->address_2))) ? trim($shipping->address_1 . ' ' . $shipping->address_2) : $customer_info['address'];
            $customer_info['city_shipping'] = (strlen($shipping->city)) ? $shipping->city : $customer_info['city'];
            $customer_info['state_shipping'] = (strlen($shipping->state)) ? $shipping->state : $customer_info['state'];
            $customer_info['zipcode_shipping'] = (strlen($shipping->postcode)) ? $shipping->postcode : $customer_info['zip_code'];
            $customer_info['country_shipping'] = (strlen($shipping->country)) ? $shipping->country : $customer_info['country'];

            if (count($billing_value) > 1) {
                // insert customer
                $customer_id = $this->insert_customer($customer_info);
            }
            // dd($customer_id);

            // ----------------------- order ----------------------------

            $order_info['user_id'] = $shop->user_id;
            $order_info['drm_customer_id'] = $customer_id;
            $order_info['order_date'] = $order->date_created;
            $order_info['insert_type'] = 1;
            $order_info['total'] = round($order->total, 2);
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $order->order_key;

            $order_info['sub_total'] = $order->total;
            $order_info['discount'] = $order->discount_total;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $order->payment_method;
            $order_info['currency'] = $order->currency;
            $order_info['shipping_cost'] = $order->shipping_total;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            // $order_info['client_note'];
            $order_info['status'] = $order->status;

            $carts = [];
            if ($order->line_items) {
                foreach ((array)$order->line_items as $item) {
                    $cart_item = [];
                    $cart_item['id'] = $item->id;
                    $cart_item['product_name'] = preg_replace_array('/"/', [' '], $item->name);
                    $cart_item['description'] = preg_replace_array('/"/', [' '], $item->sku);
                    $cart_item['qty'] = $item->quantity;
                    $cart_item['rate'] = round($item->price, 2);
                    $cart_item['tax'] = $item->total_tax;
                    $cart_item['image'] = null;
                    $cart_item['item_number'] = $item->sku;
                    $cart_item['product_discount'] = 0;
                    $cart_item['amount'] = round($item->total, 2);
                    $carts[] = $cart_item;
                }
                $order_info['cart'] = json_encode($carts);
            }

            // ----------------------- Order Insert ---------------------
            $this->insert_order($order_info);
            // }
        }/* end foreach */

        $this->insertSyncDate($shop, $l_date);
    }


    //Amazon Order Sync
    public function syncAmazonOrder($shop)
    {

        $count = 0;
        try {
            $client = new \App\Services\Amazon\AZClient([
                'Marketplace_Id' => 'A1PA6795UKMFR9',
                'Seller_Id' => $shop->username,
                'Access_Key_ID' => '********************',
                'Secret_Access_Key' => 'oEIgkGejJYPU9NeTZs3CVacxqNuML2kx1P14ODnw',
                'MWSAuthToken' => $shop->password
            ]);

            $last_date = '2016-01-01';
            $last_sync_date = $this->syncStartDate($shop);
            if ($last_sync_date) {
                $date = strtotime($last_sync_date);
                $last_date = date("Y-m-d", strtotime("-3 days", $date));
            }

            $fromDate = new DateTime($last_date);
            $NextToken = null;

            // dump($client->GetMatchingProductForId(['B07ZNMQ2CX']));
            // dump($client->GetMatchingProductForId(['B00HJMFR82']));
            do {
                sleep(65);
                $data = ($NextToken) ? $client->ListOrdersByNextToken($NextToken) : $client->ListOrders($fromDate, true, []);

                $NextToken = isset($data['NextToken']) ? $data['NextToken'] : null;

                //Orders
                $all_orders = isset($data['ListOrders']) ? $data['ListOrders'] : $data;

                if ($all_orders) {
                    $count += count((array)$all_orders);
                    $this->bulkOrderInsertAmazon($shop, $all_orders, $client);
                    // foreach (array_chunk($all_orders, 4) as $chunk_orders){

                    //     var_dump($chunk_orders);


                    //     $this->bulkOrderInsertAmazon($shop, $chunk_orders, $client);
                    //     // sleep(130);
                    // }
                }
            } while ($NextToken);

            $this->syncSuccessReport($shop, $count);

        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
    }


    public function bulkOrderInsertAmazon($shop, $all_orders, $client = null, $import_pid = null)
    {

        $last_date = null;
        $current_req = 0;

        foreach ($all_orders as $order) {

            $exist_shop_id = DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $order['AmazonOrderId']])->first();
            if ($exist_shop_id) continue;

            //If exist on lengow shop
            // $user_lengow_shops = \App\Shop::where('user_id', $shop->user_id)->where('channel', 2)->pluck('id')->toArray();
            // if(DB::table('new_orders')->whereIn('shop_id', $user_lengow_shops)->where(['cms_user_id' => $shop->user_id, 'order_id_api' => $order['AmazonOrderId'] ])->exists()){
            //     continue;
            // }

            if (DB::table('new_orders')->where('cms_user_id', $shop->user_id)->where('order_id_api', $order['AmazonOrderId'])->exists()) {
                continue;
            }

            $customer_info = $order_info = [];
            $customer_id = null;

            $currency = ($order['OrderTotal']) ? $order['OrderTotal']['CurrencyCode'] : 'EUR';
            $total_amount = $order['OrderTotal']['Amount'];

            if (isset($order['BuyerEmail'])) {
                $billing_name = $order['BillingAddress']['Name'] ?? null;

                $street_shipping = $order['ShippingAddress']['Street'] ?? null;
                $address_shipping = $order['ShippingAddress']['Address'] ?? null;
                $city_shipping = $order['ShippingAddress']['City'];
                $state_shipping = $order['ShippingAddress']['StateOrRegion'];
                $zipcode_shipping = $order['ShippingAddress']['PostalCode'];
                $country_shipping = $order['ShippingAddress']['CountryCode'];

                $street_billing = $order['BillingAddress']['Street'] ?? null;
                $address_billing = $order['BillingAddress']['Address'] ?? null;
                $city_billing = $order['BillingAddress']['City'];
                $state_billing = $order['BillingAddress']['StateOrRegion'];
                $zipcode_billing = $order['BillingAddress']['PostalCode'];
                $country_billing = $order['BillingAddress']['CountryCode'];

                $customer_info = [
                    'customer_full_name' => $billing_name,
                    'company_name' => '',
                    'email' => $order['BuyerEmail'],
                    'city' => $city_shipping,
                    'zip_code' => $zipcode_shipping,
                    'state' => $state_shipping,
                    'country' => $country_shipping,
                    'phone' => $order['ShippingAddress']['Phone'] ?? null,

                    'currency' => $currency,
                    'address' => $address_shipping,
                    'insert_type' => 1,
                    'user_id' => $shop->user_id,

                    // shipping
                    'shipping_name' => $order['ShippingAddress']['Name'] ?? null,
                    'street_shipping' => $street_shipping,
                    'address_shipping' => $address_shipping,
                    'city_shipping' => $city_shipping,
                    'state_shipping' => $state_shipping,
                    'zipcode_shipping' => $zipcode_shipping,
                    'country_shipping' => $country_shipping,

                    //billing
                    'billing_name' => $billing_name,
                    'street_billing' => !empty($street_billing) ? $street_billing : $street_shipping,
                    'address_billing' => !empty($address_billing) ? $address_billing : $address_shipping,
                    'city_billing' => !empty($city_billing) ? $city_billing : $city_shipping,
                    'state_billing' => !empty($state_billing) ? $state_billing : $state_shipping,
                    'zipcode_billing' => !empty($zipcode_billing) ? $zipcode_billing : $zipcode_shipping,
                    'country_billing' => !empty($country_billing) ? $country_billing : $country_shipping,

                    'source' => $shop->channel,
                ];

                $customer_id = $this->insert_customer($customer_info);
            }

            // ----------------------- order ----------------------------


            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $order['AmazonOrderId'];
            $order_info['order_date'] = $order['PurchaseDate'];
            $last_date = $order_info['order_date'];
            $order_info['insert_type'] = 1;
            $order_info['total'] = $total_amount;
            $order_info['sub_total'] = $order['Subtotal']['value']; //gg
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $order['PaymentMethodDetails']['PaymentMethodDetail'];
            $order_info['currency'] = $currency;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $order_info['status'] = $order['OrderStatus'];

            $order_info['channel'] = 'amazon';

            $current_req++;

            if ($client) {
            $order_items = $client->ListOrderItems($order['AmazonOrderId']);

            //EAN List
            $eanList = [];
            try{
                $assinArr = collect($order_items)->pluck('ASIN')->toArray();
                $eanList = app(KeepaAPI::class)->amazonEanSearch($assinArr);
            }catch(\Exception $eanError) { }
            } else {
                $order_items = $order['orderItems'];
            }

            $carts = [];
            if ($order_items) {
                foreach ($order_items as $key => $item) {

                    $qty = $item['QuantityOrdered'] ?? 1;
                    $amount = $item['ItemPrice']['Amount'];
                    $rate = $qty > 1 ? round($amount / $qty, 2) : $amount;


                    $cart_item = [];
                    $cart_item['id'] = $item['OrderItemId'];
                    $cart_item['product_name'] = $item['Title'];

                    $cart_item['description'] = $item['ASIN'];
                    $cart_item['asin'] = $asin = $item['ASIN'];
                    $cart_item['item_number'] = $item['SellerSKU'];

                    if ($client) {
                    $cart_item['ean'] = $eanList[$asin] ?? null;
                    } else {
                        $cart_item['ean'] = $item['SellerSKU'];
                    }

                    $cart_item['qty'] = $qty;
                    $cart_item['rate'] = $rate;
                    $cart_item['tax'] = $item['ItemTax']['Amount'];
                    $cart_item['product_discount'] = $item['PromotionDiscount']['Amount'];
                    $cart_item['amount'] = $amount;
                    $carts[] = $cart_item;
                }
            }

            $order_info['cart'] = json_encode($carts);
            $this->insert_order($order_info);

            if ($current_req >= 4) {
                $current_req = 0;
                sleep(10);
            }
        }

        if ($client) {
            $this->insertSyncDate($shop, $last_date);
        } else if ($import_pid) {
            DB::table('import_orders_histories')->where('id', $import_pid)->update(['end_at' => now()]);
        }
    }

    //Sync Etsy order
    public function syncEtsyOrder($shop)
    {
        $count = 0;
        try {
            $etsy_credential = DB::table('etsy_credentials')->where('shop_id', $shop->id)->select('consumer_key', 'consumer_secret', 'access_token', 'access_token_secret', 'etsy_shop_id')->first();
            $etsyApi = new EtsyOrderAPI($etsy_credential);
            $count = 0;
            $next_page = null;

            $last_date = '2016-01-01';
            $last_sync_date = $this->syncStartDate($shop);
            if ($last_sync_date) {
                $date = strtotime($last_sync_date);
                $last_date = date("Y-m-d", strtotime("-3 days", $date));
            }

            $fromDate = new DateTime($last_date);
            $params = ['shop_id' => $etsy_credential->etsy_shop_id];

            do {
                if ($next_page) {
                    $params['page'] = $next_page;
                }

                $orders = $etsyApi->client->findAllShopReceipts(['params' => $params]);

                if (isset($orders['results']) && $orders['results']) {
                    //Process order
                    $this->bulkOrderInsertEtsy($etsyApi, $shop, $orders['results']);

                    $next_page = (isset($orders['pagination']['next_page'])) ? $orders['pagination']['next_page'] : null;
                    $count++;
                }
            } while ($next_page);

            $this->syncSuccessReport($shop, $count);

        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
    }


    private function bulkOrderInsertEtsy(EtsyOrderAPI $etsyApi, $shop, $orders)
    {

        $last_date = null;

        foreach ($orders as $order) {
            if (DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $order['order_id']])->exists()) {
                continue;
            }

            $customer_info = $order_info = [];
            $customer_id = null;

            $currency = $order['currency_code'] ?? 'EUR';
            $total_amount = $order['grandtotal'];

            if (isset($order['buyer_email'])) {

                $billing_name = $order['name'];
                $street_shipping = $order['first_line'];
                $city_shipping = $order['city'];
                $state_shipping = $order['state'];
                $zipcode_shipping = $order['zip'];
                $country_shipping = $etsyApi->getCountryName($order['country_id']);

                $customer_info = [
                    'customer_full_name' => $billing_name,
                    'company_name' => '',
                    'email' => $order['buyer_email'],
                    'city' => $city_shipping,
                    'zip_code' => $zipcode_shipping,
                    'state' => $state_shipping,
                    'country' => $country_shipping,
                    'phone' => null,

                    'currency' => $currency,
                    'address' => $street_shipping,
                    'insert_type' => 1,
                    'user_id' => $shop->user_id,

                    // shipping
                    'shipping_name' => $billing_name,
                    'street_shipping' => $street_shipping,
                    'address_shipping' => $street_shipping,
                    'city_shipping' => $city_shipping,
                    'state_shipping' => $state_shipping,
                    'zipcode_shipping' => $zipcode_shipping,
                    'country_shipping' => $country_shipping,

                    //billing
                    'billing_name' => $billing_name,
                    'street_billing' => $street_shipping,
                    'address_billing' => $street_shipping,
                    'city_billing' => $city_shipping,
                    'state_billing' => $state_shipping,
                    'zipcode_billing' => $zipcode_shipping,
                    'country_billing' => $country_shipping,

                    'source' => $shop->channel,
                ];

                // $customer_id = $this->insert_customer($customer_info);
                $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);
            }

            // ----------------------- order ----------------------------


            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $order['order_id'];
            $order_info['order_date'] = $last_date = \Carbon\Carbon::createFromTimestamp($order['creation_tsz'])->toDateTimeString();
            $order_info['insert_type'] = 1;
            $order_info['total'] = $total_amount;
            $order_info['sub_total'] = $order['total_price'];
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $order['payment_method'];
            $order_info['currency'] = $currency;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $order_info['status'] = 'Shipped'; //$order['OrderStatus'];

            //Cart
            $order_info['cart'] = json_encode($etsyApi->getCartItems($order['receipt_id']));
            $this->insert_order($order_info);
        }

        $this->insertSyncDate($shop, $last_date);
    }


    //Sync otto order
    public function syncOttoOrder($shop)
    {
        $count = 0;
        $api_url = 'https://api.otto.market';
        if($shop->id == 388){
            $api_url = 'https://sandbox.api.otto.market';
        }

        try {

            $last_date = '2016-01-01';
            $last_sync_date = $this->syncStartDate($shop);
            if ($last_sync_date) {
                $date = strtotime($last_sync_date);
                $last_date = date("Y-m-d", strtotime("-7 days", $date));
            }

            $last_order_date = \DateTime::createFromFormat('Y-m-d', $last_date);

            $otto_order_query = http_build_query([
                'fromDate' => date_format($last_order_date, 'c'),
                'limit' => 100,
                'orderDirection' => 'ASC',
                'orderColumnType' => 'ORDER_DATE'
            ]);

            $otto_order_query = ($otto_order_query) ? '?' . $otto_order_query : '';
            $next_link = null;

            do {
                $token = $this->getOttoAccessToken($shop);

                if (is_null($token)) throw new Exception('Token can not be blank');

                //Order api url
                $order_api_url = ($next_link) ? $next_link : $api_url . '/v4/orders' . $otto_order_query;

                $client = new \GuzzleHttp\Client();
                $response = $client->request('GET', $order_api_url, [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Authorization' => "Bearer {$token}"
                    ],
                ]);

                if ($response->getStatusCode() !== 200) {
                    throw new Exception('Order response not success.');
                }

                $response_data = $response->getBody()->getContents();
                $response_arr = json_decode($response_data, true);

                //Orders arr
                $orders = isset($response_arr['resources']) ? $response_arr['resources'] : [];
                if (is_array($orders) && $orders) {
                    //Bulk insert otto orders
                    $this->bulkOrderInsertOtto($orders, $shop);
                    $count += count($orders);
                }

                //Get next url
                $links = isset($response_arr['links']) ? $response_arr['links'] : [];
                $next_link = null;

                if (is_array($links) && $links) {
                    $nexts = array_filter($links, function ($i) {
                        return $i['rel'] == 'next';
                    });

                    if ($nexts) {
                        $next_arr = reset($nexts);
                        $next_link = (is_array($next_arr) && $next_arr) ? $api_url . $next_arr['href'] : null;
                    }
                }

            } while ($next_link);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        } finally {
            OttoCustomerSync::dispatch($shop->id);
            OttoCustomerSync::dispatch($shop->id)->delay(now()->addMinutes(40));
        }
    }

    private function getOttoAccessToken(&$shop)
    {
        if(!in_array($shop->user_id, [2911, 3602, 62])){
            return $this->getOttoTokenV1((string)$shop->username, (string)$shop->password);
        }
        $currentTime = strtotime(Carbon::now('Europe/Berlin')->toDateTimeString());

        if ($shop->token_expires_at && $shop->token_expires_at > $currentTime) {
            $accessToken = $shop->password;
        }
        else {
            $accessToken = $this->getOttoTokenByInstallationId($shop->username);
            $shop->token_expires_at = strtotime(Carbon::now('Europe/Berlin')->addSeconds(1800)->toDateTimeString());
            $shop->password = $accessToken;
            $shop->save();
        }

        if (!$accessToken) {
            trace('Could not fetch token.');
        }
        return $accessToken;
    }

    private function getOttoTokenV1($username,$password)
    {
        $api_url = 'https://api.otto.market';
        $expires_in = 0;

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $api_url . '/v1/token',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query([
                'username' => $username,
                'password' => $password,
                'grant_type' => 'password',
                'client_id' => 'token-otto-api'
            ]),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));

        $auth_response = curl_exec($curl);
        $err = curl_error($curl);
        $auth_responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        if ($auth_responseCode == 401) {
            throw new Exception("Something went wrong - Credentials!");
        }
        if ($auth_responseCode != 200) {
            throw new Exception('Something went wrong - Credentials!');
        }
        $credentials = json_decode($auth_response, true);

        $token = $credentials['access_token'];

        $expires_in = $credentials['expires_in'];
        return $token;
    }
    private function getOttoTokenByInstallationId(string $installationId)
    {
        $appId = config('channel.otto_app_id');
        $appToken = $this->getOttoAppToken();

        $url = 'https://api.otto.market/v1/apps/' . $appId . '/installations/' . $installationId . '/accessToken';
        $data = http_build_query([
            'scope' => 'products orders'
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Authorization: Bearer ' . $appToken
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        $response_data = json_decode($response, true);

        return $response_data['access_token'];
    }

    private function getOttoAppToken()
    {
        $client_id = config('channel.otto_client_id');
        $client_secret = config('channel.otto_client_secret');

        $url = 'https://api.otto.market/oauth2/token';

        $data = http_build_query([
            'grant_type' => 'client_credentials',
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'scope' => 'developer'
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        $response_data = json_decode($response, true);

        return $response_data['access_token'];
    }

    public function bulkOrderInsertOtto($orders, $shop)
    {
        $l_date = null;
        foreach ($orders as $order) {

            $order_id_api = $order['orderNumber'];

            if(DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $order_id_api])->exists())
            {
                continue;
            }

            $l_date = $order['orderDate'];
            $customer_info = $order_info = $customer_id = null;

            $deliveryAddress = $order['deliveryAddress'];
            $invoiceAddress = $order['invoiceAddress'];

            //Full country name finding
            $alpha3_countries = [];
            $alpha3_countries[] = $inv_countryCode = $invoiceAddress['countryCode'] ?? null;
            $alpha3_countries[] = $delv_countryCode = $deliveryAddress['countryCode'] ?? null;
            $alpha3_countries = array_filter($alpha3_countries);

            // Empty billing address
            if(empty($inv_countryCode)) continue;

            if(!$this->canInsertOttoOrder($shop->id,$order)) continue;


            $country_arr = [];
            if ($alpha3_countries) {
                $alpha3_countries = array_unique(array_map('strtoupper', $alpha3_countries));
                $country_arr = DB::table('tax_rates')->whereIn('alpha_three', $alpha3_countries)->selectRaw('UPPER(alpha_three) as alpha, country')->pluck('country', 'alpha')->toArray();
            }

            $inv_country = isset($country_arr[$inv_countryCode]) ? $country_arr[$inv_countryCode] : $inv_countryCode;
            $delv_country = ($delv_countryCode) ? (isset($country_arr[$delv_countryCode]) ? $country_arr[$delv_countryCode] : $delv_countryCode) : $inv_country;
            //Country name finding end

            $customer_name = trim($invoiceAddress['salutation'] . ' ' . $invoiceAddress['firstName'] . ' ' . $invoiceAddress['lastName']);
            $shipping_name = trim($deliveryAddress['salutation'] . ' ' . $deliveryAddress['firstName'] . ' ' . $deliveryAddress['lastName']);
            $customer_zip = $invoiceAddress['zipCode'];

            //Generate email address
            $email_str = $customer_name . $customer_zip;
            $email_str = strtolower(preg_replace('/\s+/', '', $email_str));

            if(empty($order['invoiceAddress']))
            {
                $email_str = $order['orderNumber'];
                $inv_country = 'Germany';
                $delv_country = $delv_country ?? $inv_country;
            }
            $email = $email_str . '@drmfake.com';

            $customer_address = trim($invoiceAddress['houseNumber'] . ' ' . $invoiceAddress['street']);

            if (!empty($delv_country) && in_array(strtolower($delv_country), ['germany', 'german', 'deutschland', 'de', 'deu'])) {
                $delivery_address = trim($deliveryAddress['street'] . ' ' . $deliveryAddress['houseNumber']);
            } else {
                $delivery_address = trim($deliveryAddress['houseNumber'] . ' ' . $deliveryAddress['street']);
            }

            $customer_info = [
                'customer_full_name' => $customer_name,
                'company_name' => null,
                'email' => $email,
                'city' => $invoiceAddress['city'],
                'zip_code' => $customer_zip,
                // 'state' => $invoiceAddress['city'],
                'country' => $inv_country,
                // 'phone' => $billing->phone,
                // 'website' => ,
                'currency' => 'EUR',
                //  'default_language' => ,
                'address' => $customer_address,
                'insert_type' => 1,
                'user_id' => $shop->user_id,
                //  'vat_number' => ,

                'source' => $shop->channel,
            ];

            //Billing
            $customer_info['billing_name'] = $customer_info['customer_full_name'];
            $customer_info['billing_company'] = $customer_info['company_name'];
            $customer_info['street_billing'] = $customer_info['address'];
            $customer_info['city_billing'] = $customer_info['city'];
            $customer_info['state_billing'] = null;
            $customer_info['zipcode_billing'] = $customer_info['zip_code'];
            $customer_info['country_billing'] = $customer_info['country'];


            //Shipping
            $customer_info['shipping_name'] = (strlen($shipping_name) > 1) ? $shipping_name : $customer_info['customer_full_name'];
            $customer_info['shipping_company'] = null;
            $customer_info['street_shipping'] = (strlen($delivery_address) > 1) ? $delivery_address : $customer_info['address'];
            $customer_info['city_shipping'] = $deliveryAddress['city'] ?? $customer_info['city'];
            $customer_info['state_shipping'] = null;
            $customer_info['zipcode_shipping'] = $deliveryAddress['zip_code'] ?? $customer_info['zip_code'];
            $customer_info['country_shipping'] = $delv_country;

            $customer_info = array_filter($customer_info);
            if ($customer_info) {
                $customer_id = $this->insert_customer($customer_info);
            }

            //Initial value
            $total_item_price = 0;
            $order_status = null;
            $currency = 'EUR';
            $cost = 0;

            $carts = [];
            if (isset($order['positionItems'])) {
                $positionItems = $order['positionItems'];

                foreach ($positionItems as $positionItem) {
                    $product = $positionItem['product'];

                    $itemValueGrossPrice = $positionItem['itemValueGrossPrice'];
                    $currency = $itemValueGrossPrice['currency'];
                    $gross_price = $itemValueGrossPrice['amount'];
                    $order_status = strtolower($positionItem['fulfillmentStatus']);

                    $cart_item = [];
                    $cart_item['id'] = $product['articleNumber'];
                    $cart_item['product_name'] = preg_replace_array('/"/', [' '], $product['productTitle']);
                    $cart_item['description'] = $product['articleNumber'];
                    $cart_item['qty'] = 1;
                    $cart_item['rate'] = round($gross_price, 2);
                    $cart_item['tax'] = 0;
                    $cart_item['image'] = null;
                    $cart_item['ean'] = $product['ean'];
                    $cart_item['item_number'] = $product['sku'];
                    $cart_item['product_discount'] = 0;
                    $cart_item['amount'] = round($gross_price, 2);
                    $carts[] = $cart_item;
                    $order_info['cart'] = json_encode($carts);

                    $total_item_price += $gross_price;
                }
            }

            // Announced order
            if($order_status === 'announced')
            {
                $announced_api_id = $order['orderNumber'];
                if(!(DB::table('otto_announces')->where('shop_id', $shop->id)->where('api_id', $announced_api_id)->exists()))
                {
                    DB::table('otto_announces')->insert([
                        'shop_id' => $shop->id,
                        'api_id' => $announced_api_id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
            // Announced order end

            //Shipping fee calculation
            $initialServiceFees = $order['initialDeliveryFees'];
            if (is_array($initialServiceFees)) {
                $cost = array_sum(array_map(function ($a) {

                    if(isset($a['deliveryFeeAmount']) && isset($a['deliveryFeeAmount']['amount']))
                    {
                        return $a['deliveryFeeAmount']['amount'];
                    }
                    return 0;
                }, $initialServiceFees));
            }

            //Total price
            $total_price = ($total_item_price + $cost);

            //Order info
            $order_info['user_id'] = $shop->user_id;
            $order_info['drm_customer_id'] = $customer_id;
            $order_info['order_date'] = date('Y-m-d H:i:s', strtotime($order['orderDate']));
            $order_info['insert_type'] = 1;
            $order_info['total'] = round($total_price, 2);
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $order['orderNumber'];

            $order_info['sub_total'] = round($total_item_price, 2);
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = null;
            $order_info['currency'] = $currency;
            $order_info['shipping_cost'] = $cost;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            //Order status
            $order_info['status'] = $order_status;
            // ----------------------- Order Insert ---------------------
            $this->insert_order($order_info);
            // }
        }/* end foreach */

        $this->insertSyncDate($shop, $l_date);
    }

    public function syncKauflandOrder($shop){

        $secretKey = $shop->password;
        $clientKey = $shop->username;
        try{
            if ($shop) {
                $last_date = '2021-09-06 21:55:25';
                $l_date = $this->syncStartDate($shop);

                if ($l_date) {
                    $date = strtotime($l_date);
                    $last_date = date("Y-m-d H:i:s", strtotime("-7 days", $date));
                }
                $last_order_date = \DateTime::createFromFormat('Y-m-d H:i:s', $last_date);

                 $kaufland = new KauflandApi($secretKey, $clientKey);
                    if($kaufland->ApiMonitoring()['message'] == ""){
                        throw new Exception("Internal Server Error from Kaufland.");
                    }
                      $count = 0;
                      $offsetOrder = 0;
                        $all_orders = $kaufland->getOrder($last_order_date,$offsetOrder);

                        if (isset($all_orders['message']) == 'API Client was not found') {
                            throw new Exception('Ops! Your API Key is expired, please re-generated Client Key and Secret Key and update your Shop Setting.');
                        }else if(isset($all_orders['message']) == 'Internal Server Error'){
                            throw new Exception('Ops! Internal Server Error in Kaufland.');
                        }
                        if ($all_orders) {
                            $count += count((array)$all_orders);
                                $allitem =array();
                                $allitem_id =array();
                                 $all_final_order = array();
                                foreach($all_orders as $key=>$value) {
                                    if(in_array($value['id_order'],$allitem_id)){
                                        array_push($allitem[$value['id_order']],$value);
                                    }else {
                                        array_push($allitem_id, $value['id_order']);
                                        $allitem[$value['id_order']] = array();
                                        array_push($allitem[$value['id_order']], $value);
                                    }
                                }
                                foreach($allitem as $value) {
                                   array_push($all_final_order,$value);
                                }
                            $this->bulkOrderInsertKaufland($shop,$all_final_order);
                        }
            }else{
                throw new Exception("You have not configured any Kaufland shop yet!");
            }
         $this->syncSuccessReport($shop, $count);
        }catch (Exception $e){
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }


    }

    public function bulkOrderInsertKaufland($shop,$all_orders){

        $last_sync_date = $this->syncStartDate($shop);
        $last_date = $last_sync_date;


         foreach ($all_orders as $orders){

            //Check if this order_api_id already exist
           $order = $this->formatKauflandOrder($orders);

           if(strtolower($order['status']) == 'cancelled' || empty($order['billing_address']['first_name']) || empty($order['shipping_address']['country'])){
               continue;
           }

            if (DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $order['id_order']])->exists()) continue;
            $customer_info = $order_info = [];
            $currency = $order['currency'] ?? 'EUR';
            $state_shipping = null;

            $billing_street = $order['billing_address']['street'].' '.$order['billing_address']['house_number'];
            $billing_street = trim($billing_street);

            // if (!empty($order['shipping_address']['country']) && in_array(strtolower($order['shipping_address']['country']), ['germany', 'german', 'deutschland', 'de', 'deu'])) {
            //     $shipping_street = $order['shipping_address']['house_number'].' '.$order['shipping_address']['street'];
            // } else {
                $shipping_street = $order['shipping_address']['street'].' '.$order['shipping_address']['house_number'];
            // }
            $shipping_street = trim($shipping_street);



            $total_price = (array_sum(array_values($order['total_price'])[0]) /100) + (array_sum(array_values($order['shipping_rate'])[0]) /100);
            $tax = $order['tax'] ?? 0;
            $customer_id = null;

            $customer_info = [
                'customer_full_name' => $order['billing_address']['first_name'] .' '.$order['billing_address']['last_name'],
                'company_name' => $order['billing_address']['company_name'] ? $order['shipping_address']['company_name'] : null,
                'email' => $order['buyer'],
                'city' => $order['billing_address']['city'],
                'zip_code' => $order['billing_address']['postcode'],
                'state' => $state_shipping,
                'country' => $order['billing_address']['country'],
                'phone' => $order['billing_address']['phone'],
                'currency' => $currency,
                'address' => $billing_street,
                'insert_type' => 1,
                'user_id' => $shop->user_id,

                // shipping
                'shipping_name' => $order['shipping_address']['first_name'] .' '.$order['shipping_address']['last_name'],
                'street_shipping' => $shipping_street,
                'address_shipping' => $order['shipping_address']['street'],
                'city_shipping' => $order['shipping_address']['city'],
                'state_shipping' => $state_shipping,
                'zipcode_shipping' => $order['shipping_address']['postcode'],
                'country_shipping' => $order['shipping_address']['country'],

                //billing
                'billing_name' => $order['billing_address']['first_name'] .' '.$order['billing_address']['last_name'],
                'street_billing' => $billing_street,
                'address_billing' => $order['billing_address']['street'],
                'city_billing' => $order['billing_address']['city'],
                'state_billing' => $state_shipping,
                'zipcode_billing' => $order['billing_address']['postcode'],
                'country_billing' =>$order['billing_address']['country'],

                'source' => $shop->channel,

            ];
           $customer_id = $this->insert_customer($customer_info);


            // -----------------------Kaufland items ----------------------------
               $carts = [];
               $Items = array_values($order['item'])[0];

                if (isset($Items)) {

                    foreach ($Items  as $item) {
                        $cart_item = [];
                        $cart_item['id'] = $item['id_product'];
                        $cart_item['product_name'] =  $item['title'];
                        $cart_item['qty'] = $item['quantity'];
                        $cart_item['tax'] = $tax;
                        $cart_item['image'] = $item['main_picture'];
                        $cart_item['ean'] = $item['eans'][0];
                        $cart_item['product_discount'] = 0;
                        $cart_item['amount'] = round($item['item_price'] /100, 2);
                        $cart_item['rate'] = round($item['item_rate'] /100, 2);
                        $carts[] = $cart_item;
                        $order_info['cart'] = json_encode($carts);
                    }
                }

            // -----------------------Kaufland order ----------------------------

            $order_info['user_id'] = $shop->user_id;
            $order_info['drm_customer_id'] = $customer_id;
            $order_info['shop_id'] = ($shop->id === 1210) ? 1310 : $shop->id;
            $order_info['order_id_api'] = $order['id_order'];
            $order_info['order_date'] = $last_date = $order['ts_created_iso'];
            $order_info['insert_type'] = 1;
            $order_info['total'] = $total_price;
            $order_info['sub_total'] = $total_price;
            $order_info['shipping_cost'] = (array_sum(array_values($order['shipping_rate'])[0]) /100);
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_status'] = null;
            $order_info['currency'] = $currency;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $order_info['status'] = $order['status'];

           $this->insert_order($order_info);

        }/* end foreach */

       $this->insertSyncDate($shop, $last_date);

    }


    public function insertMissingTB($shop, $id)
    {
        if (empty($shop)) return;

        $user = $shop->username;
        $pass = $shop->password;
        $auth = 'Basic ' . base64_encode("$user:$pass");

        foreach([$id] as $order_id)
        {
            try {

                $curl = curl_init();
                curl_setopt_array($curl, array(
                  CURLOPT_URL => 'https://rest.trade-server.net/'.$shop->hnr.'/orders/'.$order_id,
                  CURLOPT_RETURNTRANSFER => true,
                  CURLOPT_ENCODING => '',
                  CURLOPT_MAXREDIRS => 10,
                  CURLOPT_TIMEOUT => 0,
                  CURLOPT_FOLLOWLOCATION => true,
                  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                  CURLOPT_CUSTOMREQUEST => 'GET',
                  CURLOPT_HTTPHEADER => array(
                    'Authorization: '.$auth,
                  ),
                ));

                $content = curl_exec($curl);
                curl_close($curl);
                if(empty($content)) continue; // Empty content

                $xml = simplexml_load_string($content, 'SimpleXMLElement', LIBXML_COMPACT);

                if( !isset($xml->ORDER) ) continue;
                $orderXml = $xml->ORDER;
                if( !isset($orderXml->ORDER_DATA) || empty($orderXml->ORDER_DATA) ) continue;

                $order = app(\App\Services\OrderSync\Tradebyte::class)->generateOrderData($orderXml);
                $this->insertCheck24OrderToDatabase($shop, $order);

                //Export
                app(\App\Services\OrderSync\Tradebyte::class)->exportOrder($auth, $shop->hnr, $order['tb_id']);


            } catch (\Exception $e) {
                dump($e);
                continue;
            }
        }
    }

    //Tradebyte order
    public function insertTradebyteOrder($shop)
    {
        $count = 0;
        try {

            if ($shop) {
                $user = $shop->username;
                $pass = $shop->password;
                $shopId = $shop->id;
                $base_url = $shop->url;
                $api_path = "api.php/v2/";
            } else {
                throw new Exception("You have not configured any shop yet!");
            }

            $auth = 'Basic ' . base64_encode("$user:$pass");

            $curl = curl_init();
            curl_setopt_array($curl, array(
              CURLOPT_URL => 'https://rest.trade-server.net/'.$shop->hnr.'/orders/',
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_ENCODING => '',
              CURLOPT_MAXREDIRS => 10,
              CURLOPT_TIMEOUT => 0,
              CURLOPT_FOLLOWLOCATION => true,
              CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
              CURLOPT_CUSTOMREQUEST => 'GET',
              CURLOPT_HTTPHEADER => array(
                'Authorization: '.$auth,
              ),
            ));

            $content = curl_exec($curl);
            curl_close($curl);

            // Empty content
            if(empty($content)) {
                $this->syncSuccessReport($shop, $count);
                return;
            }

            $xml = simplexml_load_string($content, 'SimpleXMLElement', LIBXML_COMPACT);

            if(is_iterable($xml)) {
                foreach($xml as $orderXml)
                {
                    if( !isset($orderXml->ORDER_DATA) || empty($orderXml->ORDER_DATA) ) continue;
                    $order = app(\App\Services\OrderSync\Tradebyte::class)->generateOrderData($orderXml);
                    $canInsert = app(\App\Services\OrderSync\Tradebyte::class)->canInsertOrder($shop->user_id, $orderXml);

                    // Can insert order
                    if($canInsert)
                    {
                        $this->insertCheck24OrderToDatabase($shop, $order);
                        $count++;

                        //Export
                        app(\App\Services\OrderSync\Tradebyte::class)->exportOrder($auth, $shop->hnr, $order['tb_id']);
                    }
                }
            }

            $this->syncSuccessReport($shop, $count);
        }catch (Exception $e){

            if (str_contains($e->getMessage(), 'simplexml_load_string')) {
                $this->syncSuccessReport($shop, $count);
            } else {
                $this->syncErrorReport($shop, $count, $e->getMessage());
            }
        }
    }

    private function canInsertOttoOrder($shop_id, $order)
    {
        if (in_array($shop_id, [911, 957])) {
            if (isset($order['positionItems'])) {
                $cartProduct = $order['positionItems'][0]['product'];
                return ChannelProduct::where([
                    'shop_id' => $shop_id,
                    'ean' => $cartProduct['ean'],
                    'is_connected' => true
                ])->exists();
            }
        }
        return true;
    }

    //Insert check 24 order
    public function insertCheck24Order($shop)
    {
        $count = 0;
        try {

            $adapter = new Ftp([
                'host' => 'partnerftp.shopping.check24.de', // required
                'root' => '/', // required
                'username' => $shop->username, // required
                'password' => $shop->password, // required
                'port' => 44021,
                'ssl' => true,
                'timeout' => 90,
                'utf8' => false,
                'passive' => true,
                'transferMode' => FTP_BINARY,
                'systemType' => null, // 'windows' or 'unix'
                'ignorePassiveAddress' => null, // true or false
                'timestampsOnUnixListingsEnabled' => false, // true or false
                'recurseManually' => false // true
            ]);

            $filesystem = new Filesystem($adapter);

            $dir = '/outbound';
            $allPaths = $filesystem->listContents($dir);
            $filePaths = array_filter($allPaths, function($item){
                return strtolower($item['type'] === 'file') && strpos($item['path'], '-ORDER.xml') !== false;
            });

            foreach($filePaths as $filePath)
            {
                try {

                    $content = $filesystem->read($filePath['path']);
                    $order = app(\App\Services\OrderSync\Check24::class)->generateOrderData($content);
                    $this->insertCheck24OrderToDatabase($shop, $order);
                    $count ++;

                    //Move files
                    $basename = $filePath['basename'];
                    $filesystem->write('outbound/done/'.$basename, $content);
                    $filesystem->delete($filePath['path']);

                } catch (FilesystemException $e) {
                    throw new Exception($e->getMessage());
                }
            }

            $this->syncSuccessReport($shop, $count);
        }catch (Exception $e){
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
    }

    //Insert check-24 order
    private function insertCheck24OrderToDatabase($shop, $order)
    {
        if(empty($order)) return;
        if (DB::table('new_orders')->where(['shop_id' => $shop->id, 'order_id_api' => $order['order_id']])->exists()) return;

        //Last date
        $last_date = $this->syncStartDate($shop);

        $customer_info = $order['customer'];
        $customer_info['insert_type'] = 1;
        $customer_info['user_id'] = $shop->user_id;
        $customer_info['source'] = $shop->channel;
        $customer_id = $this->insert_customer($customer_info);

        // order
        $order_info['user_id'] = $shop->user_id;
        $order_info['drm_customer_id'] = $customer_id;
        $order_info['order_date'] = $last_date = $order["order_date"];
        $order_info['insert_type'] = 1;
        $order_info['total'] = $order['total'];
        $order_info['currency'] = $order['currency'];
        $order_info['shop_id'] = $shop->id;
        $order_info['order_id_api'] = $order['order_id'];

        $order_info['sub_total'] = $order['total'];
        $order_info['discount'] = 0;
        $order_info['discount_type'] = "fixed";
        $order_info['adjustment'] = 0;
        $order_info['payment_type'] = null;

        //customer info
        $order_info['customer_info'] = customerInfoJson($customer_info);

        //billing
        $order_info['billing'] = billingInfoJson($customer_info);

        //shipping
        $order_info['shipping'] = shippingInfoJson($customer_info);

        //Carts
        $order_info['cart'] = json_encode($order['carts']);

        //Status
        $order_info['status'] = 'paid';

        // order add
        if($this->insert_order($order_info)){
            $this->insertSyncDate($shop, $last_date);
        }
    }

    //Insert order
    public function insert_order($order_info)
    {
//dump("BEGIN TR: ", DB::connection(DB::getDefaultConnection())->transactionLevel() );
        $validator = Validator::make($order_info, [
            'user_id' => 'required',
            'shop_id' => 'required',
            'order_id_api' => 'required',
            'drm_customer_id' => 'required',
        ]);
        if ($validator->fails()) {
            return null;
        }

        // $product = $order_info['product_id'];
        // $product_name = $order_info['product_name'];
        // $description = $order_info['description'];
        // $quantity = $order_info['qty'];
        // $unit = $order_info['unit'];
        // $rate = $order_info['rate'];
        // $tax = $order_info['tax'];
        // $product_discount = $order_info['product_discount'];
        // $final_price = $order_info['final_price'];
        $amount = $order_info['amount'];
        /* ----------------- End Calculation ------------------ */
        $check['cms_user_id'] = $cms_user_id = $order_info['user_id'];
        // $check['drm_customer_id']   = $order_info['drm_customer_id'];
        $check['order_date'] = date('Y-m-d H:i:s', strtotime($order_info['order_date']));
        $check['insert_type'] = $order_info['insert_type'];

        $check['order_id_api'] = $order_id_api = $order_info['order_id_api'];
        if (DB::table('new_orders')->where($check)->count() > 0) return false;

        $check['shop_id'] = $shop_id = $order_info['shop_id'];


        /* --------------- invoice number --------------------- */
        if (DB::table('new_orders')->where($check)->count() > 0) return false;

        //Extra layer of duplication check
        if (DB::table('new_orders')->where(['order_id_api' => $order_id_api, 'cms_user_id' => $cms_user_id, 'shop_id' => $shop_id])->count() > 0) {
            return false;
        }

        $order_inv = DB::table('new_orders')->where($check)->first();


        if (!($order_inv == null || $order_inv == [] || $order_inv->id == null)) {
            return false; //not need to update order
            $invoice_number = $order_inv->invoice_number;
        }


        // Invoice number generator start
        $inv_setting = DB::table('drm_invoice_setting')->where('cms_user_id', $check['cms_user_id'])->orderBy('id', 'desc')->select('start_from_1', 'start_invoice_number', 'suffix')->first();
        $start_from_1 = (bool)$inv_setting->start_from_1;
        $inv_suffix = $inv_setting->suffix;

        $last_order_item = DB::table('new_orders')->where('cms_user_id', $check['cms_user_id'])->where('invoice_number', '!=', -1)->where('credit_number', 0);

        if ($start_from_1) {
            $last_order_item->whereYear('created_at', date('Y'));
        }

        $inv1 = $last_order_item->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
        $inv2 = $inv_setting->start_invoice_number;
        $invoice_number = ($start_from_1) ? $inv1 : (($inv1 > $inv2) ? $inv1 : $inv2);


        //Extra layer of duplication check
        $inv_used_count = DB::table('new_orders')->where(['cms_user_id' => $cms_user_id, 'invoice_number' => $invoice_number])->where('credit_number', 0);
        if ($start_from_1) {
            $inv_used_count->whereYear('created_at', date('Y'));
        }
        $inv_used_count = $inv_used_count->count();

        if ($inv_used_count > 0) {
            User::find(71)->notify(new DRMNotification('DUPLICATE INV NUBMER TRY: ' . inv_number_string($invoice_number, $inv_suffix) . ' USER ID: ' . $cms_user_id . ' SHOP_ID: ' . $shop_id . ' API_ID: ' . $order_id_api, 'DUPLICATE_INV_TRY'));
            return false;
        }
        //Invoice number generator end


        /* -------------- future invoice ------------------- */
        $status = $order_info['status'] ?? "nicht_bezahlt";

        if ($order_info['invoice_date'] != "" || $order_info['invoice_date'] != null) {
            $now = new DateTime();
            $due = new DateTime($order_info['invoice_date']);

            if ($due > $now) {
                $invoice_number = -1;
            }
        }

        //Amazon paid status
        if(isset($order_info['channel']) && $order_info['channel'] === 'amazon' && $status === 'nicht_bezahlt')
        {
            $status = 'paid';
        }

        /* ------------------ insert order ----------------- */
        $row['invoice_number'] = $invoice_number;
        $row['invoice_date'] = $order_info['invoice_date'] ? date('Y-m-d H:i:s', strtotime($order_info['invoice_date'])) : null;

        $status = drmOrderLabelByGroupId($status);

        if (strpos($order_info['total'], ",")) {
            $have = [".", ","];
            $will_be = ["", "."];
            $order_info['total'] = str_replace($have, $will_be, $order_info['total']);
        }
        $row['total'] = $total = abs($order_info['total']);
        $row['sub_total'] = abs($order_info['sub_total']);
        $row['total_tax'] = abs($order_info['total_tax']);

        $row['drm_customer_id'] = $drm_customer_id = $order_info['drm_customer_id'];
        $row['vat_number'] = DB::table('new_customers')->where('id', '=', $drm_customer_id)->value('vat_number');

        $row['discount'] = abs($order_info['discount']);
        $row['discount_type'] = $order_info['discount_type'];
        $row['adjustment'] = $order_info['adjustment'];
        $row['payment_type'] = $order_info['payment_type'];

        if($status == 'paid' || !empty($row['payment_type']))
        {
            $row['payment_date'] = now();
        }

        $row['currency'] = $order_info['currency'];
        $row['shipping_cost'] = abs($order_info['shipping_cost']);
        $row['customer_info'] = $order_info['customer_info'];
        $row['billing'] = $order_info['billing'];
        $row['shipping'] = $order_info['shipping'];
        $row['client_note'] = $order_info['client_note'];
        $row['status'] = $status;
        $row['cms_client'] = $order_info['cms_client'];
        $row['payment_status'] = $order_info['payment_status'] ?? null;

        if(isset($order_info['handling_time'])){
            $row['delivery_days'] = $order_info['handling_time'];
        }

        //DT TEST ORDER
        if(isset($order_info['test_order'])) {
            $row['test_order'] = $order_info['test_order'];
            $row['test_item'] = 1;
        }

        // Offer options
        if(isset($order_info['offer_options']) && is_array($order_info['offer_options']) && !empty($order_info['offer_options']))
        {
            $row['offer_options'] = json_encode($order_info['offer_options']);
        }

        if(isset($order_info['ebay_payment_status'])) {
            $row['ebay_payment_status'] = $order_info['ebay_payment_status'];
        }

        $row['eur_total'] = $order_info['eur_total'] ?? $total;
        $row['currency_rate'] = $order_info['currency_rate'] ?? 1;

        if(isset($order_info['tax_rate'])) {
            $row['tax_rate'] = $order_info['tax_rate'];
        }

        $row['cart'] = $carts_json = $order_info['cart'];
        $row['inv_pattern'] = drm_invoice_number_format($invoice_number, $inv_suffix);

        if (DB::table('new_orders')->where(['cms_user_id' => $cms_user_id, 'order_id_api' => $order_id_api, 'total' => $total, 'drm_customer_id' => $drm_customer_id])->exists()) {
            User::find(71)->notify(new DRMNotification('DUPLICATE ORDER TRY USER ID: ' . $cms_user_id . ' SHOP_ID: ' . $shop_id . ' API_ID: ' . $order_id_api, 'DUPLICATE_ORDER_TRY'));
            return false;
        }

        // Calculate dropmatix amount
        $calculate = app(\App\Services\Order\ApiOrderCalculation::class)->calculate([
            'cms_user_id' => $check['cms_user_id'],
            'billing' => $row['billing'],
            'order_date' => $check['order_date'],
            'total' => $row['total'],
            'cart' => $row['cart'],
            'tax_rate' => $row['tax_rate'] ?? 0,
            'has_tax' => isset($order_info['tax_rate']),
            'total_tax' => $order_info['total_tax'],
            'dtNew' => $order_info['dtNew'] ?? false,
            'shipping_cost' => $order_info['shipping_cost'],
            'discount' => $row['discount'],
        ]);

        $row['dropmatix_sub_total'] = $calculate['dropmatix_sub_total'];
        $row['dropmatix_total_tax'] = $calculate['dropmatix_total_tax'];
        $row['dropmatix_discount'] = $calculate['dropmatix_discount'];
        $row['dropmatix_shipping_cost'] = $calculate['dropmatix_shipping_cost'];
        $row['dropmatix_tax_rate'] = $calculate['dropmatix_tax_rate'];
        // Calculate dropmatix amount end

        $carts = (drmIsJSON($carts_json)) ? json_decode($carts_json) : [];
        $new_carts = [];

        if ($carts) {
            foreach ($carts as $cart) {
                $cart->rate = abs($cart->rate);
                $cart->amount = abs($cart->amount);
                $cart->product_discount = abs($cart->product_discount);
                $new_carts[] = $cart;
            }
            $row['cart'] = json_encode($new_carts);
        }

        $order = NewOrder::updateOrCreate($check, $row); //Insert order
        if (empty($order)) return false;

        if (!isset($order_info['eur_total'])) {
            $this->convertNonEurToEur($order); //non EUR Convert
        }

        //Log History
        try {
            drmOrderFirstHistory($order);
        } catch (Exception $eee) {
        }

        if ($order && in_array(strtolower($order->status), ['storniert', 'canceled', 'cancelled'])) {
            $this->makeCreditNote($order);
        }

        $send_status_mail = true;
        try {
            //order insert after event
            $this->orderInsertAfertEvent($order);
        } catch (Exception $eexo) {
        }


        //Send droptienda order mail
        try {
            $o_a_pid = $order->order_id_api;
            if ($o_a_pid && str_starts_with($o_a_pid, 'drop_t')) {
                $this->insert_dt_order_misc_information($order->id, $order_info);

                if (DB::table('droptienda_order_confirmation_template')->where('cms_user_id', $check['cms_user_id'])->value('auto_mail')) {
                    app('App\Http\Controllers\AdminDrmAllOrdersController')->droptienda_send_email($order->id);
                    $send_status_mail = false;
                }
            }
        } catch (Exception $emx) {}


        //Send order mail
        try {
            // if(isLocal() || in_array($check['cms_user_id'], [212, 2592])){
                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                $channel_drm_order_mail = DB::table('drm_order_mail')
                ->where(['cms_user_id' => $check['cms_user_id'], 'channel' => $channel])
                ->first();

                if($channel_drm_order_mail){
                    if($channel_drm_order_mail->auto_mail){
                        app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id, $channel);
                        $send_status_mail = false;
                    }
                }else if(DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'])->whereNull('channel')->first()->auto_mail){
                    app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
                    $send_status_mail = false;
                }
            // }else{
            // if (DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'])->value('auto_mail')) {
            //     app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
            //     $send_status_mail = false;
            // }
            // }
        } catch (Exception $exv) {}


        //Send status mail
        if ($send_status_mail) {
            send_order_email($order->id);
        }

        try{
            if(!empty($order_info['trackings_data']))
            {
                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertOrderTrackings($order->id, $order_info['trackings_data']);
            }
        }catch (Exception $e) {}

        // dump($order, DB::connection(DB::getDefaultConnection())->transactionLevel() );

        return true;
    }

    public function insert_customer($customer_info)
    {
        $validator = Validator::make($customer_info, [
            'email' => 'required',
            'user_id' => 'required',
        ]);
        if ($validator->fails()) {
            return null;
        }

        $customer_data = [
            'full_name' => $customer_info['customer_full_name'],
            'company_name' => $customer_info['company_name'],
            'country' => drmCountryNameFull($customer_info['country']),
            'phone' => $customer_info['phone'],
            'website' => $customer_info['website'],
            'city' => $customer_info['city'],
            'zip_code' => $customer_info['zip_code'],
            'state' => $customer_info['state'],
            'currency' => $customer_info['currency'],
            'default_language' => $customer_info['default_language'],
            'address' => (isset($customer_info['street_shipping']) && !empty($customer_info['street_shipping'])) ?
                $customer_info['street_shipping'] : $customer_info['address'],
            'insert_type' => $customer_info['insert_type'],
            'source' => $customer_info['source'],
        ];

        //customer_check
        $user_id = $customer_info['user_id'];
        $customer_check = [
            'email' => $customer_info['email'],
            'user_id' => $user_id,
        ];

        //customer_data
        $customer_data = array_filter($customer_data);

        // customer add
        $customer = NewCustomer::updateOrCreate($customer_check, $customer_data);

        $customer->update([
            'billing' => updateBillingShippingAddress(billingInfoJson($customer_info), $customer->billing),
            'shipping' => updateBillingShippingAddress(shippingInfoJson($customer_info), $customer->shipping)
        ]);

        return $customer->id;
    }

    private function insert_dt_order_misc_information ($order_id, $order_info) {
        $coupon = $order_info['coupon'] ?? null;
        $discount = $order_info['discount'] ?? null;
        $discount_type = $order_info['discount_type'] ?? null;

        if (!empty($coupon)) {
            DB::table('dt_order_infos')->updateOrInsert([
                    'order_id' => $order_id,
                    'cms_user_id' => $order_info['user_id'],
                ],
                [
                    'coupon' => $coupon,
                    'discount' => $discount,
                    'discount_type' => $discount_type,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }
    }

    private function syncSuccessReport($shop, $count)
    {
        DB::table('new_order_sync_reports')->updateOrInsert([
            'shop_id' => $shop->id,
        ],
            [
                'status' => 1,
                'end_time' => Carbon::now()->unix(),
                'item' => $count
            ]);
    }

    private function syncErrorReport($shop, $count, $message)
    {
        if ($shop) {
            $error_message = ($message) ? $message : 'During the last sync. your store was not available as expected. Do not worry, we will try again soon.';
            DB::table('new_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
                [
                    'status' => 2,
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count,
                    'report' => 'Shop: ' . $shop->shop_name . ' - Error: ' . $error_message . ' Php: ' . phpversion()
                ]);

            //Send user error report
            if (($shop->channel != 4) && ($message) && ($shop->user_id != 3514)) {
                $message = 'Shop: ' . $shop->shop_name . ' - Error: ' . $message;
                $shop_user = User::find($shop->user_id);
                try {
                    if ($shop_user) $shop_user->notify(new DRMNotification($message, 'ORDER_SYNC_ERROR'));
                } catch (Exception $ee) {
                }
            }

        }
        echo 'Shop: ' . $shop->shop_name . ' - Error: ' . $message . "\n";
    }

    //get last sync time from db
    private function syncStartDate($shop)
    {
        return (DB::table('new_order_sync_histories')->select('last_date')->where(['shop_id' => $shop->id, 'drm_user_id' => $shop->user_id])->first())->last_date;
    }

    //Sync last time insert to db
    private function insertSyncDate($shop, $last_date)
    {
        if ($shop && $last_date) {
            DB::table('new_order_sync_histories')->updateOrInsert([
                'shop_id' => $shop->id,
                'drm_user_id' => $shop->user_id
            ],
                [
                    'last_date' => $last_date
                ]);
        }
    }

    //Dispatch telegram notification queue
    private function orderInsertAfertEvent($order)
    {
        // $user = User::find($order->cms_user_id);
        // if($user->telegram_user_token){
        // OrderSendToTelegram::dispatch($order->id);
        // }

        try {
            //Update customer orders
            if ($order->drm_customer_id) {
                $this->updateOrderCustomerInfo($order->drm_customer_id);
            }
        } catch (Exception $ee) {

        }

        try {
            $this->insertCustomerTag($order); //Fire customer tag insert code
        } catch (Exception $tagErr) {
            User::find(71)->notify(new DRMNotification('Customer tag insert error ' . $tagErr->getMessage(), 'TAG_NOTIFICATION_ADMIN_ERROR'));
            Log::channel('command')->info('Customer tag insertion failed! ORDER_ID: ' . $order->id);
        }

        // Auto invoice send to supplier
        if ($order->invoice_number && ($order->credit_number < 1) &&  $order->test_order != 1 ) {


            // Send push notification
            try {
                (new PushNotifySell)->send($order);
            } catch (Exception $e) {}



            //core DRM
            try {
                app('App\Http\Controllers\AdminDrmAllOrdersController')->getOrderSupplier($order->id);

                // if (DB::table('drm_supplier_mail')->where('cms_user_id', $order->cms_user_id)->value('auto_mail')) {
                //     app('App\Http\Controllers\AdminDrmAllOrdersController')->getOrderSupplier($order->id);
                // }
            } catch (Exception $exception) {}
        }

        //Droptienda product tag insert
        try {
            $is_droptienda = \App\Shop::where('id', $order->shop_id)->where('channel', 10)->exists();
            if ($is_droptienda) {
                // notificationToTelegram('Droptienda shop exists');
                $carts = json_decode($order->cart, true);
                $productEans = collect($carts)->pluck('ean')->toArray();
                // foreach ($carts as $cart) {
                //     if (array_key_exists('ean', $cart)) {
                //         $productEans[] = $cart['ean'];
                //     }
                // }

                $products = DB::table('drm_products')
                    ->where('user_id', $order->cms_user_id)
                    ->whereNotNull('tags')
                    ->whereIn('ean', $productEans)
                    ->get();
                // $log = ['ean' => $productEans, 'user_id' => $order->cms_user_id];
                // notificationToTelegram(json_encode($log));

                if ($products->isNotEmpty()) {
                    // notificationToTelegram('Product found in price tag');
                    $tag_info = array();
                    foreach ($products as $key => $product) {
                        $tags = explode(',', $product->tags);
                        $campaigns = EmailMarketing::with(['steps' => function ($q) use ($tags) {
                            foreach ($tags as $key => $tag) {
                                if ($key == 0) {
                                    $q->whereJsonContains('product_tags', $tag);
                                } else {
                                    $q->orWhereJsonContains('product_tags', $tag);
                                }
                            }
                        }])
                            ->select('id', 'calculation_percentage', 'calculation_tax', 'product_tags')
                            ->where('user_id', $order->cms_user_id)
                            ->where(function ($query) use ($tags) {
                                foreach ($tags as $key => $tag) {
                                    if ($key == 0) {
                                        $query->whereJsonContains('product_tags', $tag);
                                    } else {
                                        $query->orWhereJsonContains('product_tags', $tag);
                                    }
                                }
                            })
                            ->get();

                        foreach ($campaigns as $campaign) {
                            $price = $product->ek_price + $product->ek_price * ((float)$campaign->calculation_percentage / 100);
                            $price = $price + $price * ((float)$campaign->calculation_tax / 100); // price with tax
                            $tag_info[$product->id][] = (string)$price;
                            foreach ($campaign->steps as $step) {
                                $tag_info[$product->id][] = (string)$price;
                            }
                        }
                    }
                    // notificationToTelegram('json_encode($tag_info)');
                    // notificationToTelegram(json_encode($tag_info));
                    $this->storeProductTags($tag_info, $order->cms_user_id);
                }
            }
        } catch (Exception $exception) {
            Log::channel('command')->info('Dropfunnel Product tag insertion failed!');
            Log::channel('command')->info($exception);
            Log::channel('command')->info('Exception print end');
            // notificationToTelegram(json_encode($exception));
        }
    }

    public function storeProductTags($array, $customerId)
    {
        foreach ($array as $productId => $priceArray) {
            $duplicatePriceCount = array_count_values($priceArray);
            foreach ($duplicatePriceCount as $price => $count) {
                $startRange = floor($price / 10) * 10;
                if ($startRange < 10) {
                    $startRange = 0;
                }
                $range = range($startRange, $price + 10, 10);
                $generated_tag = '#' . $range[0] . ' - ' . $range[1] . ' EUR';

                $product_tag = ProductTag::firstOrNew(['tag' => $generated_tag]);
                $product_tag->product_id = $productId;
                $product_tag->count = ($product_tag->count ?? 0) + $count;
                $product_tag->save();

                DropfunnelCustomerTag::insertTag($generated_tag, 98, $customerId, 16);
            }
        }
    }

    //Fire telegram notification queue
    public function orderSendToTelegramAppQueue($order_id)
    {
        try {
            $order = NewOrder::find($order_id);
            //, ['id', 'credit_number', 'invoice_number', 'status', 'cms_user_id']

            if ($order) {

                $user = User::find($order->cms_user_id);
                $message = '';
                $file = '';
                $credit_number = null;
                $invoice_number = null;

                $message = 'New Order! article ' . inv_number_string($order->invoice_number, $order->inv_pattern) . ' with a value of ' . $order->total . ' sold via Shop ' . $order->shop->shop_name . '.';

                // if(in_array(strtolower($order->status), ['storniert','canceled', 'cancelled'])){
                //     //Send credit note
                //     $message='Article '.$order->invoice_number.' with a value of -'.$order->total.' is now Canceled. More Infos on Credit-Note '.$order->credit_number.'.';
                //     $file = ''; //($user->telegram_user_token)? $this->generate_credit_note_pdf_stream($order_id) : null;
                // }else{
                //     //send invoice
                //     $message='New Order! article '.$order->invoice_number.' with a value of '.$order->total.' sold via Shop '.$order->shop->shop_name.'.';
                //     $file = ''; //($user->telegram_user_token)? $this->generate_invoice_pdf_stream($order_id) : null;
                // }

                $user->notify(new DRMNotification($message, 'ORDER_SYNC'));
            }
        } catch (Exception $e) {
            User::find(71)->notify(new DRMNotification('Telegram send Notification error #ORD: ' . $order_id . ' ' . $e->getMessage(), 'TELEGRAM_NOTIFICATION_ADMIN'));
        }
    }

    //Generate credit note by passing order_id
    private function generate_credit_note_pdf_stream($order_id)
    {
        $data = [];
        $data['page_title'] = 'Credit Note';

        $order = NewOrder::find($order_id);
        $data['order'] = $order;
        $data['product_list'] = json_decode($order->cart);
        $data['customer'] = $order->customer;
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

        $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.credit_note.daily' : 'admin.credit_note.general';
        $pdf_view = ($order->insert_type == 4) ? 'admin.credit_note.charge_inv' : $pdf_view;

        $pdf_path = 'order_credit_note/credit_note' . $order->id . '.pdf';
        $pdf = PDF::loadView($pdf_view, $data)->setWarnings(false)->stream();
        Storage::disk('spaces')->put($pdf_path, $pdf, 'public');
        if (Storage::disk('spaces')->exists($pdf_path)) {
            return Storage::disk('spaces')->url($pdf_path);
        }
        return null;
    }

    //Generate invoice by passing order_id
    private function generate_invoice_pdf_stream($order_id)
    {
        $data = [];
        $data['page_title'] = 'Invoice';

        $order = NewOrder::find($order_id);
        $data['order'] = $order;
        $data['product_list'] = json_decode($order->cart);
        $data['customer'] = $order->customer;
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

        $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
        $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

        $pdf_path = 'order_invoice_new/order' . $order->id . '.pdf';
        $pdf = PDF::loadView($pdf_view, $data)->setWarnings(false)->stream();
        Storage::disk('spaces')->put($pdf_path, $pdf, 'public');
        if (Storage::disk('spaces')->exists($pdf_path)) {
            return Storage::disk('spaces')->url($pdf_path);
        }
        return null;
    }

    //Insert customer tag
    public function insertCustomerTag($order, $customer_id = null)
    {
        if (empty($order)) {
            return false;
        }

        try {

            $shop_types = [
                1 => 'Gambio',
                2 => 'Lengow',
                3 => 'Yatego',
                4 => 'Ebay',
                5 => 'AMAZON',
                6 => 'Shopify',
                7 => 'Woocommerce',
                8 => 'ClouSale',
                9 => 'Chrono24',
                10 => 'Droptienda',
                11 => 'Etsy',
                12 => 'Otto',
                13 => 'Kaufland',
                201 => 'Marketplace',
            ];

            $decodedCartTag = json_decode($order->cart, true);
            $drm_customer_id = $customer_id ?? $order->drm_customer_id;
            $cms_user_id = $order->cms_user_id;
            $shop_id = $order->shop_id;

            if (empty($drm_customer_id)) return;

            $tagsArray = [];

            //Product tag
            foreach ($decodedCartTag as $productNameasTag) {
                $tag_label = strip_tags(preg_replace_array('/"/', [' '], $productNameasTag['product_name']));
                $tagsArray[] = trim(ltrim($tag_label, '"')); // removing double quote from product name
            }

            //Channel tag
            $shop = \App\Shop::where('id', $shop_id)->select('channel', 'shop_name')->first();
            if ($shop) {
                $channel_name = (isset($shop_types[$shop->channel])) ? trim($shop_types[$shop->channel]) : null;
                if ($channel_name) {
                    $tagsArray[] = $channel_name;
                }
            }

            //Country tag
            $tagsArray[] = DB::table('new_customers')->where('id', '=', $drm_customer_id)->value('country');

            // Insert campaign mail
            $label_arr = array_filter(array_map('trim', $tagsArray));
            if (!empty($label_arr)) {
                foreach ($label_arr as $tag) {
                    DropfunnelCustomerTag::insertTag($tag, $cms_user_id, $drm_customer_id, 1);
                }
            }

            //Insert lead
            $hasOrder = NewCustomer::whereHas('orders', function($q){
                $q->where('new_orders.invoice_number', '!=', -1)
                ->where('new_orders.test_order', '!=', 1)
                ->where('new_orders.credit_number', '=', 0)
                ->whereNull('new_orders.credit_ref')
                ->where('new_orders.eur_total', '>', 0);
            })
            ->where('id', $drm_customer_id)
            ->exists();

            if($hasOrder)
            {
               DropfunnelCustomerTag::insertLeads($cms_user_id, $drm_customer_id, 2);
            }
            //Lead insert end

        } catch (Exception $e) {
            User::find(71)->notify(new DRMNotification('Customer tag insert error ' . $e->getMessage() . ' Order ID:' . $order->id, 'TAG_NOTIFICATION_ADMIN_ERROR'));
            Log::channel('command')->info('Customer tag insertion failed! ORDER_ID: ' . $order->id);
        }
    }

    //Fire campaign mail job
    public function campaignMailJob($id)
    {
        $this->sendCampaignByCampaignId($id);
    }

    public function sendCampaignMailJobAfterInserting($tag_id)
    {
        $this->sendCampaignByTagId($tag_id);
    }


    //Update existion order by Customer info
    private function updateOrderCustomerInfo($customer_id)
    {
        // TODO:: DROPMATIX
        return app(\App\Services\Order\Store\CustomerOrder::class)->updateOrderCustomerInfo($customer_id);
    }

    //Make Credit note
    private function makeCreditNote($order)
    {
        $order_id_api = 'inv_id_' . $order->id;

        $check['cms_user_id'] = $order->cms_user_id;
        $check['insert_type'] = $order->insert_type;
        $check['shop_id'] = $order->shop_id;
        $check['order_id_api'] = $order_id_api;
        $check['drm_customer_id'] = $order->drm_customer_id;

        $row['order_date'] = date('Y-m-d H:i:s');

        $row['total'] = -1 * abs($order->total);
        $row['sub_total'] = -1 * abs($order->sub_total);
        $row['total_tax'] = -1 * abs($order->total_tax);
        $row['discount'] = -1 * abs($order->discount);

        $row['discount_type'] = $order->discount_type;
        $row['adjustment'] = $order->adjustment;
        $row['payment_type'] = $order->payment_type;
        $row['currency'] = $order->currency;
        $row['shipping_cost'] = -1 * abs($order->shipping_cost);
        $row['customer_info'] = $order->customer_info;
        $row['billing'] = $order->billing;
        $row['shipping'] = $order->shipping;
        $row['client_note'] = $order->client_note;
        $row['status'] = 'Canceled';
        $row['cms_client'] = $order->cms_client;

        $row['currency_rate'] = $order->currency_rate;
        $row['eur_total'] = -1 * abs($order->eur_total);

        $row['tax_rate'] = $order->tax_rate;


        $carts = json_decode($order->cart);
        $new_carts = [];

        if ($carts) {
            foreach ($carts as $cart) {
                $cart->rate = -1 * abs($cart->rate);
                $cart->amount = -1 * abs($cart->amount);
                $cart->product_discount = -1 * abs($cart->product_discount);
                $new_carts[] = $cart;
            }
        }

        $row['cart'] = json_encode($new_carts);

        if (DB::table('new_orders')->where($check)->count() > 0) {
            return false;
        }

        if (DB::table('new_orders')->where(['order_id_api' => $order_id_api])->count() > 0) {
            return false;
        }

        //Credit number generator start
        $inv_setting = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->select('start_from_1', 'start_invoice_number', 'suffix')->first();
        $start_from_1 = (bool)$inv_setting->start_from_1;
        $inv_suffix = $inv_setting->suffix;

        $last_credit_item = DB::table('new_orders')->where('cms_user_id', $order->cms_user_id);

        if ($start_from_1) {
            $last_credit_item->whereYear('created_at', date('Y'));
        }


        $row['credit_number'] = $last_credit_item->orderByRaw('CAST(credit_number AS UNSIGNED) desc')->select('credit_number')->first()->credit_number + 1;
        $row['invoice_number'] = $order->invoice_number;
        $row['inv_pattern'] = $order->inv_pattern;
        // Credit number generate end

        //Dropmatix column
        $row['dropmatix_sub_total'] = -1 * abs($order->dropmatix_sub_total);
        $row['dropmatix_total_tax'] = -1 * abs($order->dropmatix_total_tax);
        $row['dropmatix_discount'] = -1 * abs($order->dropmatix_discount);
        $row['dropmatix_shipping_cost'] = -1 * abs($order->dropmatix_shipping_cost);
        $row['dropmatix_tax_rate'] = $order->dropmatix_tax_rate;
        //Dropmatix column - end


        $credit_order = NewOrder::updateOrCreate($check, $row);
        if ($credit_order) {
            try {
                DB::table('new_orders')->where('id', $order->id)->update(['credit_ref' => $credit_order->id, 'status' => 'credit_note_created']);
                updateOrderHistory($credit_order, 'canceled', 'Credit note created from Order ID: ' . $order->id);
                updateOrderHistory($order, $order->status, 'Credit note created successfully! Credit number: ' . $credit_order->credit_number);
                // UpdateProductRq::dispatch($carts, $order->cms_user_id, $order->shop_id);
                send_order_email($credit_order->id);
            } catch (Exception $eee) {
            }
            return true;
        }
        return false;
    }

    /*==================================================
    ============ Currency Convert ======================
    ===================================================*/
    private function convertNonEurToEur(NewOrder $order)
    {
        try {
            $access_key = '027cf82d8380ce2c435c11eaf68052d8';

            $base = 'EUR';
            $date = Carbon::parse($order->order_date)->format('Y-m-d');
            $currency = strtoupper($order->currency);
            $total = $order->total;

            $currency_rate = 0;
            $eur_total = 0;

            if ($currency === 'EUR') {
                $currency_rate = 1;
                $eur_total = $order->total;
            } else {
                // Initialize CURL:
                $ch = curl_init('http://data.fixer.io/api/' . $date . '?access_key=' . $access_key . '&base=' . $base);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                // Store the data:
                $json = curl_exec($ch);
                curl_close($ch);

                // Decode JSON response:
                $exchangeRates = json_decode($json, true);

                if ($exchangeRates['success'] && $exchangeRates['rates']) {
                    $rates = $exchangeRates['rates'];
                    if (isset($rates[$currency])) {
                        $currency_rate = removeCommaPrice($rates[$currency]);
                        $eur_total = ($total / $currency_rate);
                    } else {
                        throw new Exception($currency . " not funnd on fixer API.", 2);
                    }
                } else {
                    throw new Exception("API connection problem.", 1);
                }
            }

            //Update calculated currency value
            if ($order->update(['currency_rate' => $currency_rate, 'eur_total' => $eur_total])) {
                return true;
            } else {
                throw new Exception("Currency convert update failed.", 3);
            }

        } catch (Exception $e) {
            $err_message = "Currency Conversion Failed for Order ID: " . $order->id . " Currency: " . $order->currency . ". Error: " . $e->getMessage();
            User::find(71)->notify(new DRMNotification($err_message)); //Notify developer about error
            return false;
        }
    }
    /*==================================================
    ============ Kaufland Product fromat ======================
    ===================================================*/

    private function formatKauflandOrder($orders){
        $quantity = 1;
        $item_eans = [];
        $allitem_id =array();
        $order = [];
        foreach($orders as $key=>$value) {

            $price  = $value['price'];

            $order['currency'] = $value['currency'];

            if(in_array($value['id_order'],$allitem_id)){

                $test = (array)$value['product'];
                array_push($order['total_price'][$value['id_order']], $price);
                if(in_array($test['eans'][0], $item_eans)) {
                    $order['item'][$value['id_order']][$test['id_product']]['quantity'] += 1;
                    $order['item'][$value['id_order']][$test['id_product']]['item_price'] += $price;
                    $order['item'][$value['id_order']][$test['id_product']]['item_rate'] = $price;
                }else{
                    array_push($item_eans, $test['eans'][0]);
                    $test = array_merge($test, array('quantity'=>$quantity, 'item_price' => $price, 'item_rate' => $price));
                    $order['item'][$value['id_order']][$test['id_product']] =  $test;
                }
            } else {
                array_push($allitem_id, $value['id_order']);

                $order['id_order'] = $value['id_order'];
                $order['ts_created_iso'] = date( "Y-m-d H:i:s", strtotime($value['ts_created_iso']));
                $order['status'] = $value['status'];
                $order['tax'] = $value['vat'];
                $order['total_price'][$value['id_order']] = array();
                $order['item'][$value['id_order']] = array();
                $order['shipping_rate'][$value['id_order']] = array();
                array_push($order['total_price'][$value['id_order']], $price);
                array_push($order['shipping_rate'][$value['id_order']],$value['shipping_rate']);
                $order['buyer'] = $value['buyer']['email'];
                $order['billing_address'] = $value['billing_address'];
                $order['shipping_address'] = $value['shipping_address'];

                $test = (array)$value['product'];
                $test = array_merge($test, array('quantity'=>$quantity,'item_price' => $price, 'item_rate' => $price));
                array_push($item_eans, $test['eans'][0]);

                $order['item'][$value['id_order']][$test['id_product']] = $test;
            }
        }

        return $order;
    }



    //Create credit note
    public function makeMpCreditNoteFormOrder(NewOrder $order)
    {
        return $this->makeCreditNote($order);
    }

    // DT offer option
    private function dtServicePrice($options, $price)
    {
        if(!(is_array($options) || drmIsJSON($options)) || empty($options)) return [];

        if(!is_array($options))
        {
            $options = @json_decode($options, true) ?? [];
        }

        if(empty($options)) return [];

        $price = removeCommaFromPrice($price);
        return ['price' => $price, 'service' => implode(", ", $options)];
    }


    /**
     * ===================================================
     * ================ Qolizey Api ======================
     * ===================================================
     * Sync order from Colizey Api
     * @param shop{Collection}
     * @return success status
     * Colizey Insert Query - INSERT INTO `shops` (`id`, `created_at`, `updated_at`, `channel`, `shop_name`, `username`, `password`, `user_id`, `url`, `hnr`, `status`, `country_id`, `lang`, `shop_version`, `category`, `protected_shop`, `is_history`, `auto_transfer`, `is_suspended_dt`, `is_locked_dt`, `api_status`, `deactivated_at`) VALUES (NULL, '2020-06-01 15:57:43', NULL, '20', 'Colizey', 'colizey', '8f4526318fb54690a44010d328934f89', '212', 'https://api.colizey.fr/merchant/orders', NULL, '1', '1', 'de', '4.20', '1', NULL, '0', '0', '0', '0', '1', NULL);
     */

    public function syncColizeyOrder($shop)
    {
        $count = 0;

        try {
            $last_date = $this->syncStartDate($shop);

            $url = 'https://api.colizey.fr/merchant/orders';
            $params = [
                'headers' => [
                    'x-apikey' => $shop->password
                ],
            ];

            $query = [];

            $client = new \GuzzleHttp\Client();
            $offset = 0;

            do {

                if ($last_date) {
                    $query['from'] = Carbon::parse($last_date)->toIso8601String();
                }

                $query['offset'] = $offset;
                $query['limit'] = 25;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $orders = json_decode($response->getBody()->getContents(), true);

                if (!empty($orders)) {
                    $this->bulkOrderInsertColizey($shop, $orders);
                    $offset += count($orders);
                }

            } while ($offset < $count);

            $this->syncSuccessReport($shop, $count);
        } catch (Exception $e) {
            $this->syncErrorReport($shop, $count, $e->getMessage());
        }
    }


    //Colizy status
    private function _colizyStatus($status)
    {
        if($status == 0) return 'NEW';
        if($status == 1) return 'PAID';
        if($status == 2) return 'ACCEPTED';
        if($status == 3) return 'PROCESSED';
        if($status == 4) return 'REFUSED';
        if($status == 6) return 'CANCELED';
    }

    /**
     * Save orders to the new orders table
     * @param shop[collection], @param $all_orders[api response]
     * @return single order save report
     */
    private function bulkOrderInsertColizey($shop, $all_orders = [])  {
        foreach ($all_orders as $key => $value) {

            $billing = $value['billingAddress'];
            $shipping = $value['shippingAddress'];

            if(empty($billing))
            {
                $billing = $shipping;
            }

            $billingCountry = drmCountryNameFull($billing['countryCode']);
            $shippingCountry = $billing['countryCode'] == $shipping['countryCode'] ? $billingCountry : drmCountryNameFull($shipping['countryCode']);

            $billingFullName = $billing['firstName'] . ' ' . $billing['lastName'];

            $billingStreet = trim($billing['street3'] . ' ' . $billing['street2']. ' ' . $billing['street']);
            $shippingStreet = trim($shipping['street3'] . ' ' . $shipping['street2']. ' ' . $shipping['street']);

            $email = $this->_getEmail($billing['email'], $billingFullName);

            $currency = 'EUR';
            $customer_id = null;
            $order_info = [];

            $customer_info = $order_info = null;

            $customer_info = [
                "customer_full_name" => $billingFullName,
                "company_name" => $billing['company'],
                "currency" => $currency,
                'email' => $email,
                'address' => $billingStreet,
                'country' => $billingCountry,
                'default_language' => $value['locale'],
                'zip_code' => $billing['postcode'],
                'state' => $billing['state'],
                'insert_type' => 1,
                'phone' => $billing['phoneNumber'],
                'city' => $billing['city'],

                //shipping
                'street_shipping' => $shippingStreet,
                'city_shipping' => $shipping['city'],
                'state_shipping' => $shipping['state'],
                'zipcode_shipping' => $shipping['postcode'],
                'country_shipping' => $shippingCountry,

                //billing
                'street_billing' => $billingStreet,
                'city_billing' => $billing['city'],
                'state_billing' => $billing['state'],
                'zipcode_billing' => $billing['postcode'],
                'country_billing' => $billingCountry,

                'user_id' => $shop->user_id,
                'source' => $shop->channel,
            ];

            $customer_id = $this->insert_customer($customer_info);

            $order_info['user_id'] = $shop->user_id;
            $order_info['drm_customer_id'] = $customer_id;

            $order_info['order_date'] = $value['date'];
            $order_info['insert_type'] = 1;

            $order_info['shipping_cost'] = round($value['shippingPrice'] / 100, 2);
            $order_info['total'] = round($value['price'] / 100, 2);

            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $value['orderNumber'];

            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = null;
            $order_info['currency'] = $currency;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $order_info['status'] = $this->_colizyStatus($value['status']);

            $carts = [];

            foreach ($value['orderLines'] as $k => $item) {
                $cart_item = [];
                $cart_item['id'] = $k + 1;
                $cart_item['product_name'] = preg_replace_array('/"/', [' '], $item['productDescription']);
                $cart_item['description'] = preg_replace_array('/"/', [' '], $item['id']);
                $cart_item['qty'] = $item['original_quantity'];
                $cart_item['rate'] = round($item['itemPrice'] / 100, 2);
                $cart_item['tax'] = $item['vatRate'] ? round($item['vatRate'] / 100, 2) : 0;
                $cart_item['item_number'] = $item['sku'];
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($cart_item['rate'] * $cart_item['qty'], 2);
                $carts[] = $cart_item;
            }
            $order_info['cart'] = json_encode($carts);


            $order_info['sub_total'] = collect($carts)->sum('amount');
            // ----------------------- Order Insert ---------------------
            $this->insert_order($order_info);

        }/* foreach end */

        $this->insertSyncDate($shop, $order_info['order_date']);

    }
    /**
     * Return email if not founcd make drm dummy email
     * @param email
     * @return email
     */
    private function _getEmail($email, $name){
        if (empty($email)) {
            //Generate email address
            $email_str = $name;
            $email_str = strtolower(preg_replace('/\s+/', '', $email_str));
            $email_str = preg_replace( '/[\W]/', '', $email_str);
            $email = $email_str . '@drmdtfake.com';
            $email = \App\Helper\Encoding::toUTF8($email);
        }
        return $email;
    }

    public function syncMiraklConnectOrder($shop)
    {
        try {
            $count = 0;
            $last_date = self::syncStartDate($shop);

            // Get Mirakl Connect credentials from shop settings
            $client_id = $shop->username;
            $client_secret = $shop->password;
            $seller_company_id = $shop->seller_company_id;

            // Get access token
            $access_token = $this->getMiraklConnectAccessToken($client_id, $client_secret, $seller_company_id);

            // Fetch all orders with pagination
            $all_orders = [];
            $next_page_token = null;

            do {
                $response = $this->fetchMiraklConnectOrders($shop, $access_token, $last_date, $next_page_token);
                $orders = $response['data'] ?? [];
                $next_page_token = $response['next_page_token'] ?? null;

                if (!empty($orders)) {
                    $all_orders = array_merge($all_orders, $orders);
                }
            } while ($next_page_token !== null);

            if (!empty($all_orders)) {
                $this->bulkOrderInsertMiraklConnect($shop, $all_orders);
                self::syncSuccessReport($shop, count($all_orders));
            }

            return response()->json([
                'success' => true,
                'message' => 'Mirakl Connect orders synced successfully!'
            ]);

        } catch (Exception $e) {
            self::syncErrorReport($shop, $count, $e->getMessage());
            throw $e;
        }
    }

    private function fetchMiraklConnectOrders($shop, $access_token, $last_date = null, $page_token = null)
    {
        $url = 'https://miraklconnect.com/api/v2/orders';
        $params = [];

        if ($last_date) {
            // Convert the date to ISO 8601 format
            $date = new \DateTime($last_date);
            $params['updated_from'] = $date->format('c'); // 'c' format gives ISO 8601
        }

        if ($page_token) {
            $params['page_token'] = $page_token;
        }

        $response = Http::withToken($access_token)
            ->get($url, $params);

        if (!$response->successful()) {
            throw new Exception('Failed to fetch Mirakl Connect orders: ' . $response->body());
        }

        return $response->json();
    }

    private function getMiraklConnectAccessToken($client_id, $client_secret, $seller_company_id)
    {
        $response = Http::asForm()->post('https://auth.mirakl.net/oauth/token', [
            'grant_type' => 'client_credentials',
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'audience' => $seller_company_id
        ]);

        if (!$response->successful()) {
            throw new Exception('Failed to get Mirakl Connect access token: ' . $response->body());
        }

        return $response->json()['access_token'];
    }

    private function bulkOrderInsertMiraklConnect($shop, $orders)
    {
        foreach ($orders as $order) {
            try {
                if($order['origin']['channel_name'] == 'Decathlon'){
                    continue;
                }
                // First insert customer to get customer_id
                $customer_info = [
                    'shop_id' => $shop->id,
                    'user_id' => $shop->user_id, // Required
                    'first_name' => $order['shipping_info']['address']['first_name'] ?? '',
                    'last_name' => $order['shipping_info']['address']['last_name'] ?? '',
                    'email' => $order['shipping_info']['email'] ?? $this->_getEmail($order['shipping_info']['email'] ?? '', $order['shipping_info']['address']['first_name'] ?? ''), // Required
                    'phone' => $order['shipping_info']['address']['phone'] ?? '',
                    'address' => $order['shipping_info']['address']['street'] ?? '',
                    'city' => $order['shipping_info']['address']['city'] ?? '',
                    'postcode' => $order['shipping_info']['address']['zip_code'] ?? '',
                    'country' => $order['shipping_info']['address']['country'] ?? '',
                    'insert_type' => 1
                ];

                $customer_id = $this->insert_customer($customer_info);

                $order_info = [
                    'user_id' => $shop->user_id, // Required
                    'shop_id' => ($shop->id === 1210) ? 1310 : $shop->id, // Required
                    'order_id_api' => $order['channel_order_id'], // Required
                    'drm_customer_id' => $customer_id, // Required
                    'order_id' => $order['channel_order_id'],
                    'channel' => 'miraklconnect',
                    'order_date' => $order['created_at'],
                    'status' => $this->mapMiraklConnectStatus($order['status']),
                    'total' => $this->calculateOrderTotal($order['order_lines']),
                    'currency' => $order['order_lines'][0]['price']['currency'] ?? 'EUR',
                    'shipping_cost' => $this->calculateShippingCost($order['order_lines']),
                    'payment_method' => 'Unknown', // Not provided in the API response
                    'insert_type' => '1', // Set insert_type to 1
                    'customer_info' => json_encode([
                        'name' => trim(($order['shipping_info']['address']['first_name'] ?? '') . ' ' . ($order['shipping_info']['address']['last_name'] ?? '')),
                        'company' => $order['shipping_info']['address']['company'] ?? '',
                        'address' => $order['shipping_info']['address']['street'] ?? '',
                        'zip_code' => $order['shipping_info']['address']['zip_code'] ?? '',
                        'city' => $order['shipping_info']['address']['city'] ?? '',
                        'state' => $order['shipping_info']['address']['state'] ?? null,
                        'country' => $order['shipping_info']['address']['country'] ?? '',
                    ]),
                    'billing' => json_encode([
                        'name' => trim(($order['billing_info']['address']['first_name'] ?? '') . ' ' . ($order['billing_info']['address']['last_name'] ?? '')),
                        'company' => $order['billing_info']['address']['company'] ?? '',
                        'street' => $order['billing_info']['address']['street'] ?? '',
                        'address' => null,
                        'zip_code' => $order['billing_info']['address']['zip_code'] ?? '',
                        'city' => $order['billing_info']['address']['city'] ?? '',
                        'state' => $order['billing_info']['address']['state'] ?? null,
                        'country' => $order['billing_info']['address']['country'] ?? '',
                    ]),
                    'shipping' => json_encode([
                        'name' => trim(($order['shipping_info']['address']['first_name'] ?? '') . ' ' . ($order['shipping_info']['address']['last_name'] ?? '')),
                        'company' => $order['shipping_info']['address']['company'] ?? '',
                        'street' => $order['shipping_info']['address']['street'] ?? '',
                        'address' => null,
                        'zip_code' => $order['shipping_info']['address']['zip_code'] ?? '',
                        'city' => $order['shipping_info']['address']['city'] ?? '',
                        'state' => $order['shipping_info']['address']['state'] ?? null,
                        'country' => $order['shipping_info']['address']['country'] ?? '',
                    ]),
                    'cart' => []
                ];

                // Process order items
                $carts = [];
                foreach ($order['order_lines'] as $line) {
                    $sku = '';
                    $image_url = '';

                    // Extract SKU and image URL from custom attributes
                    foreach ($line['custom_attributes'] as $attr) {
                        switch ($attr['id']) {
                            case 'sku':
                                $sku = $attr['value'];
                                break;
                            case 'image-url':
                                $image_url = $attr['value'];
                                break;
                        }
                    }

                    $lineTotalPrice = $line['price']['amount'];
                    $quantity = $line['quantity'];
                    $unitPrice = $quantity > 0 ? $lineTotalPrice / $quantity : $lineTotalPrice;

                    $taxAmount = 0;
                    if (isset($line['taxes']) && is_array($line['taxes'])) {
                        foreach ($line['taxes'] as $tax) {
                            if (isset($tax['amount']['amount'])) {
                                $taxAmount += $tax['amount']['amount'];
                            }
                        }
                    }

                    $lineShippingCost = 0;
                    if (isset($line['total_shipping_price']['amount'])) {
                        $lineShippingCost = $line['total_shipping_price']['amount'];

                        if (isset($line['shipping_taxes']) && is_array($line['shipping_taxes'])) {
                            foreach ($line['shipping_taxes'] as $shippingTax) {
                                if (isset($shippingTax['amount']['amount'])) {
                                    $lineShippingCost += $shippingTax['amount']['amount'];
                                }
                            }
                        }
                    }

                    $cart_item = [];
                    $cart_item['id'] = $line['id'] ?? 0;
                    $cart_item['product_name'] = $line['product']['title'];
                    $cart_item['description'] = null;
                    $cart_item['item_number'] = $sku;
                    $cart_item['ean'] = $line['product']['id'];
                    $cart_item['qty'] = $quantity;
                    $cart_item['rate'] = round($unitPrice, 2);
                    $cart_item['tax'] = round($taxAmount, 2);
                    $cart_item['product_discount'] = 0;
                    $cart_item['amount'] = round($lineTotalPrice, 2);
                    $cart_item['product_id'] = $line['product']['id'];
                    $cart_item['marketplace_product_id'] = $line['id'] ?? 0;
                    $cart_item['supplier_id'] = 0;
                    $cart_item['delivery_days'] = 0;
                    $cart_item['shipping_cost'] = round($lineShippingCost, 2);
                    $cart_item['image'] = $image_url;
                    $carts[] = $cart_item;
                }

                $order_info['cart'] = json_encode($carts);

                // Insert order
                $this->insert_order($order_info);

            } catch (Exception $e) {
                Log::error('Error inserting Mirakl Connect order: ' . $e->getMessage(), [
                    'shop_id' => $shop->id,
                    'order_id' => $order['channel_order_id'] ?? 'unknown'
                ]);
                continue;
            }
        }
    }

    private function calculateOrderTotal($orderLines)
    {
        $total = 0;

        foreach ($orderLines as $line) {
            $linePrice = $line['price']['amount'];

            $lineTaxes = 0;
            if (isset($line['taxes']) && is_array($line['taxes'])) {
                foreach ($line['taxes'] as $tax) {
                    if (isset($tax['amount']['amount'])) {
                        $lineTaxes += $tax['amount']['amount'];
                    }
                }
            }

            $lineTotal = $linePrice + $lineTaxes;
            $total += $lineTotal;
        }

        return $total;
    }



    private function calculateShippingCost($orderLines)
    {
        $shippingCost = 0;

        foreach ($orderLines as $line) {
            if (isset($line['total_shipping_price']['amount'])) {
                $shippingCost += $line['total_shipping_price']['amount'];
            }

            if (isset($line['shipping_taxes']) && is_array($line['shipping_taxes'])) {
                foreach ($line['shipping_taxes'] as $shippingTax) {
                    if (isset($shippingTax['amount']['amount'])) {
                        $shippingCost += $shippingTax['amount']['amount'];
                    }
                }
            }
        }

        return $shippingCost;
    }

    private function mapMiraklConnectStatus($status)
    {
        $statusMap = [
            'WAITING_ACCEPTANCE' => 'pending',
            'WAITING_DEBIT' => 'pending',
            'WAITING_DEBIT_PAYMENT' => 'pending',
            'SHIPPING' => 'processing',
            'SHIPPED' => 'processing',
            'TO_COLLECT' => 'processing',
            'DELIVERED' => 'completed',
            'CLOSED' => 'completed',
            'REFUSED' => 'cancelled',
            'CANCELLED' => 'cancelled',
            'REFUNDED' => 'refunded'
        ];

        return $statusMap[$status] ?? 'pending';
    }


}
