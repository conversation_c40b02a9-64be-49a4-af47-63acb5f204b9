<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use App\Helper\LengowApi;
use DateTime;
use App\Helper\GambioApi;
use App\Helper\ShopifyApi;
use App\Helper\EbayApi;
use \Hkonnet\LaravelEbay\EbayServices;
use \DTS\eBaySDK\Trading\Services;
use \DTS\eBaySDK\Trading\Types;
use \DTS\eBaySDK\Trading\Enums;
use \DTS\eBaySDK\Constants;
use Ebay;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Notifications\DRMNotification;

use Illuminate\Support\Facades\Validator;
use App\User;
use App\DrmOrder;
use App\DrmCustomer;
use App\DrmOrderProduct;
use App\Models\Export\UniversalExport;
use Carbon\Carbon;

class ShopSyncController extends Controller
{

// public function getsyncAll(){
//     $shop_arr = \App\Shop::where('status', 1)->get();
//     foreach ($shop_arr as $key => $value) {
//         $this->getsync($value->id);
//     }
// }



    public function getsync($id)
    {
        $shop = \App\Shop::find($id);

        if ($shop) {
            $shoptype = $shop->channel;
            $shopname   = $shop->shop_name;
            if ($shop->url == "") return false;

	        if ($shoptype == 1) {
	            self::syncGambioorder($shop);
	        } else if ($shoptype == 2) {

	            self::syncLengowOrder($shop);
	        } else if ($shoptype == 3) {

	            self::syncYategoOrder($shop);
	        } else if ($shoptype == 4) {

				self::syncEbayOrder($shop);
	        }else if ($shoptype == 6) {

	            self::syncShopifyOrder($shop);
	        }
        }
    }

    public function syncGambioorder($shop, $page = 1, &$count = 0)
    {
        try{
	        if ($shop) {
	            $user = $shop->username;
	            $pass = $shop->password;
	            $shopId = $shop->id;
	            $base_url = $shop->url;
	            $api_path = "api.php/v2/";
	        } else {
	            throw new \Exception("You have not configured any shop yet!");
	        }

	        $auth = base64_encode("$user:$pass");

	        $per_page = 50;
	        $url  = $base_url . $api_path . "orders/?page=$page&per_page=$per_page";

	        $headers = array("Authorization: Basic " . $auth);

	        $ch = curl_init();
	        curl_setopt($ch, CURLOPT_URL, $url);
	        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
	        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");

	        $response = curl_exec($ch);
	        $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	        $errNo = curl_errno($ch);
	        $errStr = curl_error($ch);
	        curl_close($ch);

	        if ($responseCode != 200) {
	            throw new \Exception("Shop connection problem!");
	        }

	        $allOrder = json_decode($response);
	        $cartsInfo = [];

	        foreach ((array) $allOrder as $indexv => $value) {
	            $url  = $base_url . $api_path . "orders/$value->id/items";
	            $headers = array("Authorization: Basic " . $auth);

	            $ch = curl_init();
	            curl_setopt($ch, CURLOPT_URL, $url);
	            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
	            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");

	            $response = curl_exec($ch);
	            $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	            $errNo = curl_errno($ch);
	            $errStr = curl_error($ch);
	            curl_close($ch);
	            $cartsInfo[$value->id] = json_decode($response);
	        }

	        $this->bulkOrderInsertGambio($allOrder, $shop, $cartsInfo);

	        if (count((array) $allOrder) != 0) {
	            $page++;
	            $count += count((array) $allOrder);
	            $this->syncGambioorder($shop, $page, $count);
	        }

    		$this->syncSuccessReport($shop, $count);

	    }  catch (\Exception $e) {
	    	$this->syncErrorReport($shop, $count, $e->getMessage());
	    }
    }

    public function bulkOrderInsertGambio($allOrder, $shop, $cartsInfo)
    {
        $shopId = $shop->id;
        $shop_name = $shop->shop_name;
        $userId = $userId   = $shop->user_id;

        $sync_date = $this->syncStartDate($shop);
        $l_date = $sync_date;
        if (count((array) $allOrder) > 0) {

            foreach ($allOrder as $key => $value) {
                $customer_id = null;
                $order_info = [];

                $new = new DateTime($value->purchaseDate);
                $old = new DateTime($sync_date);

                if ($sync_date != null) {
                    if ($old >= $new) {
                        continue;
                    }
                }
                $l_date = $value->purchaseDate;

		  //       $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->id)->first();
				// if($exist_shop_id) continue;
                // ---------- customer insert -------------

                list($total_sum, $currency) = explode(" ", $value->totalSum);

                $country = $value->deliveryAddress->country ?? $value->billingAddress->country ??  $value->deliveryAddress->countryIsoCode ?? $value->billingAddress->countryIsoCode;

                $customer_info = $order_info = null;

                $customer_info = [
                    "customer_full_name" => $value->customerName ?? $value->deliveryAddress->firstName . ' ' . $value->deliveryAddress->lastName,
                    "company_name" =>  $value->deliveryAddress->company ?? $value->billingAddress->company,
                    "currency" => $currency,
                    'email' => $value->customerEmail,
                    'address' =>  $value->deliveryAddress->additionalAddressInfo ?? $value->billingAddress->additionalAddressInfo,
                    'country' => $country,
                    'default_language' => $value->deliveryAddress->countryIsoCode ?? $value->billingAddress->countryIsoCode,
                    'zip_code' => $value->deliveryAddress->postcode ?? $value->billingAddress->postcode,
                    'state' => $value->deliveryAddress->state ??  $value->billingAddress->state,
                    'insert_type' => 'API',

                    //shipping
                    'street_shipping' => $value->deliveryAddress->street . ' ' . $value->deliveryAddress->houseNumber,
                    'city_shipping' => $value->deliveryAddress->city,
                    'state_shipping' => $value->deliveryAddress->state,
                    'zipcode_shipping' => $value->deliveryAddress->postcode,
                    'country_shipping' => $value->deliveryAddress->country ?? $value->billingAddress->countryIsoCode,

                    //billing
                    'street_billing' => $value->billingAddress->street . ' ' . $value->billingAddress->houseNumber,
                    'city_billing' => $value->billingAddress->city,
                    'state_billing' => $value->billingAddress->state,
                    'zipcode_billing' => $value->billingAddress->postcode,
                    'country_billing' => $value->billingAddress->country ?? $value->billingAddress->countryIsoCode,

                    'user_id' => $shop->user_id,
                ];

                $customer_id = $this->insert_customer($customer_info);

                $order_info['user_id'] = $shop->user_id;
                $order_info['drm_customer_id'] = $customer_id;
                // $date=date_create("2013-03-15");
                $order_info['order_date'] =  $value->purchaseDate;
                $order_info['insert_type'] = "API";
                $order_info['total'] = $total_sum;
                $order_info['shop_id'] = $shop->id;
                $order_info['order_id_api'] = $value->id;
                // $order_info['shipping'] =

                $order_info['sub_total'] = $total_sum;
                $order_info['discount'] = 0;
                $order_info['discount_type'] = "fixed";
                $order_info['adjustment'] = 0;
                $order_info['payment_type'] = $value->shippingType->module;
                $order_info['currency'] = $currency;

                //customer info
                $order_info['customer_info'] = customerInfoJson($customer_info);

                //billing
                $order_info['billing'] = billingInfoJson($customer_info);

                //shipping
                $order_info['shipping'] = shippingInfoJson($customer_info);

                $order_info['status'] = $value->statusName;
                $order_info['cart'] = json_encode($cartsInfo[$value->id]);

                foreach ((array) $cartsInfo[$value->id] as $item) {

                    $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->name);
                    $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item->model);
                    $order_info['qty'][] = $item->quantity;
                    $order_info['rate'][] = $item->price;
                    $order_info['unit'][] = $item->quantityUnitName;
                    $order_info['tax'][] = $item->tax;
                    $order_info['product_discount'][] = $item->discount ?? 0;
                    $order_info['amount'][] = $item->finalPrice;
                }

                // ----------------------- Order Insert ---------------------
                $this->insert_order($order_info);
            }/* foreach end */

            $this->insertSyncDate($shop, $l_date);
        }
    }

    public function syncLengowOrder($shop, $page = 1, &$count = 0)
    {
    	try{
	        if (!$shop) {
	            throw new \Exception("You have not configured any Lengow shop yet!");
	        }

	        $access_token = $shop->username;
	        $secret   = $shop->password;
	        $lengow = new LengowApi($access_token, $secret);

	        if ($lengow->token == "") {
	            throw new \Exception("$shop->shop_name shop token problem!");
	        }

	        $all = $lengow->getOrder($page);

	        if ($all) {
		        $allorder = $all->results;

		        $this->bulkOrderInsertLengow($allorder, $shop);

		        if ($all->next) {
		            $page++;
		            $count += count($allorder);
		            $this->syncLengowOrder($shop, $page, $count);
		        }
	        }

    		$this->syncSuccessReport($shop, $count);

	    }  catch (\Exception $e) {
	        $this->syncErrorReport($shop, $count, $e->getMessage());
	    }
    }

    public function bulkOrderInsertLengow($allorder, $shop)
    {
        $shopId = $shop->id;
        $shop_name = $shop->shop_name;
        $userId   = $shop->user_id;
        $base_url = $shop->url;

        $sync_date = $this->syncStartDate($shop);

        // $sync_count = 0;


        $l_date = $sync_date;
        foreach ((object) $allorder as $key => $value) {

            // $this->stat['order'] ++;

            $name = $value->billing_address->full_name . $value->billing_address->first_name . $value->billing_address->last_name . $value->packages[0]->delivery->city;

            if (strtolower(substr($name, 0, 3)) == 'xxx') {
                // $this->stat['details'][] =$name;

                continue;
            }

            $new = new DateTime($value->marketplace_order_date);
            $old = new DateTime($sync_date);

            if ($sync_date != null) {
                if ($old >= $new) {
                    continue;
                }
            }

	  //       $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->marketplace_order_id)->first();
			// if($exist_shop_id) continue;


            $l_date = $value->marketplace_order_date;

            $customer_info = $order_info = null;

            $customer_info = [
                'customer_full_name' => $value->billing_address->full_name ?? $value->billing_address->first_name . " " . $value->billing_address->last_name,
                'company_name' => $value->packages[0]->delivery->company ?? $value->billing_address->company,
                'email' =>  $value->billing_address->email,
                'city' => $value->packages[0]->delivery->city ?? $value->billing_address->city,
                'zip_code' => $value->packages[0]->delivery->zipcode ?? $value->billing_address->zipcode,
                'state' =>  $value->packages[0]->delivery->state_region ?? $value->billing_address->state_region,
                'country' => $value->packages[0]->delivery->common_country_iso_a2 ?? $value->billing_address->common_country_iso_a2,
                'phone' =>  $value->packages[0]->delivery->phone_mobile ?? $value->billing_address->phone_mobile,
                // 'website' => ,
                'currency' => $value->original_currency->iso_a3,
                // 	'default_language' => ,
                'address' => $value->contact_address ?? $value->billing_address->full_address ?? $value->packages[0]->delivery->full_address,
                'insert_type' => "API",
                'user_id' => $shop->user_id,
                // 	'vat_number' => ,

                // shipping
                'street_shipping' => $value->packages[0]->delivery->first_line,
                'city_shipping' => $value->packages[0]->delivery->city,
                'state_shipping' => $value->packages[0]->delivery->state_region,
                'zipcode_shipping' => $value->packages[0]->delivery->zipcode,
                'country_shipping' => $value->packages[0]->delivery->common_country_iso_a2,

                //billing
                'street_billing' => $value->billing_address->first_line,
                'city_billing' => $value->billing_address->city,
                'state_billing' => $value->billing_address->state_region,
                'zipcode_billing' => $value->billing_address->zipcode,
                'country_billing' =>  $value->billing_address->common_country_iso_a2,

            ];

            if (isset($value->billing_address)) {
                // insert customer
                $customer_id = $this->insert_customer($customer_info);
            }
            // dd($customer_id);


            // ----------------------- order ----------------------------

            $order_info['user_id'] = $shop->user_id;
            $order_info['drm_customer_id'] = $customer_id;
            // $date=date_create("2013-03-15");
            $order_info['order_date'] =  $value->marketplace_order_date;
            $order_info['insert_type'] = "API";
            $order_info['total'] = $value->total_order;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $value->marketplace_order_id;

            $order_info['sub_total'] = $value->original_total_order;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $value->payments[0]->type;
            $order_info['currency'] = $value->original_currency->iso_a3;
            $order_info['shipping_cost'] = $value->shipping;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            // $order_info['client_note'];
            $order_info['status'] = $value->lengow_status;
            $order_info['cart'] = json_encode($value->packages);

            foreach ((array) $value->packages[0]->cart as $j => $item) {
                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->title);
                $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item->category);
                $order_info['qty'][] = $item->quantity;
                $order_info['rate'][] = $item->amount;
                $order_info['tax'][] = $item->tax;
                $order_info['image'][] = $item->url_image;
                $order_info['product_discount'][] = $item->discount ?? 0;
                $order_info['amount'][] = $item->amount;
            }


            // ----------------------- Order Insert ---------------------
            $this->insert_order($order_info);
        }/* end foreach */

        $this->insertSyncDate($shop, $l_date);
    }


    function syncYategoOrder($shop)
    {
    	$count = 0;
        try{
	        if (!$shop) {
	        	throw new \Exception("You have not configured any Yatego shop yet!");
	        }
	        $order_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_order";
	        // $csv = array_map('str_getcsv', file($url));
	        // dd(file($url));

	        $order_content = @file_get_contents($order_url);
	        if (!$order_content) {
	            throw new \Exception("Can not access url or no order found!");
	        }
	        // dd($content);

	        $putted_orders = @file_put_contents(storage_path() . "/tempOrder.csv", $order_content);
	        if (!$putted_orders) {
	            throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrder.csv");
	        }
	        // dd(realpath('storage/tempOrder.csv'));
	        // dd($csv);
	        $l_date = null;

	        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
	        $reader->setInputEncoding('UTF-8');
	        $reader->setDelimiter(';');
	        $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrder.csv"));

	        // dd($spreadsheet->getActiveSheet()->toArray());
	        $order_arr = $spreadsheet->getActiveSheet()->toArray();
	        $order_columns = $order_arr[0];
	        unset($order_arr[0]);
	        // dd($order_arr);

	        if (count($order_arr)) {

		        $order_id_from = $order_arr[1][1];
		        $order_id_to = $order_arr[count($order_arr)][1];
		        // dd($order_id_from,$order_id_to);

		        $order_product_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_products&von=" . $order_id_from . "&bis=" . $order_id_to . "&varids=1";

		        $product_content = @file_get_contents($order_product_url);
		        // dd($content);

		        if (!$product_content) {
		            throw new \Exception('Can not access Product url. Please contact admin. ');
		        }

		        $putted = @file_put_contents(storage_path() . "/tempOrderProduct.csv", $product_content);
		        if (!$putted) {
		            throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderProduct.csv");
		        }

		        // dd(realpath('storage/tempOrder.csv'));
		        // dd($csv);

		        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
		        $reader->setInputEncoding('UTF-8');
		        $reader->setDelimiter(';');
		        $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderProduct.csv"));

		        // dd($spreadsheet->getActiveSheet()->toArray());
		        $product_arr = $spreadsheet->getActiveSheet()->toArray();
		        // dd($product_arr);
		        $product_columns = $product_arr[0];
		        unset($product_arr[0]);
		        // dd($product_columns, $product_arr);
		        $product_arr_new = [];
		        foreach ($product_arr as $item) {
		            $product = @array_combine($product_columns, $item);

		            if (!$product) {
		                throw new \Exception('Error in product details. Please contact admin.');
		            }

		            $product_arr_new[] = $product;
		        }

		        // $product_arr_new[] = $product;

		        $product_collection = collect($product_arr_new);

		        // dd($product_collection);

		        foreach ($order_arr as $item) {

		            $order = @array_combine($order_columns, $item);
		            // $order_arr = @array_combine($columns,$product_arr[2]);

		            if ($order) {
		                // dd();
		                $products = $product_collection->where('Bestellnummer', $order['Bestellnummer'])->toArray();

		                if(count($products)){
			                $l_date = $this->orderInsertYatego($shop, $order, $products);
			                $count += count($products);
		                }

		            } else {
		                throw new \Exception('Shop Setting Changed. Please contact admin.');
		            }
		        }

	        }

    		$this->syncSuccessReport($shop, $count);

	    }  catch (\Exception $e) {
	        $this->syncErrorReport($shop, $count, $e->getMessage());
	    }
        //  dd($shop);

    }

    public function orderInsertYatego($shop, $order, $products)
    {
		$sync_date = $this->syncStartDate($shop);

        // customer
        $new = new DateTime($order['Bestelldatum']);
        $old = new DateTime($sync_date);

        if ($sync_date != null && $old >= $new) {
            return;
        }

  //       $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $order['Order_ID'])->first();
		// if($exist_shop_id) return;

        $l_date = $order['Bestelldatum'];

        $customer_info = $order_info = null;

        $customer_info = [
            'customer_full_name' => $order["R_Vorname"] . " " . $order["R_Nachname"],
            'company_name' => $order["R_Firma"],
            'email' =>  $order["E-Mail-Adresse"],
            'city' => $order["R_Stadt"],
            'zip_code' => $order["R_PLZ"],
            // 'state' =>  $order["R_Stadt"] ,
            'country' => $order["R_Land"],
            'phone' =>  $order["R_Telefon"],
            // 'website' => ,
            // 'currency' => ,
            // 	'default_language' => ,
            'insert_type' => "API",
            'user_id' => $shop->user_id,
            // 	'vat_number' => ,

            // shipping
            'street_shipping' => $order["L_Strasse"],
            'city_shipping' => $order["L_Stadt"],
            // 'state_shipping' => ,
            'zipcode_shipping' => $order["L_PLZ"],
            'country_shipping' => $order["L_Land"],

            //billing
            'street_billing' =>  $order["R_Strasse"],
            'city_billing' => $order["R_Stadt"],
            // 'state_billing' =>  ,
            'zipcode_billing' =>  $order["R_PLZ"],
            'country_billing' =>   $order["R_Land"],
        ];

        $customer_id = $this->insert_customer($customer_info);

        // order
        $order_info['user_id'] = $shop->user_id;
        $order_info['drm_customer_id'] = $customer_id;
        $order_info['order_date'] = $order["Bestelldatum"];
        $order_info['insert_type'] = "API";
        $order_info['total'] = $order['Gesamtumsatz'];
        $order_info['shop_id'] = $shop->id;
        $order_info['order_id_api'] = $order['Order_ID'];

        $order_info['sub_total'] = $order['Gesamtumsatz'];
        $order_info['discount'] = $order['Bestellwertrabatt'];
        $order_info['discount_type'] = "fixed";
        $order_info['adjustment'] = 0;
        $order_info['payment_type'] = $order['Zahlart'];
        // $order_info['currency'] = $order['zzzz'];

        //customer info
        $order_info['customer_info'] = customerInfoJson($customer_info);

        //billing
        $order_info['billing'] = billingInfoJson($customer_info);

        //shipping
        $order_info['shipping'] = shippingInfoJson($customer_info);

        $order_info['cart'] = json_encode($products);

        foreach ((object) $products as $item) {
            $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item['Produktname']);
            $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item['Artikelnummer']);
            $order_info['qty'][] = $item['Anzahl'];
            $order_info['rate'][] = $item['Einzelpreis'];
            $order_info['tax'][] = $item['Steuer'];
            $order_info['product_discount'][] = $item['Mengenrabatt'] ?? 0;
            $order_info['amount'][] = $item['Gesamtpreis'];
        }

        // order add
        $this->insert_order($order_info);

        $this->insertSyncDate($shop, $l_date);
    }


    /* Ebay Orders	*/

    public function syncEbayOrder($shop)
    {
    	$count = 0;
    	try{
    		if($shop){
	    		$ebay_service = new EbayServices();
				$service = $ebay_service->createTrading();
				$request = new Types\GetOrdersRequestType();
				$request->RequesterCredentials = new Types\CustomSecurityHeaderType();
				$request->RequesterCredentials->eBayAuthToken = $shop->password;
				$request->CreateTimeFrom = \DateTime::createFromFormat('Y-m-d H:i:s', '2019-02-01 18:33:00');
				$request->CreateTimeTo = \DateTime::createFromFormat('Y-m-d H:i:s', now());
				$request->OrderStatus = 'All';
				$response = $service->getOrders($request);

				if(json_decode($response,true)['Ack'] == 'Success'){
					$response = (object)(json_decode($response,true)['OrderArray']);
					$all_orders = $response->Order;

					if($all_orders){
						$count += count($all_orders);
						$this->bulkOrderInsertEbay($shop,$all_orders);
					}
				}else{
					throw new \Exception('Authentication error!');
				}
    		}else{
    			throw new \Exception('You have not configured any Ebay shop!');
    		}

    		$this->syncSuccessReport($shop, $count);

	    }  catch (\Exception $e) {
	        $this->syncErrorReport($shop, $count, $e->getMessage());
	    }

    }

    public function bulkOrderInsertEbay($shop,$all_orders){

    	$last_sync_date = $this->syncStartDate($shop);

        $last_date = $last_sync_date;

        foreach ($all_orders as $order) {
        	$new = new DateTime($order['CreatedTime']);
        	$old = new DateTime($last_sync_date);

            if ($last_sync_date != null && $old >= $new) {
                continue;
            }

   //          $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $order['OrderID'])->first();
			// if($exist_shop_id) continue;

            $customer_info = $order_info = null;

            $customer_info = [
                'customer_full_name' => $order['TransactionArray']['Transaction'][0]['Buyer']['UserFirstName'] . ' ' . $order['TransactionArray']['Transaction'][0]['Buyer']['UserLastName'] ,
                'company_name' => '',
                'email' =>  $order['TransactionArray']['Transaction'][0]['Buyer']['Email'],
                'city' => $order['ShippingAddress']['CityName'],
                'zip_code' => $order['ShippingAddress']['PostalCode'],
                'state' => $order['ShippingAddress']['StateOrProvince'],
                'country' => $order['ShippingAddress']['CountryName'],
                'phone' =>  $order['ShippingAddress']['Phone'],

                'currency' => $order['AmountPaid']['currencyID'],
                'address' => '',
                'insert_type' => "API",
                'user_id' => $shop->user_id,

                // shipping
                'street_shipping' => $order['ShippingAddress']['Street1'] ?? $order['ShippingAddress']['Street2'],
                'city_shipping' => $order['ShippingAddress']['CityName'],
                'state_shipping' => $order['ShippingAddress']['StateOrProvince'],
                'zipcode_shipping' => $order['ShippingAddress']['PostalCode'],
                'country_shipping' => $order['ShippingAddress']['CountryName'],

                //billing
                'street_billing' => $order['ShippingAddress']['Street1'] ?? $order['ShippingAddress']['Street2'],
                'city_billing' => $order['ShippingAddress']['CityName'],
                'state_billing' => $order['ShippingAddress']['StateOrProvince'],
                'zipcode_billing' => $order['ShippingAddress']['PostalCode'],
                'country_billing' =>  $order['ShippingAddress']['CountryName'],
            ];

            if (isset($order['TransactionArray']['Transaction'][0]['Buyer'])) {
                $customer_id = $this->insert_customer($customer_info);
            }

            // ----------------------- order ----------------------------


            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] =  $order['OrderID'];
            $order_info['order_date'] =  $order['CreatedTime'];
            $last_date = $order_info['order_date'];
            $order_info['insert_type'] = "API";
            $order_info['total'] =  $order['Total']['value'];
            $order_info['sub_total'] = $order['Subtotal']['value'];
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $order['CheckoutStatus']['PaymentMethod'];
            $order_info['currency'] = $order['Total']['currencyID'];

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $order_info['status'] = $order['OrderStatus'];
            $order_info['cart'] = json_encode($order['TransactionArray']['Transaction']);

            foreach ($order['TransactionArray']['Transaction'] as $item) {
                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item['Item']['Title']);
                $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item['Item']['Title']);
                $order_info['qty'][] = $item['QuantityPurchased'];
                $order_info['rate'][] = $item['TransactionPrice']['value'];
                $order_info['tax'][] = $item['Taxes']['TotalTaxAmount']['value'];
                $order_info['product_discount'][] = 0;
                $order_info['amount'][] = $item['QuantityPurchased'] * $item['TransactionPrice']['value'];
            }

            $this->insert_order($order_info);

        }

        $this->insertSyncDate($shop, $last_date);

    }


    // Shopify Orders
    public function syncShopifyOrder($shop)
    {
    	$count = 0;
    	try{
    		if($shop){
		        $url = $shop->url . 'admin/api/2020-01/orders.json?status=any&fulfillment_status=any';
		        // $url = $shop_details->url . 'admin/api/2020-01/draft_orders.json';
		        $client = new \GuzzleHttp\Client();
		        $response = $client->request('GET', $url, [
		            'auth' => [$shop->username, $shop->password]
		        ]);

		        if($response->getStatusCode() !== 200){
		        	throw new \Exception('Connection problem!');
		        }

				$data = $response->getBody()->getContents();
	        	$all_orders = (json_decode($data))->orders;

		        if($all_orders){
			        $count += count((array) $all_orders);
			        $this->bulkorderInsertShopify($shop, $all_orders);
		        }
	    	}else{
	    		throw new \Exception('You have not configured shop!');
	    	}

    		$this->syncSuccessReport($shop, $count);

	    }  catch (\Exception $e) {
	        $this->syncErrorReport($shop, $count, $e->getMessage());
	    }
    }

    public function bulkorderInsertShopify($shop, $all_orders)
    {
        $sync_date = $this->syncStartDate($shop);

        $l_date = $sync_date;
        // dd($all_orders);
        foreach ((object) $all_orders as $value) {
            // dd($value);
            // dd($value->billing_address->city);

   //          $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->id)->first();
			// if($exist_shop_id) continue;

            $new = new DateTime($value->created_at);
            $old = new DateTime($sync_date);

            if ($sync_date != null) {
                if ($old >= $new) {
                    continue;
                }
            }

            $customer_info = $order_info = null;
            $customer_info = [
                'customer_full_name' => $value->customer->default_address->name,
                'company_name' => $value->customer->default_address->company,
                'email' =>  $value->customer->email,
                'city' => $value->customer->default_address->city,
                'zip_code' => $value->customer->default_address->zip,
                'state' =>  $value->customer->default_address->province,
                'country' => $value->customer->default_address->country_name,
                'phone' =>  $value->customer->default_address->phone,
                // 'website' => ,
                'currency' => $value->customer->currency,
                // 	'default_language' => ,
                'address' => $value->customer->default_address->address1,
                'insert_type' => "API",
                'user_id' => $shop->user_id,
                // 	'vat_number' => ,

                // shipping
                'street_shipping' => $value->shipping_address->address1,
                'city_shipping' => $value->shipping_address->city,
                'state_shipping' => $value->shipping_address->province,
                'zipcode_shipping' => $value->shipping_address->zip,
                'country_shipping' => $value->shipping_address->country,

                //billing
                'street_billing' => $value->billing_address->address1,
                'city_billing' => $value->billing_address->city,
                'state_billing' => $value->billing_address->province,
                'zipcode_billing' => $value->billing_address->zip,
                'country_billing' =>  $value->billing_address->country,

            ];



            if (isset($value->customer)) {
                // insert customer
                $customer_id = $this->insert_customer($customer_info);
            }

            // ----------------------- order ----------------------------

            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            // $date=date_create("2013-03-15");
            $order_info['order_date'] =  $value->created_at;

            $l_date = $order_info['order_date'];

            $order_info['insert_type'] = "API";
            $order_info['total'] = $value->total_price;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $value->id;

            $order_info['sub_total'] = $value->subtotal_price;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $value->gateway;
            $order_info['currency'] = $value->currency;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);

            //billing
            $order_info['billing'] = billingInfoJson($customer_info);

            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            // $order_info['client_note'];
            $order_info['status'] = $value->financial_status;
            $order_info['cart'] = json_encode($value->line_items);

            foreach ((object) $value->line_items as $item) {

                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->name);
                $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item->title);
                $order_info['qty'][] = $item->quantity;
                $order_info['rate'][] = $item->price;
                $order_info['tax'][] = $item->tax;
                $order_info['product_discount'][] = $item->total_discount ?? 0;
                $order_info['amount'][] = $item->quantity * $item->price;
            }

            // ----------------------- Order Insert ---------------------
            $this->insert_order($order_info);
        }

        $this->insertSyncDate($shop, $l_date);
    }

    public function insert_order($order_info)
    {

        $validator = Validator::make($order_info, [
            'user_id' => 'required',
            'shop_id'   => 'required',
            'order_id_api'   => 'required',
        ]);
        if ($validator->fails()) {
            return null;
        }

        $product = $order_info['product_id'];
        $product_name = $order_info['product_name'];
        $description = $order_info['description'];
        $quantity = $order_info['qty'];
        $unit = $order_info['unit'];
        $rate = $order_info['rate'];
        $tax = $order_info['tax'];
        $product_discount = $order_info['product_discount'];
        // $final_price = $order_info['final_price'];
        $amount = $order_info['amount'];
        /* ----------------- End Calculation ------------------ */
        $check['cms_user_id']       = $order_info['user_id'];
        // $check['drm_customer_id']   = $order_info['drm_customer_id'];
        $check['order_date']        = $order_info['order_date'];
        $check['insert_type']       = 'API';
        $check['shop_id']           = $order_info['shop_id'];
        $check['order_id_api']      = $order_info['order_id_api'];

        /* --------------- invoice number --------------------- */
        if(DB::table('drm_orders_new')->where($check)->count() >1) return false;

        $order_inv = DB::table('drm_orders_new')->where($check)->first();


        if( ! ($order_inv == null || $order_inv ==[] || $order_inv->id ==null) )
        {
            $invoice_number = $order_inv->invoice_number;
        }
        else
        {
            $inv1 = DB::table('drm_orders_new')->where('cms_user_id',$check['cms_user_id'])->where('invoice_number','!=',-1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
            $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id',$check['cms_user_id'])->orderBy('id', 'desc')->first()->start_invoice_number ;
            $invoice_number = ($inv1 > $inv2 )? $inv1 : $inv2;
        }

        /* -------------- future invoice ------------------- */
        $status = "Shipped";

        if($order_info['invoice_date'] != "" || $order_info['invoice_date'] != null)
        {
            $now = new DateTime();
            $due = new DateTime($order_info['invoice_date']);

            if( $due > $now )
            {
                $status = "In Progress";
                $invoice_number = -1;
            }
        }

        /* ------------------ insert order ----------------- */
        $row['invoice_number']  = $invoice_number;
        $row['invoice_date']    = $order_info['invoice_date'];

        if(strpos($order_info['total'],"," ))
        {
            $have = [".", ","];
            $will_be   = ["", "."];
            $order_info['total'] = str_replace($have, $will_be, $order_info['total']);
        }
        $row['total']           = $order_info['total'] ;
        $row['sub_total']       = $order_info['sub_total'];
        $row['total_tax']       = $order_info['total_tax'];

        $row['drm_customer_id'] = $order_info['drm_customer_id'];

        $row['discount']        = $order_info['discount'];
        $row['discount_type']   = $order_info['discount_type'];
        $row['adjustment']      = $order_info['adjustment'];
        $row['payment_type']    = $order_info['payment_type'];
        $row['currency']        = $order_info['currency'];
        $row['shipping_cost']   = $order_info['shipping_cost'];
        $row['customer_info']   = $order_info['customer_info'];
        $row['billing']         = $order_info['billing'];
        $row['shipping']        = $order_info['shipping'];
        $row['client_note']     = $order_info['client_note'];
        $row['status']          = ucfirst($order_info['status'] ?? $status);
        $row['cms_client']      = $order_info['cms_client'];
        $row['cart']            = $order_info['cart'];

       $order = DrmOrder::updateOrCreate($check , $row);
       if ($order) {
            foreach ((array)$quantity as $i => $item) {
                DB::table('drm_order_products')->updateOrInsert([
                    'product_id' => $product[$i],
                    'name' => $product_name[$i],
                    'drm_order_id' => $order->id,
                ],
                [
                    'quantity' => $quantity[$i] ?? 0,
                    'rate' =>round($rate[$i],2)?? 0,
                    'description' => $description[$i],
                    'unit' => $unit[$i],
                    'tax' => round($tax[$i],2) ?? 0,
                    'discount' =>round($product_discount[$i],2) ?? 0,
                    'amount' => round($amount[$i],2)?? 0,
                ]);
            }

            // if(isLocal() || in_array($check['cms_user_id'], [212, 2592])){
                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                $channel_drm_order_mail = DB::table('drm_order_mail')
                ->where(['cms_user_id' =>$check['cms_user_id'], 'channel' => $channel])
                ->first();

                if($channel_drm_order_mail){
                    if($channel_drm_order_mail->auto_mail){
                        app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id, $channel);
                    }
                }else if(DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'] )->whereNull('channel')->first()->auto_mail){
                    app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
                }
            // }else{
            // if(DB::table('drm_order_mail')->where( 'cms_user_id', $check['cms_user_id'] )->first()->auto_mail)
            // {
            //     app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
            // }
            // }
        }
        return true;
    }


    private function insert_customer($customer_info)
    {
        $validator = Validator::make($customer_info, [
            'email' => 'required',
            'user_id'   => 'required',
        ]);
        if ($validator->fails()) {
            return null;
        }
        $user_id = $customer_info['user_id'];
        // customer add
        $customer = DrmCustomer::updateOrCreate([
            'email' => $customer_info['email'],
            'user_id' => $user_id,
        ],
        [
            'full_name' => $customer_info['customer_full_name'],
            'company_name' => $customer_info['company_name'],
            'country' => $customer_info['country'],
            'phone' => $customer_info['phone'],
            'website' => $customer_info['website'],
            'city' => $customer_info['city'],
            'zip_code' => $customer_info['zip_code'],
            'state' => $customer_info['state'],
            'currency' => $customer_info['currency'],
            'default_language' => $customer_info['default_language'],
            'address' => $customer_info['address'],
            'insert_type'=>$customer_info['insert_type'],
            // 'vat_number' => $customer_info['vat_number'],
        ]);
        $customer_id = $customer->id;
        if($customer_id){
            // sh1ipping
            DB::table('drm_customer_address')->updateOrInsert([
                'drm_customer_id' => $customer_id,
                'type' => 'shipping',
            ],
            [
                'street' => $customer_info['street_shipping'],
                'city' => $customer_info['city_shipping'],
                // 'state' => $customer_info['state_shipping'],
                'zipcode' => $customer_info['zipcode_shipping'],
                'country' => $customer_info['country_shipping'],
            ]);

            // billing
            if(isset($customer_info['is_same_address']))
            {
                DB::table('drm_customer_address')->updateOrInsert([
                    'drm_customer_id' => $customer_id,
                    'type' => 'billing',
                ],
                [
                    'street' => $customer_info['street_shipping'],
                    'city' => $customer_info['city_shipping'],
                    // 'state' => $customer_info['state_shipping'],
                    'zipcode' => $customer_info['zipcode_shipping'],
                    'country' => $customer_info['country_shipping'],

                ]);
            }
            else{
                DB::table('drm_customer_address')->updateOrInsert([
                    'drm_customer_id' => $customer_id,
                    'type' => 'billing',
                ],
                [
                    'street' => $customer_info['street_billing'],
                    'city' => $customer_info['city_billing'],
                    // 'state' => $customer_info['state_billing'],
                    'zipcode' => $customer_info['zipcode_billing'],
                    'country' => $customer_info['country_billing'],
                ]);
            }
        }
        return $customer_id;
    }


	private function syncSuccessReport($shop, $count){
		DB::table('api_order_sync_reports')->updateOrInsert([
            'shop_id' => $shop->id,
        ],
        [
            'status' => '1',
            'end_time' => Carbon::now()->unix(),
            'item' => $count
        ]);
	}

	private function syncErrorReport($shop, $count, $message){
        if($shop){
			DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
            [
                'status' => '2',
                'end_time' => Carbon::now()->unix(),
                'item' => $count,
                'report' => 'Shop: '.$shop->shop_name. ' - Error: '.$message
            ]);
			$shop_user = User::find($shop->user_id);
    		if($shop_user) $shop_user->notify(new DRMNotification('Shop: '.$shop->shop_name. ' - Error: '.$message, 'SHOP_SYNC_ERROR', '#'));
    	}
    	echo 'Shop: '.$shop->shop_name. ' - Error: '.$message."\n";
	}

	//get last sync time from db
	private function syncStartDate($shop){
		return (DB::table('drm_order_sync')->where(['shop_id' => $shop->id, 'drm_user_id' => $shop->user_id])->first())->last_date;
	}

	//Sync last time insert to db
	private function insertSyncDate($shop, $last_date){
		if($shop && $last_date){
			DB::table('drm_order_sync')->updateOrInsert([
                'shop_id' => $shop->id,
                'drm_user_id' => $shop->user_id
            ],
            [
                'last_date' => $last_date
            ]);
		}
	}

    /**
     * Build automatic feed
     * when a new product connected to dt -> add that to automatic feed
     * test: http://localhost/drm_background_interval/automatic-feed-create?feed_id=1
     *
     * add product to the <product_ids> column
     */
    public function buildAutomaticFeed(Request $request){
        $product = $request->product;
        $user_id = $request->user_id;
        $universal_feeds = UniversalExport::where([
            'user_id' => $user_id,
            'is_automatic_feed' => 1,
            'dt_feed' => 1
        ])->get();

        foreach ($universal_feeds as $key => $universal_feed) {

            $tmp = $universal_feed->product_ids;
            array_push($tmp, $product);
            $universal_feed->product_ids = $tmp;
            $universal_feed->save();
        }
    }
}
