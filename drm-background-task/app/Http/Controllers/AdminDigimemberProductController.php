<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use DigiStore24\DigiStoreApi;

	class AdminDigimemberProductController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->table 						= "digimember_product";
			$this->title_field 				= "name";
			$this->limit 						= "20";
			$this->orderby 					= "id,desc";
			$this->global_privilege 		= false;
			$this->button_table_action 	= false;
			$this->button_bulk_action 		= true;
			$this->button_action_style 	= "button_icon";
			$this->button_add 				= false;
			$this->button_edit 				= true;
			$this->button_delete 			= true;
			$this->button_detail	 			= true;
			$this->button_show 				= false;
			$this->button_filter 			= true;
			$this->button_import 			= false;
			$this->button_export 			= false;
			$this->sidebar_mode		  		= "normal"; //normal,mini,collapse,collapse-mini

			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Product Id","name"=>"ds24_product_ids"];
			$this->col[] = ["label"=>"Name","name"=>"name"];
			$this->col[] = ["label"=>"Created","name"=>"created_at"];
			$this->col[] = ["label"=>"Modified","name"=>"modified"];
			$this->col[] = ["label"=>"Currecy","name"=>"ds24_currency"];
			$this->col[] = ["label"=>"Affiliate Commission","name"=>"ds24_affiliate_commission"];
			$this->col[] = ["label"=>"Type","name"=>"type"];
			$this->col[] = ["label"=>"Product Type Id","name"=>"ds24_sync_product_id"];
			$this->col[] = ["label"=>"User Id","name"=>"ds24_sync_user_id"];
			$this->col[] = ["label"=>"Approval Status","name"=>"ds24_approval_status"];
			$this->col[] = ["label"=>"Approval Message","name"=>"ds24_approval_status_msg"];
			// $this->col[] = ["label"=>"Currecy","name"=>"ds24_currency"];
			// $this->col[] = ["label"=>"Currecy","name"=>"ds24_currency"];
			// $this->col[] = ["label"=>"Currecy","name"=>"ds24_currency"];
			// $this->col[] = ["label"=>"Currecy","name"=>"ds24_currency"];

			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Unlock Mode','name'=>'unlock_mode','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Unlock Start Date','name'=>'unlock_start_date','type'=>'datetime','validation'=>'required|date_format:Y-m-d H:i:s','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Is Right Of Withdrawal Waiver Required','name'=>'is_right_of_withdrawal_waiver_required','type'=>'radio','validation'=>'required|integer','width'=>'col-sm-10','dataenum'=>'Array'];
			$this->form[] = ['label'=>'Right Of Withdrawal Waiver Page Id','name'=>'right_of_withdrawal_waiver_page_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'right_of_withdrawal_waiver_page,id'];
			$this->form[] = ['label'=>'Name','name'=>'name','type'=>'text','validation'=>'required|string|min:3|max:70','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
			$this->form[] = ['label'=>'Auth Key','name'=>'auth_key','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Type','name'=>'type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Access Denied Type','name'=>'access_denied_type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Access Denied Url','name'=>'access_denied_url','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Access Denied Page','name'=>'access_denied_page','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Access Denied Text','name'=>'access_denied_text','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Unlock Policy','name'=>'unlock_policy','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Are Comments Protected','name'=>'are_comments_protected','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Lock Type','name'=>'lock_type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Lock Url','name'=>'lock_url','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Lock Page','name'=>'lock_page','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Lock Text','name'=>'lock_text','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'First Login Url','name'=>'first_login_url','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Login Url','name'=>'login_url','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Shortcode Url','name'=>'shortcode_url','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Flags','name'=>'flags','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Publish','name'=>'publish','type'=>'datetime','validation'=>'required|date_format:Y-m-d H:i:s','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Access Granted For Days','name'=>'access_granted_for_days','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Max Download Times','name'=>'max_download_times','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Sales Letter','name'=>'sales_letter','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Content Later Type','name'=>'content_later_type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Content Later Url','name'=>'content_later_url','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Content Later Page','name'=>'content_later_page','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Content Later Msg','name'=>'content_later_msg','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Properties Serialized','name'=>'properties_serialized','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Product Ids','name'=>'ds24_product_ids','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Is Ds24 Sync Enabled','name'=>'is_ds24_sync_enabled','type'=>'radio','validation'=>'required|integer','width'=>'col-sm-10','dataenum'=>'Array'];
			$this->form[] = ['label'=>'Ds24 Sync User Id','name'=>'ds24_sync_user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'ds24_sync_user,id'];
			$this->form[] = ['label'=>'Ds24 Sync User Name','name'=>'ds24_sync_user_name','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Sync Product Id','name'=>'ds24_sync_product_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'ds24_sync_product,id'];
			$this->form[] = ['label'=>'Ds24 Sync Payplan Id','name'=>'ds24_sync_payplan_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'ds24_sync_payplan,id'];
			$this->form[] = ['label'=>'Ds24 Sync Image Id','name'=>'ds24_sync_image_id','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'ds24_sync_image,id'];
			$this->form[] = ['label'=>'Ds24 Sync Image Url','name'=>'ds24_sync_image_url','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Last Sync At','name'=>'ds24_last_sync_at','type'=>'datetime','validation'=>'required|date_format:Y-m-d H:i:s','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Description','name'=>'ds24_description','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Currency','name'=>'ds24_currency','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 First Amount','name'=>'ds24_first_amount','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Other Amount','name'=>'ds24_other_amount','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Affiliate Commission','name'=>'ds24_affiliate_commission','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Image Url','name'=>'ds24_image_url','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Salespage','name'=>'ds24_salespage','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Thankyoupage','name'=>'ds24_thankyoupage','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Approval Status','name'=>'ds24_approval_status','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Ds24 Approval Status Msg','name'=>'ds24_approval_status_msg','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Deleted','name'=>'deleted','type'=>'datetime','validation'=>'required|date_format:Y-m-d H:i:s','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Modified','name'=>'modified','type'=>'datetime','validation'=>'required|date_format:Y-m-d H:i:s','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        	$this->index_button = array();
				$this->index_button[] = ['label'=>'Synce Digi','url'=>CRUDBooster::mainpath("sync"),"icon"=>"fa fa-print"];



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)

			public function getSync()
			{
				//DB::table('products')->where('id',$id)->update(['status'=>$status]);
  				//$key=config('app.digi_api_key');
				DB::table('digimember_product')->delete();

				$keys = '289824-HzAb7gxI1SB3LylXYJVRF3vpJj3MeCeIlyF9aD3h';
				$digi_con = DigiStoreApi::connect($keys);
				$data = $digi_con->listProducts('name');
				$data_p=$data->products;
				//dd($data_p);
				//$i = DB::table('digimember_product')->count();

				foreach ($data_p as $i=>$value) {

					$row['name'] = $value->name;
					$row['created_at'] = $value->created_at;
					$row['modified'] = $value->modified_at;
					$row['ds24_product_ids'] = $value->id;
					$row['ds24_currency'] = $value->currency;
					$row['ds24_affiliate_commission'] = $value->affiliate_commission;
				  	$row['type'] = $value->buyer_type;
					$row['ds24_sync_product_id'] = $value->product_type_id;
					$row['ds24_sync_user_id'] = $value->user_id;
					$row['ds24_approval_status'] = $value->approval_status;
					$row['ds24_approval_status_msg'] = $value->approval_status_msg;
					// $row['ds24_affiliate_commission'] = $value->affiliate_commission;
					// $row['ds24_affiliate_commission'] = $value->affiliate_commission;
					// $row['ds24_affiliate_commission'] = $value->affiliate_commission;

					DB::table('digimember_product')->insert($row);
				}

				//This will redirect back and gives a message
				CRUDBooster::redirect($_SERVER['HTTP_REFERER'],"The status product has been updated !","info");
			}



		public function getAllOrder(){
					//$digi = new DigiStoreApi(config('app.digi_api_key'));
					$key='304282-I9hUcwVlDQmJGwRjKpza6ddxZBlKhqoqdhTEocMZ';
					$digi = DigiStoreApi::connect($key);
					$search=array();

					if(isset($_GET['purchase_id'])){
						$search['purchase_id']=$_GET['purchase_id'];
					}
					// if(isset($_GET['created_at_from'])){
					// 	$search['created_at_from']=$_GET['created_at_from'];
					// }
					// if(isset($_GET['affiliate_id'])){
					// 	$search['affiliate_id']=$_GET['affiliate_id'];
					// }
					if(isset($_GET['pay_method'])){
						$search['pay_method']=$_GET['pay_method'];
					}
					// if(isset($_GET['created_at_to'])){
					// 	$search['created_at_to']=$_GET['created_at_to'];
					// }
					// if(isset($_GET['note'])){
					// 	$search['note']=$_GET['note'];
					// }
					// if(isset($_GET['pay_sequence_no'])){
					// 	$search['pay_sequence_no']=$_GET['pay_sequence_no'];
					// }
					if(isset($_GET['first_name'])){
						$search['first_name']=$_GET['first_name'];
					}
					// if(isset($_GET['transaction_id'])){
					// 	$search['transaction_id']=$_GET['transaction_id'];
					// }
					if(isset($_GET['last_name'])){
						$search['last_name']=$_GET['last_name'];
					}
					// if(isset($_GET['tracking_param'])){
					// 	$search['tracking_param']=$_GET['tracking_param'];
					// }
					// if(isset($_GET['billing_type'])){
					// 	$search['billing_type']=$_GET['billing_type'];
					// }
					if(isset($_GET['email'])){
						$search['email']=$_GET['email'];
					}
					if(isset($_GET['product_id'])){
						$search['product_id']=$_GET['product_id'];
					}
					if(isset($_GET['currency'])){
						$search['currency']=$_GET['currency'];
					}
					if(isset($_GET['pay_method'])){
						$search['pay_method']=$_GET['pay_method'];
					}
					// if(isset($_GET['vat_country'])){
					// 	$search['vat_country']=$_GET['vat_country'];
					// }
                    $page_size=100;
                    $page_no=isset($_GET['page'])?$_GET['page']:1;
					$allorder=$digi ->listTransactions('-365d','now' ,$search,'date', 'asc',$page_no,$page_size);
					$order=$allorder->transaction_list;
					//dd($allorder);
					return view('admin.digistore.allorders',compact('order','allorder'));
			}
		public function getAllRefund(){
					//$digi = new DigiStoreApi(config('app.digi_api_key'));
					$key='304282-I9hUcwVlDQmJGwRjKpza6ddxZBlKhqoqdhTEocMZ';
					$digi = DigiStoreApi::connect($key);
					$search=array();

					if(isset($_GET['purchase_id'])){
						$search['purchase_id']=$_GET['purchase_id'];
					}
					// if(isset($_GET['created_at_from'])){
					// 	$search['created_at_from']=$_GET['created_at_from'];
					// }
					// if(isset($_GET['affiliate_id'])){
					// 	$search['affiliate_id']=$_GET['affiliate_id'];
					// }
					if(isset($_GET['pay_method'])){
						$search['pay_method']=$_GET['pay_method'];
					}
					// if(isset($_GET['created_at_to'])){
					// 	$search['created_at_to']=$_GET['created_at_to'];
					// }
					// if(isset($_GET['note'])){
					// 	$search['note']=$_GET['note'];
					// }
					// if(isset($_GET['pay_sequence_no'])){
					// 	$search['pay_sequence_no']=$_GET['pay_sequence_no'];
					// }
					if(isset($_GET['first_name'])){
						$search['first_name']=$_GET['first_name'];
					}
					// if(isset($_GET['transaction_id'])){
					// 	$search['transaction_id']=$_GET['transaction_id'];
					// }
					if(isset($_GET['last_name'])){
						$search['last_name']=$_GET['last_name'];
					}
					// if(isset($_GET['tracking_param'])){
					// 	$search['tracking_param']=$_GET['tracking_param'];
					// }
					// if(isset($_GET['billing_type'])){
					// 	$search['billing_type']=$_GET['billing_type'];
					// }
					if(isset($_GET['email'])){
						$search['email']=$_GET['email'];
					}
					if(isset($_GET['product_id'])){
						$search['product_id']=$_GET['product_id'];
					}
					if(isset($_GET['currency'])){
						$search['currency']=$_GET['currency'];
					}
					if(isset($_GET['pay_method'])){
						$search['pay_method']=$_GET['pay_method'];
					}
					// if(isset($_GET['vat_country'])){
					// 	$search['vat_country']=$_GET['vat_country'];
					// }
				    $search['transaction_type']='refund';
                    $page_size=100;
                    $page_no=isset($_GET['page'])?$_GET['page']:1;
					$allorder=$digi ->listTransactions('-365d','now' ,$search,'date', 'asc',$page_no,$page_size);
					$order=$allorder->transaction_list;
					//dd($allorder);
					return view('admin.digistore.allorders',compact('order','allorder'));
			}
	     	public function getAllChargeback(){
					//$digi = new DigiStoreApi(config('app.digi_api_key'));
					$key='304282-I9hUcwVlDQmJGwRjKpza6ddxZBlKhqoqdhTEocMZ';
					$digi = DigiStoreApi::connect($key);
					$search=array();

					if(isset($_GET['purchase_id'])){
						$search['purchase_id']=$_GET['purchase_id'];
					}
					// if(isset($_GET['created_at_from'])){
					// 	$search['created_at_from']=$_GET['created_at_from'];
					// }
					// if(isset($_GET['affiliate_id'])){
					// 	$search['affiliate_id']=$_GET['affiliate_id'];
					// }
					if(isset($_GET['pay_method'])){
						$search['pay_method']=$_GET['pay_method'];
					}
					// if(isset($_GET['created_at_to'])){
					// 	$search['created_at_to']=$_GET['created_at_to'];
					// }
					// if(isset($_GET['note'])){
					// 	$search['note']=$_GET['note'];
					// }
					// if(isset($_GET['pay_sequence_no'])){
					// 	$search['pay_sequence_no']=$_GET['pay_sequence_no'];
					// }
					if(isset($_GET['first_name'])){
						$search['first_name']=$_GET['first_name'];
					}
					// if(isset($_GET['transaction_id'])){
					// 	$search['transaction_id']=$_GET['transaction_id'];
					// }
					if(isset($_GET['last_name'])){
						$search['last_name']=$_GET['last_name'];
					}
					// if(isset($_GET['tracking_param'])){
					// 	$search['tracking_param']=$_GET['tracking_param'];
					// }
					// if(isset($_GET['billing_type'])){
					// 	$search['billing_type']=$_GET['billing_type'];
					// }
					if(isset($_GET['email'])){
						$search['email']=$_GET['email'];
					}
					if(isset($_GET['product_id'])){
						$search['product_id']=$_GET['product_id'];
					}
					if(isset($_GET['currency'])){
						$search['currency']=$_GET['currency'];
					}
					if(isset($_GET['pay_method'])){
						$search['pay_method']=$_GET['pay_method'];
					}
					
					$search['transaction_type']='chargeback';
				
					
					
					
					// if(isset($_GET['vat_country'])){
					// 	$search['vat_country']=$_GET['vat_country'];
					// }
                    $page_size=100;
                    $page_no=isset($_GET['page'])?$_GET['page']:1;
					$allorder=$digi ->listTransactions('-365d','now' ,$search,'date', 'asc',$page_no,$page_size);
					$order=$allorder->transaction_list;
					//dd($allorder);
					return view('admin.digistore.allorders',compact('order','allorder'));
			}
		
			public function getAllOrder11(){
					//$digi = new DigiStoreApi(config('app.digi_api_key'));
					//$key='283689-pNkLnEjrYYAJt1lRbmbZn0gmFegtnUecrdJxS5h2';
					$key='304282-I9hUcwVlDQmJGwRjKpza6ddxZBlKhqoqdhTEocMZ';
					$digi = DigiStoreApi::connect($key);
					$search=array();
					if(isset($_GET['first_name'])){
						$search['first_name']=$_GET['first_name'];
					}
					if(isset($_GET['purchase_id'])){
						$search['purchase_id']=$_GET['purchase_id'];
					}

					$allorder=$digi->listTransactions('-365d','now' ,$search,'date', 'asc');
					//dd($allorder);
 					$order=$allorder->transaction_list;
 					$perPage = 50;
					$page = 1;
					$collection = collect($order);

					$order = new LengthAwarePaginator($collection->forPage($page, $perPage), $collection->count(), $perPage, $page, ['path'=>CRUDBooster::adminPath('common/all-order')]);
					//dd($order);
					
					return view('admin.menudashboard.allorders',compact('order'));
			}
			
			public function getAllTransactions(){
			        $key='304282-I9hUcwVlDQmJGwRjKpza6ddxZBlKhqoqdhTEocMZ';
					$digi = DigiStoreApi::connect($key);
					$search=array();

					if(isset($_GET['purchase_id'])){
						$search['purchase_id']=$_GET['purchase_id'];
					}
					// if(isset($_GET['created_at_from'])){
					// 	$search['created_at_from']=$_GET['created_at_from'];
					// }
					// if(isset($_GET['affiliate_id'])){
					// 	$search['affiliate_id']=$_GET['affiliate_id'];
					// }
					if(isset($_GET['pay_method'])){
						$search['pay_method']=$_GET['pay_method'];
					}
					// if(isset($_GET['created_at_to'])){
					// 	$search['created_at_to']=$_GET['created_at_to'];
					// }
					// if(isset($_GET['note'])){
					// 	$search['note']=$_GET['note'];
					// }
					// if(isset($_GET['pay_sequence_no'])){
					// 	$search['pay_sequence_no']=$_GET['pay_sequence_no'];
					// }
					if(isset($_GET['first_name'])){
						$search['first_name']=$_GET['first_name'];
					}
					// if(isset($_GET['transaction_id'])){
					// 	$search['transaction_id']=$_GET['transaction_id'];
					// }
					if(isset($_GET['last_name'])){
						$search['last_name']=$_GET['last_name'];
					}
					// if(isset($_GET['tracking_param'])){
					// 	$search['tracking_param']=$_GET['tracking_param'];
					// }
					// if(isset($_GET['billing_type'])){
					// 	$search['billing_type']=$_GET['billing_type'];
					// }
					if(isset($_GET['email'])){
						$search['email']=$_GET['email'];
					}
					if(isset($_GET['product_id'])){
						$search['product_id']=$_GET['product_id'];
					}
					if(isset($_GET['currency'])){
						$search['currency']=$_GET['currency'];
					}
					if(isset($_GET['pay_method'])){
						$search['pay_method']=$_GET['pay_method'];
					}
					
					$search['transaction_type']='chargeback';
					// if(isset($_GET['vat_country'])){
					// 	$search['vat_country']=$_GET['vat_country'];
					// }
                    $page_size=100;
                    $page_no=isset($_GET['page'])?$_GET['page']:1;
					$allorder=$digi ->listTransactions('-365d','now' ,$search,'date', 'asc',$page_no,$page_size);
					$order=$allorder->transaction_list;
					dd($allorder);
					//return view('admin.digistore.allorders',compact('order','allorder'));
			}
			
			



	}
