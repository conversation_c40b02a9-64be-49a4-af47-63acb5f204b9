<?php namespace App\Http\Controllers;

    use App\NewCustomer;
    use Illuminate\Support\Facades\DB;
    use Session;
	use Request;
	use CRUDBooster;
	use App\User;
	use App\NewOrder;
	use App\MonthlyPaywall;
	use App\Mail\DRMSEndMail;
	use App\Jobs\MonthlyPaywallJob;
	use App\Jobs\MonthlyPaywallExpiredJob;
	use App\Notifications\DRMNotification;
	use App\Services\Payment\Service\InvoicePayment;
use Carbon\Carbon;

	class AdminMonthlyPaywallsController extends \crocodicstudio\crudbooster\controllers\CBController {
	    public function cbInit() {
	    	//Without fabian or superadmin deny access

	    	# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->table 			   = "monthly_paywalls";
			$this->title_field         = "id";
			$this->limit               = 20;
			$this->orderby             = "id,desc";
			$this->show_numbering      = FALSE;
			$this->global_privilege    = FALSE;
			$this->button_table_action = true;
			$this->button_action_style = "icon";
			$this->button_add          = FALSE;
			$this->button_delete       = FALSE;
			$this->button_edit         = FALSE;
			$this->button_detail       = FALSE;
			$this->button_show         = FALSE;
			$this->button_filter       = FALSE;
			$this->button_export       = FALSE;
			$this->button_import       = FALSE;
			$this->button_bulk_action  = FALSE;
			$this->sidebar_mode		   = "normal"; //normal,mini,collapse,collapse-mini
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
	        $this->col = [];
			$this->col[] = array("label"=>"ID","name"=>"id");
			$this->col[] = array("label"=>"Customer","name"=>"user_id","join"=>"cms_users,name");
			$this->col[] = array("label"=>"Invoice Number","name"=>"order_id","join"=>"new_orders,invoice_number");
			$this->col[] = array("label"=>"Amount","name"=>"order_id","join"=>"new_orders,total");
			$this->col[] = array("label"=>"Paid at","name"=>"paid_at" );
			$this->col[] = array("label"=>"Status","name"=>"status" );
			$this->col[] = array("label"=>"Mail Send Count","name"=>"mail_send_count" );
			$this->col[] = array("label"=>"Payment Id","name"=>"payment_id");
			// $this->col[] = array("label"=>"Data","name"=>"data");
			$this->col[] = array("label"=>"Collect Total","name"=>"total" );

			# END COLUMNS DO NOT REMOVE THIS LINE
			# START FORM DO NOT REMOVE THIS LINE
		$this->form = [];
		// $this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
		// $this->form[] = ["label"=>"Generated At","name"=>"generated_at","type"=>"datetime","required"=>TRUE,"validation"=>"required|date_format:Y-m-d H:i:s"];
		// $this->form[] = ["label"=>"Paat","name"=>"paid_at","type"=>"datetime","required"=>TRUE,"validation"=>"required|date_format:Y-m-d H:i:s"];
		// $this->form[] = ["label"=>"Status","name"=>"status","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Mail Send Count","name"=>"mail_send_count","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
		// $this->form[] = ["label"=>"Payment Id","name"=>"payment_id","type"=>"select2","required"=>TRUE,"validation"=>"required|min:1|max:255","datatable"=>"payment,id"];
		// $this->form[] = ["label"=>"Total","name"=>"total","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Sub Total","name"=>"sub_total","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Discount","name"=>"discount","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Data","name"=>"data","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Response","name"=>"response","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];

			# END FORM DO NOT REMOVE THIS LINE

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();

	        if(CRUDBooster::isSuperadmin()){
	        	$this->addaction[] = ['title'=>'Send direct', 'label' => 'Send direct', 'url'=>CRUDBooster::mainpath('send/[id]'), 'confirmation_text' => 'Are you sure to send!', "icon" => 'fa fa-envelope', 'confirmation' => true, 'color' => 'info', 'showIf'=>"(is_null([paid_ad]))"];
	        }

	        $this->addaction[] = ['title'=>'Send', 'label' => 'Send', 'url'=>CRUDBooster::mainpath('send-queue/[id]'), 'confirmation_text' => 'Are you sure to send using queue!', "icon" => 'fa fa-envelope', 'confirmation' => true, 'color' => 'warning', 'showIf'=>"(is_null([paid_ad]))"];
	        $this->addaction[] = ['title'=>'Mark as Paid', 'label' => 'Mark as Paid', 'url'=>CRUDBooster::mainpath('paid/[id]'), 'confirmation_text' => 'Are you sure to mark this paywall as Paid!', "icon" => 'fa fa-money', 'confirmation' => true, 'color' => 'danger', 'showIf'=>"(is_null([paid_ad]))"];

	        if(CRUDBooster::isSuperadmin()){
	        	$this->addaction[] = ['title'=>'Payment', 'label' => 'Payment', 'url'=> CRUDBooster::adminPath('paywall-payment/[id]'), "icon" => 'fa fa-money', 'color' => 'info'];
	        }



	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	$amount_col = 3;
	    	$status_col = 5;
	    	$amount_collect_col = 8;

	    	if($column_index == $amount_col){
	    		$column_value = number_format((float)$column_value, 2, ',', '.').formatCurrency('eur');
	    	}

	    	if($column_index == $amount_collect_col){
	    		$column_value = number_format((float)$column_value, 2, ',', '.').formatCurrency('eur');
	    	}

	    	if($column_index == $status_col){
	    		$color = in_array(strtolower($column_value), ['succeeded', 'paid'])? '#398439' : '#f39c12';
	    		$color = in_array(strtolower($column_value), ['over_due'])? '#ff0000' : $color;
	    		$color = in_array(strtolower($column_value), ['processing'])? '#00c0ef' : $color;
	    		$column_value = '<label class="btn btn-xs" style="background-color:'.$color.';color:#fff;">'.ucfirst(str_replace('_', ' ', $column_value)).'</label>';
	    	}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)

	    public function getSend($id){
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

	    	try{
	    		$paywall = MonthlyPaywall::find($id);
	    		if(is_null($paywall)){
	    			throw new \Exception('Invalid action!');
	    		}
	    		if($this->sendMonthlyPaywall($id)){
	    			CRUDBooster::redirect(CRUDBooster::mainpath(), 'Send success!', 'success');
	    		}else{
	    			CRUDBooster::redirect(CRUDBooster::mainpath(), 'Send failed!', 'error');
	    		}
	    	}catch(\Exception $e){
	    		CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error');
	    	}
	    }

	    public function getSendQueue($id){
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

	    	try{
	    		$paywall = MonthlyPaywall::find($id);
	    		if(is_null($paywall)){
	    			throw new \Exception('Invalid action!');
	    		}
	    		MonthlyPaywallJob::dispatch($id);
	    		CRUDBooster::redirect(CRUDBooster::mainpath(), 'Send processing on Queue!', 'info');
	    	}catch(\Exception $e){
	    		CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error');
	    	}
	    }

	    //Send monthly paywall
	    public function sendMonthlyPaywall($id){
        	// //DB::beginTransaction();
			try{
		    	$paywall = MonthlyPaywall::has('order')->whereNull('paid_at')->find($id);
			    if(is_null($paywall)) throw new \Exception('Invalid monthly paywall!');

				$data = [];
				$invoice_data = [];
				$order = NewOrder::find($paywall->order_id);

			    $admin_url = config('global.admin_url');
			    $due_date = config('global.paywall_due_date');
			    $url = $admin_url.'/paywall-payment/'.$id; //CRUDBooster::adminPath('monthly_paywalls/payment/'.$id);

			    //Send Email
		    	$username = ($paywall->user)? $paywall->user->name : '';
		    	$max_due_date = ($paywall->start_at)? $paywall->start_at->addDays($due_date)->format('Y-m-d') : config('global.paywall_due_date').' days';
				$tags = [
				    'user' =>  $username,
				    'amount' =>  number_format((float)$order->total, 2, ',', '.').' EUR',
				    'date' =>  $paywall->paywall_date,
				    'max_due_date' => $max_due_date,
				    'pay_url' => $url,// $this->get_tiny_pay_url($url),
				];

				$slug = 'monthly_paywall'; //Page slug
				$mail_data = DRMParseMailTemplate($tags, $slug); //Generated html
				$user_email = $paywall->user->email;
				if(filter_var($user_email, FILTER_VALIDATE_EMAIL)){
					// Mail::to($user_email)->send(new DRMSEndMail($mail_data)); //Send
					app('drm.mailer')->getMailer()->to($user_email)->send(new DRMSEndMail($mail_data));

				}else{
					throw new \Exception('Invalid user email. User ID:'.$paywall->user_id);
				}

				//Unpaid order product delete warning email

				// Action only allow for Moritz(<EMAIL>) and Özgür (<EMAIL>) and Betül1(<EMAIL>) Account
				if(in_array($order->cms_user_id, [2956, 3445, 3624])){
					app('App\Http\Controllers\AdminDrmAllOrdersController')->productDeleteWarningEmail($order->id, 'mahnung');
				}

				$paywall->increment('mail_send_count');
				DB::table('new_orders')->where('id', $paywall->order_id)->update(['status' => 'mahnung']);
				// //DB::commit(); //All ok

				try{
					$orderModel = NewOrder::find($paywall->order_id);
					updateOrderHistory($orderModel, 'mahnung', 'Paywall remainder email sent to '.$user_email);
					updateOrderHistory($orderModel, 'mahnung', 'Order status changed to '.drmHistoryLabel('mahnung'));
				}catch(\Exception $ex){
					User::find(71)->notify(new DRMNotification('Paywall remainder email sent error '.$ex->getMessage().' Line:'.$ex->getLine(), '', '#'));
					if($paywall){
					    $error_log = $paywall->error_log?? [];
					    $error_log[] = ['message' => mb_convert_encoding($e->getMessage(), 'UTF-8', 'UTF-8'), 'time' => date('Y-m-d H:i:s')];
					    $paywall->update(['error_log' => $error_log]);
			    	}
				}

				return true;
			} catch (\Exception $e) {
			    // //DB::rollBack();
			    User::find(71)->notify(new DRMNotification('Paywall Send customer invoice error '.$e->getMessage().' Line:'.$e->getLine(), '', '#'));

			    if($paywall){
				    $error_log = $paywall->error_log?? [];
				    $error_log[] = ['message' => mb_convert_encoding($e->getMessage(), 'UTF-8', 'UTF-8'), 'time' => date('Y-m-d H:i:s')];
				    $paywall->update(['error_log' => $error_log]);
			    }

			    return false;
			}
	    }

	    //generate tiny url using tiny url
		private function get_tiny_pay_url($url)  {
			$ch = curl_init();
			$timeout = 5;
			curl_setopt($ch,CURLOPT_URL,'http://tinyurl.com/api-create.php?url='.$url);
			curl_setopt($ch,CURLOPT_RETURNTRANSFER,1);
			curl_setopt($ch,CURLOPT_CONNECTTIMEOUT,$timeout);
			$data = curl_exec($ch);
			curl_close($ch);
			return $data;
		}

		//paywall stripe payment
		public function paywallPaymentSCA($purchase_data){
			// //DB::beginTransaction();
			try{

				$paywall = MonthlyPaywall::find($purchase_data['paywall_id']);
				if(is_null($paywall)) throw new \Exception("Invalid paywall charge!");

				$discount = $purchase_data['discount']?? 0;
                $total = $purchase_data['total']?? 0;
                $sub_total = $purchase_data['sub_total']?? 0;

                $update_data = [];
                $update_data['payment_id'] = $purchase_data['id'];
                $update_data['total'] = $total;
                $update_data['sub_total'] = $sub_total;
                $update_data['discount'] = $discount;
                $update_data['paid_at'] = now();
                $update_data['status'] = 'paid';

                $update_data = array_filter($update_data);
                $paywall->update($update_data);

                DB::table('new_orders')->where('id', $paywall->order_id)->update(['status' => 'paid']);

				// //DB::commit();    // Commiting  ==> There is no problem whatsoever
            	return ['success' => true, 'message' => 'Paywall charge payment successfully!'];
			}catch(\Exception $e){
				// //DB::rollBack();   // rollbacking  ==> Something went wrong
            	return ['success' => false, 'message' => $e->getMessage()];
			}
		}

		//payment notification to user
		public function sendPaywallAction(){
			try{
				//Get last 7 days paywall
				$due_date = config('global.paywall_due_date');
				$date_sub = Carbon::today()->subDays($due_date)->toDateString();
				$date_sub_mp = Carbon::today()->subDays(3)->toDateString();
				$today = Carbon::today()->toDateString();

				$due_paywalls = MonthlyPaywall::whereHas('order', function($order) use ($due_date, $date_sub, $today){
					$order->whereNotIn('status', ['mahnung', 'inkasso'])->whereNull('credit_ref')->where('test_order', '<>', 1)
						->where('insert_type', 4)
						->whereNotNull('mail_sent')
						->whereDate('mail_sent', '<=', $today)
						->whereRaw("DATE_ADD(mail_sent, INTERVAL '$due_date' DAY) <= '$today'");
				})->whereNull('paid_at')->whereNotIn('status', ['paid', 'processing', 'over_due'])->pluck('id')->toArray();

				$expired_paywalls = MonthlyPaywall::whereHas('order', function($order) use ($due_date, $date_sub, $today){
					$allowed_days = $due_date + $due_date;
					$order->where('status', 'mahnung')->whereNull('credit_ref')->where('test_order', '<>', 1)
						->where('insert_type', 4)
						->whereNotNull('mail_sent')
						->whereDate('mail_sent', '<', $today)
						->whereRaw("DATE_ADD(mail_sent, INTERVAL '$allowed_days' DAY) < '$today'");
				})->whereNull('paid_at')->whereNotIn('status', ['paid', 'processing', 'over_due'])->pluck('id')->toArray();

				// Remainder Email sending for manual invoice
                try {
                	\App\NewOrder::where('insert_type', '<>', 4)
                    ->where('test_order', '<>', 1)
                    ->where('invoice_number', '>', 0)
                    ->whereNotNull('mail_sent')
                    ->whereNull('remainder_date')
                    ->whereNull('credit_ref')
					->whereNull('mp_payment_agreement_at')
                    ->whereNull('intend_id')
                    ->where('status', '<>', 'paid')
                    ->where('invoice_number', '>',  0)
                    ->where('credit_number', 0)
                    ->whereNull('offer_number')
                    ->where(function($q) {
                     	$q->where('status', 'nicht_bezahlt')
                     	->orWhere(function($qq) {
                        	$qq->where('marketplace_paid_status', '<>', 1)
                        	->where('insert_type', 8);
                     	});
                    })
                    ->where(function($q) use ($date_sub, $date_sub_mp) {
				        $q->where(function($qq) use ($date_sub_mp) {
				            $qq->where('cms_user_id', '=', 2455)
				            ->whereNotNull('marketplace_order_ref')
				            ->whereDate('mail_sent', '<=', $date_sub_mp);
				        })
				        ->orWhere(function($qq) use ($date_sub) {
				            $qq->whereDate('mail_sent', '<=', $date_sub);
				        });
				    })
                    ->select('id')
                    ->get()
                    ->each(function ($order){

						$channel = \App\Shop::where('id', $order->shop_id)->value('channel');

						$channel_remainder_mail = DB::table('remainder_email_settings')
						->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
						->exists();

						if($channel_remainder_mail){
							app('App\Http\Controllers\AdminDrmAllOrdersController')->send_remainder_email($order->id, $channel);
						}else{
							app('App\Http\Controllers\AdminDrmAllOrdersController')->send_remainder_email($order->id);
						}
                    });

				    //Mp agreements order
				    $d = \App\NewOrder::join('mp_payment_agreements', 'mp_payment_agreements.user_id', '=', 'new_orders.cms_client')
				    ->where('new_orders.insert_type', 8)
				    ->where('new_orders.test_order', '<>', 1)
				    ->where('new_orders.invoice_number', '>', 0)
				    ->whereNull('new_orders.remainder_date')
				    ->whereNull('new_orders.credit_ref')
				    ->whereNotNull('new_orders.mp_payment_agreement_at')
				    ->where('new_orders.marketplace_paid_status', '<>', 1)
				    ->where('new_orders.cms_user_id', '=', 2455)
				    ->whereNotNull('new_orders.marketplace_order_ref')
				    ->whereRaw('DATEDIFF(NOW(), new_orders.created_at) >= mp_payment_agreements.due_days')
				    ->select('new_orders.id', 'new_orders.cms_user_id', 'new_orders.status', 'new_orders.marketplace_paid_status', 'new_orders.intend_id')
				    ->get()
				    ->each(function ($order) {

				    	$pending = resolve(InvoicePayment::class)->isDuplicatePayment($order);
				    	if(!$pending) {

				    		//MP agreement payment
				    		app('App\Http\Controllers\AdminDrmAllOrdersController')->chargeMpAgreement($order->id);
				    	}
				    });

				    //Remainder payment -> Inkasso
                    \App\NewOrder::where('status', 'mahnung')
                    ->where('insert_type', '<>', 4)
                    ->where('test_order', '<>', 1)
                    ->where('invoice_number', '>', 0)
                    ->whereNotNull('remainder_date')
                    ->whereNull('credit_ref')
                    ->where('marketplace_paid_status', '<>', 1)
                    ->whereDate('remainder_date', '<=', $date_sub)
                    ->select('id')
                    ->get()
                    ->each(function ($expired_order) {
                		app('App\Http\Controllers\AdminMonthlyPaywallsController')->blockOrderService($expired_order->id);
                    });

				    /* For order status -> Inkasso -> after 3 days disconnect product */
					$disconnect_days = 10; // From inkasso + 3 days
					$disconnect_sub = \Carbon\Carbon::today()->subDays($disconnect_days)->toDateString();

                } catch (\Exception $ex) {
                    User::find(71)->notify(new DRMNotification('Manual invoice remainder Error: '.$ex->getMessage().' Line:'.$ex->getLine(), '', '#'));
                }


                //Offer remainder
                try{
					$today_date = \Carbon\Carbon::today();
					\App\NewOrder::whereNotNull('offer_number')
					->whereNotNull('offer_remainder')
					->where('status', 'offer_sent')
					->where('offer_remainder', '<=', $today_date)
					->select('id')
					->pluck('id')
					->each(function($id) {
						\App\Jobs\OfferRemainderJob::dispatch($id);
					});
                }catch(\Exception $ex){}

				//Notify due paywalls user with payment url
				if($due_paywalls){
					foreach ($due_paywalls as $due_paywall) {
						$mahnung_exists = MonthlyPaywall::whereHas('order', function($order) {
							$order->leftJoin('order_logs', 'new_orders.id', '=', 'order_logs.order_id')
								->whereColumn('order_logs.created_at', '>=', 'mail_sent')
								->whereJsonContains('payload', ['status' => 'mahnung']);
						})
						->where('monthly_paywalls.id', $due_paywall)
						->exists();
				
						if ($mahnung_exists) continue;

						sleep(1);
						// app('App\Http\Controllers\AdminMonthlyPaywallsController')->sendMonthlyPaywall($due_paywall);
						MonthlyPaywallJob::dispatch($due_paywall); //Dispatch due paywall
					}
				}

				if($expired_paywalls){
					sleep(1);
					MonthlyPaywallExpiredJob::dispatch($expired_paywalls); //Dispatch expired paywall data
				}

			} catch(\Exception $e){
				User::find(71)->notify(new DRMNotification('Due paywall dispatch Error: '.$e->getMessage().' Line:'.$e->getLine(), '', '#'));
			}
		}

		public function getPaywallExpiredAction($paywall_ids){
			// //DB::beginTransaction();
			try{
				if($paywall_ids){
					DB::table('monthly_paywalls')->whereIn('id', $paywall_ids)->update(['status' => 'over_due']);
					$id_string = implode(',', $paywall_ids);
					User::find(71)->notify(new DRMNotification('Paywall Expired Ids: '.$id_string, '', CRUDBooster::adminPath('monthly_paywalls')));

					//block user
					$block_user_ids = DB::table('monthly_paywalls')->whereIn('id', $paywall_ids)->select('user_id')->pluck('user_id')->toArray();
					// DB::table('cms_users')->whereIn('id', $block_user_ids)->update(['status' => null]);

					$user_string = implode(',', $block_user_ids);
					User::find(71)->notify(new DRMNotification('Paywall Block users Ids: '.$user_string, '', CRUDBooster::adminPath('monthly_paywalls')));

					//Reminder Inkasso
					$block_order_ids = DB::table('monthly_paywalls')->whereIn('id', $paywall_ids)->select('order_id')->pluck('order_id')->toArray();
					DB::table('new_orders')->whereIn('id', $block_order_ids)->update(['status' => 'inkasso']);

					if($block_order_ids){
						foreach ($block_order_ids as $block_order_id) {
							try{
								$orderModel = NewOrder::find($block_order_id);
								updateOrderHistory($orderModel, 'inkasso', 'Order status changed to '.drmHistoryLabel('inkasso'));

								if(!empty($orderModel->cms_client))
								{
									app('App\Http\Controllers\AdminDrmAllCustomersController')->lockUnlockCustomer($orderModel->cms_client, 'inkasso');
								}

								send_order_email($block_order_id);
							}catch(\Exception $ex){}
						}
					}

					$order_id_string = implode(',', $block_order_ids);
					User::find(71)->notify(new DRMNotification('Paywall Reminder Inkasso Order Ids: '.$order_id_string, '', CRUDBooster::adminPath('drm_all_orders')));
				}
				// //DB::commit();
				//Success notification

			}catch(\Exception $e){
				// //DB::rollBack();
				User::find(71)->notify(new DRMNotification('Paywall Reminder Inkasso falied!', '', CRUDBooster::adminPath('monthly_paywalls')));
			}

		}

		public function getPaid($id){
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

	    	try{
	    		$paywall = MonthlyPaywall::find($id);
	    		if(is_null($paywall)){
	    			throw new \Exception('Invalid action!');
	    		}

	    		if( $paywall->update(['status' => 'paid', 'paid_at' => now(), 'payment_id' => 'mnl_'.$id.'_'.date('YmdHis')]) ){
	    			CRUDBooster::redirect(CRUDBooster::mainpath(), 'Manual paid success!', 'success');
	    		}else{
	    			CRUDBooster::redirect(CRUDBooster::mainpath(), 'Manual paid failed!', 'error');
	    		}
	    	}catch(\Exception $e){
	    		CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error');
	    	}
		}


		//block order service
		public function blockOrderService($order_id){

			if(empty($order_id)) return;

			try{

				$order = \App\NewOrder::find($order_id);
				if(empty($order)) throw new \Exception("Invalid order ID: ".$order_id);


				$mpAgreement = !empty($order->mp_payment_agreement_at);

				$pending = resolve(InvoicePayment::class)->isDuplicatePayment($order);
				if($pending) return;

				$aditional_log = '';
				$is_service = false;

				//service block
				$customer = NewCustomer::find($order->drm_customer_id);
	            if ($customer && $order->feature_type == 1) {

	                $app = \DB::table('app_stores')->where('id', $order->feature_id)->select('id', 'menu_name')->first();
	                $assign_app = \DB::table('app_assigns')->where('user_id', $customer->cc_user_id)->where('app_id', $order->feature_id)->select('id')->first(); //assign app id

	                if($assign_app){
	                	//Delete assign app
	                	$is_service = false;
	                	\DB::table('app_assigns')->where('id', $assign_app->id)->delete();
						$feature_name = ($app->menu_name)? $app->menu_name : 'App';
	                	$aditional_log .= $feature_name.' blocked successfully!';
	                }

	            } elseif ($customer && $order->feature_type == 2) {

	            	$import_plan = DB::table('import_plans')->where('id', $order->feature_id)->select('id', 'plan')->first();
	            	$feature_name = ($import_plan->plan)? $import_plan->plan : 'Import Plan';

	            	//delete manual tariff
	            	$is_service = false;
	            	$manual_tariff = \DB::table('manual_import_tarrif')->where('user_id', $customer->cc_user_id)->select('id', 'import_amount')->first(); //assign plan id
	            	if($manual_tariff){
	            		\DB::table('manual_import_tarrif')->where('id', $manual_tariff->id)->delete();
	            		$aditional_log .= ' Manual assigned import tariff blocked successfully';
	            	}
	            }

	            //paywall block
	            if($order->insert_type == 4){
		            $paywall = MonthlyPaywall::has('order')->whereNull('paid_at')->where('order_id', $order->id)->first();
		            if($paywall){

		            	$is_service = false;
		            	$paywall->update(['status' => 'over_due']);
		            	// DB::table('cms_users')->where('id', $paywall->user_id)->update(['status' => null]);
		            	$aditional_log .= ' Paywall user blocked!';
		            }
	            }


	            $delivered = DB::table('order_logs')->where('order_id', $order->id)->where('payload->status', 'Shipped')->exists();

	            //Marketplace due order cancel
	            if(!$mpAgreement && !$delivered && (int)$order->cms_user_id === 2455 && !empty($order->marketplace_order_ref) && $order->cms_client)
	            {
	            	return $this->dropmatixMpOrderCancel($order->id);
	            }

	            $aditional_log = trim($aditional_log);
                $logMessage = 'Status changed from '.drmHistoryLabel($order->status).' to '.drmHistoryLabel('inkasso').' successfully! '.$aditional_log;
                $logMessage = trim($logMessage);

                $order->update(['status' => 'inkasso']);
                updateOrderHistory($order, 'inkasso', $logMessage);

            	// Lock user
            	if(!empty($order->cms_client))
				{
					app('App\Http\Controllers\AdminDrmAllCustomersController')->lockUnlockCustomer($order->cms_client, 'inkasso');
				}

				send_order_email($order->id);

			}catch(\Exception $e){
				User::find(71)->notify(new DRMNotification('Manual payment inkasso failed! Order id: '.$order->id.' Error: '.$e->getMessage(), '', '#'));
			}
		}



		//Dropmatix marketplace order cancel
		private function dropmatixMpOrderCancel($order_id)
		{
			$order = \App\NewOrder::find($order_id);
			$user_id = $order->cms_client;

			$res = app('App\Http\Controllers\NewShopSyncController')->makeMpCreditNoteFormOrder($order);
			if(!$res) return;

			$old = DB::table('options')->where('user_id', $user_id)
	        ->where('option_group', 'mp_due_order')
	        ->where('option_key', 'mp_due_order')
	        ->select('id', 'option_value')
	        ->first();

	        if($old && $old->id)
	        {
	        	$count = (int)$old->option_value + 1;
	        	DB::table('options')->where('id', '=', $old->id)
	        	->update([
	        		'option_value' => $count,
	        		'updated_at' => now(),
	        	]);

	        	if($count >= 3) {
	        		DB::table('cms_users')->where('id', $user_id)
	        		->update([
	        			'marketplace_access' => 0,
	        		]);
	        	}

	        }else {
	        	DB::table('options')->insert([
	        		'option_group' => 'mp_due_order',
	        		'option_key' => 'mp_due_order',
	        		'user_id' => $user_id,
	        		'option_value' => 1,
	        		'created_at' => now(),
	        		'updated_at' => now(),
	        	]);
	        }
		}
	}
