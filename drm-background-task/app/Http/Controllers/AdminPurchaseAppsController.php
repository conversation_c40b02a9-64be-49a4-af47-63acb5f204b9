<?php namespace App\Http\Controllers;

	use Session;
	use Illuminate\Http\Request;
	use DB;
	use CRUDBooster;
	use App\AppStore;
	use App\Comment;
	use App\User;
	use Illuminate\Support\Facades\Validator;

	// use App\Mail\AppPurchaseConfirmation;
	// use App\Mail\AppTrialConfirmation;

	use App\Mail\DRMSEndMail;


	use Illuminate\Support\Facades\Mail;
	use ServiceKey;
	use Illuminate\Support\Facades\Http;
	use GuzzleHttp\Client;

	class AdminPurchaseAppsController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = false;
			$this->button_edit = false;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "purchase_apps";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"App Name","name"=>"app_id","join"=>"app_stores,menu_name"];
			$this->col[] = ["label"=>"Type","name"=>"app_id","join"=>"app_stores,type"];
			$this->col[] = ["label"=>"User Name","name"=>"cms_user_id","join"=>"cms_users,name"];
			$this->col[] = ["label"=>"Plan Type","name"=>"type"];
			$this->col[] = ["label"=>"Price","name"=>"price"];
			$this->col[] = ["label"=>"Status","name"=>"status"];
			$this->col[] = ["label"=>"Subscription Date Start","name"=>"subscription_date_start"];
			$this->col[] = ["label"=>"Subscription Date End","name"=>"subscription_date_end"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE

			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM

			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {

	    	// if($column_index == 6){
	    	//    if($column_value == 1){
	    	//    	 $column_value ='<p">Active</p>';
	    	//     }else{
	    	//         $column_value ='<p>Un Active</p>';
	    	//     }
	    	// }

	    	// if($column_index == 8){
	    	// 		if($column_value >= date('Y-m-d')){
	    	//    	     $column_value ='<p">Active</p>';
	    	//     }else{
	    	//         $column_value ='<p>Un Active</p>';
	    	//     }
	    	//    }
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)

	    public function getAdd() {
			  //Create an Auth
			  if(!CRUDBooster::isCreate() && $this->global_privilege==FALSE || $this->button_add==FALSE) {
			    CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			  }

			  $data = [];
			  $data['page_title'] = 'Create App Store';

			  //Please use cbView method instead view method from laravel
			  $this->cbView('app_store.create_app_store',$data);
			}


	    public function appList(Request $request, $id=null){
	    	// $this->CheckAppPurchase('DRM-Projekt');
	   		$user = User::find(CRUDBooster::myId());

	    	if(!CRUDBooster::myId()){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
	    	}
	    	 $subs=DB::table('purchase_apps')
                    ->where('cms_user_id',CRUDBooster::myId())
                    ->where('type','!=','Free Trail')
                    ->where(function($q) {
                            $q->where('subscription_date_end','>', date('Y-m-d'))
                            	->orwhere('subscription_life_time',1);
                    })
                    ->pluck('app_id')->toArray();
	    			$query=AppStore::Leftjoin('app_categories','app_categories.id','=','app_stores.app_category_id')
	    			->select('app_stores.*');
	    			if(!empty($subs)){
	    				$query->whereNotIn('app_stores.id',$subs);
	    			}

	    			if(!empty($request->search)){
	    				$query->where('app_stores.menu_name', 'LIKE', '%'.$request->search.'%');
	    				$searchValues = preg_split('/\s+/', $request->search, -1, PREG_SPLIT_NO_EMPTY);
	    				foreach ($searchValues as $value) {

	    				$query->orWhere('app_stores.menu_name', 'LIKE', '%'.$value.'%')
	    				->orWhere('app_stores.description', 'LIKE', '%'.$value.'%');
		    				if(is_numeric($value)){
								$query->orWhereBetween('app_stores.fixed_price', [(floor($value)), (float)(floor($value) + 0.9999 )] );
		    				}
						}
	    			}

	    			if($id !=null){
	    				$query->where('app_stores.app_category_id',$id);
	    			}

	    			if(!empty($request->created_at)){
	    				$query->where('app_stores.created_at','<=',$request->created_at.' 00:00:00');
	    			}
	    			if(!empty($request->filter)){
	    				$search_str = $request->filter;

	    				if($search_str == 'price_max'){
							$query->orderby('app_stores.fixed_price', 'desc');
	    				}elseif ($search_str == 'price_min') {
	    					$query->orderby('app_stores.fixed_price', 'asc');
	    				}elseif ($search_str == 'desc') {
	    					$query->orderby('app_stores.created_at', 'desc');
	    				}else{
	    					$query->orderby('app_stores.created_at', 'asc');
	    				}
	    			}
	    			$results=$query->orderBy('feature_product','asc')->paginate(15);
	    			$categories=DB::table('app_categories')->get();

	    	return view('app_store.app_list',compact('results','categories', 'user'));
	    }

	    public function Purchased(){

	    	if(!CRUDBooster::myId()){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
	    	}

	    	$user = User::find(CRUDBooster::myId());

	    	$results=DB::table('purchase_apps')
	    	        ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
	    	        ->where('purchase_apps.type','!=','Free Trail')
                    ->where('purchase_apps.cms_user_id',CRUDBooster::myId())
                    ->where(function($query) {
                            $query->where('purchase_apps.subscription_date_end','>', date('Y-m-d'))
                            ->orwhere('purchase_apps.subscription_life_time',1);
                    })
                    ->select('app_stores.menu_name','app_stores.icon','purchase_apps.*')
                    ->paginate(15);
                    $categories=DB::table('app_categories')->get();
	    	return view('app_store.purchase_app_list',compact('results','categories', 'user'));
	    }

	    public function appView(){

	    		$id = $_REQUEST['id'];
	    		$result=AppStore::select('app_stores.*')
	    		->where('app_stores.id', $id)
	    		->first();

	    		$plans=DB::table('app_store_plans as asp')
		          ->join('subscription_plans as sp','sp.id','=','asp.subscription_plans_id')
		          ->where('asp.app_stores_id', $id)
		          ->select('sp.*')
		          ->get();
		          $download='';
		          if(isset($_REQUEST['app_id'])){
		          	$download='yes';
		        }
	    		return view('app_store.view',compact('result','plans','download'));

	    }


	    public function appForm(){
	    		$id = $_REQUEST['id'];
	    		$result=AppStore::Leftjoin('purchase_apps','purchase_apps.app_id','=','app_stores.id')
	    		->select('app_stores.*','purchase_apps.is_free_trail as is_free_trail')
	    		->where('app_stores.id', $id)
	    		->first();

	    		$is_trial = DB::table('purchase_apps')->where([
	    			'app_id'=>$id,
	    			'cms_user_id'=>CRUDBooster::myId(),
	    			'is_free_trail'=>1
	    			])->first();

	    		$plans=DB::table('app_store_plans as asp')
		          ->join('subscription_plans as sp','sp.id','=','asp.subscription_plans_id')
		          ->where('asp.app_stores_id', $id)
		          ->orderBy('sp.price','asc')
		          ->select('sp.*')
		          ->get();
		        $user = User::find(CRUDBooster::myId());

		        //User term
		        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
		        $term = ($privacy)? $privacy->page_content : '';
		        $user_data = '<div id="customer_data_term"></div>';
		        if($user->billing_detail){
		            $billing = $user->billing_detail;
		            $user_data = '<div id="customer_data_term">'.$billing->company_name.'<br>'.$billing->address.'<br>'.$billing->zip.' '.$billing->city.'<br>'.$billing->country->name.'</div>';
		        }
		        if (strpos($term, '{customer}') !== false) {
		            $term = str_replace('{customer}', $user_data, $term);
		        }

	    		return view('app_store.form',compact('result','plans', 'user', 'is_trial', 'term'));

	    }

	     public function updateForm(){
	     		$id = $_REQUEST['id'];

	    		$result=AppStore::join('purchase_apps','purchase_apps.app_id','=','app_stores.id')
		    		->select('app_stores.*','purchase_apps.id as  purchase_id','purchase_apps.plan_id')
		    		->where('app_stores.id', $id)
		    		->where('purchase_apps.cms_user_id', CRUDBooster::myId())
		    		->first();

	        $plans=DB::table('app_store_plans as asp')
		          ->join('subscription_plans as sp','sp.id','=','asp.subscription_plans_id')
		          ->where('asp.app_stores_id', $id)
		          ->select('sp.*')
		          ->orderBy('sp.price','asc')
		          ->get();
		         $user = User::find(CRUDBooster::myId());

		         //User term
		        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
		        $term = ($privacy)? $privacy->page_content : '';
		        $user_data = '<div id="customer_data_term"></div>';
		        if($user->billing_detail){
		            $billing = $user->billing_detail;
		            $user_data = '<div id="customer_data_term">'.$billing->company_name.'<br>'.$billing->address.'<br>'.$billing->zip.' '.$billing->city.'<br>'.$billing->country->name.'</div>';
		        }
		        if (strpos($term, '{customer}') !== false) {
		            $term = str_replace('{customer}', $user_data, $term);
		        }
	    		return view('app_store.update_plan',compact('result','plans','user','term'));

	    }

	    public function getComment(){
	    		return Comment::where('cms_modul_id',$_REQUEST['app_id'])
	    		->with('user','replycomment','replycomment.user')
	    		->orderby('comments.id','desc')
	    		->get();

	    }


	    public function commentStore(){

	    	// return request();
	    		DB::table('comments')
	    		->insert([
	    			'comment'=>request()->comment,
	    			'cms_modul_id'=>request()->cms_modul_id,
	    			'cms_user_id'=>CRUDBooster::myId(),
	    			'status'=>0
	    		]);

	    		return response(['msg','Message Inserted']);

	    }


	    public function reply_comment_modal(){

	    	$result=Comment::where('id',$_REQUEST['id'])
	    		->first();
	    		return view('app_store.reply_modal',compact('result'));
	    }

	    public function replyCommentStore(){

	    		DB::table('reply_comments')
	    		->insert([
	    			'reply_comment'=>request()->reply_comment,
	    			'comment_id'=>request()->comment_id,
	    			'cms_user_id'=>CRUDBooster::myId()
	    		]);

	    		return response(['msg','Message Inserted']);

	    }

	    public function check_app_purchase(){

	    	$results=DB::table('purchase_apps')
	    		->select('purchase_apps.*')
		    	->where('purchase_apps.cms_user_id',CRUDBooster::myId())
		    	->where(function($query) {
	                            $query->where('purchase_apps.subscription_date_end','>=', date('Y-m-d'))
	                            	->orwhere('purchase_apps.subscription_life_time',1);
	                    })->get();

	    	if(count($results)>0){
	    		return view('app_store.check_app_purchase_modal',compact('results'));

	    	}else{
	    		DB::table('cms_users')->where('id',CRUDBooster::myId())->update(['status'=>null]);

	    		$me = CRUDBooster::me();
		        CRUDBooster::insertLog(trans("crudbooster.log_logout", ['email' => $me->email]),'','logout');

		        Session::flush();
		        setcookie('user_id',session('admin_id') , time()-1000);
		        setcookie('user_name',session('admin_name') , time()-1000);

		        return response(['message'=> 'logout']);
	    	}

	}

	public function account_close(){
		DB::table('cms_users')->where('id',CRUDBooster::myId())->update(['status'=>null]);
		$me = CRUDBooster::me();
        CRUDBooster::insertLog(trans("crudbooster.log_logout", ['email' => $me->email]),'','logout');

        Session::flush();
        setcookie('user_id',session('admin_id') , time()-1000);
        setcookie('user_name',session('admin_name') , time()-1000);

        return redirect()->route('getLogin')->with('message', trans("crudbooster.message_after_logout"));
	}

	public function appBuy(Request $request){
		$productName = DB::table('app_stores')->where('id',$request->app)->select('menu_name')->first();
		$freescout = false;
		$user = User::with('billing_detail')->find(CRUDBooster::myId());
		if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

		\Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
		 $token = $_POST['stripeToken']; // Token ID

		 // //DB::beginTransaction();
        try{

        	$app = DB::table('app_stores')->find(request()->app);
        	if($app->url == 'admin/inbox'){
				$this->installInbox();
        		$freescout = true;
        	}

		  $charge = \Stripe\Charge::create([
		       'amount' => $request->fixed_price * 100,
		       'currency' => 'eur',
		       'description' => $_POST['app_name'].' Purchase from Apps Store',
		       'source' => $token,
		   ]);

		    if($charge['status'] == "succeeded"){  // if success
		        DB::table('purchase_apps')->insert([
		          'price'=>$request->fixed_price,
		          'app_id'=>request()->app,
		          'type'=>'fixed price',
		          'stripe_subscription_id'=>$charge['id'],
		          'cms_user_id'=>CRUDBooster::myId(),
		          'status'=>'active',
		          'subscription_life_time'=>1
            ]);

		     //sendmail
			// $postdata=[
			// 	'app_name'=>$_POST['app_name'],
			// 	'price'=>$_POST['fixed_price'],
			// 	'subject'=>'Order confirmation of your DRM extension',
			// 	'freescout' => $freescout
			// ];
			// Mail::to($user->billing_detail->email)->send(new AppPurchaseConfirmation($postdata));
			$tags = [
	    		'app_name' => $_POST['app_name'],
	    		'app_price' => $_POST['fixed_price'],
	    		'subscription_interval' => 0,
	    		'period_start' => 0,
	    		'period_end' => 0,

	    		'INTERVAL' => false,
	    		'FIXED' => true,
	    		'FREESCOUT' => $freescout,
	    	];
			$slug = 'app_purchase_confirmation';
                $lang = getUserSavedLang($user->billing_detail->email);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                // Mail::to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));
				app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));

                // jahidulhasanzahid
			// $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
			// ->join('countries','countries.id','=','billing_details.country_id')
			// ->first();


			// $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";

			$taxShow = config('global.tax_for_invoice');
			$price = $request->fixed_price.'00';
			$total_tax = ($price * $taxShow) /100;
			$order_info = [
				'user_id' => 98,
            	'cms_client'  => CRUDBooster::myId(),
				'order_date'    => date('Y-m-d H:i:s'),
				'total' => round(($price),2),
				'sub_total' => round($price-$total_tax,2),
				'total_tax' => round($total_tax,2),
				'payment_type'  => "Stripe Card",
				'status'    => "Succeeded",
				'currency'  => "EUR",
				'adjustment'    => 0,
				'insert_type'   => 3,
				'shop_id'       => 8,
				'billing'   => userToBillingJson(CRUDBooster::myId()),
				'order_id_api'  => $charge['id'],
			];


			$carts = [];
			$cart_item = [];
			$cart_item['id'] = 1;
			$cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT',$productName->menu_name);
			$cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT','App Store Purchase Complete. App Name is "'.$productName->menu_name.'".Purchase Date '.date('Y-m-d H:i:s'));
			$cart_item['qty'] = 1;
			$cart_item['rate'] = round($price,2);
			$cart_item['tax'] = $taxShow;
			$cart_item['product_discount'] = 0;
			$cart_item['amount'] = round($price,2);
			$carts[] = $cart_item;
			$order_info['cart'] = json_encode($carts);

	        app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);

			// jahidulhasanzahid

		    }
		     // //DB::commit();    // Commiting  ==> There is no problem whatsoever
		    return response(['status'=>true, 'message'=>'App purchase success!']);
		    } catch (\Exception $e) {
		        // //DB::rollBack();   // rollbacking  ==> Something went wrong

		        return response(['status'=> false,'message'=>$e->getMessage()]);
		    }

	}


	public function testpayment(Request $request){

		$productName = DB::table('app_stores')->where('id',$request->app)->select('menu_name')->first();

		$freescout = false;
		$user = User::with('billing_detail')->find(CRUDBooster::myId());
		if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

	    \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
		$database_plan=DB::table('subscription_plans')->find($_POST['plan_id']);

	      // Add customer to stripe
		$customers = \Stripe\Customer::all(['email' => $user->email]);
		$cust=$customers->jsonSerialize();

		// //DB::beginTransaction();
    try{

        $app = DB::table('app_stores')->find(request()->app);
    	if($app->url == 'admin/inbox'){
			$this->installInbox();
    		$freescout = true;
    	}

		if(!empty($cust['data'])){
		    	$custId=$cust['data'][0]['id'];
		}else{

		    $customer = \Stripe\Customer::create(array(
		        'email' => $user->email,
		        'source'  => $_POST['stripeToken'],
		    ));
		    $custId=$customer->id;
		}
		// Convert price to cents
		$priceCents = ($database_plan->price*100);

		// Create a plan
	    $plans = \Stripe\Plan::all(["amount" => $priceCents,
	        "currency" => 'eur',
	        "interval" => $database_plan->interval]);
	    $plan=$plans->jsonSerialize();
	    if(!empty($plan['data'])) {
	    	$planId=$plan['data'][0]['id'];
	    }else{
	    	$plan = \Stripe\Plan::create(array(
	        "product" => [
	            "name" => $database_plan->name
	        ],
	        "amount" => $priceCents,
	        "currency" => 'eur',
	        "interval" => $database_plan->interval,
	        "interval_count" => 1
	      ));
	    	$planId=$plan->id;
	    }

	    if(empty($api_error) && $plan){
	        // Creates a new subscription
            $subscription = \Stripe\Subscription::create(array(
                "customer" => $custId,
                "items" => array(
                    array(
                        "plan" => $planId,
                    ),
                ),
            ));
	    }

		$subsData = $subscription->jsonSerialize();
		$subscrID = $subsData['id'];
	    $custID = $subsData['customer'];
	    $planID = $subsData['plan']['id'];
	    $planAmount = ($subsData['plan']['amount']/100);
	    $planCurrency = $subsData['plan']['currency'];
	    $planinterval = $subsData['plan']['interval'];
	    $planIntervalCount = $subsData['plan']['interval_count'];
	    $created = date("Y-m-d H:i:s", $subsData['created']);
	    $current_period_start = date("Y-m-d", $subsData['current_period_start']);
	    $current_period_end = date("Y-m-d", $subsData['current_period_end']);
	    $status = $subsData['status'];

        DB::table('purchase_apps')->updateOrInsert([
          'app_id'=>request()->app,'cms_user_id'=>CRUDBooster::myId()],
          [
          'price'=>$planAmount,
          'app_id'=>request()->app,
          'plan_id'=>request()->plan_id,
          'stripe_plan_id'=>$planID,
          'stripe_customer_id'=>$custID,
          'payer_email'=>$user->email,
          'type'=>$planinterval,
          'stripe_subscription_id'=>$subscrID,
          'status'=>$status,
          'subscription_date_start'=>$current_period_start,
          'subscription_date_end'=>$current_period_end
        ]);
		 // //DB::commit();    // Commiting  ==> There is no problem whatsoever
	    return response(['status'=>true, 'message'=>'App purchase success!']);
	    } catch (\Exception $e) {
	        // //DB::rollBack();   // rollbacking  ==> Something went wrong

	        return response(['status'=> false,'message'=>$e->getMessage()]);
	    }
    }

	public function installInbox()
	{
		try {
			$cmsuserInfo = DB::table('cms_users')->where('id',CRUDBooster::myId())->first();

			$database_name = str_replace(' ', '_', $cmsuserInfo->name);
			$db_name_lower = strtolower($database_name);
			$db_name = preg_replace("/[^A-Za-z0-9\-\']/", '', $db_name_lower);
			$apiRequest['id'] = $cmsuserInfo->id;
			$apiRequest['db_host'] = '************';
			$apiRequest['db_name'] = $db_name;
			$apiRequest['db_user'] = 'forge';
			$apiRequest['db_password'] = 'EVlesfB2hRbs5VE3657S';
			$apiRequest['db_port'] = '3306';
			$apiRequest['name'] = $cmsuserInfo->name;
			$apiRequest['email'] = $cmsuserInfo->email;
			$apiRequest['password'] = $cmsuserInfo->password;

			$url = "https://drm.network/api/install-freescout";
			$client = new Client();

			$response  = $client->post($url,  ['form_params'=>$apiRequest]);
			$result = json_decode($response->getBody()->getContents(), TRUE);

		} catch (\Exception $e) {
			CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
		}
	}

    public function freeTrial(){
    	$freescout = false;
    	//DB::beginTransaction();
    	try{
    		$user = User::find(CRUDBooster::myId());

    		if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

	    	    $app = DB::table('app_stores')->find(request()->app);
		    	if($app->url == 'admin/inbox'){
					$this->installInbox();
		    		$freescout = true;
		    	}

    		DB::table('purchase_apps')->updateOrInsert([
          		'app_id'=>request()->app,'cms_user_id'=>CRUDBooster::myId()
          		],
    			[
    			'price'=>0.00,
    			// 'app_id'=>request()->app,
    			'type'=>'Free Trail',
    			// 'cms_user_id'=>CRUDBooster::myId(),
    			'status'=>'active',
    			'is_free_trail'=>1,
    			'subscription_date_start'=> date('Y-m-d'),
    			'subscription_date_end'=>$this->subscription('free')
    		]);


			// jahidulhasanzahid
			// $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
			// ->join('countries','countries.id','=','billing_details.country_id')
			// ->first();
			// $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";
			$taxShow = config('global.tax_for_invoice');
			$price = 0.00;
			$total_tax = ($price * $taxShow) /100;
			$order_info = [
				'user_id' => 98,
            	'cms_client'  => CRUDBooster::myId(),
				'order_date'    => date('Y-m-d H:i:s'),
				'total' => round(($price),2),
				'sub_total' => round($price-$total_tax,2),
				'total_tax' => round($total_tax,2),
				'payment_type'  => "Free Trial",
				'status'    => "Succeeded",
				'currency'  => "EUR",
				'adjustment'    => 0,
				'insert_type'   => 3,
				'shop_id'       => 8,
				'billing'   => userToBillingJson(CRUDBooster::myId()),
				'order_id_api'  => "Free Trial",
			];


			    $carts = [];
			    $cart_item = [];
			    $cart_item['id'] = 1;
			    $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT',request()->app_name);
			    $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT','App Store Free Trial Complete. App Name is "'.request()->app_name.'".Purchase Date '.date('Y-m-d H:i:s').' Trails Interval '.date('Y-m-d').' to '.$this->subscription('free'));
			    $cart_item['qty'] = 1;
			    $cart_item['rate'] = round($price,2);
			    $cart_item['tax'] = $taxShow;
			    $cart_item['product_discount'] = 0;
			    $cart_item['amount'] = round($price,2);
			    $carts[] = $cart_item;
			    $order_info['cart'] = json_encode($carts);

			    app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
			// jahidulhasanzahid


    		$tags = [
	    		'app_name' => request()->app_name,
	    		'app_price' => 0,
	    		'subscription_interval' => 0,
	    		'period_start' => 0,
	    		'period_end' => 0,

	    		'INTERVAL' => false,
	    		'FIXED' => false,
	    		'FREESCOUT' => $freescout,
	    	];
    		$slug = 'app_purchase_confirmation';
            $lang = getUserSavedLang($user->billing_detail->email);
			$mail_data = DRMParseMailTemplate($tags, $slug, $lang);
    		// Mail::to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));
			app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));



   //  		$postdata=[
			// 	'app_name'=>request()->app_name,
			// 	'subject'=>'Order Trial confirmation of '.request()->app_name,
			// 	'freescout' => $freescout
			// ];
			// Mail::to($user->billing_detail->email)->send(new AppTrialConfirmation($postdata));


    		//DB::commit();
    		return response()->json([
    			'success' => true,
    			'message' => request()->app_name .'  App Get For Free Trail !'
    		]);
    	}catch (\Exception $e) {
    		//DB::rollBack();
    		return response()->json([
    			'success' => false,
    			'message' => $e->getMessage()
    		]);
    	}
    }


    public function appUpdate(){

		$user=User::with('billing_detail')->find(CRUDBooster::myId());
	    \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
		$database_plan=DB::table('subscription_plans')->find($_POST['plan_id']);
		$priceCents = ($database_plan->price*100);

		$purchase=DB::table('purchase_apps')->where('id',$_POST['id'])->whereNotNull('stripe_subscription_id')->first();

		$customers = \Stripe\Customer::all(['email' => $user->email]);
		$cust=$customers->jsonSerialize();
		//DB::beginTransaction();
    try{
    	// customer create
    	if(!empty($cust['data'])){
		    	$custId=$cust['data'][0]['id'];
		}else{

		    $customer = \Stripe\Customer::create(array(
		        'email' => $user->email,
		        'source'  => $_POST['stripeToken'],
		    ));
		    $custId=$customer->id;
		}
    	// plan create

	    $plans = \Stripe\Plan::all(["amount" => $priceCents,
	        "currency" => 'eur',
	        "interval" => $database_plan->interval]);
	    $plan=$plans->jsonSerialize();
	    if(!empty($plan['data'])) {
	    	$planId=$plan['data'][0]['id'];
	    }else{
	    	$plan = \Stripe\Plan::create(array(
	        "product" => [
	            "name" => $database_plan->name
	        ],
	        "amount" => $priceCents,
	        "currency" => 'eur',
	        "interval" => $database_plan->interval,
	        "interval_count" => 1
	      ));
	    	$planId=$plan->id;
	    }
	    // cancel subscription
	    if($purchase !=null){
	    	$old_subscription = \Stripe\Subscription::retrieve($purchase->stripe_subscription_id);
			if($old_subscription){
			$old_subscription->cancel();
			}
	    }


		 $data = \Stripe\Subscription::create(array(
            "customer" => $custId,
            "items" => array(
                array(
                    "plan" => $planId,
                ),
            ),
        ));

		$subsData = $data->jsonSerialize();
		$subscrID = $subsData['id'];
	    $custID = $subsData['customer'];
	    $planID = $subsData['plan']['id'];
	    $planAmount = ($subsData['plan']['amount']/100);
	    $planCurrency = $subsData['plan']['currency'];
	    $planinterval = $subsData['plan']['interval'];
	    $planIntervalCount = $subsData['plan']['interval_count'];
	    $created = date("Y-m-d H:i:s", $subsData['created']);
	    $current_period_start = date("Y-m-d", $subsData['current_period_start']);
	    $current_period_end = date("Y-m-d", $subsData['current_period_end']);
	    $status = $subsData['status'];

        DB::table('purchase_apps')->where('id',$_POST['id'])->update([
          'price'=>$planAmount,
          'stripe_subscription_id'=>$subscrID,
          'plan_id'=>request()->plan_id,
          'stripe_plan_id'=>$planID,
          'type'=>$planinterval,
          'status'=>$status,
          'subscription_date_start'=>$current_period_start,
          'subscription_date_end'=>$current_period_end
		]);
		//DB::commit();    // Commiting  ==> There is no problem whatsoever

	    return response(['status'=>true, 'message'=>'Update App Plan success!']);
	    } catch (\Exception $e) {
	        //DB::rollBack();   // rollbacking  ==> Something went wrong

	        return response(['status'=> false,'message'=>$e->getMessage()]);
	    }

	}


    public function subscription($type){

      $time = strtotime(date('Y-m-d'));

       return date("Y-m-d", strtotime("+14 days", $time));
    }



    public function cancelSubscription($id){
    	\Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
    	$data=DB::table('purchase_apps')
    			->join('app_stores','app_stores.id','=','purchase_apps.app_id')
    			->select('purchase_apps.subscription_date_end','purchase_apps.cms_user_id','purchase_apps.type','app_stores.menu_name','purchase_apps.stripe_subscription_id')
    			->where('purchase_apps.id',$id)
    			->first();
    	$user=User::with('billing_detail')->find($data->cms_user_id);
    	//DB::beginTransaction();
    	try{
    		$old=\Stripe\Subscription::retrieve($data->stripe_subscription_id);
    		if($old){
    		\Stripe\Subscription::update($data->stripe_subscription_id, [
			   'cancel_at_period_end' => true
			]);
    		}
    		DB::table('purchase_apps')->where('id',$id)->update(['is_renew_cancel'=>1]);

    		$tags = [
			    'app_name' =>  $data->menu_name,
			    'subscription_interval' =>  ucfirst($data->type),
			    'period_end' =>  $data->subscription_date_end,
			    'period_start' =>  date('Y-m-d'),
			];

			$slug = 'subscription_cancel'; //Page slug
            $lang = getUserSavedLang($user->billing_detail->email);
			$mail_data = DRMParseMailTemplate($tags, $slug, $lang); //Generated html
			// Mail::to($user->billing_detail->email)->send(new DRMSEndMail($mail_data)); //Send
			app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));


    		//DB::commit();    // Commiting  ==> There is no problem whatsoever

	    return response(['status'=>true, 'message'=>'Subscription Cancel Success!']);
	    } catch (\Exception $e) {
	        //DB::rollBack();   // rollbacking  ==> Something went wrong

	        return response(['status'=> false,'message'=>$e->getMessage()]);
	    }
    }





    public function insertDailyOrder(){
    	return 'Not used now!';
   //  	try{
			// $last_item = DB::table('order_sync_reports')->where('api_type', 'daily_insert')->orderBy('id', 'DESC')->first();
			// $last =  ($last_item->timestamp)? $last_item->timestamp : 0;

			// $timestamp =  \Carbon\Carbon::now()->unix();
			// return $this->insertDayliOrderData($last, $timestamp);
   //  	} catch (\Exception $e) {
	  //       return response(['status'=> false,'message'=>$e->getMessage()]);
	  //   }
    }


    // new webhook event

    public function webhookInvoice(){

    	if (app()->environment('development')) {
        $data = json_decode(request()->getContent(), true);

        if($data['type']=='invoice.payment_succeeded'){

        	$sub_id=$data['data']['object']['lines']['data'][0]['subscription'];
        	$price=($data['data']['object']['lines']['data'][0]['amount']/100);
		    $startDate=date("Y-m-d",$data['data']['object']['lines']['data'][0]['period']['start']);
		    $endDate= date("Y-m-d",$data['data']['object']['lines']['data'][0]['period']['end']);

        	$app=DB::table('purchase_apps')
        		->join('app_stores','app_stores.id','=','purchase_apps.app_id')
        		->where('stripe_subscription_id',$sub_id)
        		->select('purchase_apps.type','purchase_apps.cms_user_id','app_stores.menu_name')
        		->first();

        	$import=DB::table('purchase_import_plans')
        	 		->join('import_plans','import_plans.id','=','purchase_import_plans.import_plan_id')
        	 		->where('purchase_import_plans.stripe_subscription_id',$sub_id)
        	 		->select('purchase_import_plans.product_amount_import','import_plans.*')
        	 		->first();

        	 if($app !=null){
        	 	DB::table('purchase_apps')->where('stripe_subscription_id',$sub_id)
        	 	->update([
	              'subscription_date_start'=> $startDate,
	              'subscription_date_end'=>$endDate,
				]);


				$tags = [
		    		'app_name' => $app->menu_name,
		    		'app_price' => $price,
		    		'subscription_interval' => ucfirst($app->type),
		    		'period_start' => $startDate,
		    		'period_end' => $endDate,

		    		'INTERVAL' => true,
		    		'FIXED' => false,
		    		'FREESCOUT' => false,
				];

				// jahidulhasanzahid
				$result = DB::table('purchase_apps')
				->join('cms_users','purchase_apps.cms_user_id','=','cms_users.id')
				->join('billing_details','purchase_apps.cms_user_id','=','billing_details.user_id')
				->join('app_stores','purchase_apps.app_id','=','app_stores.id')
				->where('purchase_apps.stripe_subscription_id','=',$sub_id)
				->select('purchase_apps.*','billing_details.*','cms_users.name','app_stores.menu_name')
				->first();
				$appPrice = $result->price;$endDate = $result->subscription_date_end;$startDate = $result->subscription_date_start;$menuName = $result->menu_name;$companyName = $result->company_name;$billingAddress = $result->address;$billingZip = $result->zip;$billingCity = $result->city;$billingPhone = $result->phone;$userEmail = $result->email;$userName = $result->name;$countryID = $result->country_id;
				// $country = DB::table('countries')->where('id','=',$countryID)
				// ->select('name')
				// ->first();
				// $billingCountry = $country->name;
				// $detailsInformationForBilling = "<b>Company Name: $companyName</b></br><p>Address: $billingAddress,$billingCity,$billingZip,$billingCountry</p><p>Contact Information:</p><p>E-mail: $userEmail</p><p>Phone: $billingPhone</p>";
				$price = $appPrice;
				$taxShow = config('global.tax_for_invoice');
				$total_tax = ($price * $taxShow) /100;
				$order_info = [
					'user_id' => 98,
					'cms_client'  => $result->cms_user_id,
					'order_date'    => date('Y-m-d H:i:s'),
					'total' => round(($price),2),
                    'sub_total' => round($price-$total_tax,2),
                    'total_tax' => round($total_tax,2),
					'payment_type'  => "Stripe Card",
					'status'    => "Succeeded",
					'currency'  => "EUR",
					'adjustment'    => 0,
					'insert_type'   => 3,
					'shop_id'       => 8,
					'billing'   => userToBillingJson($result->cms_user_id),
					'order_id_api'  => $sub_id,
				];



				    $carts = [];
				    $cart_item = [];
				    $cart_item['id'] = 1;
				    $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT',$menuName);
				    $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT','App Purchase Complete from App Store. App Name is "'.$menuName.'".Subscription Start from '.$startDate.' to '.$endDate.'.Payment by Stripe.');
				    $cart_item['qty'] = 1;
				    $cart_item['rate'] = round($price,2);
				    $cart_item['tax'] = $taxShow;
				    $cart_item['product_discount'] = 0;
				    $cart_item['amount'] = round($price,2);
				    $carts[] = $cart_item;
				    $order_info['cart'] = json_encode($carts);

				    app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
				// jahidulhasanzahid

				$slug = 'app_purchase_confirmation';
                 $lang = getUserSavedLang($userEmail);
				$mail_data = DRMParseMailTemplate($tags, $slug, $lang);
				// Mail::to($userEmail)->send(new DRMSEndMail($mail_data));
				app('drm.mailer')->getMailer()->to($userEmail)->send(new DRMSEndMail($mail_data));


        	 }else if($import !=null){
        	 	DB::table('purchase_import_plans')->where('stripe_subscription_id',$sub_id)->update([
	              'start_date'=> $startDate,
	              'end_date'=>$endDate,
	              'product_amount_import'=>$import->product_amount_import +$import->product_amount
				]);

				$tags = [
		    		'app_name' => 'Import Plan '.$import->plan,
		    		'app_price' => $price,
		    		'subscription_interval' => ucfirst($import->interval),
		    		'period_start' => $startDate,
		    		'period_end' => $endDate,

		    		'INTERVAL' => true,
		    		'FIXED' => false,
		    		'FREESCOUT' => false,
				];

				// jahidulhasanzahid
				$importResult = DB::table('purchase_import_plans')
				->join('cms_users','purchase_import_plans.cms_user_id','=','cms_users.id')
				->join('billing_details','purchase_import_plans.cms_user_id','=','billing_details.user_id')
				->join('import_plans','purchase_import_plans.import_plan_id','=','import_plans.id')
				->where('purchase_import_plans.stripe_subscription_id','=',$sub_id)
				->select('purchase_import_plans.*','billing_details.*','cms_users.name','import_plans.plan','import_plans.interval')
				->first();
				$appPrice = $importResult->price;$endDate = $importResult->end_date;$startDate = $importResult->start_date;$menuName = $importResult->plan;$companyName = $importResult->company_name;$billingAddress = $importResult->address;$billingZip = $importResult->zip;$billingCity = $importResult->city;$billingPhone = $importResult->phone;$userEmail = $importResult->email;$userName = $importResult->name;$countryID = $importResult->country_id;
				// $country = DB::table('countries')->where('id','=',$countryID)
				// ->select('name')
				// ->first();
				// $billingCountry = $country->name;
				// $detailsInformationForBilling = "<b>Company Name: $companyName</b></br><p>Address: $billingAddress,$billingCity,$billingZip,$billingCountry</p><p>Contact Information:</p><p>E-mail: $userEmail</p><p>Phone: $billingPhone</p>";
				$price = $appPrice;
				$taxShow = config('global.tax_for_invoice');
				$total_tax = ($price * $taxShow) /100;
				$order_info = [
					'user_id' => 98,
					'cms_client'  => $importResult->cms_user_id,
					'order_date'    => date('Y-m-d H:i:s'),
					'total' => round(($price+$total_tax),2),
					'sub_total' => round($price,2),
					'total_tax' => round($total_tax,2),
					'payment_type'  => "Stripe Card",
					'status'    => "Succeeded",
					'currency'  => "EUR",
					'adjustment'    => 0,
					'insert_type'   => 3,
					'shop_id'       => 8,
					'billing'   => userToBillingJson($importResult->cms_user_id),
					'order_id_api'  => $sub_id,
				];

				$carts = [];
				    $cart_item = [];
				    $cart_item['id'] = 1;
				    $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT',$menuName);
				    $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT','Product Import Plan Purchase Complete. App Name is "'.$menuName.'".Subscription Start from '.$startDate.' to '.$endDate.'.Payment by Stripe.');
				    $cart_item['qty'] = 1;
				    $cart_item['rate'] = round($price,2);
				    $cart_item['tax'] = $taxShow;
				    $cart_item['product_discount'] = 0;
				    $cart_item['amount'] = round($price,2);
				    $carts[] = $cart_item;
				    $order_info['cart'] = json_encode($carts);

				app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
				// jahidulhasanzahid

				$slug = 'app_purchase_confirmation';
                 $lang = getUserSavedLang($userEmail);
				$mail_data = DRMParseMailTemplate($tags, $slug, $lang);
				app('drm.mailer')->getMailer()->to($userEmail)->send(new DRMSEndMail($mail_data));

			//email send end

        	 }
        }

        var_dump($data['data']['object']['lines']['data']);
        }
    }

}
