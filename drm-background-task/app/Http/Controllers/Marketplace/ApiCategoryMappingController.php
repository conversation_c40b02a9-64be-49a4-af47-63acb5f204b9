<?php

namespace App\Http\Controllers\Marketplace;

use App\Models\DrmCategory;
use Illuminate\Http\Request;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\Category;
use App\Models\Marketplace\ApiCategory;


class ApiCategoryMappingController extends Controller
{
    public function categoryAutoMapping($id,$api_id){
        
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $count = 0;
        $mapped_category = ApiCategory::where('id',$id)->where('api_id',$api_id)
                                        ->where('is_complete',1)
                                        ->select('id','api_id','api_category_id','mp_category_id','sync_status')
                                        ->first();

        if(isset($mapped_category)){

            $local_products = Product::with('core_products')
                                            ->where('api_id' , $mapped_category->api_id)
                                            ->where('api_category_id',$mapped_category->api_category_id)
                                            ->where('category_id','!=',$mapped_category->mp_category_id)
                                            ->get();

            if(count($local_products) > 0){
                $mp_category = Category::find($mapped_category->mp_category_id);

                foreach($local_products as $local_product){

                    $drm_products = $local_product->core_products;

                   if(count($drm_products) > 0){
                        foreach($drm_products as $drm_product){

                            $update_status = json_decode($drm_product->update_status, 1);
                            if(isset($update_status['category']) && $update_status['category'] == 1){
                                
                                $drmCategory = DrmCategory::where('user_id',$drm_product->user_id)
                                                        ->where('category_name_de', $mp_category->name)
                                                        ->first();

                                if (!$drmCategory) {
                                    $drmCategory = DrmCategory::create([
                                        'category_name_de' => $mp_category->name,
                                        'user_id'          => $drm_product->user_id,
                                        'country_id'       => 1,
                                    ]);
                                }
                                $updateableColumns['category'] = [$drmCategory->id];

                                app(\App\Services\DRMProductService::class)->update($drm_product->id,$updateableColumns);

                            }
                        }
                   }

                    $local_product->category_id = $mapped_category->mp_category_id;
                    $local_product->update();

                    $count++;
                }
            }

            $mapped_category->sync_status = 1;
            $mapped_category->update();

        }else{
            info('Auto sync not successfull');
        }
    }
}
