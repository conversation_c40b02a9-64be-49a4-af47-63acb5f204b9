<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use DigiStore24\DigiStoreApi;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use CRUDBooster;
use GuzzleHttp\Client;
class TowFactorController extends Controller
{

    public function verifyToken(){

        $data['email'] = Session::get('email');
        $data['pass'] = Session::get('password');

        return view('auth.twofactor')->with($data);
    }

    public function store(Request $request)
    {

        $request->validate([
            'two_factor_code' => 'integer|required',
        ]);
        
        $user = DB::table(config('crudbooster.USER_TABLE'))->where("two_factor_code", $_POST['two_factor_code'])->first();
        $expire_date = $user->two_factor_expires_at;
        $expire_time = strtotime($expire_date);
        $current_date = date('d-m-Y H:i:s');
        $current_time = strtotime($current_date);
        $time = $expire_time - $current_time;
        
        
        if($user!=null && $time > 0)
        {

            $this->setLogin($user);
            DB::table(config('crudbooster.USER_TABLE'))->where("two_factor_code", $_POST['two_factor_code'])->update(['two_factor_code'=>null]);
            return redirect(CRUDBooster::adminPath());

            
        }else{
        return redirect()->back()->withErrors(['two_factor_code' => 'The two factor code you have entered does not match']);
            
        }

    }



    public function setLogin($users){
        
        $priv = DB::table("cms_privileges")->where("id", $users->id_cms_privileges)->first();
            $roles = DB::table('cms_privileges_roles')->where('id_cms_privileges', $users->id_cms_privileges)->join('cms_moduls', 'cms_moduls.id', '=', 'id_cms_moduls')->select('cms_moduls.name', 'cms_moduls.path', 'is_visible', 'is_create', 'is_read', 'is_edit', 'is_delete')->get();
            $photo = ($users->photo) ? asset($users->photo) : asset('vendor/crudbooster/avatar.jpg');
            Session::put('admin_is_superadmin', $priv->is_superadmin);
            Session::put('admin_id', $users->id);
            Session::put('admin_is_superadmin', $priv->is_superadmin);
            Session::put('admin_is_developer', $priv->is_dev);
            Session::put('admin_name', $users->name);
            Session::put('admin_photo', $photo);
            Session::put('admin_privileges_roles', $roles);
            Session::put("admin_privileges", $users->id_cms_privileges);
            Session::put('admin_privileges_name', $priv->name);
            Session::put('admin_lock', 0);
            Session::put('theme_color', $priv->theme_color);
            Session::put("appname", CRUDBooster::getSetting('appname'));
            CRUDBooster::insertLog(trans("crudbooster.log_login", ['email' => $users->email, 'ip' => request()->server('REMOTE_ADDR')]),'', 'login');
            $cb_hook_session = new \App\Http\Controllers\CBHook;
            $cb_hook_session->afterLogin();
            return true;
            
    }


    public function forgetPassword($id){
        $forget_password_token = $id;
        return view('auth.password-change',compact('forget_password_token'));
    }

    public function passwordChange(Request $request){

        // \Hash::make($rand_string);
        $password = $_POST['password'];
        $hashPassword = \Hash::make($password);
        // dd($password);

        $user = DB::table(config('crudbooster.USER_TABLE'))->where("forget_password_token", $_POST['forget_password_token'])->first();

        if($user!=null)
        {

            $this->setLogin($user);
            DB::table(config('crudbooster.USER_TABLE'))->where("forget_password_token", $_POST['forget_password_token'])->update([
                'forget_password_token'=>null,
                'password' => $hashPassword,
                ]);

                try {
                    $cmsuserInfo = $user;
                
                    $apiRequest['id'] = $cmsuserInfo->id;
                    $apiRequest['db_host'] = '************';
                    $apiRequest['db_user'] = 'forge';
                    $apiRequest['db_password'] = 'EVlesfB2hRbs5VE3657S';
                    $apiRequest['db_port'] = '3306';
                    $apiRequest['name'] = $cmsuserInfo->name;
                    $apiRequest['email'] = $cmsuserInfo->email;
                    $apiRequest['password'] = $hashPassword;
                
                    $url = "https://drm.network/api/forget-password";
                    $client = new Client();
                
                    $response  = $client->post($url,  ['form_params'=>$apiRequest]);
                    $result = json_decode($response->getBody()->getContents(), TRUE);
                
                  } catch (\Exception $e) {}
            return redirect()->route('getLogin');
        }else{
            
            return redirect()->back()->withErrors(['two_factor_code' => 'The two factor code you have entered does not match']);
            
        }

    }
    
    public function twofactorlogin(Request $request){
        DB::table(config('crudbooster.USER_TABLE'))->where("id", $_POST['user_id'])->update([
            'two_fa_status'=>$_POST['status'],
            ]);
        
            return CRUDBooster::redirectBack("Two Factor Login is Update!", "success");
         
    }

}
