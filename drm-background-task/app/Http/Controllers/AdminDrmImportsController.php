<?php namespace App\Http\Controllers;
	use Session;
	use Request;
	use DB;
	use CB;
	use CRUDBooster;
	use App\Jobs\ProcessTranslation;
	use App\Jobs\ImportToGambio;
	use App\User;
	use App\CsvHeader;
	use Illuminate\Support\Facades\Schema;
	use DigiStore24\DigiStoreApi;
	use App\Helper\LengowApi;
	use App\Helper\AppStore;
	use App\DeliveryCompany;
	use App\DrmImport;
	use App\DrmProduct;
	use App\TmpDrmProduct;
	use App\DrmProductField;
	use App\DrmProductProfitMargin;
	use App\UserCsvHeaderValue;
	use Illuminate\Support\Facades\Storage;
	use Illuminate\Support\Str;
	use PhpOffice\PhpSpreadsheet\IOFactory;
	use DataTables as DataTables;
	use App\DrmProductCountryVat;
	use App\ProductCategory;
	use Carbon\Carbon;
	use XMLReader;
	use SimpleXMLElement;
	use App\Events\ProgressEvent;
	use App\Events\ManualUpdateEvent;
	use Illuminate\Pagination\LengthAwarePaginator;
	use App\Notifications\DRMNotification;
	use App\Events\SyncProgressEvent;
	use Illuminate\Support\LazyCollection;
	use App\DrmImportTemplate;
	use App\Jobs\ProcessSyncImport;
	use Illuminate\Support\Facades\Cache;
	use Illuminate\Support\Facades\Redis;
	use App\Http\Controllers\AdminManualImportTarrifController;
	use App\Services\DateTime\DateTime;
	use App\Http\Controllers\AdminDrmExportsController;
	use App\Jobs\UpdateProductBundle;
	use App\Enums\DroptiendaPlan;
	
	class AdminDrmImportsController extends \crocodicstudio\crudbooster\controllers\CBController {

			public $all_translation = [];
			public $all_products = [];
			public $update_status_all = [];
			public $import_categories = [];
			public $table;
			public $insertArray = [];
			public $existing_products = [];
			public $profit_margins;
			public $calc_config;
			public $all_categories;
			public $fallback;
			public $errors;
			public $ean_field;
			public $ean;
			public $deleted_products = [];
			public $filter = [];
			public $import_check;
			public $count;
			public $import;
			public $image_changed = [];
			public $changed_products = [];

	    public function cbInit(){
			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "csv_file_name";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = false;
			$this->button_edit = false;
			$this->button_delete = false;
			$this->button_detail = true;
			$this->button_show = false;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "drm_imports";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Supplier","name"=>"delivery_company_id","join"=>"delivery_companies,name"];
			$this->col[] = ["label"=>" Name","name"=>"csv_file_name"];
			$this->col[] = ["label"=>"Country","name"=>"country_id","join"=>"countries,name"];

			$this->col[] = ["label"=>"File Type","name"=>"type"];
			$this->col[] = ["label"=>"Download Feed","name"=>"csv_file_path"];

			$this->col[] = ["label"=>"Status","name"=>"import_finished"];
			$this->col[] = ["label"=>"Last Sync","name"=>"id"];
			$this->col[] = ["label"=>"Import Time","name"=>"created_at"];
			$this->col[] = ["label"=>"Total Products","name"=>"(SELECT COUNT(*) FROM drm_products WHERE drm_import_id = drm_imports.id) as total"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			// $this->form[] = ['label'=>'Delivery Company Id','name'=>'delivery_company_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'delivery_companies,id'];
			// $this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'user_csv_header_values,id'];
			// $this->form[] = ['label'=>'Csv File Name','name'=>'csv_file_name','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Country Id','name'=>'country_id','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'countries,id'];
			// $this->form[] = ['label'=>'Delimiter','name'=>'delimiter','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Language Id','name'=>'language_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'languages,id'];
			// $this->form[] = ['label'=>'Delivery Time','name'=>'delivery_time','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Csv File Path','name'=>'csv_file_path','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Max Price','name'=>'max_price','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Min Price','name'=>'min_price','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'File Url','name'=>'file_url','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Type','name'=>'type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Csv Headers','name'=>'csv_headers','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Demo Data','name'=>'demo_data','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Fielter Min','name'=>'fielter_min','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Fielter Max','name'=>'fielter_max','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Fielter Names','name'=>'fielter_names','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Fielter Categories','name'=>'fielter_categories','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Overwrite','name'=>'overwrite','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Import Finished','name'=>'import_finished','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

	        $this->sub_module = array();
	        $this->addaction = array();
					$this->addaction[] = ['url'=>'[id]'];
	        $this->button_selected = array();
	        $this->alert        = array();
	        $this->index_button = array();
	        $this->table_row_color = array();
	        $this->index_statistic = array();
	        $this->script_js = NULL;
	        $this->pre_index_html = null;
	        $this->post_index_html = null;
	        $this->load_js = array();

	        $this->style_css = "
                .button_action>a {
                    margin-bottom: 5px;
                }
	        ";
	        $this->load_css = array();


	    }

	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here
	    }

	    public function hook_query_index(&$query) {
	       if(!CRUDBooster::isSuperadmin()){
	     	 $user_id=CRUDBooster::myId();
	         $query->where("drm_imports.user_id",$user_id);
	         //$query->where("drm_imports.import_finished",'1');
	        }

					$query->where("drm_imports.import_finished",1);

	        if(isset($_GET['filter'])){
	            $query->where("drm_imports.delivery_company_id",$_GET['filter']);
	        }

	    }

	    public function hook_row_index($column_index,&$column_value) {
				if($column_index == 10){
					$explode = explode("title='' onclick='' href=",$column_value);
					$explode_again = explode("target='_self'><i class=''></i> </a>&nbsp;",$explode[1]);
					$id = $explode_again[0];

					$str = str_replace("'","",$id);
					$str = str_replace(" ","",$str);
					$id = (int)$str;

					$import = DB::table('drm_imports')->where('id',$id)->first()->status;
                    $site_url = url('');

					$function = "modal($id)";
					$template_btn = "";
					$template_purchased = AppStore::ActiveFeature('Produkt-Template');
					// if (app()->environment('development')) {
					// 	$template_purchased = true;
					// }
					if($template_purchased){
						$template_btn = "&nbsp;
							<a class='btn btn-xs btn-warning' title='Product Template' href=".CRUDBooster::mainpath('product-template/').$id." target='_self'><i class='fa fa-sync'></i> Product Template</a>&nbsp;";
					}
                    $type = DB::table('drm_imports')->where('id',$id)->first()->type;

                    if($type == 'url'){
                        $column_value = "<div class='button_action' style='text-align:right'>
												<a class='btn btn-xs btn-primary' title='Manange Filter'
											href=".CRUDBooster::mainpath('update-csv?id='.$id)."><i class='fa fa-edit'></i> Customize Filters</a>&nbsp;
												$template_btn
                        <a class='btn btn-xs btn-primary btn-detail' title='Update CSV URL'
        	            href=".CRUDBooster::mainpath('update-csv?id='.$id)."><i class='fa fa-edit'></i> Update CSV URL</a>&nbsp;
                        <a class='btn btn-xs btn-success' title='Sync CSV' onclick='' href='$site_url/admin/drm-sync-import/$id' target='_self'><i class='fa fa-sync'></i> Sync CSV</a>&nbsp;    <a class='btn btn-xs btn-warning' title='Delete'
					    onclick=$function;
        	            href='#' onclick= $function ><i class='fa fa-trash'></i></a>
        	            </div>";
                    }
                    else{
                        $column_value = "<div class='button_action' style='text-align:right'>$template_btn
												<a class='btn btn-xs btn-primary btn-detail' title='Manual Update CSV'
        	            href=".CRUDBooster::mainpath('manual-update-csv?id=').$id."><i class='fa fa-edit'></i> Manual Update CSV</a>&nbsp;<a class='btn btn-xs btn-warning' title='Delete'
					    onclick=$function;
        	            href='#' onclick= $function ><i class='fa fa-trash'></i></a>
        	            </div>";
                    }

			}


				// <a class='btn btn-xs btn-success' title='Translation' onclick='' href='$site_url/admin/drm_imports/do-translation/$id' target='_self'><i class='fa fa-check'></i> Translation</a>&nbsp;

// 			if($column_index == 5){
// 			    $import = DB::table('')
// 			}

			if($column_index == 7){
			    $sync_time = DB::table('drm_import_sync')->where('drm_import_id',$column_value)->orderBy('id','desc')->first();
			    $column_value = $sync_time->created_at;
			}

			if($column_index == 5){
			    $column_value = Storage::disk('spaces')->url($column_value);
					$html = '<a class="btn btn-xs btn-primary" href="'.$column_value.'?download=1" target="_blank" title="Download File"><i class="fa fa-download"></i> Download</a>';
					$column_value = $html;
			}

	    }

	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    public function hook_after_add($id) {
	        //Your code here

	    }

	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    public function hook_after_delete($id) {
	        //Your code here

	    }


			public function getIndex(){
	        $this->cbLoader();

	        $module = CRUDBooster::getCurrentModule();

	        if (! CRUDBooster::isView() && $this->global_privilege == false) {
	            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
	            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	        }

	        if (Request::get('parent_table')) {
	            $parentTablePK = CB::pk(g('parent_table'));
	            $data['parent_table'] = DB::table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
	            if (Request::get('foreign_key')) {
	                $data['parent_field'] = Request::get('foreign_key');
	            } else {
	                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
	            }

	            if ($parent_field) {
	                foreach ($this->columns_table as $i => $col) {
	                    if ($col['name'] == $parent_field) {
	                        unset($this->columns_table[$i]);
	                    }
	                }
	            }
	        }

	        $data['table'] = $this->table;
	        $data['table_pk'] = CB::pk($this->table);
	        $data['page_title'] = $module->name;
	        $data['page_description'] = trans('crudbooster.default_module_description');
	        $data['date_candidate'] = $this->date_candidate;
	        $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

	        $tablePK = $data['table_pk'];
	        $table_columns = CB::getTableColumns($this->table);
	        $result = DB::table($this->table)->select(DB::raw($this->table.".".$this->primary_key));

	        if (Request::get('parent_id')) {
	            $table_parent = $this->table;
	            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
	            $result->where($table_parent.'.'.Request::get('foreign_key'), Request::get('parent_id'));
	        }

	        $this->hook_query_index($result);

	        if (in_array('deleted_at', $table_columns)) {
	            $result->where($this->table.'.deleted_at', null);
	        }

	        $alias = [];
	        $join_alias_count = 0;
	        $join_table_temp = [];
	        $table = $this->table;
	        $columns_table = $this->columns_table;
	        foreach ($columns_table as $index => $coltab) {

	            $join = @$coltab['join'];
	            $join_where = @$coltab['join_where'];
	            $join_id = @$coltab['join_id'];
	            $field = @$coltab['name'];
	            $join_table_temp[] = $table;

	            if (! $field) {
	                continue;
	            }

	            if (strpos($field, ' as ') !== false) {
	                $field = substr($field, strpos($field, ' as ') + 4);
	                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
	                $result->addselect(DB::raw($coltab['name']));
	                $columns_table[$index]['type_data'] = 'varchar';
	                $columns_table[$index]['field'] = $field;
	                $columns_table[$index]['field_raw'] = $field;
	                $columns_table[$index]['field_with'] = $field_with;
	                $columns_table[$index]['is_subquery'] = true;
	                continue;
	            }

	            if (strpos($field, '.') !== false) {
	                $result->addselect($field);
	            } else {
	                $result->addselect($table.'.'.$field);
	            }

	            $field_array = explode('.', $field);

	            if (isset($field_array[1])) {
	                $field = $field_array[1];
	                $table = $field_array[0];
	            } else {
	                $table = $this->table;
	            }

	            if ($join) {

	                $join_exp = explode(',', $join);

	                $join_table = $join_exp[0];
	                $joinTablePK = CB::pk($join_table);
	                $join_column = $join_exp[1];
	                $join_alias = str_replace(".", "_", $join_table);

	                if (in_array($join_table, $join_table_temp)) {
	                    $join_alias_count += 1;
	                    $join_alias = $join_table.$join_alias_count;
	                }
	                $join_table_temp[] = $join_table;

	                $result->leftjoin($join_table.' as '.$join_alias, $join_alias.(($join_id) ? '.'.$join_id : '.'.$joinTablePK), '=', DB::raw($table.'.'.$field.(($join_where) ? ' AND '.$join_where.' ' : '')));
	                $result->addselect($join_alias.'.'.$join_column.' as '.$join_alias.'_'.$join_column);

	                $join_table_columns = CRUDBooster::getTableColumns($join_table);
	                if ($join_table_columns) {
	                    foreach ($join_table_columns as $jtc) {
	                        $result->addselect($join_alias.'.'.$jtc.' as '.$join_alias.'_'.$jtc);
	                    }
	                }

	                $alias[] = $join_alias;
	                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
	                $columns_table[$index]['field'] = $join_alias.'_'.$join_column;
	                $columns_table[$index]['field_with'] = $join_alias.'.'.$join_column;
	                $columns_table[$index]['field_raw'] = $join_column;

	                @$join_table1 = $join_exp[2];
	                @$joinTable1PK = CB::pk($join_table1);
	                @$join_column1 = $join_exp[3];
	                @$join_alias1 = $join_table1;

	                if ($join_table1 && $join_column1) {

	                    if (in_array($join_table1, $join_table_temp)) {
	                        $join_alias_count += 1;
	                        $join_alias1 = $join_table1.$join_alias_count;
	                    }

	                    $join_table_temp[] = $join_table1;

	                    $result->leftjoin($join_table1.' as '.$join_alias1, $join_alias1.'.'.$joinTable1PK, '=', $join_alias.'.'.$join_column);
	                    $result->addselect($join_alias1.'.'.$join_column1.' as '.$join_column1.'_'.$join_alias1);
	                    $alias[] = $join_alias1;
	                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
	                    $columns_table[$index]['field'] = $join_column1.'_'.$join_alias1;
	                    $columns_table[$index]['field_with'] = $join_alias1.'.'.$join_column1;
	                    $columns_table[$index]['field_raw'] = $join_column1;
	                }
	            } else {

	                if(isset($field_array[1])) {
	                    $result->addselect($table.'.'.$field.' as '.$table.'_'.$field);
	                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
	                    $columns_table[$index]['field'] = $table.'_'.$field;
	                    $columns_table[$index]['field_raw'] = $table.'.'.$field;
	                }else{
	                    $result->addselect($table.'.'.$field);
	                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
	                    $columns_table[$index]['field'] = $field;
	                    $columns_table[$index]['field_raw'] = $field;
	                }

	                $columns_table[$index]['field_with'] = $table.'.'.$field;
	            }
	        }

	        if (Request::get('q')) {
	            $result->where(function ($w) use ($columns_table, $request) {
	                foreach ($columns_table as $col) {
	                    if (! $col['field_with']) {
	                        continue;
	                    }
	                    if ($col['is_subquery']) {
	                        continue;
	                    }
	                    $w->orwhere($col['field_with'], "like", "%".Request::get("q")."%");
	                }
	            });
	        }

	        if (Request::get('where')) {
	            foreach (Request::get('where') as $k => $v) {
	                $result->where($table.'.'.$k, $v);
	            }
	        }

	        $filter_is_orderby = false;
	        if (Request::get('filter_column')) {

	            $filter_column = Request::get('filter_column');
	            $result->where(function ($w) use ($filter_column, $fc) {
	                foreach ($filter_column as $key => $fc) {

	                    $value = @$fc['value'];
	                    $type = @$fc['type'];

	                    if ($type == 'empty') {
	                        $w->whereNull($key)->orWhere($key, '');
	                        continue;
	                    }

	                    if ($value == '' || $type == '') {
	                        continue;
	                    }

	                    if ($type == 'between') {
	                        continue;
	                    }

	                    switch ($type) {
	                        default:
	                            if ($key && $type && $value) {
	                                $w->where($key, $type, $value);
	                            }
	                            break;
	                        case 'like':
	                        case 'not like':
	                            $value = '%'.$value.'%';
	                            if ($key && $type && $value) {
	                                $w->where($key, $type, $value);
	                            }
	                            break;
	                        case 'in':
	                        case 'not in':
	                            if ($value) {
	                                $value = explode(',', $value);
	                                if ($key && $value) {
	                                    $w->whereIn($key, $value);
	                                }
	                            }
	                            break;
	                    }
	                }
	            });

	            foreach ($filter_column as $key => $fc) {
	                $value = @$fc['value'];
	                $type = @$fc['type'];
	                $sorting = @$fc['sorting'];

	                if ($sorting != '') {
	                    if ($key) {
	                        $result->orderby($key, $sorting);
	                        $filter_is_orderby = true;
	                    }
	                }

	                if ($type == 'between') {
	                    if ($key && $value) {
	                        $result->whereBetween($key, $value);
	                    }
	                } else {
	                    continue;
	                }
	            }
	        }

	        if ($filter_is_orderby == true) {
	            $data['result'] = $result->paginate($limit);
	        } else {
	            if ($this->orderby) {
	                if (is_array($this->orderby)) {
	                    foreach ($this->orderby as $k => $v) {
	                        if (strpos($k, '.') !== false) {
	                            $orderby_table = explode(".", $k)[0];
	                            $k = explode(".", $k)[1];
	                        } else {
	                            $orderby_table = $this->table;
	                        }
	                        $result->orderby($orderby_table.'.'.$k, $v);
	                    }
	                } else {
	                    $this->orderby = explode(";", $this->orderby);
	                    foreach ($this->orderby as $o) {
	                        $o = explode(",", $o);
	                        $k = $o[0];
	                        $v = $o[1];
	                        if (strpos($k, '.') !== false) {
	                            $orderby_table = explode(".", $k)[0];
	                        } else {
	                            $orderby_table = $this->table;
	                        }
	                        $result->orderby($orderby_table.'.'.$k, $v);
	                    }
	                }
	                $data['result'] = $result->paginate($limit);
	            } else {
	                $data['result'] = $result->orderby($this->table.'.'.$this->primary_key, 'desc')->paginate($limit);
	            }
	        }

	        $data['columns'] = $columns_table;

	        if ($this->index_return) {
	            return $data;
	        }

	        //LISTING INDEX HTML
	        $addaction = $this->data['addaction'];

	        if ($this->sub_module) {
	            foreach ($this->sub_module as $s) {
	                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
	                $addaction[] = [
	                    'label' => $s['label'],
	                    'icon' => $s['button_icon'],
	                    'url' => CRUDBooster::adminPath($s['path']).'?return_url='.urlencode(Request::fullUrl()).'&parent_table='.$table_parent.'&parent_columns='.$s['parent_columns'].'&parent_columns_alias='.$s['parent_columns_alias'].'&parent_id=['.(! isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']).']&foreign_key='.$s['foreign_key'].'&label='.urlencode($s['label']),
	                    'color' => $s['button_color'],
	                    'showIf' => $s['showIf'],
	                ];
	            }
	        }

	        $mainpath = CRUDBooster::mainpath();
	        $orig_mainpath = $this->data['mainpath'];
	        $title_field = $this->title_field;
	        $html_contents = [];
	        $page = (Request::get('page')) ? Request::get('page') : 1;
	        $number = ($page - 1) * $limit + 1;
	        foreach ($data['result'] as $row) {
	            $html_content = [];

	            if ($this->button_bulk_action) {

	                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='".$row->{$tablePK}."'/>";
	            }

	            if ($this->show_numbering) {
	                $html_content[] = $number.'. ';
	                $number++;
	            }

	            foreach ($columns_table as $col) {
	                if ($col['visible'] === false) {
	                    continue;
	                }

	                $value = @$row->{$col['field']};
	                $title = @$row->{$this->title_field};
	                $label = $col['label'];

	                if (isset($col['image'])) {
	                    if ($value == '') {
	                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='".asset('vendor/crudbooster/avatar.jpg')."'><img width='40px' height='40px' src='".asset('vendor/crudbooster/avatar.jpg')."'/></a>";
	                    } else {
	                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
	                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='".$pic."'><img width='40px' height='40px' src='".$pic."'/></a>";
	                    }
	                }

	                if (@$col['download']) {
	                    header("Content-Type: application/force-download");
	                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value).'?download=1';
	                    if ($value) {
	                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
	                    } else {
	                        $value = " - ";
	                    }
	                }

	                if ($col['str_limit']) {
	                    $value = trim(strip_tags($value));
	                    $value = Str::limit($value, $col['str_limit']);
	                }

	                if ($col['nl2br']) {
	                    $value = nl2br($value);
	                }

	                if ($col['callback_php']) {
	                    foreach ($row as $k => $v) {
	                        $col['callback_php'] = str_replace("[".$k."]", $v, $col['callback_php']);
	                    }
	                    @eval("\$value = ".$col['callback_php'].";");
	                }

	                //New method for callback
	                if (isset($col['callback'])) {
	                    $value = call_user_func($col['callback'], $row);
	                }

	                $datavalue = @unserialize($value);
	                if ($datavalue !== false) {
	                    if ($datavalue) {
	                        $prevalue = [];
	                        foreach ($datavalue as $d) {
	                            if ($d['label']) {
	                                $prevalue[] = $d['label'];
	                            }
	                        }
	                        if ($prevalue && count($prevalue)) {
	                            $value = implode(", ", $prevalue);
	                        }
	                    }
	                }

	                $html_content[] = $value;
	            } //end foreach columns_table

	            if ($this->button_table_action):
	                $button_action_style = $this->button_action_style;
	                $html_content[] = "<div class='button_action' style='text-align:right'>".view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render()."</div>";

	            endif;//button_table_action

	            foreach ($html_content as $i => $v) {
	                $this->hook_row_index($i, $v);
	                $html_content[$i] = $v;
	            }

	            $html_contents[] = $html_content;
	        } //end foreach data[result]

	        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

	        $data['html_contents'] = $html_contents;

	        return view("admin.crudbooster.followup_index", $data);
	    }


	    public function getDoTranslation($id) {

              //Create an Auth
            if(!CRUDBooster::isCreate() && $this->global_privilege==FALSE || $this->button_add==FALSE) {
                CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
            }

            $userLang = DB::table('drm_imports')->select('language_id')->where('id',$id)->first();

            $products=DB::table('drm_products')->where('drm_import_id',$id)->get();
            $data['products'] = $products;
            $charecter = 0;

            foreach($products as $product){

                $name=$product->name;
                $description=$product->description;
                $total=strlen($name)+strlen(($description));
                $charecter += $total;
            }

            $data = [];
            $data['page_title'] = 'Page Translation';
            $data['id'] = $id;
            $data['default_lang'] = DB::table('languages')->where('id', $userLang->language_id)->first();
            $data['languages'] = DB::table('languages')->where('id','<>', $userLang->language_id)->get();

            $data['charecter']=$charecter;

            //Please use cbView method instead view method from laravel
            $this->cbView('admin.drm.translation',$data);
        }

        public function getManageShop($id) {

              //Create an Auth
              if(!CRUDBooster::isCreate() && $this->global_privilege==FALSE || $this->button_add==FALSE) {
                CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
              }

              $data = [];
              $data['page_title'] = 'Manage Shop';
              $shops=\App\Shop::where('user_id',$user_id=CRUDBooster::myId())->get();
              $languages=DB::table('languages')->get();
              $data['languages']=$languages;
              $data['shops']=$shops;
              $data['import_id']=$id;

              //Please use cbView method instead view method from laravel
              $this->cbView('admin.drm.manageshop',$data);
        }

        public function test()
        {
            $h = 'hello';
            return $h;
        }

        public function postProcessTranslation() {

			$id = $_REQUEST['id'];
			$token = $_REQUEST['_token'];
			$source_lang = $_REQUEST['source_lang'];
			$language_code = $_REQUEST['language'];


			$ProcessTranslation = new ProcessTranslation($id, $source_lang, $language_code);
			$ProcessTranslation->handle();

            //Create an Auth
            if(!CRUDBooster::isCreate() && $this->global_privilege==FALSE || $this->button_add==FALSE) {
                CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
            }

            CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'),"Translation Complete!","success");

//          $data = [];
//          $data['page_title'] = 'Page Translation';
//          $data['id'] = $id;
// 			$data['token'] = $token;
// 			$data['code'] = $language_code;

//             //Please use cbView method instead view method from laravel
//             $this->cbView('admin.drm.process_translation',$data);
        }



        public function postUserShop() {

              //Create an Auth
              if(!CRUDBooster::isCreate() && $this->global_privilege==FALSE || $this->button_add==FALSE) {
                CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
              }

              $data = [];
              $data['page_title'] = 'Page Translation';
              $shop_id = $_REQUEST['shop_id'];
              $langarray =$_REQUEST['lan_id'];
              $import_id = $_REQUEST['import_id'];
             // $import_id=100;

    foreach($shop_id as $shop) {
    $shop_details=\App\Shop::where('id',$shop)->first();
    $user=$shop_details->username;
    $pass=$shop_details->password;
    $base_url=$shop_details->url;
    $api_path="api.php/v2/";
    $auth = base64_encode("$user:$pass");




         $products=DB::table('drm_products')
         ->leftjoin('shop_products','drm_products.ean','=','shop_products.ean')
         //->where('shop_products.shop_id','!=',$shop)
        ->whereNull('shop_products.ean')
        ->where('drm_products.drm_import_id',$import_id)
        ->select('drm_products.*','shop_products.*','shop_products.ean as sean','drm_products.id')->orderBy('drm_products.id', 'desc');




        // $products = DB::table("drm_products")->select('*')
        //             ->whereNOTIn('drm_products.ean',function($query) use ($shop){
        //               $query->select('shop_products.ean')->from('shop_products')->where('shop_id',$shop);
        //             })
        //             ->where('drm_import_id',$import_id)->orderBy('drm_products.id', 'desc');
        $products->chunk(200, function($rows) use ($auth,$base_url,$shop_details,$langarray,$import_id) {
           $ids=$rows->pluck('id')->toarray();
           ImportToGambio::dispatch($ids,$auth,$base_url,$shop_details,$langarray,$import_id);
        });

        echo "done";

    }

              //Please use cbView method instead view method from laravel
            //  $this->cbView('admin.drm.process_usershop',$data);
       }

    public function getContinueImport(){
			$incomplete_imports = DB::table('drm_imports')->where('user_id',CRUDBooster::myId())->where('import_finished','<>','1')->get();
			return view('admin.drm_import.incomplete_imports',compact('incomplete_imports'));
		}


    public function deleteImport($id){
        $this->importDeleteProcess($id);
        return redirect()->back();
    }

		public function importDeleteProcess($id){
				$import = DrmImport::find($id);
				$country = DB::table('countries')->where('id',$import->country_id)->first()->language_shortcode;
				$table = "drm_translation_".$country;
		    DB::table('drm_imports')->where('id',$id)->delete();
		    DB::table('drm_product_profit_margins')->where('drm_import_id',$id)->delete();
		    DB::table('drm_import_errors')->where('drm_import_id',$id)->delete();
		    DB::table('drm_import_filter')->where('drm_import_id',$id)->delete();
		    DB::table('drm_import_sync')->where('drm_import_id',$id)->delete();
		    $products = DB::table('drm_products')->where('drm_import_id',$id)->pluck('id')->toArray();
		    DB::table('drm_products')->where('drm_import_id',$id)->delete();
		    DB::table('drm_category')->where('drm_import_id',$id)->delete();
		    DB::table('drm_product_fields')->where('drm_import_id',$id)->delete();
		    DB::table('tmp_drm_products')->where('drm_import_id',$id)->delete();
				$chunks = array_chunk($products,2000);
				foreach($chunks as $chunk){
					DB::table($table)->whereIn('product_id',$chunk)->delete();
					if($id == 1){
						DB::table("drm_translation_en")->whereIn('product_id',$chunk)->delete();
					}
					DB::table('drm_product_categories')->whereIn('product_id',$chunk)->delete();
				}
		}


		public function getManualUpdateCsv(){
			$import_id = $_REQUEST['id'];
			$user_id = CRUDBooster::myId();
			$check_importing = $this->importProductCheck();
			if(CRUDBooster::myPrivilegeId() =='3'){
				if(($check_importing['blocked'] !='') or ($check_importing['product_amount']<=0)) {
					return redirect()->route('import_paymet')->with('msg','you Need To Purchase Plan For Importing Product');
				}
			}
			$drm = User::find($user_id)->drm_imports()->where('id',$import_id)->where('type','file')->where('import_finished',1)->first();
			if($drm == null){
				CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'),"Import not found","danger");
			}
			else{
				$data['drm'] = $drm;
				$data['step'] = 1;
				return view('admin.drm_import.after_import.manual_update_csv',$data);
			}
		}

		public function getProductTemplate($id=null){
			$template_purchased = AppStore::ActiveFeature('Produkt-Template');
			// if (app()->environment('local')) {
			// 	$template_purchased = true;
			// }
			if($template_purchased){
				$import = DrmImport::find($id);
				if($import){
					$data['template'] = $import->drm_import_template;
					$fields = $import->drm_product_fields->toArray();
					$data['vk_price'] = $fields['vk_price'];
					unset($fields['created_at']);
					unset($fields['updated_at']);
					unset($fields['drm_import_id']);
					unset($fields['id']);
					unset($fields['ek_price']);
					unset($fields['vk_price']);
					unset($fields['vat']);
					$data['title_fields'] = $fields;
					unset($data['title_fields']['description']);
					unset($data['title_fields']['image']);
					$image_fields = explode('|',$fields['image']);
					foreach ($image_fields as $key => $value) {
						$i = $key+1;
						$fields['image_'.$i] = $value;
					}
					unset($fields['image']);
					$data['template'] = $import->drm_import_template;
					$data['desc_fields'] = $fields;
					$data['import'] = $import;
					return view('admin.drm_import.after_import.product_template_edit',$data);
				}
				else{
					CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'),"Import not found","danger");
				}
			}
			else {
				CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'),"Please buy the app","danger");
			}
		}


        public function postUpdateImportUrl(){
            $id = $_REQUEST['drm_import_id'];
						$import = DrmImport::find($id);
						if($import){
							$import->file_url = $_REQUEST['url'];
							$import->csv_file_name = $_REQUEST['csv_filename'];
							$import->save();
						}
            return redirect('admin/drm_imports');
        }

        public function getUpdateCsv(){
            $id = $_REQUEST['id'];
            $drm = DB::table('drm_imports')->where('id',$id)->first();
            return view('admin.drm_import.after_import.update_csv_url',compact('drm'));
        }


		//////////////////////////////////////////////////////////////////////////
		////////////////////////////// Initial Step //////////////////////////////
		//////////////////////////////////////////////////////////////////////////


			public function getImport(){
				ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);

				$check_importing = $this->importProductCheck();
				// $import_details=AppStore::ShowImportLimitBanner(CRUDBooster::myId());
				if(CRUDBooster::myPrivilegeId() =='3'){
					if(($check_importing['blocked'] !='') or ($check_importing['product_amount']<=0)) {
						return redirect()->route('import_paymet')->with('msg','you Need To Purchase Plan For Importing Product');
					}
				}
				$data=[];
			  $data['import_new_product'] = "https://player.vimeo.com/video/393321002";
				$data['product_sync'] = "https://player.vimeo.com/video/391571545";
				$data['import_product'] = $check_importing;

				$data['template_purchased'] = AppStore::ActiveFeature('Produkt-Template');
				// if (app()->environment('local')) {
				// 	$data['template_purchased'] = true;
				// }
					ini_set('memory_limit',-1);
					if(CRUDBooster::myId()==null){
							return redirect(CRUDBooster::adminPath());
					}
					if(isset($_REQUEST['id'])){
					$id = $_REQUEST['id'];
					if($id!=null){
						$drm = User::find(CRUDBooster::myId())->drm_imports()->where('id',$id)->first();
						Request::session()->put('curr_tab',$drm->current_step);
						$country = DB::table('countries')->where('id',$drm->country_id)->first();
						$data['drm_categories'] = [];
						if($country!=null){
							$data['drm_categories'] = DB::table('drm_category')->select('category_name_'.$country->language_shortcode.' as category_name','id')->where('user_id',$drm->user_id)->where('country_id',$drm->country_id)->get();
						}

						 $data['price_categories'] = DB::table('price_category')->where('user_id',CRUDBooster::myId())->get();
						 $category_ids = json_decode($drm->category_ids);

						 $data['product_categories'] = [];
						 if(is_array($category_ids)){
							 $data['product_categories'] = DB::table('drm_category')
												->select('drm_category.category_name_'.$country->language_shortcode.' as category_name','drm_category.id')
												->whereIn('id',$category_ids)
												->get();
						 }


						 $data['drm'] = $drm;
						 if($data['product_categories'] == null){
							 $data['product_categories'] = [];
						 }
						 if(isset($_GET['tab'])){
							 $drm->current_step = $_GET['tab'];
						 }

						 if($drm->current_step=='fields'){
							 $view = "admin.drm_import.field_mapping";
						 }
						 if($drm->current_step=='template'){
							 if($data['template_purchased']){
								 $fields = $drm->drm_product_fields->toArray();
								 $data['vk_price'] = $fields['vk_price'];
								 unset($fields['created_at']);
								 unset($fields['updated_at']);
								 unset($fields['drm_import_id']);
								 unset($fields['id']);
								 unset($fields['ek_price']);
								 unset($fields['vk_price']);
								 unset($fields['vat']);
								 $data['title_fields'] = $fields;
								 unset($data['title_fields']['description']);
								 unset($data['title_fields']['image']);
								 $image_fields = explode('|',$fields['image']);
								 foreach ($image_fields as $key => $value) {
									 $i = $key+1;
									 $fields['image_'.$i] = $value;
								 }
								 unset($fields['image']);
								 $data['template'] = $drm->drm_import_template;
								 $data['desc_fields'] = $fields;
								 $view = "admin.drm_import.import_product_template";
							 }
							 else {
							 	return redirect()->back();
							 }
						 }

						 if($drm->current_step=='calc_money'){
							 $view = "admin.drm_import.price_calculation";
						 }
						 if($drm->current_step=='products'){
							 $data['total'] = TmpDrmProduct::where('drm_import_id',$id)->count();

							 $data['ean_missing'] = 0;
							 $data['ean_module_purchased'] = false;
							 if(!app()->environment('production')){
								 $data['ean_missing'] = TmpDrmProduct::where('drm_import_id',$id)->where(function ($q) {
										return $q->where('ean',null)->orWhere('ean',"");
								 })->count();
								 // $data['ean_module_purchased'] = AppStore::ActiveFeature('EAN Mapping Module');
								 $data['ean_module_purchased'] = true;
							 }

							 $view = "admin.drm_import.temp_product_list";
						 }
						 if($drm->current_step=='search_and_replace'){
							 $view = "admin.drm_import.search_and_replace";
						 }
						 if($drm->current_step=='filter'){
							 $view = "admin.drm_import.filter";
						 }

						if($drm->import_finished==1){
							Request::session()->remove('curr_tab');
							return redirect(CRUDBooster::mainpath());
						}
						 else{
							 $data['errors'] = DB::table('drm_import_errors')->where('drm_import_id',$id)->limit(10)->get();
							 $select_option = json_decode($drm->csv_headers);
							 $data['select_option'] = removeNulls($select_option);
							 if(view()->exists($view)){
									return view($view,$data);
							 }
							 else {
							 	return redirect()->back();
							 }
						 }
					}
					else{
							return redirect(CRUDBooster::mainpath());
					}
			 }
			 else {
				 Request::session()->remove('curr_tab');
				 return view('admin.drm_import.upload_csv',$data);
			 }
			}

			function postSetTab(){
				$tab=Request::input('tab');
				$id = Request::input('id');
				DB::table('drm_imports')->where('id',$id)->update(['current_step'=>$tab]);
				Request::session()->put('curr_tab',$tab);
				return redirect('admin/drm_imports/import?id='.$id.'&tab='.$tab);
			}

			function getSetTab(){
				$tab=$_REQUEST['tab'];
				$id = $_REQUEST['id'];
				DB::table('drm_imports')->where('id',$id)->update(['current_step'=>$tab]);
				return redirect('admin/drm_imports/import?id='.$id);
			}

			//////////////////////////////////////////////////////////////////////////
			////////////////////////////// Save Import (1st Step) ////////////////////
			//////////////////////////////////////////////////////////////////////////



			public function postSaveImport(){
				$message = ['text' => 'Initializing...','percent'=>'10'];
				sentProgress($message,'import');
				$paths=array();
				$url_file=null;
				$type=null;
				$rand=Str::random(40);
				if(Request::input('file_type')==1){
					$type='file';
					if(!Request::hasFile('csv_file')){
						$message = ['error' => 'CSV / Excel File is Invalid','percent'=>'100'];
						sentProgress($message,'import');
						return 0;
					}
					foreach(Request::file('csv_file') as $file){
						$message = ['text' => 'Uploading File...','percent'=>'50'];
						// event(new progressEvent($message));
						sentProgress($message,'import');
						$extention = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
						if($extention == 'csv' || $extention == 'txt'){
							$data = file_get_contents_utf8($file->getRealPath());
							Storage::disk('spaces')->put('test/csv_files/'.$rand.'.'.$extention,$data,'public');
							$paths[] = 'test/csv_files/'.$rand.'.'.$extention;
						}
						else {
							$paths[] = $file->storeAs('test/csv_files',$rand.'.'.$extention,['visibility'=>'public','disk'=>'spaces']);
						}
					}
				}
				else if(Request::input('file_type')!=4){
					$check_file = DB::table('drm_imports')->where('user_id',CRUDBooster::myId())->where('file_url',trim(Request::input('csv_link')))->count();
					if($check_file){
						$message = ['error'=>'already_exists'];
						return json_encode($message);
					}
					else{
						$type='url';
						$url_file=trim(Request::input('csv_link'));
						$message = ['text' => 'Getting Data From : <i>'.substr(Request::input('csv_link'),0,40).'...</i>','percent'=>'50'];
						// event(new progressEvent($message));
						sentProgress($message,'import');
						$csv_data= file_get_contents_utf8($url_file);
						Storage::disk('spaces')->put('test/csv_files/'.$rand.'.csv',$csv_data,'public');
						$paths[] = 'test/csv_files/'.$rand.'.csv';
					}
				}

				if(Request::input('file_type')==4){
					$type = 'xml';
					$rand=Str::random(40);
					$url_file=trim(Request::input('csv_link'));
					$message = ['text' => 'Getting Data From '.substr(Request::input('csv_link'),40).'...','percent'=>'50'];
					// event(new progressEvent($message));
					sentProgress($message,'import');
					$xml_data = file_get_contents_utf8($url_file);
					Storage::disk('spaces')->put('test/csv_files/'.$rand.'.xml',$xml_data,'public');
					$paths[] = 'test/csv_files/'.$rand.'.xml';
					$xml = simplexml_load_string($xml_data);
					$array = $this->simpleXmlToArray($xml->product);
					$csv_headers = $this->xmlToJsonHeader($xml->product[0]);
				}

				else{
					$message = ['text' => 'Listing Headers...','percent'=>'70'];
					// event(new progressEvent($message));
					sentProgress($message,'import');
					$csv = $this->csvToCsvHeaderJson(implode(';',$paths),Request::input('delimiter'),true,true);
					if($csv!=false){
						$csv_headers = makeArrayUtf8(makeArrayUtf8($csv[0]));
						$csv_headers = array_map('removeDots',$csv_headers);
						$csv_headers = json_encode($csv_headers);
					}
				}
				if($csv == false){
					$message = ['error' => 'CSV / Excel File is Invalid','percent'=>'100'];
					// event(new progressEvent($message));
					sentProgress($message,'import');
					return 0;
				}

				$message = ['text' => 'Storing Information...','percent'=>'90'];
				// event(new progressEvent($message));
				sentProgress($message,'import');

				if(Request::input('company_user_type')==1){
					$company_id= DeliveryCompany::create([
						'user_id'=>CRUDBooster::myId(),
						'name'=>Request::input('company_name'),
						'address'=>Request::input('company_address'),
						'phone'=>Request::input('company_contactnumber'),
						'state'=>Request::input('company_state'),
						'country_id'=>Request::input('company_country'),
						'zip'=>Request::input('company_zip'),
						'email'=>Request::input('company_email'),
						'contact_name'=>Request::input('company_contactname'),
					])->id;
				}else{
					$company_id=Request::input('company_user');
				}

				$drm_id = DrmImport::create([
					'user_id'=>CRUDBooster::myId(),
					'delivery_company_id'=>$company_id,
					'csv_file_name'=>Request::input('csv_filename'),
					'csv_headers'=> $csv_headers,
					'type'=>$type,
					'file_url'=>$url_file,
					'country_id'=> Request::input('item_country'),
					'delimiter' => Request::input('delimiter'),
					"delivery_time"=>Request::input('delivery_time'),
					'csv_file_path'=>implode(';',$paths),
					'demo_data'=>Request::session()->pull('demoData')
				])->id;
				try{
					$redis = Redis::connection();
					$redis->set('import_csv_'.$drm_id,json_encode($csv));
				}catch (\Predis\Connection\ConnectionException $e){}



				DB::table('drm_imports')->where('id',$drm_id)->update(['current_step' => 'fields']);
				return (int)$drm_id;
			}



			public function csvToCsvHeaderJson($file,$delimiter,$cloud = true, $initial = false){
				ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
				$paths=explode(';',$file);
				$count=0;
				$rand=Str::random(40);
				foreach($paths as $path){
					$file_type = pathinfo($path, PATHINFO_EXTENSION);
					try{
						if($cloud == true){
							$path = Storage::disk('spaces')->url($path);
							file_put_contents($rand.'.'.$file_type, fopen($path, 'r'));
							$path = $rand.'.'.$file_type;
						}
						if($file_type == 'csv' || $file_type == 'txt'){
							$reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Csv');
							if($delimiter!='auto'){
								$reader->setDelimiter($delimiter);
							}
							$spreadsheet = $reader->load($path);
						}
						else{
							$spreadsheet = IOFactory::load($path);
						}
						$spreadsheet = $spreadsheet->getActiveSheet()->toArray();

						$total = count($spreadsheet);
						for($i=0;$i<=5;$i++){
							if($total>=$i+1){
								$collection[$i] = $spreadsheet[$i];
							}
						}
						$valid = $this->validateFile($collection);
						if($valid==false){
							if($cloud == true){
								unlink($path);
							}
							return false;
						}

					}catch(\Exception $e){
						return false;
					}
					$demoFinal = makeArrayUtf8(makeArrayUtf8($spreadsheet[1]));
					Request::session()->put('demoData',json_encode($demoFinal));
				}
				if($cloud == true){
					unlink($path);
				}
				if($initial){
					return $spreadsheet;
				}
				else {
					$headers = makeArrayUtf8(makeArrayUtf8($spreadsheet[0]));
					$headers = array_map('removeDots',$headers);
					return json_encode($headers);
				}
			}

		//END Step 1


		//////////////////////////////////////////////////////////////////////////
		////////////////////////////// Field Mapping (2nd Step) //////////////////
		//////////////////////////////////////////////////////////////////////////


		public function getDemoData(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			if(Request::input('id')!=null){
				$drm=DrmImport::find(Request::input('id'));
				$headers=json_decode($drm->csv_headers,true);
				$demo=json_decode($drm->demo_data,true);
				$demo_data=array_combine($headers,$demo);
				$demo_data = removeNullKeys($demo_data);
				$request=Request::input();
				return view('admin.drm_import.demo_data_table',compact('demo_data','request'));
			}
		}

		public function getCheckFormat(){
			$drm=DrmImport::find(Request::input('id'));
			$headers=json_decode($drm->csv_headers,true);
			$demo=json_decode($drm->demo_data,true);
			$demo_data=array_combine($headers,$demo);
			$demo_data = removeNullKeys($demo_data);
			$request=Request::input();

			$format = $request['money_format'];
			$ek_price = $demo_data[$request['ek_price']];
			$vk_price = $demo_data[$request['vk_price']];

			$mod_ek = drm_convert_european_to_decimal($ek_price,$format);
			$mod_vk = drm_convert_european_to_decimal($vk_price,$format);

			$return = true;
      if($ek_price!=0 && $mod_ek == 0){
        $return = false;
      }
			if($vk_price!=0 && $mod_vk == 0){
        $return = false;
      }
			return json_encode(['status'=>$return]);
		}

		public function getFallbackCalc(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$id = $_REQUEST['id'];
			$fallback = DB::table('drm_fallback_calculations')->where('id',$id)->first();
			return json_encode($fallback);
		}


		public function postSaveFields(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
		  $message = ['text' => 'Initializing Fields', 'percent' => '10'];
		  // event(new progressEvent($message));
			sentProgress($message,'import');
		  // $vat = Request::input('vat');
		  // $custom_vat = Request::input('custom_vat');
		  $custom_category = Request::input('custom_category');
		  $import_id = Request::input('drm_import_id');
		  $drm_category = Request::input('category_from_drm');

			$drm = DrmImport::find($import_id);

			$country = DB::table('countries')->where('id',$drm->country_id)->first();
			DB::table('drm_category')->where('drm_import_id',$import_id)->delete();
			DB::table('drm_import_categories')->where('drm_import_id',$import_id)->delete();
			DB::table('drm_product_fields')->where('drm_import_id',$import_id)->delete();
			DB::table('drm_product_profit_margins')->where('drm_import_id',$import_id)->delete();

			$image_separator = Request::input('image_separator');
			if(Request::input('image_separator') == null || Request::input('image_separator') == ""){
				$image_separator = " ";
			}
			$drm->image_prefix = Request::input('prefix');
			$drm->image_suffix = Request::input('suffix');
			$drm->image_separator = $image_separator;
			$drm->item_number_prefix = Request::input('item_number_prefix');
			$drm->image_validation = Request::input('image_validation');
			$drm->money_format = Request::input('money_format');

			if(Request::input('fallback')!=null){
				$drm->fallback = Request::input('fallback');
			}

			// if(Request::input('ean') == null){
			// 	$drm->ean_field = 0;
			// }

		  if($drm_category!=null){
				$drm->existing_category = $drm_category;
		    $custom_category = DB::table('drm_category')->where('id',$drm_category)->first();
				if($custom_category!=null){
					$custom_category = (array)$custom_category;
					$custom_category = $custom_category["category_name"."_".$country->language_shortcode];
				}
		  }

		  if($custom_category!=null){
				$drm->custom_category = 1;
		  }

		  $message = ['text' => 'Processing Fields', 'percent' => '20'];
			sentProgress($message,'import');
		  $images = Request::input('image');

		  foreach ($images as $key => $value){
		    if($key == 0){
		      $image = $value;
		    }
		    else{
		      $image = $image."|".$value;
		    }
		  }

		  ($custom_category!=null)?$category=$custom_category:$category=Request::input('category');
		  (Request::input('item_number')!=null)?$item_number=Request::input('item_number'):$item_number=Request::input('ean');
		  $message = ['text' => 'Saving Fields', 'percent' => '50'];
			sentProgress($message,'import');

			DrmProductField::create([
				'drm_import_id' => $import_id,
				'name' => Request::input('name'),
				'item_number' => $item_number,
				'item_weight' => Request::input('item_weight'),
				'item_size' => Request::input('item_size'),
				'item_color' => Request::input('item_color'),
				'production_year' => Request::input('production_year'),
				'brand' => Request::input('brand'),
				'materials' => Request::input('materials'),
				'ean' => Request::input('ean'),
				'description' => Request::input('description'),
				'image' => $image,
				'ek_price' => Request::input('ek_price'),
				'vk_price' => Request::input('vk_price'),
				'stock' => Request::input('stock'),
				'category' => $category,
				'gender'	=> Request::input('gender'),
				'status'	=> Request::input('status'),
			]);

		  $fields = DB::table('drm_product_fields')->where('drm_import_id',$import_id)->first();
		  $message = ['text' => 'Creating Categories', 'percent' => '70'];
			sentProgress($message,'import');
		  $res = $this->drmSaveCsvCategories($fields);
			$template_purchased = AppStore::ActiveFeature('Produkt-Template');
			// if (app()->environment('local')) {
			// 	$template_purchased = true;
			// }
			if($template_purchased){
				$drm->current_step = 'template';
			}
			else {
				if($fields->vk_price!=null){
					$drm->current_step = 'products';
				}
				else{
					$drm->current_step = 'calc_money';
				}
			}
			$drm->save();
			$message = ['text' => 'Mapping Completed', 'percent' => '100'];
			// event(new progressEvent($message));
			sentProgress($message,'import');
		  if($fields->vk_price!=null){
		    $message = ['text' => 'Imporing Products', 'percent' => '0'];
		    // event(new progressEvent($message));
				sentProgress($message,'import');
		    $this->postImportTmpProducts($import_id,'drm_imports/import?id='.$import_id.'');
		  }
		  else{
		    return redirect()->back();
		  }
		}

		public function drmSaveCsvCategories($fields){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit','-1');
			$id = $fields->drm_import_id;
			$import = DrmImport::find($id);
			$country = DB::table('countries')->where('id',$import->country_id)->first();
			$trans_cat = 'category_name_'.$country->language_shortcode;

			$drm_categories = [];
			if(\Schema::hasColumn('drm_category', $trans_cat)){
				$categories_cache = $this->getCategoryCache($import->user_id);
				$drm_categories = $categories_cache->pluck($trans_cat)->toArray();
				if(is_array($drm_categories)){
					$drm_categories = removeNulls($drm_categories);
				}
			}

			$all_imported_categories = [];
			if($import->custom_category==1){
				if($import->existing_category){
					$category_name = $this->getCategoryById($import->existing_category,$import->user_id);
					if($category_name!=null){
						$category_array = (array)$category_name;
						$category_name = $category_array[$trans_cat];
					}
					$all_imported_categories[] = $category_name;
				}
				else{
					if(!in_array($fields->category, $drm_categories)){
						DB::table('drm_category')
							->insert([
								'drm_import_id' => $id,
								$trans_cat => $fields->category,
								'country_id' => $import->country_id,
								'user_id' => $import->user_id,
							]);
					}
					$all_imported_categories[] = $fields->category;
				}
			}
			else{
				$path = $import->csv_file_path;
				$type = pathinfo($path,PATHINFO_EXTENSION);
				if($type == 'xml'){
						$csv = $this->xmlToArray($path);
				}
				else{
					 $csv = $this->getCsv($import);
					 $key = $csv[0];
					 $key = array_map('removeDots',$key);
					 $key = makeArrayUtf8($key);
					 unset($csv[0]);
					 array_walk($csv, function (&$item) use($key){
					   $item = array_combine($key, $item);
						 $item = makeArrayUtf8(makeArrayUtf8($item));
					});
				}
				$collection = collect($csv);
				$all_imported_categories = $collection->pluck($fields->category)->unique()->toArray();

				$new_categories = array_diff($all_imported_categories,$drm_categories);
				$new_categories = removeNulls($new_categories);
				$insertValue = [];
				foreach ($new_categories as $key => $value){
					$insertValue[] = [
						'drm_import_id' => $id,
						$trans_cat => $value,
						'country_id' => $import->country_id,
						'user_id' => $import->user_id
					];
				}
				if(is_array($insertValue)){
					DB::table('drm_category')->insert($insertValue);
				}
			}

			$inserted_categories = DB::table('drm_category')->whereIn($trans_cat,$all_imported_categories)->where('user_id',$import->user_id)->pluck('id')->toArray();
			$inserted_categories = json_encode($inserted_categories);
			$import->category_ids = $inserted_categories;
			$import->save();
		}

		public function csvToArray($csv,$type,$delimiter,$deleteFile = true){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$paths=explode(';',$csv);
			$key=null;
			$key_count=0;
			$array = array();
			$rand=Str::random(40);
			foreach($paths as $path){
				if($deleteFile){
					$path = Storage::disk('spaces')->url($path);
					$file_type = pathinfo($path, PATHINFO_EXTENSION);
					$file = file_get_contents($path);
					file_put_contents($rand.'.'.$file_type,$file);
					$localpath = $rand.'.'.$file_type;
				}
				else{
					$localpath = $path;
				}
				if($type =='csv'|| $type == 'txt'){
					$reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
					$reader->setInputEncoding('UTF-8');

					if($delimiter!='auto'){
						$reader->setDelimiter($delimiter);
					}
					$spreadsheet = $reader->load($localpath);
				}
				else{
					$spreadsheet = IOFactory::load($localpath);
				}
				$spreadsheet = $spreadsheet->getActiveSheet()->toArray();
				$collection = LazyCollection::make($spreadsheet);


				if($key==null){
					$key = array_map('trim', $collection->first());
					$key_count=count($key);
				}
				$key = array_map('removeDots',$key);
				$collection = $collection->except(0);
				foreach($collection as $row){

					if(count($row)==$key_count && !containsOnlyNull($row)){
						$array[] = array_combine($key, $row);
					}

				}

				if(!pathIsUrl($path) && $deleteFile){
					unlink($localpath);
				}
			}
			return $array;
		}


		public function getColumnName($csv,$type,$delimiter,$deleteFile = true)
		{
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$paths=explode(';',$csv);
			$key=null;
			$key_count=0;
			$array = array();
			$rand=Str::random(40);
			foreach($paths as $path){
				if($deleteFile){
					$path = Storage::disk('spaces')->url($path);
					$file_type = pathinfo($path, PATHINFO_EXTENSION);
					$file = file_get_contents($path);
					file_put_contents($rand.'.'.$file_type,$file);
					$localpath = $rand.'.'.$file_type;
				}
				else{
					$localpath = $path;
				}
				if($type =='csv'|| $type == 'txt'){
					$reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
					$reader->setInputEncoding('UTF-8');

					if($delimiter!='auto'){
						$reader->setDelimiter($delimiter);
					}

					$spreadsheet = $reader->load($localpath);
				}
				else{
					$spreadsheet = IOFactory::load($localpath);
				}

				$spreadsheet = $spreadsheet->getActiveSheet()->toArray();

				if($key==null){
					$key = array_map('trim', $spreadsheet[0]);
					$key_count=count($key);
				}

			}

			return $key;
		}

		public function xmlToArray($path){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			$xml_file = file_get_contents_utf8($path);
			$xml = simplexml_load_string($xml_file);
			$array = $this->simpleXmlToArray($xml->product);

			foreach($xml->product as $key => $value){
				$array = $this->simpleXmlToArray($value);
				$final_array[] = $array;
				// yield $array;
			}
			return $final_array;
		}

		public function xmlToJsonHeader($xml){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			$array = $this->simpleXmlToArray($xml);
			foreach($array as $key => $value){
				 $headers_array[] = $key;
			}
			$Headers = json_encode($headers_array);
			Request::session()->put('demoData',json_encode($array));
			return $Headers;
		}

		function simpleXmlToArray($xmlObject){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
				ini_set('memory_limit',-1);
				$array = [];
				foreach ($xmlObject->children() as $node) {
						$array[$node->getName()] = is_array($node) ? simplexml_to_array($node) : (string) $node;
				}
				return $array;
		}


		public function getDrmCategories(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$id = $_REQUEST['id'];
			$drm = DrmImport::find($id);
			$country = DB::table('countries')->where('id',$drm->country_id)->first();
			$data['drm_categories'] = [];
			if($country!=null){
				$drm_categories = DB::table('drm_category')
					->select('category_name_'.$country->language_shortcode.' as category_name','id')
					->where('user_id',$drm->user_id)
					->where('country_id',$drm->country_id)
					->whereNotNull('category_name_'.$country->language_shortcode)
					->get();
			}
			$html.="<option value=''>Please select category</option>";
			foreach($drm_categories as $key=> $value){

				$html.="<option value='$value->id'>$value->category_name</option>";
			}
			return $html;
		}


		//END Step 2

		//////////////////////////////////////////////////////////////////////////
		////////////////////////////// Product Template (3rd Step) //////////////
		//////////////////////////////////////////////////////////////////////////

		public function postSaveProductTemplate(){
			$desc_template = $_REQUEST['desc_template'];
			$title_template = $_REQUEST['title_template'];
			$import_id = $_REQUEST['drm_import_id'];
			$import = DrmImport::find($import_id);
			$data = [
				'drm_import_id' => $import_id,
				'description' => $desc_template,
				'title' => $title_template
			];
			DrmImportTemplate::updateOrCreate(['drm_import_id' => $import_id],$data);
			$fields = $import->drm_product_fields;
			if(isset($_REQUEST['update'])){
				if($_REQUEST['update'] == '1'){
					return redirect('admin/drm_imports');
				}
			}
			else {
				if($fields->vk_price!=null){
					$import->current_step = 'products';
				}
				else {
					$import->current_step = 'calc_money';
				}
				$import->save();
				return redirect('admin/drm_imports/import?id='.$import_id);
			}
		}

		public function getTemplatePreview(){
			$import_id = $_REQUEST['id'];
			$tempate = $_REQUEST['template'];
			$user_id = CRUDBooster::myId();
			$tempProduct = \Cache::remember('temp_product_'.$user_id,05.0, function() use($import_id){
				$drm=DrmImport::find($import_id);
				$headers=json_decode($drm->csv_headers,true);
				$demo=json_decode($drm->demo_data,true);
				$demo_data=array_combine($headers,$demo);
				$data = removeNullKeys($demo_data);

				$fields = $drm->drm_product_fields;
				$image_headers = explode('|',$fields->image);
				$image_separator = $drm->image_separator;
				$final_array = array();
				foreach ($image_headers as $header){
					$csv_images = explode($image_separator,$data[$header]);
					$final_array = array_merge($final_array,$csv_images);
				}
				$json_image = $this->getImageJson($final_array,$drm);
				$demo_product = [
					'drm_import_id'=>$import_id,
					'title'=> $data[$fields->name],
					'item_number'=> strip_tags($data[$fields->item_number]),
					'item_weight'=> strip_tags($data[$fields->item_weight]),
					'item_size'=> strip_tags($data[$fields->item_size]),
					'item_color'=> strip_tags($data[$fields->item_color]),
					'production_year'=> strip_tags($data[$fields->production_year]),
					'brand'=> strip_tags($data[$fields->brand]),
					'materials'=> strip_tags($data[$fields->materials]),
					'ean'=> strip_tags($data[$fields->ean]),
					'TransDescription'=> $data[$fields->description],
					'image'=> $json_image,
					'stock'=> strip_tags($data[$fields->stock]),
					'category'=> $category,
					'status' => $fields->status,
					'gender'	=> strip_tags($data[$fields->gender]),
				];
				$product = (object)$demo_product;
				return $product;
			});
			$input = $tempate;
			$template = $this->generateTemplate($tempProduct,null,$input);
			return $template;
		}

		public function generateTemplate($product,$field,$template=null){
			$import_id = $product->drm_import_id;
			if($template==null){
				if($field=="title"){
					$template = \App\DrmImportTemplate::where('drm_import_id',$import_id)->first()->title;
				}
				if($field=="description"){
					$template = \App\DrmImportTemplate::where('drm_import_id',$import_id)->first()->description;
				}
			}
			if($template!=null){
				$images = json_decode($product->image);
				if(!$images){
					$images = [];
				}
				if(isset($_COOKIE['languageShortcode'])){
						$this->lang = $_COOKIE['languageShortcode'];
				}
				else{
					 $this->lang = "de";
				}
				if($this->lang == null || $this->lang == ''){
						$this->lang = "de";
				}
				$product_tags = [
					'#ITEM_WEIGHT#',
					'#ITEM_SIZE#',
					'#ITEM_COLOR#',
					'#BRAND#',
					'#MATERIALS#',
					'#PRODUCTION_YEAR#',
					'#DESCRIPTION#',
					'#GENDER#',
					'#ITEM_NUMBER#',
					'#STOCK#',
					'#CATEGORY#',
					'#NOTE#',
					'#TAGS#',
					'#NAME#',
					'#EAN#'
				];
				if($product->category==null){
					$categories = DB::table('drm_product_categories')
					->select('drm_category.category_name_'.$this->lang.' as category_name')
					->join('drm_category','drm_product_categories.category_id','=','drm_category.id')
					->where('product_id',$product->id)
					->pluck('category_name')->toArray();

					$categories = implode('<br>',$categories);
				}
				else {
						$categories = $product->category;
				}
				$template_product = [];
				$template_product['#CATEGORY#'] = $categories;
				$template_product['#DESCRIPTION#'] = $product->TransDescription;
				foreach($images as $key => $value){
					$i = $key+1;
					$template_product['#IMAGE_'.$i."#"] = "<img width='300px' src='$value->src' />";
					$product_tags[] = '#IMAGE_'.$i."#";
				}
				$template_product['#NAME#'] = $product->title;
				$template_product['#ITEM_NUMBER#'] = $product->item_number;
				$template_product['#ITEM_WEIGHT#'] = $product->item_weight;
				$template_product['#ITEM_SIZE#'] = $product->item_size;
				$template_product['#ITEM_COLOR#'] = $product->item_color;
				$template_product['#BRAND#'] = $product->brand;
				$template_product['#MATERIALS#'] = $product->materials;
				$template_product['#PRODUCTION_YEAR#'] = $product->production_year;
				$template_product['#TAGS#'] = $product->tags;
				$template_product['#NOTE#'] = $product->note;
				$template_product['#STOCK#'] = $product->stock;
				$template_product['#GENDER#'] = $product->gender;
				$template_product['#EAN#'] = $product->ean;

				foreach($product_tags as $key => $value){
					$template = str_replace($value,$template_product[$value],$template);
				}

				return $template;
			}
			else {
				return false;
			}
		}

		//////////////////////////////////////////////////////////////////////////
		////////////////////////////// Price Calcualtion (3rd Step) //////////////
		//////////////////////////////////////////////////////////////////////////



		public function postSaveMargin(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			Request::session()->put('curr_tab','calc_money');
			DB::table('drm_imports')->where('id',Request::input('drm_import_id'))->update(['current_step' => 'calc_money']);
			$drm = DrmImport::find(Request::input('drm_import_id'));
			$categories = Request::input('product_category');
			if(Request::input('id')!=null){
				$margin=DrmProductProfitMargin::find(Request::input('id'));
				foreach($categories as $key => $category){
					$margin->category_id = $category;
					$margin->price_from= drm_convert_european_to_decimal(Request::input('price_from'),2);
					$margin->price_to= drm_convert_european_to_decimal(Request::input('price_to'),2);
					$margin->shipping_cost= drm_convert_european_to_decimal(Request::input('shipping_cost'),2);
					$margin->profit_percent= drm_convert_european_to_decimal(Request::input('profit_percent'),2);
					$margin->round_scale=Request::input('round_scale');
					$margin->additional_charge_fixed= drm_convert_european_to_decimal(Request::input('additional_charge_fixed'),2);
					$margin->save();
				}
				return redirect()->back();
			}
			$margins = [];
			foreach($categories as $key => $category){
				$margins[] = [
					'drm_import_id' => Request::input('drm_import_id'),
					'category_id' => $category,
					'price_from' => drm_convert_european_to_decimal(Request::input('price_from'),2),
					'price_to' => drm_convert_european_to_decimal(Request::input('price_to'),2),
					'shipping_cost' => drm_convert_european_to_decimal(Request::input('shipping_cost'),2),
					'profit_percent' => drm_convert_european_to_decimal(Request::input('profit_percent'),2),
					'round_scale' => Request::input('round_scale'),
					'additional_charge_fixed' => drm_convert_european_to_decimal(Request::input('additional_charge_fixed'),2),
				];
			}
			DrmProductProfitMargin::insert($margins);
			return redirect()->back();
		}

		function postDeleteMargin(){
			$id=Request::input('id');
			DrmProductProfitMargin::find($id)->delete();
			return redirect()->back();
		}

		function getEditMargin(){
			$id=Request::input('id');
			$item= DrmProductProfitMargin::find($id);
			$drm = DrmImport::find($item->drm_import_id);
			$item->price_from=drm_convert_european_to_decimal($item->price_from,2);
			$item->price_to=drm_convert_european_to_decimal($item->price_to,2);
			$item->shipping_cost=drm_convert_european_to_decimal($item->shipping_cost,2);
			$item->profit_percent=drm_convert_european_to_decimal($item->profit_percent,2);
			$item->additional_charge_fixed=drm_convert_european_to_decimal($item->additional_charge_fixed,2);
			return $item;
		}

		public function postCreateFallbackCalculation(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			$value['name'] = $_REQUEST['name'];
			$value['shipping_cost'] = $_REQUEST['shipping_cost'];
			$value['profit_percent'] = $_REQUEST['profit_percent'];
			$value['additional_charge'] = $_REQUEST['additional_charge'];
			$value['user_id'] = CRUDBooster::myId();
			DB::table('drm_fallback_calculations')->insert($value);
			if($_REQUEST['ajax']=='1'){
				$fallback = DB::table('drm_fallback_calculations')->where('user_id',CRUDBooster::myId())->get();
				$html = "<option value=''>Select a fallback Calculation</option>";
				if($fallback!=null){
					foreach($fallback as $value){
						$html.= "<option value='$value->id'>$value->name</option>";
					}
				}
				return $html;
			}
			else {
				return redirect()->back();
			}
		}

		public function getCsv($import){
			$import_id = $import->id;
			$file_url = str_replace('storage','public',$import->csv_file_path);
			$type = pathinfo($file_url, PATHINFO_EXTENSION);
			try{
				$redis = Redis::connection();
				$json = $redis->get('import_csv_'.$import_id);
				if($json){
					$csv_data = json_decode($json);
				}
			}catch (\Predis\Connection\ConnectionException $e){}
				if(!$csv_data){
						$csv_data = $this->csvToArrayModified($file_url,$type,$import->delimiter);
						try{
							$redis = Redis::connection();
							$redis->set('import_csv_'.$import_id,json_encode($csv_data));
						}catch (\Predis\Connection\ConnectionException $e){}
				}
				return $csv_data;
		 }

		 public function setEanField($drm,$csv_fields){
			 if($drm->ean_field==1){
 				$this->ean_field = $csv_fields->ean;
 				$this->ean = "ean";
 			}
 			else {
 				$this->ean_field = $csv_fields->item_number;
 				$this->ean = "item_number";
 			}
		 }

		public function postImportTmpProducts($import_id = null, $return_url = null){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			$this->count = 0;
			$product_amount = $this->importProductCheck();
			$message = ['text' => 'Initializing Import'];
			sentProgress($message,'import');
			if($import_id == null){
				$import_id = Request::input('drm_import_id');
			}

			$user_id = CRUDBooster::myId();
			$drm = DB::table('drm_imports')->where('id',$import_id)->first();
			$fallback = $_REQUEST['fallback'];
			$import_update['fallback'] = $fallback;
			if($return_url==null){
				$import_update['current_step'] = 'products';
				$return_url == Request::input('return_url');
			}

			$fields = DB::table('drm_product_fields')->where('drm_import_id',$import_id)->first();
			$this->setEanField($drm,$fields);

			DB::table('drm_imports')->where('id',$import_id)->update($import_update);
			$this->existing_products = $this->getExistingProducts($user_id,$import_id);

			$file_url = str_replace('storage','public',$drm->csv_file_path);

		  $type = pathinfo($file_url, PATHINFO_EXTENSION);

		  $message = ['text' => 'Getting Data From Sheet'];
			sentProgress($message,'import');

			if($type == 'xml'){
				$csv_data = $this->xmlToArray($file_url);
			}
			else{
				$csv_data = $this->getCsv($drm);
			}

			DB::table('tmp_drm_products')->where('drm_import_id',$import_id)->delete();
			DB::table('drm_import_errors')->where('drm_import_id',$import_id)->delete();
			$message = ['text' => 'Starting Import'];
			// event(new progressEvent($message));
			sentProgress($message,'import');

			$key = array_map('trim', $csv_data[0]);
			$key = array_map('removeDots',$key);
			$key = makeArrayUtf8(makeArrayUtf8($key));
			$key_count = count($key);
		  unset($csv_data[0]);
			$total = count($csv_data);

			$country = DB::table('countries')->where('id',$drm->country_id)->first()->language_shortcode;
			$this->table = "drm_translation_".$country;
			$this->trans_cat = "category_name_".$country;

			$profit_margins = DB::table('drm_product_profit_margins')->where('drm_import_id',$import_id)->cursor();
			$this->profit_margins = collect($profit_margins->all());

			$category_ids = json_decode($drm->category_ids);
			$categories = DB::table('drm_category')->select('id',$this->trans_cat)->whereIn('id',$category_ids)->cursor();
			$category_array = makeArrayUtf8(json_decode(json_encode($categories->toArray()),true));
			$this->all_categories = collect($category_array);
			$this->fallback = DB::table('drm_fallback_calculations')
			->select('profit_percent','shipping_cost','additional_charge')
			->where('id',$drm->fallback)
			->first();

			// DB::table('drm_imports')->where('id',$import_id)->update(['sheet_total'=>$total]);
			$this->calc_config = [
				'ek_field' => $fields->ek_price,
				'vk_field' => $fields->vk_price,
				'money_format' => $drm->money_format,
				'fallback' => $this->fallback
			];
			LazyCollection::make(function () use ($csv_data,$key_count){
				foreach($csv_data as $line)
				{
					if(count($line)==$key_count && !containsOnlyNull($line)){
						yield makeArrayUtf8(makeArrayUtf8($line));
					}
				}
			})
			->chunk(500)
			->each(function ($lines) use ($product_amount, $import_id, $fields, $drm, $user_id, $total, $message, $key){
				$this->insertArray = null;
				$this->errors = null;
				array_walk($lines, function ($chunks) use($insertArray,$drm,$fields,$key,$product_amount){
					array_walk($chunks, function ($chunk) use($insertArray,$drm,$fields,$key,$product_amount){
						if(CRUDBooster::myPrivilegeId()=='3'){

							if(($product_amount['limit']=='') && ($this->count >= $product_amount['product_amount'])){
								$this->count++;
							}
							else {
								$data = array_combine($key, $chunk);
								$data = makeArrayUtf8(makeArrayUtf8($data));
								$valid = $this->drmErrorReport($data,$fields,$drm);
								if(isset($valid['errors'])){
									$this->errors[] = $valid['errors'];
								}
								if($valid['valid']){
									$this->insertArray[] = $this->insertSingleProduct($drm,$data,$fields);
								}
								else{
									$this->invalid++;
								}
								$this->count++;
							}
						}
					});
				});
				if(is_array($this->insertArray)){
					$last = end($this->insertArray);
					$name = $last['name'];
					TmpDrmProduct::insert($this->insertArray);
				}
				if($this->errors!=null){
					try{
						DB::table('drm_import_errors')->insert($this->errors);
					}
					catch(\Exception $e){}
				}
				$message = ['total' => $total, 'count' => $this->count,'percent' => round(($this->count/$total)*100,2),'invalid' => $this->invalid,'name' => $name ];
				sentProgress($message,'import');
			});
			return true;
		}

		public function insertSingleProduct($drm,$data,$fields){
			$duplicate_ean = 0;
			if(in_array($data[$this->ean_field],$this->existing_products)){
				$duplicate_ean = 1;
			}
			$image_headers = explode('|',$fields->image);
			$image_separator = $drm->image_separator;
			$final_array = array();
			foreach ($image_headers as $header){
				$csv_images = explode($image_separator,$data[$header]);
				$final_array = array_merge($final_array,$csv_images);
			}
			$json_image = $this->getImageJson($final_array,$drm);

			if($drm->custom_category==1){
				$category = makeUtf8($fields->category);
			}
			else {
				$category=strip_tags($data[$fields->category]);
			}
			if($drm->item_number_prefix!=null){
				$item_number=$drm->item_number_prefix."".strip_tags($data[$fields->item_number]);
			}
			else {
				$item_number=strip_tags($data[$fields->item_number]);
			}
			$category_id = $this->all_categories->where($this->trans_cat,makeUtf8($category))->first();
			$this->calc_config['ek_price'] = $data[$fields->ek_price];
			$this->calc_config['vk_price'] = $data[$fields->vk_price];
			$this->calc_config['profit_margins'] = $this->profit_margins->where('category_id',$category_id['id']);

			$price = drmPriceCalculationNew($this->calc_config);

			$insertArray = [
				'drm_import_id'=>$drm->id,
				'name'=> $data[$fields->name],
				'item_number'=> $item_number,
				'item_weight'=> strip_tags($data[$fields->item_weight]),
				'item_size'=> strip_tags($data[$fields->item_size]),
				'item_color'=> strip_tags($data[$fields->item_color]),
				'production_year'=> strip_tags($data[$fields->production_year]),
				'brand'=> strip_tags($data[$fields->brand]),
				'materials'=> strip_tags($data[$fields->materials]),
				'ean'=> strip_tags($data[$fields->ean]),
				'description'=> $data[$fields->description],
				'image'=> $json_image,
				'ek_price'=> drm_convert_european_to_decimal($data[$fields->ek_price],$drm->money_format),
				'vk_price'=> $price,
				'stock'=> strip_tags($data[$fields->stock]),
				'category'=> $category,
				'duplicate_ean'=> $duplicate_ean,
				'update_enabled' => '1',
				'status' => $fields->status,
				'gender'	=> strip_tags($data[$fields->gender]),
				'user_id'=> $drm->user_id,
				'delivery_company_id'=> $drm->delivery_company_id,
				'country_id' => $drm->country_id,
			];
			return $insertArray;
		}

		public function getExistingProducts($user_id,$import_id){
			$existing_products = Cache::remember('existing_products_'.$user_id,05.0, function() use($user_id){
				$existing = DB::table('drm_products')->where('user_id',$user_id)->whereNull('deleted_at')->where('drm_import_id','<>',$import_id)->pluck($this->ean)->toArray();
				return $existing;
			});
			return $existing_products;
		}

		public function getImageJson($array,$drm){
		  $final_img=array();
			$prefix = $drm->image_prefix;
			$suffix = $drm->image_suffix;
		  foreach ($array as $key => $value){
		    $img = str_replace(' ', '', $value);
		    if($img!=null){
		      if (filter_var($img, FILTER_VALIDATE_URL) === FALSE){
		        $value = $this->importImagePrefix($prefix,$img);
		      }

		      if($suffix!=null){
		        $value = $this->importImageSuffix($suffix,$value);
		      }

		      $img_data['id']=$key+1;
		      if($key>10){
		        $img_data['status']=0;
		      }
		      else{
		        $img_data['status']=1;
		      }
					if($drm->image_validation){
						$image_exists = checImageUrl($value);
					}
					else {
						$image_exists = true;
					}

					if($image_exists){
						$img_data['src']=$value;
						$final_img[]=$img_data;
					}
		    }
		  }
		  $json_image=json_encode($final_img);
		  return $json_image;
		}


		public function importImageSuffix($suffix,$image){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
		  $image = $image.$suffix;
		  return $image;
		}


		public function drmErrorReport($data,$field,$drm){
			$ean_field = $this->ean_field;
			$ean = $this->ean;
			$line = $this->count+1;
			$valid = true;
			$errors = [
				'drm_import_id'=>$drm->id
			];
			if(app()->environment('production')) {
				if(trim($data[$ean_field]) == null || trim($data[$ean_field]) == ''){
					$valid = false;
					$errors['error_type'] = 'EAN Missing';
					$errors['error'] = "Incorrect / Missing value under the field '".$ean_field."' at line number: ".$line;
				}
			}

			if(trim($data[$field->name]) == null || trim($data[$field->name]) == ''){
				 $valid = false;
				 $errors['error_type'] = 'Title Missing';
				 $errors['error'] = "Incorrect / Missing value under the field '".$field->name."' at line number: ".$line;
				// DB::table('drm_import_errors')->insert(['drm_import_id'=>$drm->id, 'error_type'=> 'Title Missing', 'error' => "Incorrect / Missing value under the field '".$field->name."' at line number: ".$line]);
			}

			if(trim($data[$field->description]) == null || trim($data[$field->description]) == ''){
				$valid = false;
				$errors['error_type'] = 'Description Missing';
				$errors['error'] = "Incorrect / Missing value under the field '".$field->description."' at line number: ".$line;
				// DB::table('drm_import_errors')->insert(['drm_import_id'=>$drm->id, 'error_type'=> 'Description Missing', 'error' => "Incorrect / Missing value under the field '".$field->description."' at line number: ".$line]);
			}

			if(trim($data[$field->category]) == null && trim($field->category) != null && !$drm->custom_category && !$drm->existing_category){
				$valid = false;
				$errors['error_type'] = 'Category Missing';
				$errors['error'] = "Incorrect / Missing value under the field '".$field->category."' at line number: ".$line;
				// DB::table('drm_import_errors')->insert(['drm_import_id'=>$drm->id, 'error_type'=> 'Category Missing', 'error' => "Incorrect / Missing value under the field '".$field->category."' at line number: ".$line]);
			}

			if(drm_convert_european_to_decimal(trim($data[$field->ek_price]),$drm->money_format) == 0){
				$valid = false;
				$errors['error_type'] = 'Purchase Price Missing';
				$errors['error'] = "Incorrect / Missing value under the field '".$field->ek_price."' at line number: ".$line;
				// DB::table('drm_import_errors')->insert(['drm_import_id'=>$drm->id, 'error_type'=> 'Purchase Prie Missing', 'error' => "Incorrect / Missing value under the field '".$field->ek_price."' Missing at line number: ".$line]);
			}


			$image_headers =  explode('|',$field->image);
			$image_separator = $drm->image_separator;
			$final_array = array();
			foreach ($image_headers as $header){
				$csv_images = explode($image_separator,$data[$header]);
				$final_array = array_merge($final_array,$csv_images);
			}
			$imageJson = $this->getImageJson($final_array,$drm);
			$images = json_decode($imageJson);
			if(count($images)<1){
				$valid = false;
				$errors['error_type'] = 'Image Missing';
				$errors['error'] = "Incorrect / Missing value under each image fields at line number ".$line;
				// DB::table('drm_import_errors')->insert(['drm_import_id'=>$drm->id, 'error_type'=> 'Image Missing', 'error' => "Incorrect / Missing value under each image fields at line number ".$line]);
			}
			$report['valid'] = $valid;
			if(!$valid){
				$report['errors'] = $errors;
			}
			return $report;
		}


		//END



		//////////////////////////////////////////////////////////////////////////
		////////////////////////////// Porducts List (4th Step) //////////////////
		//////////////////////////////////////////////////////////////////////////


		public function getTmpDrmProducts(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			$id = $_REQUEST['id'];
			$products = TmpDrmProduct::where('drm_import_id',$id)->get();
			$all_products = [];
			foreach ($products as $key => $value){
				$json = json_decode($value->image);
				$image ='';
				foreach ($json as $i=> $img){
					if($i == count($json)-1){
						$image.=$img->src;
					}
					else{
						$image.=$img->src.',';
					}
				}
				$value->image = $image;
				$all_products[] = $value;
			}
			return DataTables::of($all_products)->make(true);
		}

		public function getTmpDrmProduct(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$product = TmpDrmProduct::find(Request::input('id'));
			return $product;
		}

		public function postTmpDrmProductUpdate(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			$product = TmpDrmProduct::find(Request::input('id'));
			$product->name=Request::input('name');
			$product->item_number=Request::input('item_number');
			$product->description=Request::input('description');
			$product->ek_price=Request::input('ek_price');
			$product->vk_price=Request::input('vk_price');
			// $product->vat=Request::input('vat');
			$product->stock=Request::input('stock');
			$product->save();
			return "success";
		}


		public function postSetOverwrite(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			$import=DrmImport::findOrFail(Request::input('id'));
			$import->overwrite=Request::input('overwrite');
			$import->save();
			return "success";
		}

		public function getImportErrors(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$id = $_REQUEST['id'];
			$errors = DB::table('drm_import_errors')->where('drm_import_id',$id)->get();
			return view('admin.drm_import.drm_import_errors',compact('errors'));
		}



		//////////////////////////////////////////////////////////////////////////
		////////////////////////////// Filter (5th Step) /////////////////////////
		//////////////////////////////////////////////////////////////////////////



		public function postFilter(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$id = $_REQUEST['drm_import_id'];
			$field = $_REQUEST['field'];
			$value = $_REQUEST['value'];
			$import_id = 'drm_import_'.$id;

			if(Session::has($import_id.".".$field) && $field != 'price_below' && $field != 'price_more_than'){
				Session::push($import_id.".".$field, $value);
			}
			else if($field != 'price_below' && $field != 'price_more_than'){
				Session::put($import_id.".".$field, []);
				Session::push($import_id.".".$field, $value);
			}
			else {
				Session::put($import_id.".".$field, $value);
			}
			if($field == 'stock'){
				if(Session::has($import_id.".".'stock_operator')){
					Session::push($import_id.".".'stock_operator', $_REQUEST['operator']);
				}
				else {
					Session::put($import_id.".".'stock_operator', []);
					Session::push($import_id.".".'stock_operator', $_REQUEST['operator']);
				}
			}
			Session::save();

			$json = json_encode(Session::get('drm_import_'.$id));

			$drm_import_filter = DB::table('drm_import_filter');

			$values = array(
					'drm_import_id' => $id,
					'filter' => $json
			);


			if($drm_import_filter->where('drm_import_id',$id)->count() == 0){
					$drm_import_filter->insert($values);
			}else{
					$drm_import_filter->update($values);
			}
		}

		public function getRemoveFilter(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$id = $_REQUEST['id'];
			Session::remove('drm_import_'.$id);
			DB::table('drm_import_filter')->where('drm_import_id',$id)->delete();
			return 'success';
		}

		public function getFilterSession(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$filter_json = DB::table('drm_import_filter')->where('drm_import_id',$_REQUEST['id'])->first();
			if($filter_json->filter == null || $filter_json->filter == ''){
					return 'null';
			}
			else{
					return $filter_json->filter;
			}
		}


		public function getExcludedProducts(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			$id = $_REQUEST['id'];
			$filter_json = DB::table('drm_import_filter')->where('drm_import_id',$id)->first();
			$filters = json_decode($filter_json->filter,true);
			$stock_opertaor = $filters['stock_operator'];
			$products = TmpDrmProduct::where('drm_import_id', $id);

			if(!Session::has('drm_import_'.$id)){
				$products->where('tmp_drm_products.id',0);
				$products->get();
				return DataTables::of($products)
				->make(true);
			}

			$products->where('tmp_drm_products.id',0);

			if(isset($filters['price_more_than'])){
					$products->orWhere('vk_price','>',(float)$filters['price_more_than']);
			}

			if(isset($filters['price_below'])){
					$products->orWhere('vk_price','<',(float)$filters['price_below']);
			}

			if(isset($filters['ean'])){
				foreach ($filters['ean'] as $ean) {
					$products->orWhere('ean',$ean);
				}
			}

			if(isset($filters['category'])){
				foreach ($filters['category'] as $category) {
						$products->orWhere('category',$category);
				}
			}

			if(isset($filters['stock'])){
				foreach ($filters['stock'] as $key => $value) {
					if($stock_opertaor[$key] == '='){
						$products->orWhere('stock',(int)$value);
					}
					if($stock_opertaor[$key] == '<'){
						$products->orWhere('stock','<',(int)$value);
					}
					if($stock_opertaor[$key] == '>'){
						$products->orWhere('stock','>',(int)$value);
					}
				}
			}

			$products->where('drm_import_id', $id);
			$products = $products->get();
			return DataTables::of($products)
			->make(true);
		}



		//////////////////////////////////////////////////////////////////////////
		////////////////////////////// Search And Replace (Final Step) ///////////
		//////////////////////////////////////////////////////////////////////////



		public function postSearchAndReplace(){
			$drm_id=Request::input('drm_import_id');
			$keyword=makeUtf8(Request::input('keyword'));
			$replace_with=makeUtf8(Request::input('replace_with'));
			$queries=DB::select(DB::raw("select CONCAT(
				'UPDATE tmp_drm_products SET ',
				column_name,
				' = REPLACE(',COLUMN_NAME,',''$keyword'',''$replace_with'') where drm_import_id = $drm_id;') AS query
				from information_schema.columns
				where table_name = 'tmp_drm_products'"));
				$queries = collect($queries);
				$queries = $queries->unique();
				$filtered = $queries->filter(function($item){
				    if(strpos($item->query,"SET id") || strpos($item->query,"SET drm_import_id")
						|| strpos($item->query,"SET drm_import_id") || strpos($item->query,"SET country_id")
						|| strpos($item->query,"SET user_id") || strpos($item->query,"SET delivery_company_id")
						|| strpos($item->query,"SET duplicate_ean") || strpos($item->query,"SET created_at")
						|| strpos($item->query,"SET updated_at") || strpos($item->query,"SET update_enabled")
						|| strpos($item->query,"SET language_id")
						){
							return false;
						}
						else {
							return true;
						}
				});
			foreach($filtered as $query){
				$res= DB::statement($query->query);
			}
			return "search_and_replace";
		}


		public function getAssignEan(){
			$import_id = $_REQUEST['import_id'];
			$user_id = CRUDBooster::myId();
			$query = DB::table('tmp_drm_products')->where('drm_import_id',$import_id);
			$query = $query->where(function($query){
				$query->whereNull('ean')
				->orWhere('ean',"");
			});
			$products = $query->get();
			$count = count($products);
			$ean_query = DB::table('custom_eans')->where('user_id',$user_id)->where('used',0)->limit($count)->cursor();
			$eans = $ean_query->toArray();
			$ean_count = count($eans);
			$limit_products = $products->take($ean_count);
			foreach ($limit_products as $key => $product){
				$ean = $eans[$key]->ean;
				DB::table('tmp_drm_products')->where('id',$product->id)->update(['ean' => $ean]);
				// DB::table('custom_eans')->where('ean',$ean)->where('user_id',$user_id)->update(['used' => 1,'product_id' => $product->id]);
			}
			$ean_ids = $ean_query->pluck('id')->toArray();
			$product_ids = $limit_products->pluck('id')->toArray();
			//
			foreach (array_chunk($ean_ids,500) as $chunk) {
				DB::table('custom_eans')->whereIn('id',$chunk)->update(['used' => 1]);
			}

			foreach (array_chunk($product_ids,500) as $chunk) {
				DB::table('tmp_drm_products')->whereIn('id',$chunk)->update(['ean_field' => 0]);
			}

			return true;
		}


		public function getCsvImportToDatabase(){
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
				return redirect()->back();
		}

		public function postCsvImportToDatabase(){
			ini_set('memory_limit',-1);
			ini_set('max_execution_time', '0'); // for infinite time of execution
			$import_id = Request::input('drm_import_id');
			// $this->postImportFinal($import_id);
			$import_check=$this->importProductCheck();
			$drm = DrmImport::find($import_id);

			$fields = DB::table('drm_product_fields')->where('drm_import_id',$import_id)->first();
			$this->setEanField($drm,$fields);

			$data = TmpDrmProduct::where('drm_import_id',$import_id)->get();
			$total = count($data);
			$country = DB::table('countries')->where('id',$drm->country_id)->first();
			$table = 'drm_translation_'.$country->language_shortcode;
			$trans_cat = "category_name_".$country->language_shortcode;

			$Import_filter = DB::table('drm_import_filter')->where('drm_import_id',$import_id)->first();
			$filter = json_decode($Import_filter->filter, true);
			$category_ids = json_decode($drm->category_ids);
			$imported_categories = DB::table('drm_category')->select('id',$trans_cat)->whereIn('id',$category_ids)->cursor();
			$imported_categories_all = collect($imported_categories->all());
			$tmp_done_array = [];
			$update_status = makeUpdateStatusJson();
			if(\Schema::hasTable($table)){
				$count = 0;
				$trial_checked = 0;
				foreach($data->chunk(500) as $chunks){
					$productCategories = array();
					$insertValues = array();
					foreach($chunks as $record){
					//checking for import limit
					$tmp_id = $record->id;
					if(CRUDBooster::myPrivilegeId()=='3'){
						if (($import_check['limit']=='') and ($count >= $import_check['product_amount'])) break;
					}

					if(filter_var($record->stock, FILTER_VALIDATE_INT) === false){
						$count++;
		        continue;
		      }
					if($record->stock<0){
						$record->stock = 0;
					}

					if($this->drmCheckExcluded($record,$filter) && $this->checkValid($record)){
						$record = $record->toArray();
						$value['title'] = $record['name'];
						$value['description'] = $record['description'];
						$value['ean'] = $record['ean'];
						$category = $record['category'];
						$record['tags'] = generateTags([$category,$record['brand'],$record['gender']]);

						$category = $imported_categories_all->where($trans_cat,$category)->first();
						if($category!=null){
							$category = (array)$category;
							$category_id = $category['id'];
						}
						if($category_id!=null){
							$product_category = [
								'category_id' => $category_id,
								'country_id' => $record['country_id']
							];
						}

						unset($record['category']);
						unset($record['id']);
						unset($record['name']);
						unset($record['description']);

						if($record['duplicate_ean']==1){
							if($drm->overwrite==1){
								// unset($record['drm_import_id']);
								$ean = $record["ean"];
								unset($record["ean"]);
								unset($record['duplicate_ean']);
								$duplicate = DB::table('drm_products')->where('user_id',$drm->user_id)->where("ean",$ean)->where('drm_import_id','<>',$drm->id);
								$duplicate_product = $duplicate->first();
								if($duplicate_product!=null){
									$duplicate->update($record);
									// unset($value['ean']);
									unset($value["ean"]);
									DB::table($table)->where('product_id',$duplicate_product->id)->update($value);
									DB::table('drm_product_categories')->where('product_id',$duplicate_product->id)->delete();
									if(is_array($product_category)){
										$product_category['product_id'] = $duplicate_product->id;
										$productCategories[] = $product_category;
									}
								}
							}
							continue;
						}
						$record['update_status'] = $update_status;
						$p_id = DrmProduct::create($record)->id;

						if($trial_checked == 0){
							$this->setImportTrial($drm->user_id);
							$trial_checked = 1;
						}

						$value['product_id'] = $p_id;
						$insertValues[] = $value;
						if(is_array($product_category)){
							$product_category['product_id'] = $p_id;
							$productCategories[] = $product_category;
						}
					}
					$count++;
				}
				if(count($insertValues)){
					DB::table($table)->insert($insertValues);
				}
				if(count($productCategories)){
					DB::table('drm_product_categories')->insert($productCategories);
				}

				$message = ['total' => $total, 'count' => $count,'finalize'=> 1 ,'percent' => number_format(($count/$total)*100,2),'name' => $value['title']];
				// event(new progressEvent($message));
				sentProgress($message,'import');
			}

			try{
				$redis = Redis::connection();
				$redis->delete('import_csv_'.$import_id);
			}catch (\Exception $e){}

				$drm->import_finished=1;
				$drm->current_step = null;
				DB::table('drm_import_errors')->where('drm_import_id',$import_id)->delete();
				$drm->save();
				$drm->tmp_drm_products()->delete();
				$message=DB::table('notification_trigger')->where('hook','DRMImport')->where('status',0)->first();
				if($message){
					User::find(Crudbooster::myId())->notify(new DRMNotification($message->title, 'DRMImport','#'));
				}
				return true;
			}
			else{
				return true;
			}
		}


			public function postImportFinal(){
				ini_set('memory_limit',-1);
				ini_set('max_execution_time', '0'); // for infinite time of execution
				$import_id = Request::input('drm_import_id');
				$import_check=$this->importProductCheck();
				$drm = DrmImport::find($import_id);
				$data = TmpDrmProduct::where('drm_import_id',$import_id)->get();
				$country = DB::table('countries')->where('id',$drm->country_id)->first();
				$table = 'drm_products_'.$country->language_shortcode;
				$trans_cat = "category_name_".$country->language_shortcode;

				$Import_filter = DB::table('drm_import_filter')->where('drm_import_id',$import_id)->first();
				$filter = json_decode($Import_filter->filter, true);
				$total = $data->count();
				$tmp_done_array = [];
				if(\Schema::hasTable($table)){
					$count = 0;
					foreach($data->chunk(500) as $chunks){
						$productCategories = [];
						$insert_array = [];
						foreach($chunks as $record){
							if(CRUDBooster::myPrivilegeId()=='3'){
								if (($import_check['limit']=='') and ($count >= $import_check['product_amount'])) break;
							}
							$product_category = [];
							if($this->drmCheckExcluded($record,$filter)){
								$record=$record->toArray();
								$record['title'] = $record['name'];
								$category = $record['category'];
								unset($record['category']);
								unset($record['vat']);
								unset($record['id']);
								unset($record['update_enabled']);
								unset($record['name']);

								unset($record['language_id']);
								$record['tags'] = generateTags([$category,$record['brand'],$record['gender']]);

								if(!$drm->existing_category){
									$category_name = $this->getCategoryByName($trans_cat,$category,$drm->user_id);
								}
								else{
									$category_name = $this->getCategoryById($drm->existing_category,$drm->user_id);
									if($category!=null){
										$category_array = (array)$category;
										$category_name = $category_array[$trans_cat];
									}
								}
								if($category_name!=null){
									$product_category[] = $category_name->id;
								}
								$product_category = json_encode($product_category);

								//Action for Duplicate Products
								if($record['duplicate_ean']==1){
									if($drm->overwrite==1){
										$duplicate = DB::table('drm_products')->where('user_id',$drm->user_id)->where('ean',$record['ean'])->where('drm_import_id','<>',$drm->id);
										$duplicate_product = $duplicate->first();
										if($duplicate_product!=null){
											unset($record['ean']);
											unset($record['duplicate_ean']);
											unset($record['user_id']);
											unset($record['delivery_company_id']);
											unset($record['country_id']);
											DB::table($table)->where('product_id',$duplicate_product->id)->update($record);
										}
									}
									$count++;
									continue;
								}
								//END Action for Duplicate Products

								unset($record['duplicate_ean']);
								$record['category_ids'] = $product_category;
								$record['update_status'] = makeUpdateStatusJson();
								$insert_array[] = $record;
							}
							$count++;
						}
						DB::table($table)->insert($insert_array);
						$message = ['total' => $total, 'count' => $count,'finalize'=> 1 ,'percent' => number_format(($count/$total)*100,2),'name' => strip_tags($value['title'])];
						// event(new progressEvent($message));
						sentProgress($message,'import');
					}
					$drm->import_finished=1;
					DB::table('drm_import_errors')->where('drm_import_id',$import_id)->delete();
					$drm->tmp_drm_products()->delete();
					Request::session()->put('curr_tab',null);
					$drm->current_step = null;
					$message=DB::table('notification_trigger')->where('hook','DRMImport')->where('status',0)->first();
					if($message){
					User::find(Crudbooster::myId())->notify(new DRMNotification($message->title, 'DRMImport','#'));
					$drm->save();
					}
					return true;
				}
				else{
					return false;
				}
			}

			public function getCategoryByName($trans_cat,$category,$user_id = null){
				if($user_id == null){
					$user_id = CRUDBooster::myId();
				}
				$categories = $this->getCategoryCache($user_id);
				$category = $categories->where($trans_cat,$category)->first();
				return $category;
			}

			public function getCategoryById($id,$user_id = null){
				if($user_id == null){
					$user_id = CRUDBooster::myId();
				}
				$categories = $this->getCategoryCache($user_id);
				$category = $categories->where('id',$id)->first();
				return $category;
			}

			public function getCategoryCache($user_id){
				$categories = Cache::remember('Categories_'.$user_id,05.0, function() use($user_id){
					$db_categories = DB::table('drm_category')->where('user_id',$user_id)->cursor();
					$all_categories = collect($db_categories->all());
					return $all_categories;
				});
				return $categories;
			}

		  public function drmCheckExcluded1($product,$filter){
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
				if(!$filter){
					return true;
				}
				if ($filter['price_below']!=null) {
					if($product->vk_price < $filter['price_below']){
						return false;
					}
				}

				if ($filter['price_more_than']!=null) {
					if($product->vk_price > $filter['price_more_than']){
						return false;
					}
				}

				if($filter['ean']!=null){
					foreach ($filter['ean'] as $value){
						if($value == $product->ean){
							return false;
						}
					}
				}

				if($filter['category']!=null){
					foreach ($filter['category'] as $value){
						if($value == $product->category){
							return false;
						}
					}
				}

				if($filter['stock']!=null){
					foreach ($filter['stock'] as $key => $value) {
						if($filter['stock_operator'][$key] == '='){
							if($product->stock == (int)$value){
								return false;
							}
						}
						if($filter['stock_operator'][$key] == '>'){
							if($product->stock > (int)$value){
								return false;
							}
						}
						if($filter['stock_operator'][$key] == '<'){
							if($product->stock < (int)$value){
								return false;
							}
						}
					}
				}
				return true;
		}

		public function drmCheckExcluded($import,$csv,$csv_fields,$filter){
			if(!$filter){
				return true;
			}

			$blacklist = 1;
			if(json_decode($filter->filter)){
				$blacklist = $filter->blacklist;
			}

			if ($filter['price_below']!=null) {
				if(drm_convert_european_to_decimal($csv[$csv_fields->vk_price],$import->money_format) < $filter['price_below']){
					if($blacklist==1) {
						return false;
					}
					return true;
				}
			}

			if ($filter['price_more_than']!=null) {
				if(drm_convert_european_to_decimal($csv[$csv_fields->vk_price],$import->money_format) > $filter['price_more_than']){
					if($blacklist==1) {
						return false;
					}
					return true;
				}
			}

			if($filter['ean']!=null){
				foreach ($filter['ean'] as $value){
					if($value == $csv[$csv_fields->ean]){
						if($blacklist==1) {
							return false;
						}
						return true;
					}
				}
			}

			if($filter['category']!=null){
				foreach ($filter['category'] as $value){
					if($value == $csv[$csv_fields->category]){
						if($blacklist==1) {
							return false;
						}
						return true;
					}
				}
			}

			if($filter['stock']!=null){
				foreach ($filter['stock'] as $key => $value) {
					if($filter['stock_operator'][$key] == '='){
						if($csv[$csv_fields->stock] == (int)$value){
							if($blacklist==1) {
								return false;
							}
							return true;
						}
					}
					if($filter['stock_operator'][$key] == '>'){
						if($csv[$csv_fields->stock] > (int)$value){
							if($blacklist==1) {
								return false;
							}
							return true;
						}
					}
					if($filter['stock_operator'][$key] == '<'){
						if($csv[$csv_fields->stock] < (int)$value){
							if($blacklist==1) {
								return false;
							}
							return true;
						}
					}
				}
			}
			return true;
	}

	public function checkValid($record){
		($record->ean_field==1)?$ean_field="ean" : $ean_field="item_number";
		if($record->$ean_field == null || $record->$ean_field == ""){
			return false;
		}
		else {
			return true;
		}
	}

	public function getImportPayment($need_data=null){
		ini_set('max_execution_time', '0'); // for infinite time of execution
		ini_set('memory_limit',-1);
		$old=DB::table('purchase_import_plans')->where('cms_user_id',CRUDBooster::myId())->first();
		$product=0;
		$data=$this->importProductCheck();
		$user = User::find(CRUDBooster::myId());
		$user_products = DB::table('drm_products')->where('user_id',$user->id)->count();
		$url='';
		$prev_url=session()->get('url');

		if(!empty($prev_url)){
			$url.=$prev_url;
		}

		if($user){
			if(!empty($need_data) and $need_data <0) {
				$product += abs($need_data);
			}
			else if($data['product_amount'] <0){
				$product+=(int)$data['product_amount'];
			}
	    	$query=DB::table('import_plans')
	    			->where('status',1)
	    			->where('product_amount','>',$product)
						->where('product_amount','>',$user_products)
	    			->orderby('product_amount','asc');
	    			if($old!=null){
	    				$query->whereNOTIn('id',[$old->import_plan_id]);
	    			}

	    	$plans=$query->get();

	    	//User term
	        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
	        $term = ($privacy)? $privacy->page_content : '';
	        $user_data = '<div id="customer_data_term"></div>';
	        if($user->billing_detail){
	            $billing = $user->billing_detail;
	            $user_data = '<div id="customer_data_term">'.$billing->company_name.'<br>'.$billing->address.'<br>'.$billing->zip.' '.$billing->city.'<br>'.$billing->country->name.'</div>';
	        }
	        if (strpos($term, '{customer}') !== false) {
	            $term = str_replace('{customer}', $user_data, $term);
	        }
	    	return view('app_store.import_payment',compact('plans','data', 'user','url', 'term'));
		}
		return redirect('/admin');
	}

    public function importProductCheck($user_id = null){
		if ($user_id == null) {
            $user_id = CRUDBooster::myParentId();
        }

        $is_dt_new_user = checkDtUser($user_id) && checkTariffEligibility($user_id);

        $trial = $this->checkImportTrial($user_id);
        $check_trial = DB::table('app_trials')->where(['user_id' => $user_id, 'app_id' => 0])->count();
        $manual_tarrif = new AdminManualImportTarrifController;

        if(!$is_dt_new_user){
            $assigned_amount = $manual_tarrif->checkManualTarrif($user_id);
        }else{
            $assigned_amount = $manual_tarrif->checkDtManualTarrif($user_id);
        }

        $total_products = drmTotalProduct($user_id);

        if(!$is_dt_new_user){
            $plan = $this->checkImportPlan($user_id);
        }else{
            $plan = $this->checkDtImportPlan($user_id);
        }
        
        $plan_total = $assigned_amount['amount'] + $plan['amount'];

        //Plan amonunt
        $plan_limit = $plan['amount'] ?? 0;

        if ($plan_total) {
            $check_trial = true;
        }

        if ($check_trial && $plan['import_plan_payment_discount'] != 100) {
            if ($plan['amount'] != 0 && $plan['amount'] >= $total_products) {
                $data['product_amount'] = $plan['amount'] - $total_products;
                $data['blocked'] = '';
                $data['limit'] = '';
                $data['plan'] = "Purchased";
                $data['days'] = $plan['days'];
                $data['plan_id'] = $plan['id'];
                $data['plan_total'] = $plan_total;
                $data['plan_limit'] = $plan_limit;

            } elseif ($assigned_amount['amount'] != 0 && $assigned_amount['amount'] >= $total_products) {
                $data['product_amount'] = $assigned_amount['amount'] - $total_products;
                $data['blocked'] = '';
                $data['limit'] = '';
                $data['plan'] = "Assigned";
                $data['days'] = $assigned_amount['days'];
                $data['plan_total'] = $assigned_amount['amount'];
                $data['plan_limit'] = $plan_limit;
            } elseif ($trial > 0) {
                $data['product_amount'] = 1;
                $data['blocked'] = '';
                $data['limit'] = 'Unlimited';
                $data['plan'] = "Trial";
                $data['days'] = $trial;

                if($is_dt_new_user){
					$dt_product_amount = DroptiendaPlan::DtPlanProductAmount($plan['id']);
                    $data['product_amount'] = $dt_product_amount - $total_products;
                    $data['limit'] = $dt_product_amount;

                    if(in_array($user_id, DroptiendaPlan::SPECIAL_USER_IDS)){
                        $data['product_amount'] = DroptiendaPlan::SPECIAL_USER_PRODUCT_AMOUNT[$user_id] - $total_products; //Special priveledge user, custom amount of product
                        $data['limit'] = DroptiendaPlan::SPECIAL_USER_PRODUCT_AMOUNT[$user_id];
                    }
                }
            } else {

                // if(isLocal()){
                $data['product_amount'] = 0;
                $data['blocked'] = 'blocked';
                $data['limit'] = '';
                $data['plan_total'] = $plan_total;
                $data['plan_limit'] = $plan_limit;
                // }


                if ($assigned_amount['amount']) {
                    $plan_total = (int)$assigned_amount['amount'];
                    $data['plan'] = "Assigned";
                    $data['days'] = $assigned_amount['days'];
                } elseif ($plan['amount']) {
                    $plan_total = (int)$plan['amount'];
                    $data['plan'] = "Purchased";
                    $data['days'] = $plan['days'];
                    $data['plan_id'] = $plan['id'];
                } else {
                    $plan_total = 0;
                    $data['days'] = 0;
                    $data['total_product'] = $total_products;
                    $data['blocked'] = '';
                    $data['product_amount'] = 500 - $total_products;
                    $data['plan'] = "500 Free Products";
                    $data['plan_total'] = 500;
                    $data['plan_limit'] = $plan_limit;

                    if($is_dt_new_user){
                        $data['plan_limit'] = DroptiendaPlan::DtPlanProductAmount($plan['id']);

                        if(in_array($user_id, DroptiendaPlan::SPECIAL_USER_IDS)){
                            $data['plan_limit'] = DroptiendaPlan::SPECIAL_USER_PRODUCT_AMOUNT[$user_id]; //Special priveledge user, custom amount of product
                        }
                    }
                }
            }
        } else {
            $data['product_amount'] = 1;
            $data['blocked'] = '';
            $data['limit'] = 'Unlimited';
            $data['plan'] = "none";
            $data['days'] = 0;
            $data['plan_total'] = 0;
            $data['plan_limit'] = $plan_limit;
            $data['import_plan_discount'] = $plan['import_plan_payment_discount'];
        }

        if (empty($check_trial) && empty($plan) && $total_products <= 500) {
            $data['product_amount'] = 500 - $total_products;
            $data['blocked'] = '';
            $data['limit'] = 500;
            $data['plan'] = "Free";
            $data['days'] = null;
            $data['plan_total'] = $assigned_amount['amount'] + 500;
            $data['plan_limit'] = $plan_limit;
            $data['free_500'] = true;
        }

        if (!empty($plan['is_unlimited']) && $plan['is_unlimited']) {
            $data['product_amount'] = $plan['amount'];
            $data['blocked'] = '';
            $data['limit'] = 'Unlimited';
            $data['plan'] = "Unlimited";
            $data['is_unlimited'] = true;
            $data['days'] = $plan['days'];
            $data['plan_total'] = $plan['amount'];
            $data['plan_limit'] = $plan_limit;
            $data['import_plan_discount'] = $plan['import_plan_payment_discount'];
        }

        // isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592]) || 
        if(checkTariffEligibility($user_id)){
            if($data['plan'] == "500 Free Products"){
                $data['blocked'] = 'block';
            }
        }

        $data['import_plan_id'] = $plan['import_plan_id'];

        if($is_dt_new_user){
            if(!empty($assigned_amount)){
                $data['import_plan_id'] = $assigned_amount['plan_id'];
            }
        }

        if($data["plan"] === "Purchased" && empty($plan['stripe_subscription_id']))
        {
            $data['plan'] = "Assigned";
        }

        // TODO::MAX DATE
        if(isset($plan['end_date']))
        {
            $data['end_date'] = $plan['end_date'];
        }elseif(isset($assigned_amount['end_date']))
        {
            $data['end_date'] = $assigned_amount['end_date'];
        }

        return $data;
	}


		public function checkImportPlan($user_id){

			$data = [];

			$date_diff = 0;
			$plan = DB::table('purchase_import_plans')
				->where('cms_user_id', $user_id)
				->first();

			$import_plan_discount = DB::table('import_plan_get_discounts')
				->where('user_id', $user_id)
				->where('end_date', '>=', Carbon::now()->toDateTimeString())
				->where('status', 1)
				->first();

			if ($plan) {
				$end_date = $plan->end_date;
				$date_diff = (int)DateTime::getRemainDays(date('Y-m-d'), $end_date);
				if($date_diff === 0) {
					$date_diff = 1;
				}

				$data['end_date'] = date('Y-m-d', strtotime($end_date));
			}
			if ($date_diff > 0) {
				$data['amount'] = (int)$plan->product_amount_import;
				$data['is_unlimited'] = $plan->import_plan_id == 23;
				$data['days'] = $date_diff;

				$data['stripe_subscription_id'] = $plan->stripe_subscription_id;
				$data['import_plan_id'] = $plan->import_plan_id;

				if ($import_plan_discount) {
					$data['import_plan_payment_discount'] = $plan->import_plan_percentage_discount;
				} else {
					$data['import_plan_payment_discount'] = 0;
				}

				$data['id'] = $plan->id;

				return $data;
			} else {
				return false;
			}
		}

		public function checkDtImportPlan($user_id)
		{
			$data = [];

			$date_diff = 0;
			$plan = DB::table('dt_tariff_purchases')
			->where('user_id', $user_id)
			->where(function($q){
				$q->whereNotNull('subscription_id');
				$q->where('subscription_id', '<>', '');
			})
			->first();

			if ($plan) {
				$end_date = $plan->end_date;
				$date_diff = (int) DateTime::getRemainDays(date('Y-m-d'), $end_date);

				if($date_diff === 0) {
					$date_diff = 1;
				}

				$data['end_date'] = date('Y-m-d', strtotime($end_date));
			}

			if ($date_diff > 0) {
				$data['amount'] = DroptiendaPlan::DtPlanProductAmount($plan->plan_id);
				$data['is_unlimited'] = $plan->plan_id == 31;
				$data['days'] = $date_diff;

				$data['stripe_subscription_id'] = $plan->subscription_id;
				$data['import_plan_id'] = $plan->plan_id;

				$data['id'] = $plan->id;

				if(in_array($user_id, DroptiendaPlan::SPECIAL_USER_IDS)){
					$data['amount'] = DroptiendaPlan::SPECIAL_USER_PRODUCT_AMOUNT[$user_id]; //Special priveledge user, custom amount of product
				}

				return $data;
			} else {
				return false;
			}
		}

		public function checkImportTrial($user_id){
			$remain_days = 0;
			$trial = DB::table('app_trials')
			->select('trial_days','start_date')
			->where(['user_id' => $user_id , 'app_id' => 0])->first();
			if($trial){
				$remain_days = DateTime::getTrialRemaining($trial->start_date,$trial->trial_days);
			}
			return $remain_days;
		}

		public function setImportTrial($user_id){
			$check_trial = DB::table('app_trials')->where(['user_id' => $user_id,'app_id' => 0])->count();
			$check_product = DB::table('drm_products')->where('user_id',$user_id)->first();
			if(!$check_trial && $check_product!=null){
				$date = date("Y-m-d");
				DB::table('app_trials')->insert([
					'user_id' => $user_id,
					'app_id' => 0,
					'trial_days' => 14,
					'start_date' => $date
				]);
			}
		}

		public function checkIsProfessionalOrEnterprice($user_id)
		{
			$checkUserTariff = DB::table('purchase_import_plans')->where('cms_user_id', $user_id)
				->whereIn('import_plan_id', [26, 27])
				->first();
	
			return $checkUserTariff;
		}
		
		//////////////////////////////////////////////////////////////////////////
		////////////////////////////// Auto sync /////////////////////////////////
		//////////////////////////////////////////////////////////////////////////


		public function csvToArrayModified($path,$type,$delimiter,$cloud = true)
		{
			ini_set('memory_limit',-1);
			ini_set('max_execution_time', '0'); // for infinite time of execution
			$rand=Str::random(40);
			if($cloud == true){
				$path = Storage::disk('spaces')->url($path);
				file_put_contents($rand.'.'.$type, fopen($path, 'r'));
				$localpath = $rand.'.'.$type;
			}
			else {
				$localpath = $path;
			}
			if($type =='csv'|| $type == 'txt'){
				$reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
				// $reader->setInputEncoding('UTF-8');
				if($delimiter!='auto'){
					$reader->setDelimiter($delimiter);
				}
				$spreadsheet = $reader->load($localpath);
			}
			else{
				$spreadsheet = IOFactory::load($localpath);
			}
			if($cloud){
				unlink($localpath);
			}
			return $spreadsheet->getActiveSheet()->toArray();
		}


		public function syncProduct($import_id){
			ini_set('memory_limit',-1);
			ini_set('max_execution_time', '0');

			$time_now = Carbon::now();
			$time_string = $time_now->toDateTimeString();

			$import_info = DrmImport::find($import_id);
			$user_id = $import_info->user_id;
			$file_url = $import_info->file_url;
			$path = $import_info->csv_file_path;


			if(!checkRemoteFile($file_url)){
				User::find($user_id)->notify(new DRMNotification("Invalid URL for ".$import_info->csv_file_name, 'fileNotFound',url('admin/drm_imports/update-csv?id='.$import_id)));
				CRUDBooster::redirect('admin/drm_imports',"File not found !","warning");
				die();
			}

			
			$sync_id = DB::table('drm_import_sync')->insertGetId(['drm_import_id' => $import_info->id, 'created_at' => $time_string]);

			$type = pathinfo($file_url, PATHINFO_EXTENSION);
			$message = ['feed_name'=>$import_info->csv_file_name,'user_id'=>$user_id,'text'=> 'Update is Starting...'];
			sentProgress($message,'importSync');
			// Session::put('importSync'.$user_id,'true');
			// Session::save(); 
			$file_equal = $this->checkFileEqual($file_url,$import_info);

			if($file_equal==true)
			{
				$time_now = Carbon::now();
				$time_string = $time_now->toDateTimeString();
				$sync_id = DB::table('drm_import_sync')->insertGetId(['drm_import_id' => $import_id, 'created_at' => $time_string]);
				$message = ['message'=>'end','user_id'=>$user_id];
				sentProgress($message,'importSync');
				// Session::forget('importSync'.$user_id);
				// Session::save();
				CRUDBooster::redirect('admin/drm_imports',"No Updates Found !","info");
				die();
			}
			$this->deleted_products = DB::table('drm_deleted_products')->where('drm_import_id',$import_id)->pluck('ean')->toArray();
			$csv_fields = DB::table('drm_product_fields')->where('drm_import_id',$import_id)->first();
			// $this->setEanField($import_info,$csv_fields);
			$country = DB::table('countries')->where('id',$import_info->country_id)->first()->language_shortcode;
			$this->table = "drm_translation_".$country;
			$this->trans_cat = "category_name_".$country;

			$this->import_categories = json_decode($import_info->category_ids,true);

			// $category_ids = json_decode($import_info->category_ids);
			// $categories = DB::table('drm_category')->select('id',$this->trans_cat)->whereIn('id',$category_ids)->cursor();
			// $category_array = makeArrayUtf8(json_decode(json_encode($categories->toArray()),true));
			// $this->all_categories = collect($category_array);

			// dd(peakMemory());



			$profit_margins = DB::table('drm_product_profit_margins')->where('drm_import_id',$import_id)->cursor();
			$this->profit_margins = collect($profit_margins->all());
			
			$this->fallback = DB::table('drm_fallback_calculations')
			->select('profit_percent','shipping_cost','additional_charge')
			->where('id',$import_info->fallback)
			->first();
			$csv_data = $this->csvToArrayModified($path,$type,$import_info->delimiter);
			// $csv_data = $this->getCsv($import_info);
			$key = array_map('trim', $csv_data[0]);
			$key = array_map('removeDots',$key);
			$key_count = count($key);
			unset($csv_data[0]);
			$total = count($csv_data);
			$this->count = 0;
			// Session::put('importSyncTotal'.$user_id,$total);
			// Session::put('syncCount'.$user_id,0);
			// Session::put('sync_feed'.$user_id,$import_info->csv_file_name);
			// Session::put('importSyncPercent'.$user_id,0);
			// Session::save();

			$Import_filter = DB::table('drm_import_filter')->where('drm_import_id',$import_id)->first();
			$this->filter = json_decode($Import_filter->filter, true);
			$this->import_check = $this->importProductCheck($user_id);
			// $eans = DB::table('drm_products')
			// 							->where('drm_import_id',$import_id)->whereNull('deleted_at')->pluck($this->ean)->toArray();
			// peakMemory();
			$this->calc_config = [
				'ek_field' => $csv_fields->ek_price,
				'vk_field' => $csv_fields->vk_price,
				'money_format' => $import_info->money_format,
			];

			try{
				foreach(array_chunk($csv_data,500) as $lines){
					array_walk($lines, function (& $item) use($key){
						$item = array_combine($key, $item);
						$item = makeArrayUtf8(makeArrayUtf8($item));
					});
					$csv = collect($lines);
					// $csv_eans = $csv->pluck($this->ean_field)->toArray();
					$csv_eans = $csv->where($csv_fields->ean,"!=","")->where($csv_fields->ean,"!=",null)->pluck($csv_fields->ean)->toArray();
					$csv_eans = array_map('makeInt',$csv_eans);
					$csv_sku = $csv->where($csv_fields->item_number,"!=","")->where($csv_fields->item_number,"!=",null)->pluck($csv_fields->item_number)->toArray();
				  	$db_products = DB::table('drm_products')
							->select('drm_products.*',$this->table.'.title',$this->table.'.description as description')
							->leftjoin($this->table,$this->table.'.product_id','drm_products.id')
							->where('drm_products.drm_import_id',$import_id)
							->whereNull('drm_products.deleted_at')
							->where(function($q) use($csv_eans,$csv_sku){
								return $q->whereIn('drm_products.ean',$csv_eans)->orWhereIn('drm_products.item_number',$csv_sku);
							})
							->cursor();
	
					$db_products_array = makeArrayUtf8(json_decode(json_encode($db_products->toArray()),true));
					$this->all_products = collect($db_products_array);
					$ids = $this->all_products->pluck('id')->toArray();
					
					$product_categories = DB::table('drm_product_categories')
					->whereIn('product_id',$ids)->cursor();
					$this->all_categories = collect($product_categories->all()); 
					

					$this->image_changed = [];
					$this->changed_products = [];
					$this->bundle_update = array();
					array_walk($lines, function ($csv) use($csv_fields,$import_info){
						$product = $this->all_products->where("ean",$csv[$csv_fields->ean])->first();
						if(empty($product)){
							$product = $this->all_products->where("item_number",trim($csv[$csv_fields->item_number]))->first();
						}

						$product = (object)$product;

						$product_categories = $this->all_categories->where('product_id',$product->id)->pluck('category_id')->toArray();
						
						$this->calc_config['ek_price'] = $csv[$csv_fields->ek_price];
						$this->calc_config['vk_price'] = $csv[$csv_fields->vk_price];
						$this->calc_config['profit_margins'] = $this->profit_margins->whereIn('category_id',$product_categories);
						$this->calc_config['fallback'] = $this->fallback;

						$price = drmPriceCalculationNew($this->calc_config);

						if((array)$product){
							$changed = $this->checkChanges($csv,$product,$csv_fields,$import_info,$price);
							if($changed['status']){
								$this->changed_count++;
								$this->makeChange($csv,$csv_fields,$product,$price,$import_info,$country);
								
								if($product->has_bundle){
									$this->bundle_update[] = $product->id;
								}
								// $log = json_encode($changed['log']);
								// $this->changed_products[] = array('drm_product_id'=>$product->id, 'change_log' => $log, 'status' => '2','sync_id' => $sync_id);
								$this->changed_products[] = $product->id;
							}
						}
						else{
							if (($this->import_check['limit']=='') and ($this->new_count <= $this->import_check['product_amount'])){
								if($import_info->allow_new){
									if($this->isValid($csv,$csv_fields,$import_info) && $this->drmCheckExcluded($import_info,$csv,$csv_fields,$this->filter)){
										$id = $this->createProduct($csv,$csv_fields,$import_info,$price);
										if($id!=false){
											$this->new_count++;
											// $this->new_products[] = array('drm_product_id' => $id, 'status' => '1','sync_id' => $sync_id);
										}
									}
								}
							}
						}
						$this->count++;
					});
					
					if($drm->image_backup){
						if(count($this->image_changed)){
							UpdateBackupImages::dispatch($drm,$this->image_changed);
						}
					}

					UpdateProductBundle::dispatch($this->bundle_update,$user_id);

					AdminDrmExportsController::updateAnyConnectedShopProduct(array_unique($this->changed_products),$user_id,$country);
					$this->updateChannelProducts(array_unique($this->changed_products),$user_id);
					$message = ['feed_name'=>$import_info->csv_file_name,'user_id'=>$user_id,'total' => $total, 'count' => $this->count,'new_count' => $this->new_count, 'changed_count' => $this->changed_count, 'percent' => round(($this->count/$total)*100,2),'name' => peakMemory()];
					sentProgress($message,'importSync');
					// Session::put('syncCount'.$user_id,$this->count);
					// Session::put('importSyncPercent'.$user_id,round(($this->count/$total)*100,2));
					// Session::save();
				}
			}catch (\Exception $e){
				AdminDrmExportsController::updateAnyConnectedShopProduct(array_unique($this->changed_products),$user_id,$country);
				$this->updateChannelProducts(array_unique($this->changed_products),$user_id);
				$message = ['feed_name'=>$import_info->csv_file_name,'user_id'=>$user_id,'total' => $total, 'count' => $this->count,'new_count' => $this->new_count, 'changed_count' => $this->changed_count, 'percent' => round(($this->count/$total)*100,2),'name' => peakMemory()];
				sentProgress($message,'importSync');
				// Session::put('syncCount'.$user_id,$this->count);
				// Session::put('importSyncPercent'.$user_id,round(($this->count/$total)*100,2));
				// Session::save();
			}



			// // $csv_eans = $csv->pluck($this->ean_field)->toArray();
			// $csv_eans = $csv->pluck($csv_fields->ean)->toArray();
			// $csv_sku = $csv->pluck($csv_fields->item_number)->toArray();
			//Product Deletion Optimizwd Way
			// if(count($this->csv_products)){
			// 	$product_to_delete = array_diff($eans,$this->csv_products);
			// 	if($product_to_delete[0]){
			// 		foreach (array_chunk($product_to_delete,500) as $deletion_data)
			// 		{
			// 			$product = DrmProduct::whereIn('ean',$deletion_data)->where('drm_import_id',$import_id)->delete();
			// 			// $result = $product->delete();
			// 		}
			// 	}
			// }
			//End
			$this->deleteOutStocked($import_info,$csv_data,$csv_fields,$key);

			// $this->hookSyncCsv($this->changed_products,$this->new_products);
			$message = ['message'=>'end','user_id'=>$user_id];
			sentProgress($message,'importSync');

			$user=DB::table('cms_users')->where('id',$import_info->user_id)->first();

			User::find($user->id)->notify(new DRMNotification($import_info->csv_file_name.' has been synced successfully ', 'ProductSyncSuccess','#'.$sync_id));
			// return redirect('admin/drm_imports');
		}

		public function test1(){
			$import_id = 26;
			$import = DrmImport::find($import_id);
			$csv = $this->getCsv($import);
			$key = $csv[0];
			dd(makeArrayUtf8($key));
		}

		public function deleteOutStocked($import,$csv,$csv_fields,$key){
			$import_id = $import->id;
			$eans = DB::table('drm_products')
					->where('drm_import_id',$import_id)
					->where('ean_field',1)
					->whereNull('deleted_at')
					->pluck("ean")->toArray();

			$sku = DB::table('drm_products')
					->where('drm_import_id',$import_id)
					->where('ean_field',0)
					->whereNull('deleted_at')
					->pluck("item_number")->toArray();
					
				$key_count = count($key);
				$new_csv = [];
				array_walk($csv, function (&$line) use($key,$key_count,&$new_csv){
					if(count($line)==$key_count && !containsOnlyNull($line)){
						$key = makeArrayUtf8(makeArrayUtf8($key));
						$line = makeArrayUtf8(makeArrayUtf8($line));
						$new_csv[] = array_combine($key,$line);
					}
					$i++;
				});
			$csv = collect($new_csv);
			$csv_eans = $csv->whereNotNull($csv_fields->ean)->pluck($csv_fields->ean)->toArray();
			$csv_sku = $csv->whereNotNull($csv_fields->item_number)->pluck($csv_fields->item_number)->toArray();
			$product_to_delete_ean = array_diff($eans,$csv_eans);
			$product_to_delete_sku = array_diff($sku,$csv_sku);

			if($import_id!=1127){
				foreach (array_chunk($product_to_delete_ean,500) as $deletion_data){
					$product = DrmProduct::whereIn('ean',$deletion_data)->where('ean_field',1)->where('drm_import_id',$import_id)->update(['stock' => 0]);
				}
			}

			foreach (array_chunk($product_to_delete_sku,500) as $deletion_data){
				$product = DrmProduct::whereIn('item_number',$deletion_data)->where('ean_field',0)->where('drm_import_id',$import_id)->update(['stock' => 0]);
			}

		}

		public function hookSyncCsv($changed_products,$new_products)
		{
			ini_set('max_execution_time', '0'); // for infinite time of execution
			ini_set('memory_limit',-1);
			DB::table('drm_import_sync_report')->insert($changed_products);
			DB::table('drm_import_sync_report')->insert($new_products);
		}

		public function checkChanges($csv,$product,$csv_fields,$drm,$price)
		{
			$update_status = json_decode($product->update_status);
			$changed['status'] = false;
			$changed['log'] = [];
			$import_id = $drm->id;

			$stock = trim($csv[$csv_fields->stock]);
			if((int)$stock<0){
				$stock = 0;
			}

			$image_prefix = $drm->image_prefix;
			$separator = $drm->image_separator;
			$images = $this->drmGetCsvImageJson($csv,$csv_fields->image,$image_prefix,$separator);
			
			if($drm->image_backup){
				$product->image = $product->original_images;
			}

			if($images!=$product->image && $update_status->image){
				$changed['status'] = true;
				$changed['log'][] = 'Image';
				$this->image_changed[] = $product->id;
			}

			if($csv_fields->vk_price!=null && $csv_fields->vk_price!=''){
				$csv_price = drm_convert_european_to_decimal($csv[$csv_fields->vk_price],$drm->money_format);
				$product_price = $product->vk_price;
			}
			else{
				$csv_price = $price;
				$product_price = $product->vk_price;
			}
			if($csv_price!= $product_price && $update_status->vk_price){
				$changed['status'] = true;
				$changed['log'][] = 'Price';
			}
			if($csv[$csv_fields->name] != $product->title && $update_status->title){
				$changed['status'] = true;
				$changed['log'][] = 'Title';
			}
			if($csv[$csv_fields->description] != $product->description && $update_status->description){
				$changed['status'] = true;
				$changed['log'][] = 'Description';
			}
			if($stock!= $product->stock && $update_status->stock){
				$changed['status'] = true;
				$changed['log'][] = 'Stock';
			}
			if(trim($csv[$csv_fields->gender])!= $product->gender && $update_status->gender){
				$changed['status'] = true;
				$changed['log'][] = 'Gender';
			}
			if(trim($csv[$csv_fields->materials])!= $product->materials && $update_status->materials){
				$changed['status'] = true;
				$changed['log'][] = 'Materials';
			}
			if(trim($csv[$csv_fields->production_year])!= $product->production_year && $update_status->production_year){
				$changed['status'] = true;
				$changed['log'][] = 'Production Year';
			}
			if(trim($csv[$csv_fields->brand])!= $product->brand && $update_status->brand){
				$changed['status'] = true;
				$changed['log'][] = 'Brand';
			}
			if(trim($csv[$csv_fields->item_weight])!= $product->item_weight && $update_status->item_weight){
				$changed['status'] = true;
				$changed['log'][] = 'Item Weight';
			}
			if(trim($csv[$csv_fields->item_size])!= $product->item_size && $update_status->item_size){
				$changed['status'] = true;
				$changed['log'][] = 'Item Size';
			}
			if(trim($csv[$csv_fields->item_color])!= $product->item_color && $update_status->item_color){
				$changed['status'] = true;
				$changed['log'][] = 'Item Color';
			}

			return $changed;
		}

		public function isValid($data,$field,$drm)
		{
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			$valid = true;
			if(in_array(trim($data[$field->ean]),$this->deleted_products)){
				return false;
			}
			if(trim($data[$field->ean]) == null || trim($data[$field->ean]) == ''){
				$valid = false;
			}
			if(trim($data[$field->name]) == null || trim($data[$field->name]) == ''){
				$valid = false;
			}

			if(trim($data[$field->description]) == null || trim($data[$field->description]) == ''){
				$valid = false;
			}

			if(trim($data[$field->category]) == null && trim($field->category) != null && !$drm->custom_category && !$drm->existing_category){
				$valid = false;
			}

			if(drm_convert_european_to_decimal(trim($data[$field->ek_price]),$drm->money_format) == 0){
				$valid = false;
			}

			if((filter_var(trim($data[$field->stock]), FILTER_VALIDATE_INT) === false) && (int)$data[$field->stock] == 0){
				$valid = false;
			}

			if(!$this->validateEan(trim($data[$field->ean]))){
				$valid = false;
			}

			return $valid;
		}

		public function makeChange($csv,$csv_fields,$product,$price,$drm,$country)
		{
			$update_status = json_decode($product->update_status);
			$import_id = $product->drm_import_id;
			$image_prefix = $drm->image_prefix;
			$separator = $drm->image_separator;
			$images = $this->drmGetCsvImageJson($csv,$csv_fields->image,$image_prefix,$separator);

			if($update_status->vk_price){
				$values['ek_price'] = drm_convert_european_to_decimal($csv[$csv_fields->ek_price],$drm->money_format);
				$values['vk_price'] = $price;
			}
			if($update_status->stock && !$drm->unlimited_quantity){
				$values['stock'] = (int)trim($csv[$csv_fields->stock]);
			}
			if($update_status->image){
				if($drm->image_backup){
					$values['original_images'] = $images;
				}
				else{
					$values['image'] = $images;
				}
			}
			if($update_status->gender){
				$values['gender'] = trim($csv[$csv_fields->gender]);
			}
			if($update_status->materials){
				$values['materials'] = trim($csv[$csv_fields->materials]);
			}
			if($update_status->production_year){
				$values['production_year'] = trim($csv[$csv_fields->production_year]);
			}
			if($update_status->item_weight){
				$values['item_weight'] = trim($csv[$csv_fields->item_weight]);
			}
			if($update_status->brand){
				$values['brand'] = trim($csv[$csv_fields->brand]);
			}
			if($update_status->item_size){
				$values['item_size'] = trim($csv[$csv_fields->item_size]);
			}
			if($update_status->item_color){
				$values['item_color'] = trim($csv[$csv_fields->item_color]);
			}
			// if($update_status->title){
			// 	$values['title'] = $csv[$csv_fields->name];
			// }
			// if($update_status->description){
			// 	$values['description'] = $csv[$csv_fields->description];
			// }
			if(is_array($values)){
				if((int)$values['stock'] != (int)$product->stock){
					$time_now = Carbon::now();
					$time_string = $time_now->toDateTimeString();
					$values['old_stock'] = (int)$product->stock;
					$values['stock_updated_at'] = $time_string;
				}

				DB::table('drm_products')->where('id',$product->id)->update($values);
			}
		}


		public function validateEan($ean){
			$ean_original = strip_tags($ean);
			$sanitize = explode('.',$ean_original);
			$ean_original = $sanitize[0];
			$ean = trim($ean_original);
			$int_ean = (int)$ean;

			if(strlen($ean) > 7 && strlen($ean) < 14 && $int_ean){
				return $ean;
			}
			else {
				return null;
			}
		}

		public function createProduct($csv,$csv_fields,$import,$price)
		{
			$image_prefix = $import->image_prefix;
			$image = $this->drmGetCsvImageJson($csv,$csv_fields->image,$image_prefix,$import->image_separator);

			$ean=$csv[$csv_fields->ean];
			$stock = trim($csv[$csv_fields->stock]);
			if((int)$stock<0){
				$stock = 0;
			}
			if($ean!=null && $ean!='')
			{
				$values = array(
					'drm_import_id' => $import->id,
					'user_id' => $import->user_id,
					'delivery_company_id' => $import->delivery_company_id,
					'country_id' => $import->country_id,
					'item_number' => $csv[$csv_fields->item_number],
					'ean' => $csv[$csv_fields->ean],
					// 'title' => $csv[$csv_fields->name],
					// 'description' => $csv[$csv_fields->description],
					'ek_price' => $csv[$csv_fields->ek_price],
					'vk_price' => $price,
					'stock' => $stock,
					'status' => $csv_fields->status,
					'image' => $image,
					'gender' => $csv[$csv_fields->gender],
					'materials' => $csv[$csv_fields->materials],
					'production_year' => $csv[$csv_fields->production_year],
					'item_weight' => $csv[$csv_fields->item_weight],
					'brand' => $csv[$csv_fields->brand],
					'item_size' => $csv[$csv_fields->item_size],
					'item_color' => $csv[$csv_fields->item_color],
					'update_status' => makeUpdateStatusJson(),
					'tags' =>  generateTags([strip_tags($csv[$csv_fields->category]),$csv[$csv_fields->brand],$csv[$csv_fields->gender]])
				);

				// $existing_category = $this->getCategoryByName($this->trans_cat,strip_tags($csv[$csv_fields->category]),$import->user_id);
				
				$existing_category = DB::table('drm_category')->where(['user_id' => $import->user_id , $this->trans_cat => trim(strip_tags($csv[$csv_fields->category]))])->first();

				// $category = [];
				if($existing_category){
					$category = $existing_category->id;
				}
				else{
					$category = DB::table('drm_category')
					->insertGetId([
						'user_id' => $import->user_id,
						'drm_import_id' => $import->id,
						$this->trans_cat => trim(strip_tags($csv[$csv_fields->category])),
						'country_id' => $import->country_id,
					]);
				}
				// $values['category_ids'] = json_encode($category);

				$id = DB::table('drm_products')->insertGetId($values);
				DB::table($this->table)->insert(['product_id' => $id,'title' => $csv[$csv_fields->name],'description' => $csv[$csv_fields->description]]);
				DB::table('drm_product_categories')->insert(['product_id' => $id, 'category_id' => $category]);
				if($category){
					$import_category = $this->import_categories;
					$import_category[] = $category;
					DB::table('drm_imports')->where('id',$import->id)->update(['category_ids'=>json_encode($import_category)]);
				}
				return $id;
			}
			else{
				return false;
			}
		}


		public function drmGetCsvImageJson($data,$fields,$image_prefix,$separator)
		{
			ini_set('max_execution_time', '0'); // for infinite time of execution
				ini_set('memory_limit',-1);
			if($separator==null){
				$separator = ';';
			}

			$images =  explode('|',$fields);
			$final_array = array();
			foreach ($images as $header)
			{
				$csv_images = explode($separator,$data[$header]);
				$final_array = array_merge($final_array,$csv_images);
			}

			$final_img=array();
			foreach ($final_array as $key => $value)
			{
				if (filter_var($value, FILTER_VALIDATE_URL) === FALSE){
					$value = $this->importImagePrefix($image_prefix,$value);
				}

				$img_data['id']=$key+1;
				if($key>10)
				{
					$img_data['status']=0;
				}
				else
				{
					$img_data['status']=1;
				}

				$img_data['src']=$value;
				$final_img[]=$img_data;
			}

			$json_image = json_encode($final_img);
			return $json_image;
		}



				public function importImagePrefix($prefix,$image){
					ini_set('max_execution_time', '0'); // for infinite time of execution
					ini_set('memory_limit',-1);
						if(substr($prefix , -1)== '/'){
								$image = $prefix.$image;
						 }else {
								$image = $prefix.'/'.$image;
						 }
						return $image;
				}

				public function checkFileEqual($file_url,$import){
					ini_set('max_execution_time', '0'); // for infinite time of execution
					ini_set('memory_limit',-1);
						$rand=Str::random(40);
						$rand2=Str::random(40);
						$csv_data_new = file_get_contents_utf8($file_url);
						file_put_contents($rand.'.csv',$csv_data_new);
						$path = $rand.'.csv';

						if($import->csv_file_path == ""){
							$exist = false;
						}
						else {
							$exist = Storage::disk('spaces')->exists($import->csv_file_path);
						}

						if($exist){
							$db_csv_path = Storage::disk('spaces')->url($import->csv_file_path);
							$csv_data_old = file_get_contents_utf8($db_csv_path);
							file_put_contents($rand2.'.csv',$csv_data_old);
							$db_csv_path = $rand2.'.csv';
							$equal = files_are_equal($path,$db_csv_path);
							Storage::disk('spaces')->delete($import->csv_file_path);
							unlink($db_csv_path);
						}
						else {
							$equal = false;
						}
						Storage::disk('spaces')->put($import->csv_file_path,$csv_data_new,'public');
						unlink($path);
						return $equal;
				}

				public function validateFile($collection){
					$valid = true;
					$count = 0;
					foreach($collection as $key => $value){
						if($key == 0){
							if(containsOnlyNull($value)){
								$valid = false;
							}
							if(hasBigString($value)){
								$valid = false;
							}
							if(count($value)<2){
								$valid = false;
							}
							$valid = checkArrayKey($value,$collection);

							// if(arrayNullCount($value)>4){
							// 	$valid = false;
							// }
						}
						else {
							// if(containsOnlyNull($value)){
							// 	$count++;
							// }
							$utf_8 = makeArrayUtf8(makeArrayUtf8($value));
							if(json_encode($utf_8)==false){
								$valid = false;
							}
						}
					}
					// if(count($collection)==5 && $count>2){
					// 	$valid = false;
					// }
					return $valid;
				}


				public function getManualUpdateImport(){
            return redirect()->back();
        }


				// public function postManualUpdateImport(){
				// 	// ini_set('max_execution_time', '0'); // for infinite time of execution
				// 	// ini_set('memory_limit',-1);
				// 	$items = $_REQUEST['update_items'];
				// 	$delimiter = $_REQUEST['delimiter'];
				// 	$import_id = Request::input('drm_import_id');
				// 	$object = DrmImport::find($import_id);
				// 	if(Request::input('file_type') == 1){
				// 		$type='file';
				// 	}
				// 	else{
				// 		$type='url';
				// 	}
				//
				// 	if($type == 'file'){
				// 		if(!Request::hasFile('csv_file')){
				// 			return abort(500);
				// 		}
				// 		foreach(Request::file('csv_file') as $file){
				// 			$file_type = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
				// 			$rand=Str::random(40);
				// 			$path = $file->storeAs('public/csv_files',$rand.'.'.$file_type,['visibility'=>'public','disk'=>'local']);
				// 			$path = str_replace('public','storage',$path);
				// 		}
				// 	}
				// 	else{
				// 		$url_file=trim(Request::input('csv_link'));
				// 		$csv_data= file_get_contents($url_file);
				// 		$rand=Str::random(40);
				// 		Storage::disk('local')->put('public/csv_files/'.$rand.'.csv',$csv_data);
				// 		$path = 'storage/csv_files/'.$rand.'.csv';
				// 		$file_type = "csv";
				// 	}
				//
				// 	$header = $this->csvToCsvHeaderJson($path,$delimiter,false);
				//
				// 	if($header == false){
				// 		CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'), "Invalid File", "danger");
				// 	}
				//
				// 	if($_POST['button'] == 'delete'){
				// 		$csv = $this->csvToArray($path,$file_type,$delimiter,false);
				// 		$collection = LazyCollection::make($csv);
				// 		$ean = $collection->pluck($fields->ean)->unique()->toArray();
				// 		$drm_product = DB::table('drm_products')->whereIn('ean',$ean)->where('user_id',CRUDBooster::myId())->pluck('id')->toArray();
				// 		if( count($drm_product)> 0){
				// 			DB::table('drm_products')->whereIn('ean',$ean)->where('user_id',CRUDBooster::myId())->delete();
				// 		}
				// 		Storage::disk('local')->delete(str_replace('storage','public',$path));
				// 		return back()->with('success','Product Manually Deleted successfully');
				// 	}
				// 	else{
				// 		$data['header'] = $header;
				// 		$data['import_id'] = $import_id;
				// 		$data['items'] = $items;
				// 		$data['drm'] = $object;
				// 		$data['step'] = 2;
				//
				// 		Session::put('delimiter',$delimiter);
				// 		Session::put('path',$path);
				//
				// 		Session::save();
				// 		return view('admin.drm_import.after_import.manual_update_sync_fields',$data);
				// 	}
				// }



				public function getManualProgress(){
					return view('admin.drm_import.manual_update_progress');
				}

			// public function postManualUpdateSave(){
			// 	ini_set('max_execution_time', '0'); // for infinite time of execution
			// 	ini_set('memory_limit',-1);
			// 	$items = $_REQUEST;
			// 	unset($items['drm_import_id']);
			// 	unset($items['_token']);
			// 	$import_id = Request::input('drm_import_id');
			//
			// 	$message = ['text'=>'Saving Report...','percent'=>'10'];
			// 	event(new SyncProgressEvent($message));
			//
			// 	$time_now = Carbon::now();
			// 	$time_string = $time_now->toDateTimeString();
			//
			// 	// $syncId= DB::table('drm_import_sync')->insertGetId([
			// 	// 	'drm_import_id'=>$import_id,
			// 	// 	'created_at'=>$time_string
			// 	// ]);
			//
			// 	$message = ['text'=>'Getting data...','percent'=>'40'];
			// 	event(new SyncProgressEvent($message));
			//
			// 	$object = DrmImport::find($import_id);
			// 	$delimiter = Session::get('delimiter');
			// 	$path = Session::get('path');
			// 	$file_type = pathinfo($path, PATHINFO_EXTENSION);
			//
			// 	$country = DB::table('countries')->select('language_shortcode')->where('id',$object->country_id)->first()->language_shortcode;
			// 	$table = "drm_products_".$country;
			// 	$trans_cat = "category_name_".$country;
			// 	$isUpdated=false;
			// 	$csv_data = $this->csvToArrayModified($path,$file_type,$delimiter,false);
			// 	Storage::disk('local')->delete(str_replace('storage','public',$path));
			// 	$update_items = $items;
			// 	unset($update_items['ean']);
			// 	$update_items = array_keys($update_items);
			//
			// 	$count = 0;
			// 	$total = count($csv_data);
			//
			// 	$user_id = CRUDBooster::myId();
			// 	$ldate = date('Y-m-d H:i:s');
			// 	$user = \Auth::user();
			//
			// 	$message = ['text'=>'Getting All products...','percent'=>'60'];
			// 	event(new ManualUpdateEvent($message));
			//
			// 	// $product_categories = DB::table('drm_product_categories')
			// 	// ->join('drm_products','drm_products.id','=','drm_product_categories.product_id')
			// 	// ->where('drm_products.drm_import_id',$import_id)->cursor();
			// 	// $all_categories = collect($product_categories->all());
			// 	// $all_products = DrmProduct::where('drm_import_id',$import_id)->whereNull('deleted_at')->cursor();
			// 	$all_products = DB::table($table)->select('ean','id')->where('drm_import_id',$import_id)->whereNull('deleted_at');
			// 	foreach($update_items as $item){
			// 		$all_products->addSelect($item);
			// 		if($item=='ek_price'){
			// 			$all_products->addSelect('category_ids');
			// 		}
			// 	}
			// 	$all_products = $all_products->cursor();
			// 	$products = collect($all_products->all());
			//
			// 	if(in_array('ek_price',$update_items)){
			// 		$category_ids = $products->pluck('category_ids')->toArray();
			// 		$category_id = getUniqueCategories($category_ids);
			// 		$product_categories = DB::table('drm_category')->whereIn('id',$category_id)
			// 			->select('id',$trans_cat)
			// 			->cursor();
			// 		$all_categories = collect($product_categories->all());
			// 	}
			//
			// 	$fields_array = (array)$items;
			// 	$fields_array = makeArrayUtf8($fields_array);
			//
			// 	$message = ['text'=>'Update Starting...','percent'=>'90'];
			// 	event(new ManualUpdateEvent($message));
			//
			// 	if(is_array($csv_data[0])){
			// 		$header_key = array_map('trim', $csv_data[0]);
			// 	}
			// 	else {
			// 		$message = ['error'=>'CSV / Excel is invalid','percent'=>'100'];
			// 		event(new ManualUpdateEvent($message));
			// 		return 0;
			// 	}
			// 	$key_count = count($header_key);
			//   unset($csv_data[0]);
			// 	$total = count($csv_data);
			//
			// 	LazyCollection::make(function () use (&$csv_data,$key_count){
			// 		$datas = $csv_data;
			// 		foreach($datas as $line)
			// 		{
			// 			if(count($line)==$key_count && !containsOnlyNull($line)){
			// 				yield $line;
			// 			}
			// 		}
			// 	})
			// 	->chunk(200)
			// 	->each(function ($lines) use (&$products, $import_id, $fields_array, $object, $user_id, $total,$update_items,$ldate,$user,$syncId,$header_key,$all_categories,$table){
			// 		$changeLogs = [];
			// 		$syncReports = [];
			// 		foreach ($lines as $chunk){
			// 			$data = array_combine($header_key, $chunk);
			// 			$data = makeArrayUtf8($data);
			// 			$ean = $data[$fields_array['ean']];
			// 			if($ean!=null){
			// 				$drm_product = $products->where('ean',$ean);
			// 				$drm_product = $drm_product->first();
			// 				$product = [];
			// 				foreach ($update_items as $key => $value){
			// 					if($value != 'ek_price' && $value != 'vk_price' && $value != 'image'){
			// 						$product[$value] = $data[$fields_array[$value]];
			// 					}
			//
			// 					if($value == 'ek_price'){
			// 						$product_id = $drm_product->id;
			// 						$product_categories = json_decode($drm_product->category_ids);
			// 						$product['ek_price'] = $data[$fields_array['ek_price']];
			// 						$product['vk_price'] = drmPriceCalculation((object)$fields_array,$data,$object,$product_categories);
			// 					}
			//
			// 					if($value == 'image'){
			// 						$fields = (object)$fields_array;
			// 						$image_headers =  $fields->image;
			// 						$image_separator = $object->image_separator;
			// 						$final_array = array();
			// 						foreach ($image_headers as $header){
			// 							$csv_images = explode($image_separator,$data[$header]);
			// 							$final_array = array_merge($final_array,$csv_images);
			// 						}
			// 						$product['image'] = $this->getImageJson($final_array,$object);
			// 					}
			// 				}
			//
			// 				if($drm_product!=null){
			// 					$current_product = (array)$drm_product;
			// 					$changes=[];
			// 					foreach($product as $col => $val){
			// 						if($current_product[$col]!=$val){
			// 							$changes[]=$col;
			// 						}
			// 					}
			//
			// 					if(count($changes)>0){
			// 						$isUpdated=true;
			// 						$this->change_count++;
			// 						DB::table($table)->where('id',$drm_product->id)->update($product);
			// 						// $syncReports[] = [
			// 						// 	'sync_id'=>$syncId,
			// 						// 	'drm_product_id'=>$drm_product->id,
			// 						// 	'status'=>2,
			// 						// 	'change_log'=>json_encode($changes)
			// 						// ];
			// 					}
			// 					else {
			// 						$this->skip_count++;
			// 					}
			// 				}
			// 			}
			// 			$this->count++;
			// 		}
			// 		$message = ['total'=>$total,'count' => $this->count,'percent' => round(($this->count/$total)*100,2),'skipped' => $this->skip_count,'changed'=>$this->change_count,'name' => strip_tags($data[$fields_array['ean']])];
			// 		event(new ManualUpdateEvent($message));
			// 		// if(count($syncReports)){
			// 		// 	DB::table('drm_import_sync_report')->insert($syncReports);
			// 		// }
			// 	});
			// 	// 	}
			// 	// }
			// 	if($isUpdated){
			// 		User::find(CRUDBooster::myId())->notify(new DRMNotification($object->csv_file_name.' has been manually updated successfully ', 'ProductSyncSuccess',url('admin/drm_import_sync_report?id='.$syncId)));
			// 	}
			// 	return $this->change_count;
			// 	// CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'), "CSV Updated Successully", "Success");
			// }

			public function postManualUpdateImport(){
				$items = $_REQUEST['update_items'];
				if($items==null){
					return redirect()->back();
				}
				$delimiter = $_REQUEST['delimiter'];
				$import_id = Request::input('drm_import_id');
				$object = DrmImport::find($import_id);
				if(Request::input('file_type') == 1){
					$type='file';
				}
				else{
					$type='url';
				}

				if($type == 'file'){
					if(!Request::hasFile('csv_file')){
						return abort(500);
					}
					foreach(Request::file('csv_file') as $file){
						$file_type = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
						$rand=Str::random(40);
						$path = $file->storeAs('public/csv_files',$rand.'.'.$file_type,['visibility'=>'public','disk'=>'local']);
						$path = str_replace('public','storage',$path);
					}
				}
				else{
					$url_file=trim(Request::input('csv_link'));
					Session::put('url_'.$import_id,$url_file);
					$csv_data= file_get_contents($url_file);
					$rand=Str::random(40);
					Storage::disk('local')->put('public/csv_files/'.$rand.'.csv',$csv_data);
					$path = 'storage/csv_files/'.$rand.'.csv';
					$file_type = "csv";
					$object->auto_update = 1;
					$object->file_url  = $url_file;
					$object->save();
				}

				$header = $this->csvToCsvHeaderJson($path,$delimiter,false);

				if($header == false){
					CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'), "Invalid File", "danger");
				}

				if($_POST['button'] == 'delete'){
					$csv = $this->csvToArray($path,$file_type,$delimiter,false);
					$collection = LazyCollection::make($csv);
					$ean = $collection->pluck($fields->ean)->unique()->toArray();
					$drm_product = DB::table('drm_products')->whereIn('ean',$ean)->where('user_id',CRUDBooster::myId())->pluck('id')->toArray();
					if( count($drm_product)> 0){
						DB::table('drm_product_categories')->whereIn('product_id',$drm_product)->delete();
						DB::table('drm_translation_en')->whereIn('product_id',$drm_product)->delete();
						DB::table('drm_translation_de')->whereIn('product_id',$drm_product)->delete();
						DB::table('drm_products')->whereIn('ean',$ean)->where('user_id',CRUDBooster::myId())->delete();
					}
					Storage::disk('local')->delete(str_replace('storage','public',$path));
					return back()->with('success','Product Manually Deleted successfully');
				}
				else{
					$data['header'] = $header;
					$data['import_id'] = $import_id;
					$data['items'] = $items;
					$data['drm'] = $object;
					$data['step'] = 2;

					Session::put('delimiter_'.$import_id,$delimiter);
					Session::put('path_'.$import_id,$path);

					Session::save();
					return view('admin.drm_import.after_import.manual_update_sync_fields',$data);
				}
			}

			public function postManualUpdateSave(){
				ini_set('max_execution_time', '0');
				ini_set('memory_limit',-1);
				$items = $_REQUEST;
				unset($items['drm_import_id']);
				unset($items['_token']);
				$import_id = Request::input('drm_import_id');
				$delimiter = Session::get('delimiter_'.$import_id);
				$path = Session::get('path_'.$import_id);
				$update_items = $items;

				$object = DrmImport::find($import_id);

				if($object->type == "file" && $object->auto_update == 1){
					$url = Session::get('url_'.$import_id);
					$this->enableManualAutoUpdate($import_id,$update_items,$url,$delimiter);
				}

				$options = [
					'path' => $path,
					'delimiter' => $delimiter
				];
				$count = $this->makeManualUpdate($object,$update_items,$options);
				return $count;
			}

			public function enableManualAutoUpdate($import_id,$items,$url,$delimiter){
				$items_json = json_encode($items);
				DB::table('manual_update_mapping')
			    ->updateOrInsert(
			        ['drm_import_id' => $import_id],
			        ['mapping' => $items_json, 'url' => $url,'delimiter' => $delimiter]
			    );
			}

			public function getManualAutoUpdate($import_id){
				$import = DrmImport::find($import_id);
				$count = 0;
				if($import->auto_update){
					$mapping = DB::table('manual_update_mapping')->where('drm_import_id',$import_id)->first();
					$delimiter = $mapping->delimiter;
					$url = $mapping->url;
					$csv_data= file_get_contents($url);
					$rand=Str::random(40);
					Storage::disk('spaces')->put('public/update_csv_files/'.$rand.'.csv',$csv_data,'public');
					$path = 'public/update_csv_files/'.$rand.'.csv';
					$file_type = "csv";

					$options = [
						'path' => $path,
						'delimiter' => $delimiter
					];

					$update_items = json_decode($mapping->mapping,true);
					$count = $this->makeManualUpdate($import,$update_items,$options);
				}
				return $count;
			}

			public function createSyncReport($import_id){
				$message = ['text'=>'Saving Report...','percent'=>'10'];
				sentProgress($message,'manualUpdate');
				$time_now = Carbon::now();
				$time_string = $time_now->toDateTimeString();
				$syncId= DB::table('drm_import_sync')->insertGetId([
					'drm_import_id'=>$import_id,
					'created_at'=>$time_string
				]);
			}

			public function makeManualUpdate($object,$items,$options){
				$import_id = $object->id;
				$path = $options['path'];
				$sync_id = $this->createSyncReport($import_id);
				$file_type = pathinfo($path, PATHINFO_EXTENSION);
				$isUpdated=false;
				$message = ['text'=>'Getting data...','percent'=>'40'];
				sentProgress($message,'manualUpdate');

				$csv_data = $this->csvToArrayModified($path,$file_type,$delimiter,true);

				$exists = Storage::disk('spaces')->exists($object->latest_file);
				if($exists){
					Storage::disk('spaces')->delete($object->latest_file);
				}

				$object->latest_file = $path;
				$object->save();

				unset($update_items['ean']);
				$update_items = array_keys($items);
				$count = 0;
				$total = count($csv_data);

				$user_id = $object->user_id;
				$user = \Auth::user();

				$message = ['text'=>'Getting All products...','percent'=>'60'];
				sentProgress($message,'manualUpdate');

				$country = DB::table('countries')->where('id',$object->country_id)->first()->language_shortcode;
				$this->table = "drm_translation_".$country;
				$this->trans_cat = "category_name_".$country;
				if(\Schema::hasTable($this->table) == false){
					return false;
				}
				$fields_array = (array)$items;
				$fields_array = makeArrayUtf8(makeArrayUtf8($fields_array));

				$this->calc_config = [
					'ek_field' => $fields_array['ek_price'],
					'vk_field' => $fields_array['vk_price'],
					'money_format' => $object->money_format,
				];

				$message = ['text'=>'Update Starting...','percent'=>'90'];
				sentProgress($message,'manualUpdate');

				if(is_array($csv_data[0])){
					$header_key = array_map('trim', $csv_data[0]);
				}
				else {
					$message = ['error'=>'CSV / Excel is invalid','percent'=>'100'];
					sentProgress($message,'manualUpdate');
					return 0;
				}
				$header_key = array_map('removeDots',$header_key);
				$key_count = count($header_key);
				unset($csv_data[0]);
				$total = count($csv_data);
				try{
					foreach(array_chunk($csv_data,500) as $lines){
						array_walk($lines, function (&$item) use($header_key){
							$item = array_combine($header_key, $item);
							$item = makeArrayUtf8(makeArrayUtf8($item));
						});
						$csv = collect($lines);
						$csv_eans = $csv->pluck($fields_array['ean'])->toArray();
						$csv_eans = array_map('makeInt',$csv_eans);
						$db_products = DB::table('drm_products')
												->select('drm_products.*',$this->table.'.title',$this->table.'.description as description')
												->leftjoin($this->table,$this->table.'.product_id','drm_products.id')
												->whereIn('drm_products.ean',$csv_eans)
												->where('drm_products.drm_import_id',$import_id)
												->whereNull('drm_products.deleted_at')->cursor();
	
						$db_products_array = makeArrayUtf8(json_decode(json_encode($db_products->toArray()),true));
						$this->all_products = collect($db_products_array);
						$csv_ids = $this->all_products->pluck('id')->toArray();
	
						$categories = DB::table('drm_product_categories')
						->select('drm_category.id',$this->trans_cat,'drm_product_categories.product_id')
						->join('drm_category','drm_category.id','=','drm_product_categories.category_id')
						->whereIn('drm_product_categories.product_id',$csv_ids)->cursor();
	
						$category_array = makeArrayUtf8(json_decode(json_encode($categories->toArray()),true));
						$this->all_categories = collect($category_array);
	
						$profit_margins = DB::table('drm_product_profit_margins')->where('drm_import_id',$import_id)->cursor();
						$this->profit_margins = collect($profit_margins->all());
						$this->fallback = DB::table('drm_fallback_calculations')
						->select('profit_percent','shipping_cost','additional_charge')
						->where('id',$object->fallback)
						->first();

						$changed_products = [];
						$this->image_changed = [];
						$this->bundle_update = array();

						array_walk($lines, function ($csv) use($fields_array,$object,$update_items,$changed_products){
									$product = $this->all_products->where('ean',(int)$csv[$fields_array['ean']])->first();
									if($product!=null){
										$drm_product = (object)$product;
										$product_id = $drm_product->id;
										$product = [];
										$data = [];
										foreach ($update_items as $key => $value){
											if($value != 'ek_price' && $value != 'vk_price' && $value != 'image' && $value != 'title' && $value != 'description' && $value != 'stock'){
												$product[$value] = $csv[$fields_array[$value]];
											}
											if($value == 'stock'){
												$product[$value] = (int)$csv[$fields_array[$value]];
											}
	
											if($value == 'ek_price'){
												$product_categories = $this->all_categories->whereIn('product_id',$product_id)->pluck('id')->toArray();
												$this->calc_config['ek_price'] = $csv[$fields_array['ek_price']];
												$this->calc_config['vk_price'] = $csv[$fields_array['vk_price']];
												$this->calc_config['profit_margins'] = $this->profit_margins->whereIn('category_id',$product_categories);
												$this->calc_config['fallback'] = $this->fallback;
												$product['ek_price'] = drm_convert_european_to_decimal($csv[$fields_array['ek_price']],$object->money_format);
												$product['vk_price']  = drmPriceCalculationNew($this->calc_config);
											}
											if($value == 'image'){
												$fields = (object)$fields_array;
												$image_headers =  $fields->image;
												$image_separator = $object->image_separator;
												$final_array = array();
												foreach ($image_headers as $header){
													$csv_images = explode($image_separator,$csv[$header]);
													$final_array = array_merge($final_array,$csv_images);
												}

												$image = $this->getImageJson($final_array,$object);
												if($object->image_backup)
												{
													if($image!=$drm_product->original_images){
														$product['original_images'] = $image;
														$this->image_changed[] = $product_id;
													}
												}
												else{
													$product['image'] = $image;
												}
											}
	
											if($value == 'title' || $value == 'description'){
												$data[$value] = $csv[$fields_array[$value]];
											}
										}
										$current_product = (array)$drm_product;
										$changes=false;
										foreach($product as $col => $val){
											if($current_product[$col]!=$val){
												// $changes[]=$col;
												$changes=true;
											}
										}
										$trans_changes=false;
										foreach($data as $col => $val){
											if($current_product[$col]!=$val){
												$trans_changes=true;
											}
										}
										if($trans_changes){
											$isUpdated=true;
											$this->change_count++;
											DB::table($this->table)->where('product_id',$product_id)->update($data);
										}
										if($changes){
											$isUpdated=true;
											$this->change_count++;
	
											if(isset($product['stock'])){
												if((int)$product['stock'] != (int)$current_product['stock']){
													$time_now = Carbon::now();
													$time_string = $time_now->toDateTimeString();
													$product['old_stock'] = (int)$current_product['stock'];
													$product['stock_updated_at'] = $time_string;
												}
											}

											if($current_product['has_bundle']){
												$this->bundle_update[] = $product_id;
											}
	
											DB::table('drm_products')->where('id',$product_id)->update($product);
										}
										else {
											$this->skip_count++;
										}
										if($isUpdated){
											$this->changed_products[] = $product_id;
										}
									}
							$this->count++;
						});
						// ExportServices::updateConnected([$changed_products],$user_id);

						if($object->image_backup)
						{
							if(count($this->image_changed)){
								UpdateBackupImages::dispatch($object,$this->image_changed);
							}
						}
						UpdateProductBundle::dispatch($this->bundle_update,$user_id);

						$this->updateChannelProducts($this->changed_products,$user_id);
						AdminDrmExportsController::updateAnyConnectedShopProduct($this->changed_products,$user_id,$country);
						$message = ['total'=>$total,'count' => $this->count,'percent' => round(($this->count/$total)*100,2),'skipped' => $this->skip_count,'changed'=>$this->change_count,'name' => strip_tags($data[$fields_array['ean']])];
						sentProgress($message,'manualUpdate');
					}
				}catch (\Exception $e){
					$this->updateChannelProducts($this->changed_products,$user_id);
					AdminDrmExportsController::updateAnyConnectedShopProduct($this->changed_products,$user_id,$country);
					$message = ['total'=>$total,'count' => $this->count,'percent' => round(($this->count/$total)*100,2),'skipped' => $this->skip_count,'changed'=>$this->change_count,'name' => strip_tags($data[$fields_array['ean']])];
					sentProgress($message,'manualUpdate');
				}

				
		

				// if($isUpdated){
				// 	User::find(CRUDBooster::myId())->notify(new DRMNotification($object->csv_file_name.' has been manually updated successfully ', 'ProductSyncSuccess',url('admin/drm_import_sync_report?id='.$syncId)));
				// }
				return $this->change_count;
			}

			public function getManualUpdateAll(){
				$mapping = DB::table('manual_update_mapping')->whereNotNull('url')->where('id',"!=",1179)->get();
				foreach($mapping as $key => $value){
					$this->getManualAutoUpdate($value->drm_import_id);
				}
			}



		public function updateChannelProducts($products,$user_id)
		{
			$url = "https://drm.software/api/channel_products/update";
			
			foreach(array_chunk($products,40) as $product){
				$data =[
					'product_ids' 	=> $product,
					'user_id' 		=> $user_id
				];
	
				$ch = curl_init();
				$headers = array("accept: application/json","content-type: application/json");
	
				curl_setopt_array($ch, array(
					CURLOPT_URL => $url,
					CURLOPT_RETURNTRANSFER => true,
					CURLOPT_ENCODING => "",
					CURLOPT_HEADER => 1,
					CURLOPT_CUSTOMREQUEST => "POST",
					CURLOPT_HTTPHEADER => $headers,
					CURLOPT_POSTFIELDS => json_encode($data),
					CURLOPT_FOLLOWLOCATION => true
				));
				
				$output = curl_exec($ch);
			}
		}

		public function getTestChannel()
		{
			$this->updateChannelProducts([3032298,3034514,3034428,3034383,3034319],123);
		}
}
