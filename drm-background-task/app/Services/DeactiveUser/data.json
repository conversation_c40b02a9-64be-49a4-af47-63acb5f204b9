{"accounting_file_caches": {"user_id": "user_id"}, "agb_logs": {"user_id": "user_id"}, "app_assigns": {"user_id": "user_id"}, "app_trials": {"user_id": "user_id"}, "appointment_slot": {"user_id": "user_id"}, "appointment_type": {"user_id": "user_id"}, "appointments": {"user_id": "user_id"}, "billing_details": {"user_id": "user_id"}, "block_user_histories": {"user_id": "user_id"}, "campaign_history": {"user_id": "user_id"}, "cards": {"user_id": "user_id"}, "challenge_achives": {"user_id": "user_id"}, "challenge_purchases": {"user_id": "user_id"}, "challenge_takens": {"user_id": "user_id"}, "challenges": {"user_id": "user_id"}, "channel_csv_credentials": {"user_id": "user_id"}, "channel_products": {"user_id": "user_id"}, "channel_user_categories": {"user_id": "user_id"}, "chronosale": {"user_id": "user_id"}, "cliktip_histories": {"user_id": "user_id"}, "clousale": {"user_id": "user_id"}, "cms_users": {"id": "id"}, "comments": {"cms_user_id": "cms_user_id"}, "contact_form_tags": {"user_id": "user_id"}, "contact_forms": {"user_id": "user_id"}, "custom_eans": {"user_id": "user_id"}, "custom_paywall_charges": {"user_id": "user_id"}, "customer_tags": {"user_id": "user_id"}, "delivery_companies": {"user_id": "user_id"}, "df_prevents": {"user_id": "user_id"}, "drm_category": {"user_id": "user_id"}, "drm_customers": {"user_id": "user_id"}, "drm_deleted_products": {"user_id": "user_id"}, "drm_export_response": {"user_id": "user_id"}, "drm_fallback_calculations": {"user_id": "user_id"}, "drm_imports": {"user_id": "user_id"}, "drm_invoice_setting": {"cms_user_id": "cms_user_id"}, "drm_invoices": {"user_id": "user_id"}, "drm_order_mail": {"cms_user_id": "cms_user_id"}, "drm_orders": {"user_id": "user_id"}, "drm_orders_new": {"cms_user_id": "cms_user_id"}, "drm_products": {"user_id": "user_id"}, "drm_project_members": {"cms_user_id": "cms_user_id"}, "drm_projects": {"cms_user_id": "cms_user_id"}, "manual_subscriptions": {"user_id": "user_id"}, "drm_supplier_categories": {"user_id": "user_id"}, "drm_supplier_mail": {"cms_user_id": "cms_user_id"}, "drm_target": {"user_id": "user_id"}, "drm_task_checklist": {"cms_user_id": "cms_user_id"}, "drm_task_comments": {"cms_user_id": "cms_user_id"}, "drm_task_members": {"cms_user_id": "cms_user_id"}, "drop_funnel_count_downs": {"user_id": "user_id"}, "drop_funnel_signatures": {"user_id": "user_id"}, "dropfunnel_email_sent_histories": {"user_id": "user_id"}, "dropfunnel_tags": {"user_id": "user_id"}, "droptienda_order_confirmation_template": {"cms_user_id": "cms_user_id"}, "droptienda_products": {"user_id": "user_id"}, "droptienda_pull_history": {"user_id": "user_id"}, "droptienda_sync_history": {"user_id": "user_id"}, "dt_flat_rates": {"user_id": "user_id"}, "ebay_products": {"user_id": "user_id"}, "ebay_template": {"user_id": "user_id"}, "email_marketings": {"user_id": "user_id"}, "etsy_products": {"user_id": "user_id"}, "export_process": {"user_id": "user_id"}, "ftp_credentials": {"user_id": "user_id"}, "ftp_file_paths": {"user_id": "user_id"}, "gambio_products": {"user_id": "user_id"}, "handling_time_email_histories": {"user_id": "user_id"}, "html_drop_funnel_templates": {"user_id": "user_id"}, "import_plan_get_discounts": {"user_id": "user_id"}, "industry_templates": {"user_id": "user_id"}, "invoice_payments": {"user_id": "user_id"}, "invoice_products": {"cms_user_id": "cms_user_id"}, "invoice_translations": {"user_id": "user_id"}, "klick_tipp_requests": {"user_id": "user_id"}, "lengow_products": {"user_id": "user_id"}, "mail_gun_web_hook_histories": {"user_id": "user_id"}, "mail_setting": {"user_id": "user_id"}, "manual_import_tarrif": {"user_id": "user_id"}, "marketing_email_settings": {"cms_user_id": "cms_user_id"}, "marketing_plan": {"user_id": "user_id"}, "marketplace_email_credentials": {"user_id": "user_id"}, "marketplace_orders": {"user_id": "user_id"}, "monthly_paywalls": {"user_id": "user_id"}, "new_customer_tags": {"user_id": "user_id"}, "new_customers": {"user_id": "user_id"}, "new_orders": {"cms_user_id": "cms_user_id"}, "notification_trigger": {"user_id": "user_id"}, "oauth_access_tokens": {"user_id": "user_id"}, "oauth_auth_codes": {"user_id": "user_id"}, "oauth_clients": {"user_id": "user_id"}, "opln_mails": {"user_id": "user_id"}, "order_mail_templates": {"user_id": "user_id"}, "order_sync_reports": {"user_id": "user_id"}, "order_tracking_emails": {"cms_user_id": "cms_user_id"}, "premium_flat_rate_users": {"user_id": "user_id"}, "price_category": {"user_id": "user_id"}, "product_synced_histories": {"user_id": "user_id"}, "profit_calculations": {"user_id": "user_id"}, "purchase_apps": {"cms_user_id": "cms_user_id"}, "purchase_import_plans": {"cms_user_id": "cms_user_id"}, "queue_jobs": {"user_id": "user_id"}, "ratings": {"cms_user_id": "cms_user_id"}, "remainder_email_settings": {"cms_user_id": "cms_user_id"}, "reply_comments": {"cms_user_id": "cms_user_id"}, "sessions": {"user_id": "user_id"}, "shareable_links": {"user_id": "user_id"}, "shipcloud_tokens": {"user_id": "user_id"}, "shopify_products": {"user_id": "user_id"}, "statements": {"user_id": "user_id"}, "step_mail_sent_history": {"user_id": "user_id"}, "stripe_customers": {"user_id": "user_id"}, "stripe_keys": {"user_id": "user_id"}, "stripe_payments": {"user_id": "user_id"}, "sub_user_permissions": {"cms_user_id": "cms_user_id"}, "subscriptions": {"user_id": "user_id"}, "takeappointment": {"user_id": "user_id"}, "template_purchases": {"user_id": "user_id"}, "tmp_drm_products": {"user_id": "user_id"}, "tracker_sessions": {"user_id": "user_id"}, "traker_page_views": {"user_id": "user_id"}, "traker_time_spents": {"user_id": "user_id"}, "trakers": {"user_id": "user_id"}, "universal_exports": {"user_id": "user_id"}, "upcoming_invoices": {"user_id": "user_id"}, "upcomming_invoice_categories": {"user_id": "user_id"}, "user_coupons": {"user_id": "user_id"}, "user_csv_header_values": {"user_id": "user_id"}, "user_firebase_device_tokens": {"user_id": "user_id"}, "user_introjs": {"user_id": "user_id"}, "user_last_visit_langs": {"cms_user_id": "cms_user_id"}, "user_notification_settings": {"user_id": "user_id"}, "user_order_statuses": {"user_id": "user_id"}, "user_parcel_services": {"cms_user_id": "cms_user_id"}, "video_watches": {"user_id": "user_id"}, "vod_customers": {"user_id": "user_id"}, "vod_tags": {"user_id": "user_id"}, "yatego_products": {"user_id": "user_id"}}