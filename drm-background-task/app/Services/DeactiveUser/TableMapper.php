<?php
namespace App\Services\DeactiveUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class TableMapper
{
	public static function init()
	{
		$values = [];
		$tables = array_map('reset', DB::select('SHOW TABLES'));

		$t1 = 'user_id';
		$t2 = 'cms_user_id';

		foreach ($tables as $table) {
			$cols = Schema::getColumnListing($table);

			if($table == 'cms_users'){
				$values[$table]['id'] = 'id';
			}elseif (in_array($t1, $cols)) {
				$values[$table][$t1] = $t1;
			}elseif (in_array($t2, $cols)) {
				$values[$table][$t2] = $t2;
			}
		}

		file_put_contents(__DIR__.'/data.json', json_encode($values));
	}
}