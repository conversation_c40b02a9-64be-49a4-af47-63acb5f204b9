<?php
namespace App\Services\DeactiveUser;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

trait ReportTrait{


	//Send report
	public function sendReport($user_id, $uid, $message, $progress = 0)
	{
		return $this->insertReportToDB([
			'user_id' 	=> $user_id,
			'uid'		=> $uid,
			'message'	=> $message,
			'progress'	=> $progress
		]);
	}

	//Send path
	public function savePathtoReport($user_id, $uid, $message, $path, $progress = 0){
		return $this->insertReportToDB([
			'user_id' 	=> $user_id,
			'uid'		=> $uid,
			'message'	=> $message,
			'progress'	=> $progress,
			'path'		=> $path
		]);
	}

	//init db row
	public function initialReport($user_id, $uid)
	{
		$name = DB::table('cms_users')->where('id', $user_id)->value('name');
		return $this->insertReportToDB([
			'user_id' 	=> $user_id,
			'uid'		=> $uid,
			'message'	=> 'Initial process',
			'name'		=> $name
		]);
	}


	private function insertReportToDB($args)
	{

		$user_id 	= $args['user_id'];
		$uid 		= $args['uid'];
		$message 	= $args['message'];
		$label 		= $args['label']?? 'info';
		$progress 	= $args['progress']?? 0;
		$path 		= $args['path']??null;
		$name 		= $args['name']??null;

		if(empty($user_id) || empty($uid) || empty($message)) return false;
		$check_arr = ['deletable_id' => $user_id, 'uid' => $uid];

		//init reports
		$reports = [];

		$prev_record = DB::table('delete_users')
			->where($check_arr)
			->select('id', 'report', 'progress')
			->first();

		if(!empty($prev_record))
		{	
			//Previous record
			$prev_reports = $prev_record->report?? null;
			if($prev_reports){
				$reports = json_decode($prev_reports, true)?? [];
				$reports = array_filter($reports);
				$progress = max($prev_record->progress, $progress);
			}

			$reports[] = [
				'message' => $message,
				'time' => Carbon::now(),
				'progress' => $progress,
				'label' => 'warning',
			];

			$update_data = [
				'report' 	=> json_encode($reports),
				'progress' 	=> $progress,
				'updated_at' => Carbon::now(),
				'path' => $path
			];

			$update_data = array_filter($update_data);
			return DB::table('delete_users')->where('id', $prev_record->id)->update($update_data);

		}else{

			$reports[] = [
				'message' => $message,
				'time' => Carbon::now(),
				'progress' => $progress,
				'label' => $label,
			];

			$insert_data = [
				'deletable_id'	=> $user_id,
				'uid'		=> $uid,
				'report' 	=> json_encode($reports),
				'progress' 	=> $progress,
				'path' => $path,
				'created_at' => Carbon::now(),
				'updated_at' => Carbon::now(),
				'name'		=> $name
			];

			$insert_data = array_filter($insert_data);
			return DB::table('delete_users')->insert($insert_data);
		}
	}
}