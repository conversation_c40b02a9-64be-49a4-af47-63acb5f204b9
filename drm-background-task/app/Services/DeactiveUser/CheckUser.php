<?php
namespace App\Services\DeactiveUser;
use Illuminate\Support\Facades\DB;
use App\Jobs\DeactiveUser\DeleteUsesAction;
use App\Services\DeactiveUser\ReportTrait;
use carbon\Carbon;


class CheckUser{

    use ReportTrait;

	public function inactiveAction(){

        //Block non email verified user after 30 days
        DB::table('cms_users')
        ->whereNull('email_verified_at')
        ->whereNull('status')
        ->whereDate('updated_at', '<', Carbon::now()->subDays(30))
        ->delete();

        //Add tag for 30, 60, 90. Block user after 90 days
        DB::table('cms_users')
        // ->whereNotNull('status')
        ->whereNotNull('email_verified_at')
        ->whereDate('updated_at', '<', Carbon::now()->subDays(30))
        ->selectRaw('updated_at, id')
        ->get()->map(function($item){
            return [
                'last_day' => Carbon::now()->diffInDays($item->updated_at),
                'user_id' => $item->id
            ];
        })->filter(function($d){
            return $d['last_day'];
        })->each(function($user){
            $step = intval($user['last_day'] / 30);
            $step = ($step > 3) ? 3 : $step;
            $block = $step === 3;
            $this->applyUserInactiveAction($user['user_id'], $user['last_day'], $step);
        });
    }

    private function applyUserInactiveAction($user_id, $last_day, $step = 0){
        if(empty($step)) return false;
        //Block user
        // if($step === 3){
        //     DB::table('cms_users')->where('id', $user_id)->update(['status' => null]);
        // }

        //Check if user has any plan then don't add 90 days tag
        if($step === 3){
            $isDtplan = DB::table('dt_tariff_purchases')->where('user_id', $user_id)->where('end_date', '>', Carbon::now())->exists();
            $isDrmPlan = DB::table('purchase_import_plans')->where('cms_user_id', $user_id)->where('end_date', '>', Carbon::now())->exists();
            if($isDtplan || $isDrmPlan){
                return false;
            }
        }
        $customerId = app('App\Http\Controllers\AdminDrmAllCustomersController')->userBillingToCustomerProfile($user_id, 2455);
        if(empty($customerId)) return false;

        //insert activity tag
        $i = 1;
        do{
            $tag = 'Inactive for '.($i * 30).' days';

            //insert tag
            try {
                \App\DropfunnelCustomerTag::insertTag($tag, 2455, $customerId, 14);
            } catch (\Exception $ev) {}

            ++$i;
        }while($i <= $step);

        //Delete user after 93 days
        if($last_day >= 93){
            $uid = $this->generateUUID($user_id);
            $this->initialReport($user_id, $uid);
            DeleteUsesAction::dispatch($user_id, $uid)->onQueue('database-long-running');
        }
    }


    //Generate uuid
    private function generateUUID($user_id)
    {
        return trim('ua'. bin2hex(openssl_random_pseudo_bytes(10))).$user_id;
    }
}
