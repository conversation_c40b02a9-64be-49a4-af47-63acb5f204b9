<?php

namespace App\Services\DeactiveUser;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Response;
use App\Jobs\DeactiveUser\DeleteCustomerData;
use ZipArchive;
use App\Services\DeactiveUser\ReportTrait;
use App\DropfunnelCustomerTag;

class BackupUserData
{
    use ReportTrait;
    
    private $user_id = null;
    private $tables = [];
    private $backups = [];
    private $folder = null;
    private $uuid;

    public function __construct($user_id, $uuid)
    {
        $this->user_id = $user_id;
        $this->tables = $this->tables();

        $this->uuid = $uuid;

        //Folder name generate
        $root_folder = 'sql_backups';
        $child_folder = $user_id.'_'.date('y_m_d');
        $this->folder = $root_folder.'/'.$child_folder;
    }


    //Store data
    public function save()
    {
        $zip = $this->store();
        if(is_null($zip)) return false;

        if(Storage::disk('local')->exists($zip))
        {
            return true; 
        }

        return false;
    }


    //Strote data to cloud
    public function storeToCloud()
    {
        $zip = $this->store();
        if(is_null($zip)) throw new \Exception('No content');

        if(Storage::disk('local')->exists($zip))
        {
            Storage::disk('spaces')->put($zip, Storage::disk('local')->get($zip), 'public');
            if(Storage::disk('spaces')->exists($zip))
            {
                $inserted = $this->savePathtoReport($this->user_id, $this->uuid, 'Data archived!', $zip, 50);
                if($inserted){
                    $this->clearLocalData();
                    //Finally remove customer sql data
                    DeleteCustomerData::dispatch($this->user_id, $this->uuid)->onQueue('database-long-running');
                }               
               
            }
        }else{
            throw new \Exception('No content');
        }
    }

    //@return \Illuminate\Http\Response
    public function download()
    {
        $zip = $this->store();
        if(is_null($zip)) throw new \Exception('No content');

        if(Storage::disk('local')->exists($zip))
        {
            $filename = end(explode('/', $zip));
            return new Response(Storage::disk('local')->get($zip), 200, array(
                'Content-Type' => 'application/zip',
                'Content-Disposition' =>  'attachment; filename="'.$filename.'"',
                // 'Content-Length' => strlen($output),
            ));
        }

        throw new \Exception('No content');
    }

    private function getData()
    {

        $tables = $this->tables?? [];
        $user_id = $this->user_id;
        if(empty($tables) || empty($user_id)) return $this;

        foreach ($tables as $table => $user_cols) {

            $structure = '';
            $data = '';

            $show_table_query = "SHOW CREATE TABLE " . $table . "";
            $show_table_result = DB::select(DB::raw($show_table_query));

            //Generating table structure
            foreach ($show_table_result as $show_table_row) {
                $show_table_row = (array)$show_table_row;
                $schema = $show_table_row["Create Table"];
                $schema = str_replace('CREATE TABLE', 'CREATE TABLE IF NOT EXISTS', $schema);
                $structure .= "\n\n" . $schema . ";\n\n";
            }

            //Generate user data
            foreach ($user_cols as $user_col) {

                //Select table values
                $select_query = "SELECT * FROM $table WHERE $user_col = $user_id";
                $records = DB::select(DB::raw($select_query));


                if($records){
                    
                    //Generating columns
                    $cols = reset($records);
                    $table_column_array = array_keys((array)$cols);
                    foreach ($table_column_array as $key => $name) {
                        $table_column_array[$key] = '`' . $table_column_array[$key] . '`';
                    }

                    //Comma seperated column names
                    $columns = implode(", ", $table_column_array);

                    //Chunk array
                    $chunks = array_chunk($records, 20);
                    foreach ($chunks as $chunk) {

                        $col_str = "\nINSERT INTO $table ($columns) VALUES";
                        $values = '';

                        //Generate values
                        foreach ($chunk as $record) {
                            $record = (array)$record;

                            //Generating values
                            $table_value_array = array_values($record);
                            $vals = '';
                            foreach($table_value_array as $key => $record_column){
                                $col_value = is_null($record_column)? "NULL" : (is_numeric($record_column)? $record_column : "'".addslashes($record_column)."'");
                                $vals .=$col_value. ',';
                            }
                            $values .= ' ('.trim($vals, ',').'),'; // Remove last comma                            
                        }
                        
                        //Generate insert query
                        $values = trim($values, ','); // Remove last comma
                        $values = trim($values); // Remove trailing spaces
                        $data .= "$col_str $values;\n";
                    }                    
                }
            }

            //Save table data to local storage
            $sql = "START TRANSACTION;\n" . $structure . $data . "\nCOMMIT;";
            $this->backups[$table] = $this->saveLocal($table, $sql);
        }

        return $this;
    }



    //Save to local
    private function saveLocal($table, $sql)
    {
        $path = $this->folder.'/'.$table.'_'.$this->user_id.'.sql';
        Storage::disk('local')->put($path, $sql);
        return $path;
    }

    //store data
    private function store()
    {
        $this->getData();

        $backups = $this->backups;
        if(empty($backups)) return null;

        $zip = new ZipArchive(); // Load zip library
        $fil_name = 'database_'.$this->user_id.'_'.date('y_m_d').'.zip';
        $zip_name = 'storage\app\\'.str_replace('/', '\\', $this->folder).'\\'.$fil_name; // Zip name

        if($backups && ($zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE)))
        {
            foreach ($backups as $table => $path) {
                $file_path = 'storage\app\\'.str_replace('/', '\\', $path);
                $zip->addFile($file_path, $table.'.sql');
            }

            $zip->close();
            return $this->folder.'/'.$fil_name;
        }

        return null;
    }

    //get tables
    private function tables()
    {
        $data = file_get_contents(__DIR__.'/data.json')?? null;
        if(drmIsJSON($data))
        {
            return json_decode($data);
        }

        return [];
    }


    //Clear local storage
    private function clearLocalData()
    {
        if(!empty($this->folder) && Storage::disk('local')->exists($this->folder)){
            Storage::disk('local')->deleteDirectory($this->folder);
        }        
    }


    //Delete sql files
    public function removeSqlData()
    {
        $tables = $this->tables?? [];
        $user_id = $this->user_id;
        if(empty($tables) || empty($user_id)) throw new \Exception("No database table connected!");

        foreach ($tables as $table => $user_cols) {
            //Generate user data
            foreach ($user_cols as $user_col) {
                DB::table($table)->where($user_col, $user_id)->delete();
            }
        }

        $this->sendReport($this->user_id, $this->uuid, 'Inactive user database data deleted!', 100);

        //insert tag
        try {
            $customer_id = DB::table('new_customers')->where('user_id', 2455)->where('cc_user_id', $user_id)->value('id');
            if(!empty($customer_id))
            {
                $tag = 'Account deleted';
                DropfunnelCustomerTag::insertTag($tag, 2455, $customer_id, 14);
            }                
        }catch (\Exception $e) {}
        //insert tag end
    }
}