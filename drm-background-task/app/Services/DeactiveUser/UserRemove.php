<?php

namespace App\Services\DeactiveUser;

use Log;
use App\Shop;
use Exception;
use App\DrmProduct;
use App\NewCustomer;
use App\Enums\Channel;
use App\Jobs\DestroyProduct;
use App\Models\ChannelProduct;
use Illuminate\Support\Facades\DB;
use App\Services\Modules\Export\Colizey;
use crocodicstudio\crudbooster\helpers\CRUDBooster;

class UserRemove
{

	public $deletedShops = [];
	public $deletedServers = [];

	public function remove($user_id)
	{
		//step one
		$this->deletedLog($user_id);
		//define methods
		$this->adjustFabianInvoice($user_id);
		$this->removeOrders($user_id);
		$this->userStripesubscription($user_id);
		$this->removeProducts($user_id);
		$this->removeActiveCampaign($user_id);
		$this->removeUserImages($user_id);
		 $this->deleteOwnShop($user_id);
		$this->removeCategoryAccess($user_id);
		$this->removeMpAutoTransferSubscription($user_id);
		//last step
		$this->deletedLogUpdate($user_id);
		$this->deleteDropCampusUser($user_id);
		$this->deleteSubAccount($user_id);
		$this->removeUser($user_id);
        $this->removeApiIntervals($user_id);
	}

	// Adjust FABIAN orders
	private function adjustFabianInvoice($user_id)
	{
		NewCustomer::doesntHave('orders')->whereNotNull('cc_user_id')->where('cc_user_id', $user_id)->delete(); // Remove customers
		NewCustomer::whereNotNull('cc_user_id')->where('cc_user_id', $user_id)->update(['df_block_time' => now()]); // Dropfunnel block
		DB::table('new_orders')->whereNotNull('cms_client')->where('cms_client', $user_id)->update(['cms_client' => null]); // CMS CLIENT
	}


	private function removeOrders($user_id)
	{
		DB::table('new_orders')->where('cms_user_id', $user_id)->delete();
		DB::table('new_customers')->where('user_id', $user_id)->delete();
	}

	private function removeUser($user_id)
	{
		DB::table('cms_users')->where('id', $user_id)->delete();
	}


	//check user has active subscription
	private function userStripesubscription($user_id)
	{
		$stripe_customers = DB::table('stripe_customers')
		->where('user_id', '=', $user_id)
		->select('stripe_customer_id', 'stripe_key', 'user_id')
		->get();

		// Customers
		if(!empty($stripe_customers)){
			foreach ($stripe_customers as $stripe_customer) {

				 try{

					$sk = \DRM::stripeSecretKey($stripe_customer->stripe_key);
					$stripe = new \Stripe\StripeClient($sk);
					$collect = $stripe->subscriptions->all(['limit' => 100, 'status' => 'active', 'customer' => $stripe_customer->stripe_customer_id]);
					if($collect && !empty($collect['data']))
					{
					    foreach($collect['data'] as $subscription)
					    {
					        $stripe->subscriptions->cancel($subscription['id'], ['prorate' => false, 'invoice_now' => true]);
					    }
					}

	            }catch(\Exception $eee){}
			}
		}

		// Delete manual subscription
		DB::table('manual_subscriptions')->where('user_id', $user_id)->update(['deleted_at' => now()]);
	}


	private function removeProducts($user_id)
	{
        $ids = DrmProduct::where('user_id', $user_id)->select('id')->get()->pluck('id')->toArray();
        $chunkSize = 500;
        $chunks = array_chunk($ids, $chunkSize);
        foreach ($chunks as $chunk) {
            DestroyProduct::dispatch($chunk, $user_id);
        }

    }

	//delete active campaign
	private function removeActiveCampaign($user_id)
	{
		try{
			$customer = DB::table('new_customers')->where('user_id', 2455)->where('cc_user_id',$user_id)->first();
			//remove tag
			if($customer){
				DB::table('dropfunnel_customer_tags')->where('customer_id',$customer->id)->delete();
			}
			//remove campaign
			DB::table('email_marketings')->where('user_id',$user_id)->delete();
			//remove customer
			DB::table('new_customers')->where('user_id', 2455)->where('cc_user_id',$user_id)->update([
				'cc_user_id' => null,
			]);

		}catch(Exception $e){}

	}

	//delete user images from server
	private function removeUserImages($user_id)
	{

	}

	//Delete Shop after 30 days of the trial ends
	public function deleteShop($user_id){
		$this->deleteOwnShop($user_id);
	}

	//delete own Ship
	private function deleteOwnShop($user_id)
	{

		Shop::where('user_id', $user_id)
		// ->where('channel', 10)
		->orderBy('id')
		->get()
		->each(function($shop) {
            if($shop->channel == Channel::DROPTIENDA){
                $this->deletedShops[] = $shop->id;
                $this->removeDtServer($shop);
            }elseif($shop->channel == Channel::COLIZEY){
                // Unpublish products from Colizey in chunks
                ChannelProduct::where(['channel' => $shop->channel, 'shop_id' => $shop->id, 'is_connected' => true])
                    ->pluck('item_number')
                    ->chunk(100, function ($itemNumbers) use ($shop) {
                        // Convert item numbers to an array
                        $skus = $itemNumbers->toArray();

                        // Unpublish products using the chunk of SKUs
                        app(Colizey::class)->unpublishProductsUsingSkus($skus, $shop->password);
                    });
            }

			Log::channel('command')->info('After delete shop');
			$shop->delete();
		});
		Log::channel('command')->info('DT Server-> ownshop delete calling Finished');
	}


	// Remove DT servers
	public function removeDtServer($shop)
    {

    	try {

    		if(empty($shop) || empty($shop->url)) return;

	        $host = parse_url($shop->url, PHP_URL_HOST);
	        $guid = @explode('.droptienda', $host)[0];
	        if(empty($guid)) return;

	        $email = DB::table('cms_users')->where('id', $shop->user_id)->value('email');

	        $log = [
    			'time' => now(),
    			'name' => '',
    			'id' => '',
    		];

    		$logs = [$log];

	        DB::table('shop_delete_instances')
	        ->insert([
	        	'status' => 1,
	        	'shop_id' => $shop->id,
	        	'guid' => $guid,
	        	'name' => $shop->shop_name,
	        	'email' => $email,
	        	'created_at' => now(),
	        	'updated_at' => now(),
	        	'log' => json_encode($logs),
	        ]);

			$this->deletedServers[] = $shop->url;

        } catch(Exception $e) {
			Log::info('!ops Error'.$e->getMessage());
		}
    }

	public function blockDtAceess($user_id){

	}

	//remove category access
	public function removeCategoryAccess($user_id){

		try{
			DB::connection('marketplace')->table('marketplace_user_accesses')->where('user_id',$user_id)->delete();
		}catch(Exception $e){ }
	}

	//remove mp auto transfer subscription
	public function removeMpAutoTransferSubscription($user_id){

		try{
			DB::connection('marketplace')->table('marketplace_auto_transfer_subscriptions')->where('user_id',$user_id)->delete();
		}catch(Exception $e){ }
	}


	// Remove DT server manually from test route
	public function removeDtServerManual($shopUrl)
    {

    	abort(404);
    }


	//Backup Logs
	public function deletedLog($user_id){

		try{
			$shopInfos = [];
			$shops = Shop::where('user_id', $user_id)
			->where('channel', 10)
			->orderBy('id')
			->get();
			foreach($shops as $shop){
				$shopInfos[] = ['shop_id' => $shop->id , 'shop_name'=>$shop->shop_name , 'url'=>$shop->url];
			}
			DB::table('deleted_users_log')->where('user_id',$user_id)->insert([
					'user_id'=>$user_id,
					'shop_backup' => json_encode($shopInfos),
					'created_at' => \Carbon\Carbon::now(),
			]);

		}catch(Exception $e){	}


	}
	//Update Logs After the process run
	public function deletedLogUpdate($user_id){
		try{
			DB::table('deleted_users_log')->where('user_id',$user_id)->update([
				'deleted_shops'=>$this->deletedShops,
				'deleted_server_urls' => $this->deletedServers,
				'updated_at' => \Carbon\Carbon::now(),
		]);
		}catch(Exception $e){
		}

	}

	public function userAllDtShop($user_id)
	{
		return Shop::where('user_id', $user_id)
		->with('user:id,name,email')
		->where('channel', Channel::DROPTIENDA)
		->orderBy('id')
		->get();
	}

	public function allShopDeleteEntry($user_id)
	{
		$allDtShop = $this->userAllDtShop($user_id);

		$allDtShop->each(function($shop) {
			$this->shopDeleteEntry($shop);
		});
	}

	public function shopDeleteEntry($shop)
	{
		if(empty($shop) || empty($shop->url)) return;

		$host = parse_url($shop->url, PHP_URL_HOST);
		$guid = @explode('.droptienda', $host)[0];
		if(empty($guid)) return;

		$user_id = $shop->user_id;

		$log = [
			'time' => now(),
			'name' => $shop->user->name,
			'id' => $user_id,
			'is_trial' => 1, // Flag for identifying, this entry pushed due to trial expired
		];

		$logs = [$log];

		$insert_data = [
			'status' => 1,
			'guid' => $guid,
			'shop_id' => $shop->id,
			'name' => $shop->shop_name,
			'email' => $shop->user->email,
			'created_at' => now(),
			'updated_at' => now(),
			'log' => json_encode($logs),
		];

		$check_shop_exists = DB::table('shop_delete_instances')->where('shop_id', $shop->id)->exists();

		if(!$check_shop_exists){
			DB::table('shop_delete_instances')
			->insert($insert_data);
		}
	}

	private function deleteDropCampusUser($user_id) {
		$user = \App\User::where('id', $user_id)->first();
		resolve(\App\Services\DropCampus\DropmatixCampus::class)->deleteDropmatixCampusUser($user);
	}

    private function removeApiIntervals($user_id){
        app(\App\Services\CPService::class)->caRemoveIntervals($user_id);
    }

	private function deleteSubAccount($user_id){
        DB::table('cms_users')->where('parent_id', $user_id)->delete();
    }
	
}
