<?php
namespace App\Services\DeactiveUser;
use Illuminate\Support\Facades\DB;
use App\Jobs\DeactiveUser\BackupCustomerData;
use Carbon\Carbon;
use App\Services\DeactiveUser\ReportTrait;

class DeleteUser{
	use ReportTrait;

	private $user_id = null;
	private $uuid = null;

	private $tags = [];
	private $customer_id = null;

	public function __construct($user_id, $uuid){
		$this->user_id = $user_id;
		$this->uuid = $uuid;
		$this->tags = [
			'Inactive for 30 days',
			'Inactive for 60 days',
			'Inactive for 90 days'
		];
		$customer_id = DB::table('new_customers')->where('user_id', 2455)->where('cc_user_id', $this->user_id)->value('id');
		if(empty($customer_id)) throw new \Exception('This user has no customer profile.');
		$this->customer_id = $customer_id;
	}

	public function action()
	{
		//check user has active subscription
		$has_not_subscription = $this->userAllSubscriptionList();

		//has all 3 tags
		$dropfunnel_tags = $this->dropFunnelInactiveTags();

		//has all 3 tag email
		$has_email_completed = $this->dropfunnelEmailCompleted();

		//Process to delete
		if($has_not_subscription && $dropfunnel_tags && $has_email_completed)
		{
			BackupCustomerData::dispatch($this->user_id, $this->uuid)->onQueue('database-long-running');
		}
	}


	//check user has active subscription
	private function userStripesubscription(){
		$stripe_customers = DB::table('stripe_customers')
		->where('user_id', '=', $this->user_id)
		->select('stripe_customer_id', 'stripe_key', 'user_id')
		->get();



		// if(!empty($stripe_customers)){
		// 	foreach ($stripe_customers as $stripe_customer) {
				
		// 	}
		// }
	}



	//Get user has active subscription
	private function userActiveSubscriptions($customer_id, $stripe_key){

		$keys = \DRM::stripeKeys($stripe_key);
    	$secret_key = $keys['secret'];

		$stripe = new \Stripe\StripeClient($secret_key);
		$customers = $stripe->customers->retrieve('cus_JO49ITLUDqYd2o', []);

		$subscription = $customers->subscriptions;
		$collect = collect($subscription?? []);
		$data = collect($collect['data']?? [])
		->where('status', 'active')
		->map(function($item){
			return [
				'start' => date('Y-m-d', $item['current_period_start']),
				'end' => date('Y-m-d', $item['current_period_end']),
				'status' => $item['status']
			];
		})
		->toArray();

		return $data;
	}


	//Subscription check
	private function subscriptionData($subscription_id, $stripe_key)
	{

		$keys = \DRM::stripeKeys($stripe_key);
    	$secret_key = $keys['secret'];

		$stripe = new \Stripe\StripeClient($secret_key);
		$subscription = $stripe->subscriptions->retrieve('cus_JO49ITLUDqYd2o', []);

		$collect = collect($subscription?? []);
		$data = collect($collect['data']?? [])
		->where('status', 'active')
		->map(function($item){
			return [
				'start' => date('Y-m-d', $item['current_period_start']),
				'end' => date('Y-m-d', $item['current_period_end']),
				'status' => $item['status']
			];
		})
		->toArray();

		return $data;
	}


	//user all subscription id list
	private function userAllSubscriptionList()
	{
		
		$today = Carbon::now();
		$subscriptions = [];
		
		//1. App purchase
		$subscriptions[] = $purchase_apps = DB::table('purchase_apps')->where('cms_user_id', $this->user_id)
				->whereNotNull('stripe_subscription_id')
				->where('subscription_date_end', '>=', $today)
				->select('stripe_subscription_id')
				->get()
				->keyBy('stripe_subscription_id')
				->unique()
				->map(function($app){
					return 'stripe_key_2455';
				})
				->toArray();


		//2. import payment
		$subscriptions[] = $purchase_import_plans = DB::table('purchase_import_plans')->where('cms_user_id', $this->user_id)
				->whereNotNull('stripe_subscription_id')
				->where('end_date', '>=', $today)
				->select('stripe_subscription_id')
				->get()
				->keyBy('stripe_subscription_id')
				->unique()
				->map(function($import_plan){
					return 'stripe_key_2455';
				})->toArray();

		//3. Dt template
		$subscriptions[] = $template_purchases = DB::table('template_purchases')->where('user_id', $this->user_id)
				->whereNotNull('subscription_id')
				->where('end_date', '>=', $today)
				->select('subscription_id')
				->get()
				->keyBy('subscription_id')
				->unique()
				->map(function($dt_template){
					return 'stripe_key_2439';
				})->toArray();


		//4. DT flat tariff
		$subscriptions[] = $dt_flat_rates = DB::table('dt_flat_rates')->where('user_id', $this->user_id)
				->whereNotNull('subscription_id')
				->where('end_date', '>=', $today)
				->select('subscription_id')
				->get()
				->keyBy('subscription_id')
				->unique()
				->map(function($dt_flat_rate){
					return 'stripe_key_2439';
				})->toArray();

		$check = array_filter($subscriptions);
		if(empty($check)){
			return true;
		}

		

		$itr = '';
		if(!empty($dt_flat_rates) && is_array($dt_flat_rates)){
			$itr .= ' Droptienda flat rate subscriptions: '. implode(', ',array_keys($dt_flat_rates))."\n";
		}

		if(!empty($purchase_apps) && is_array($purchase_apps)){
			$itr .= ' App subscriptions: '. implode(', ',array_keys($purchase_apps))."\n";
		}

		if(!empty($purchase_import_plans) && is_array($purchase_import_plans)){
			$itr .= ' Import plan subscriptions: '. implode(', ',array_keys($purchase_import_plans))."\n";
		}

		if(!empty($template_purchases) && is_array($template_purchases)){
			$itr .= ' Droptienda template subscriptions: '. implode(', ',array_keys($template_purchases))."\n";
		}

		$message = "User has active subscription.\n$itr";
		throw new \Exception(trim($message));
	}


	//User has all 3 tags
	private function dropFunnelInactiveTags()
	{
		$tag_ids = DB::table('dropfunnel_tags')->where('user_id', '=', 2455)->whereIn('tag', $this->tags)->pluck('id')->toArray();
		if(count($tag_ids) < 3) throw new \Exception("Insufficient tags!");
		
		$dropfunnel_tags = DB::table('dropfunnel_customer_tags')->where('customer_id', $this->customer_id)->whereIn('tag_id', $tag_ids)->pluck('tag_id')->toArray();
		$diff_tags = array_diff($tag_ids, $dropfunnel_tags);
		if(empty($diff_tags)) return true;

		$not_have = DB::table('dropfunnel_tags')->where('user_id', '=', 2455)->whereIn('id', $diff_tags)->select('tag')->get()->implode('tag', ', ');
		throw new \Exception('User has no "'. $not_have.'" tags');
	}


	//user has all email
	private function dropfunnelEmailCompleted()
	{
		$day_30 = DB::table('dropfunnel_email_sent_histories')->where('customer_id', $this->customer_id)->where('tag', 'LIKE', '%Inactive for 30 days%')->exists();
		$day_60 = DB::table('dropfunnel_email_sent_histories')->where('customer_id', $this->customer_id)->where('tag', 'LIKE', '%Inactive for 60 days%')->exists();
		$day_90 = DB::table('dropfunnel_email_sent_histories')->where('customer_id', $this->customer_id)->where('tag', 'LIKE', '%Inactive for 90 days%')->exists();
		if($day_30 && $day_60 && $day_90) return true;

		$tag = [];

		if(!$day_30){
			$tag[] = 'Inactive for 30 days';
		}

		if(!$day_60){
			$tag[] = 'Inactive for 60 days';
		}

		if(!$day_90){
			$tag[] = 'Inactive for 90 days';
		}

		$tag_str = implode(', ', $tag);
		throw new \Exception('User has no email which connected with "'. $tag_str.'" tags');
	}
}