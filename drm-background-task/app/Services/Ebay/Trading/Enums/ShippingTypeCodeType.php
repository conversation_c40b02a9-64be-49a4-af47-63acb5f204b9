<?php
/**
 * DO NOT EDIT THIS FILE!
 *
 * This file was automatically generated from external sources.
 *
 * Any manual change here will be lost the next time the SDK
 * is updated. You've been warned!
 */

namespace App\Services\Ebay\Trading\Enums;

class ShippingTypeCodeType
{
    const C_CALCULATED = 'Calculated';
    const C_CALCULATED_DOMESTIC_FLAT_INTERNATIONAL = 'CalculatedDomesticFlatInternational';
    const C_CUSTOM_CODE = 'CustomCode';
    const C_FLAT = 'Flat';
    const C_FLAT_DOMESTIC_CALCULATED_INTERNATIONAL = 'FlatDomesticCalculatedInternational';
    const C_FREE = 'Free';
    const C_FREIGHT = 'Freight';
    const C_FREIGHT_FLAT = 'FreightFlat';
    const C_NOT_SPECIFIED = 'NotSpecified';
}
