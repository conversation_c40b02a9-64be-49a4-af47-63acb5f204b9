<?php
/**
 * DO NOT EDIT THIS FILE!
 *
 * This file was automatically generated from external sources.
 *
 * Any manual change here will be lost the next time the SDK
 * is updated. You've been warned!
 */

namespace App\Services\Ebay\Trading\Enums;

class TicketEventTypeCodeType
{
    const C_ANY = 'Any';
    const C_CUSTOM_CODE = 'CustomCode';
    const C_DE__COMEDY_AND_KABARETT = 'DE_ComedyAndKabarett';
    const C_DE__FREIZEIT_AND_EVENTS = 'DE_FreizeitAndEvents';
    const C_DE__KONZERTE_AND_FESTIVALS = 'DE_KonzerteAndFestivals';
    const C_DE__KULTUR_AND_KLASSIK = 'DE_KulturAndKlassik';
    const C_DE__MUSICALS_AND_SHOWS = 'DE_MusicalsAndShows';
    const C_DE__SONSTIGE = 'DE_Sonstige';
    const C_DE__SPORTVERANSTALTUNGEN = 'DE_Sportveranstaltungen';
    const C_UK__AMUSEMENT_PARKS = 'UK_AmusementParks';
    const C_UK__COMEDY = 'UK_Comedy';
    const C_UK__CONCERTS_AND_GIGS = 'UK_ConcertsAndGigs';
    const C_UK__CONFERENCES_AND_SEMINARS = 'UK_ConferencesAndSeminars';
    const C_UK__EXHIBITIONS_AND_SHOWS = 'UK_ExhibitionsAndShows';
    const C_UK__EXPERIENCES = 'UK_Experiences';
    const C_UK__OTHER = 'UK_Other';
    const C_UK__SPORTING_EVENTS = 'UK_SportingEvents';
    const C_UK__THEATRE_CINEMA_AND_CIRCUS = 'UK_TheatreCinemaAndCircus';
    const C_US__CONCERTS = 'US_Concerts';
    const C_US__MOVIES = 'US_Movies';
    const C_US__OTHER = 'US_Other';
    const C_US__SPORTING_EVENTS = 'US_SportingEvents';
    const C_US__THEATER = 'US_Theater';
}
