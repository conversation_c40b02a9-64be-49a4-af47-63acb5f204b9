<?php
/**
 * DO NOT EDIT THIS FILE!
 *
 * This file was automatically generated from external sources.
 *
 * Any manual change here will be lost the next time the SDK
 * is updated. You've been warned!
 */

namespace App\Services\Ebay\Trading\Enums;

class UnpaidItemStatusTypeCodeType
{
    const C_AWAITING_BUYER_RESPONSE = 'AwaitingBuyerResponse';
    const C_AWAITING_SELLER_RESPONSE = 'AwaitingSellerResponse';
    const C_CUSTOM_CODE = 'CustomCode';
    const C_FINAL_VALUE_FEE_CREDITED = 'FinalValueFeeCredited';
    const C_FINAL_VALUE_FEE_DENIED = 'FinalValueFeeDenied';
    const C_FINAL_VALUE_FEE_ELIGIBLE = 'FinalValueFeeEligible';
    const C_UNPAID_ITEM_ELIGIBLE = 'UnpaidItemEligible';
    const C_UNPAID_ITEM_FILED = 'UnpaidItemFiled';
}
