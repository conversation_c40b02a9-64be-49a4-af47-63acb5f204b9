<?php

namespace App\Services\Accounting\AutoExportFile;

use App\Services\Accounting\AccountingXml;
use ZipArchive;
use Carbon\Carbon;
use Exception;
use File;
use Storage;

final class ExportToXML extends ExportFile
{
	public function export(array $setting, string $type, array $data)
	{
		$xml = AccountingXml::make($data);

		if($type === 'incomming')
		{
			$leadger = $xml->getPayableLedger();

		}else {

			$leadger = $xml->getReceivableLedger();
		}

		$file = $this->generateFileArchive($setting['user_id'], $leadger->asXML());
		$this->sendTo($setting, [$file]);
		return [$file];
	}



	private function generateFileArchive($user_id, $content)
	{
		$url = null;
		$zip = new ZipArchive(); // Load zip library

        $date = Carbon::now()->format('Y_m_d');
        $unix_time = Carbon::now()->unix();
        $filename = 'accounting_xml_'.$date . "_" . $user_id . '_' . $unix_time.'.zip';

        $path = public_path().'/accounting_xml_archives';
        File::makeDirectory($path, 0777, true, true);

        $zip_name = "accounting_xml_archives/{$filename}"; // Zip name

        try {

            if ($zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE)) {

            	$zip->addFromString("ledger-{$date}.xml", $content);
            	$zip->close();

            	if(!file_exists($zip_name)) throw new Exception('Sorry, Failed to download file.');
				ob_end_clean();
				ob_start();

				Storage::disk('spaces')->put($zip_name, file_get_contents(realpath($zip_name)), 'public');
				$url = Storage::disk('spaces')->url($zip_name);
            }
            
        }finally {
        	@unlink(realpath($zip_name));
        }

        return $url;
	}
}