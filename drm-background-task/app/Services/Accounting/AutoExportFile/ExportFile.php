<?php

namespace App\Services\Accounting\AutoExportFile;

use Illuminate\Support\Facades\Mail;
use App\Mail\DRMSEndMail;
use App\Notifications\DRMNotification;
use App\User;
use Exception;

class ExportFile
{
	public function sendTo(array $setting, array $files)
	{
		$user_id = $setting['user_id'];
		$user = User::find($user_id);

		$sendFn = $setting['export_service'] === "mail" ? 'sendMail' : 'sendNotification';
		$this->{$sendFn}($user, $files);
	}

	private function sendNotification(User $user, array $files)
	{
		foreach($files as $file)
		{
			$user->notify(new DRMNotification('Finance archive '.date('Y-m-d'), null, $file));
		}
	}

	private function sendMail(User $user, array $files)
	{
		//Use in method
		$tags = [
		    'date' => date('Y-m-d'),
		    'file' => $this->generateHtmlTable($files),
		    'name' => $user->name,
		];

		$slug = 'accounting_auto_export'; //Page slug
		$mail_data = DRMParseMailTemplate($tags, $slug); //Generated html
		app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data)); //Send
	}

	private function generateHtmlTable(array $files): string
	{
		$tr = '';
		foreach($files as $file)
		{
			$tr = '<tr><td><a href="'.$file.'" target="_blank">'.$file.'</a></td></tr>';
		}
		
		return '<table>'.$tr.'</table>';
	}
}