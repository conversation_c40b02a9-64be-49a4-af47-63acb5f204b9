<?php

namespace App\Services\Accounting\AutoExportFile;

use App\Jobs\AccountingArchiveJob;
use App\User;

final class ExportToPDF extends ExportFile
{
	public function export(array $setting, string $type, array $rowIds)
	{
		$user_id = $setting['user_id'];
		$user = User::find($user_id);

		$rowIds['export_service'] = $setting['export_service'];

		AccountingArchiveJob::dispatch($user, $rowIds)->onQueue('file-archive');
	}
}