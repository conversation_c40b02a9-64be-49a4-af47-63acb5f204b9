<?php
namespace App\Services\Accounting\AutoExportFile;

use App\Exports\InvoicesExport;
use Maatwebsite\Excel\Facades\Excel;
use Storage;

final class ExportToEXCEL extends ExportFile
{
	public function export(array $setting, string $type, array $rowIds)
	{
		ob_end_clean();
        ob_start();

        $file_name = $type.'_' . date('Y_m_d') . '.xlsx';
        $file_path = "accounting_xlsx_archives/{$file_name}";

        (new InvoicesExport($rowIds))->store($file_path, 'spaces', \Maatwebsite\Excel\Excel::XLSX, [
        	'visibility' => 'public',
        ]);

        $file = Storage::disk('spaces')->url($file_path);

        $this->sendTo($setting, [$file]);
		return [$file];
	}
}