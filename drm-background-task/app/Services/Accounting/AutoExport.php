<?php

namespace App\Services\Accounting;

use App\UpcomingInvoice;
use App\NewOrder;
use DB;
use App\Jobs\FinanceAutoExport;
use App\Jobs\FinanceAutoExportFile;

use App\Services\Accounting\AutoExportFile\ExportToCSV;
use App\Services\Accounting\AutoExportFile\ExportToEXCEL;
use App\Services\Accounting\AutoExportFile\ExportToPDF;
use App\Services\Accounting\AutoExportFile\ExportToXML;

use Carbon\Carbon;

class AutoExport
{

	private $user_id;

	private $settings = [];

	private $exportServices = [
		'CSV' => ExportToCSV::class,
		'EXCEL' => ExportToEXCEL::class,
		'PDF' => ExportToPDF::class,
		'XML' => ExportToXML::class,
	];

	public function start()
	{
		$users_id = DB::table('finance_export_setting')
		->where('status', 1)
		->where(function ($q) {
			$q->where('last_time', '<', Carbon::now()->startOfDay())
			->orWhereNull('last_time');
		})
		->select('id', 'type', 'ext', 'interval', 'export_service', 'user_id', 'last_time', 'updated_at')
		->pluck('user_id', 'user_id')
		->toArray();

		foreach($users_id as $user_id)
		{
			FinanceAutoExportFile::dispatch($user_id)->onQueue('file-archive');
		}
	}

	public function run($user_id)
	{
		$this->user_id = $user_id;

		$this->settings = DB::table('finance_export_setting')
		->where('user_id', '=', $user_id)
		->where('status', 1)
		->select('id', 'type', 'ext', 'interval', 'export_service', 'user_id', 'last_time', 'updated_at')
		->get()
		->keyBy('type')
		->toArray();

		$rows = $this->getRowsId();

		foreach($rows as $k => $rowIds)
		{
			if(empty($rowIds)) continue;

			$setting = (array)$this->settings[$k];
			$ext = strtoupper($setting['ext']);

			$type = $k === 'outgoing' ? 'order' : 'invoice';

			if(isset($this->exportServices[$ext]) && $service = $this->exportServices[$ext])
			{
				app($service)->export($setting, $k, [$type => $rowIds]);
			}
		}
	}

	//Get rows
	private function getRowsId(): array
	{
		$orders_id = [];
		$invoices_id = [];

		$from = $to = null;
		if(isset($this->settings['outgoing']))
		{
            $orders_id = $this->outgoingRows($this->settings['outgoing']);
		}

		if(isset($this->settings['incomming']))
		{
            $invoices_id = $this->incommingRows($this->settings['incomming']);
		}

		return ['outgoing' => $orders_id, 'incomming' => $invoices_id];
	}


	//Outgoing rows
	private function outgoingRows($setting): array
	{
		$date = $this->dateBetween($setting);
		if(empty($date)) return [];

		$from = $date['from'];
		$to = $date['to'];
		$user_id = $this->user_id;

	    $orders = NewOrder::where('cms_user_id', $user_id)
        ->where('status', '!=', 'Canceled')
        ->where('invoice_number', '!=', -1)
        ->where('is_locked', '<>', 1)
        ->orderBy("id", "desc");

        if ($from != null) {
            if ($to == null) {
                $orders->whereBetween('order_date', [$from, now()]);
            } else {
                $orders->whereBetween('order_date', [$from, $to]);
            }
        }

        return $orders->pluck('id')->toArray();
	}

	//Incomming rows
	private function incommingRows($setting): array
	{
		$date = $this->dateBetween($setting);
		if(empty($date)) return [];

		$from = $date['from'];
		$to = $date['to'];
		$user_id = $this->user_id;

	    $invoices = UpcomingInvoice::where('user_id', $user_id);
        if ($from != null) {
            if ($to == null) {
                $invoices->whereBetween('date', [$from, now()]);
            } else {
                $invoices->whereBetween('date', [$from, $to]);
            }
        }

        return $invoices->pluck('id')->toArray();
	}


	//Date between
	private function dateBetween($setting): array
	{
		$interval = $setting->interval;
		$last_time = $setting->last_time;

		if(empty($last_time)) {
			$last_time = $setting->updated_at;
		}

		$last_interval = Carbon::parse($last_time)->startOfDay();
		if( $last_interval->greaterThanOrEqualTo(Carbon::now()->startOfDay()) ) return [];
		
		$next_interval = null;
		if($interval === 'daily')
		{
			$current_interval = Carbon::parse($last_time)->addDay();
		}
		elseif($interval === 'weekly')
		{
			$last_interval = Carbon::parse($last_time)->startOfWeek(Carbon::MONDAY)->startOfDay();
			$current_interval = Carbon::parse($last_time)->startOfWeek(Carbon::MONDAY)->addWeek();

		}elseif($interval === 'monthly')
		{
			$current_interval = Carbon::parse($last_time)->addMonth();

		}elseif($interval === 'quarterly')
		{
			$current_interval = Carbon::parse($last_time)->addMonths(3);
			
		}elseif($interval === 'yearly')
		{
			$current_interval = Carbon::parse($last_time)->addYear();
		}

		if($current_interval->copy()->startOfDay()->lessThanOrEqualTo(Carbon::now()->startOfDay()))
		{
			$current_interval = $current_interval->startOfDay();
			DB::table('finance_export_setting')->where('id', '=', $setting->id)->update(['last_time' => $current_interval]);
			return ['from' => $last_interval, 'to' => $current_interval->endOfDay()];
		}

		return [];
	}
}