<?php

namespace App\Services\Accounting;

use App\UpcomingInvoice;
use App\NewOrder;
use DB;

class AccountingXmlFormatter
{

	//Order formatted data
	protected function orderFormattedData(NewOrder $order): array
	{
    	$billing = null;
		if(drmIsJSON($order->billing)) {
	      $billing = json_decode($order->billing);
	    }

		if($order->invoice_layout_id)
		{
		    $small_business = DB::table('drm_invoice_setting')
		        ->where('cms_user_id', $order->cms_user_id)
		        ->where('id', $order->invoice_layout_id)
		        ->value('small_business');
		}else{
		    $small_business = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->value('small_business');
		}

	    $country = orderCountryName($order);
	    $tax_data = drm_order_tax_rate($order, $country, $small_business);

	    $tax_rate = isset($tax_data['tax_rate'])? $tax_data['tax_rate'] : null;
	    $tax = isset($tax_data['tax'])? $tax_data['tax'] : null;


    	$invoice_number = inv_number_string($order->invoice_number, $order->inv_pattern);

    	$customer_name = orderCustomerName($order);

    	// if($order->insert_type == 3)
    	// {
    	// 	$customer_name = $order->client ? $order->client->name : '';
    	// }else{
    	// 	$customer_name = $order->customer ? $order->customer->full_name : '';
    	// }

    	$customer_city = isset($billing->city)? $billing->city : '';
    	$order_date = date('Y-m-d', strtotime($order->order_date));

    	$information = collect(json_decode($order->cart))->pluck('product_name')->implode(', ');

    	return [
    		'order_date' => $order_date,
    		'customer_name' => htmlspecialchars($customer_name),
    		'tax_rate' => $tax_rate,
    		'customer_city' => htmlspecialchars($customer_city),
    		'invoice_number' => $invoice_number,
    		'information' => htmlspecialchars($information),
            'amount' => $order->eur_total,
            'id' => $order->id,
            'due_date' => '',
    	];
	}

    //Invoice formatted data
    protected function invoiceFormattedData(UpcomingInvoice $invoice): array
    {
        return [
            'order_date' => date('Y-m-d', strtotime($invoice->date)),
            'customer_name' => htmlspecialchars($invoice->supplier ? $invoice->supplier->name : ''),
            'tax_rate' => $invoice->tax,
            'customer_city' => htmlspecialchars($invoice->supplier ? $invoice->supplier->state : ''),
            'invoice_number' => $invoice->invoice_number,
            'information' => htmlspecialchars($invoice->description),
            'amount' => $invoice->amount,
            'id' => $invoice->id,
            'due_date' => $invoice->due_date,
        ];
    }

        //payable ledger children
    protected function payableLedgerChildren(&$ledger, $data)
    {        
        $ledger->addChild('date', $data['order_date']);
        $ledger->addChild('amount', number_format((float)$data['amount'], 2, '.', ''));
        $ledger->addChild('accountNo', '');
        $ledger->addChild('costCategoryId', '');
        $ledger->addChild('costCategoryId2', '');
        $ledger->addChild('tax', $data['tax_rate']);
        $ledger->addChild('information', $data['information']);
        $ledger->addChild('currencyCode', 'EUR');
        $ledger->addChild('invoiceId', $data['invoice_number']);
        $ledger->addChild('bookingText', "Ein Produkt, {$data['tax_rate']}% USt.");
        $ledger->addChild('partyId', '');
        $ledger->addChild('paidAt', '');
        $ledger->addChild('iban', '');
        $ledger->addChild('dueDate', $data['due_date']);
        $ledger->addChild('bpAccountNo', '');
        $ledger->addChild('deliveryDate', '');
        $ledger->addChild('orderId', $data['id']);
        $ledger->addChild('supplierName', $data['customer_name']);
        $ledger->addChild('supplierCity', $data['customer_city']);
    }

    //reciavable ledger children
    protected function reciavableLedgerChildren(&$ledger, $data)
    {
        $ledger->addChild('date', $data['order_date']);
        $ledger->addChild('amount', number_format((float)$data['amount'], 2, '.', ''));
        $ledger->addChild('accountNo', '');
        $ledger->addChild('costCategoryId', '');
        $ledger->addChild('costCategoryId2', '');
        $ledger->addChild('tax', $data['tax_rate']);
        $ledger->addChild('information', $data['information']);
        $ledger->addChild('currencyCode', 'EUR');
        $ledger->addChild('invoiceId', $data['invoice_number']);
        $ledger->addChild('bookingText', "Ein Produkt, {$data['tax_rate']}% USt.");
        $ledger->addChild('partyId', '');
        $ledger->addChild('iban', '');
        $ledger->addChild('dueDate', '');
        $ledger->addChild('bpAccountNo', '');
        $ledger->addChild('deliveryDate', '');
        $ledger->addChild('orderId', $data['id']);
        $ledger->addChild('customerName', $data['customer_name']);
        $ledger->addChild('customerCity', $data['customer_city']);
    }

    //Consolidate attributes
    protected function consolidateAttributes(&$consolidate, array $data)
    {
        $consolidate->addAttribute('consolidatedAmount', number_format((float)$data['total_amount'], 2, '.', ''));
        $consolidate->addAttribute('consolidatedDate', date('Y-m-d'));
        $consolidate->addAttribute('consolidatedInvoiceId', '');
        $consolidate->addAttribute('consolidatedCurrencyCode', 'EUR');
        $consolidate->addAttribute('consolidatedDeliveryDate', '');
        $consolidate->addAttribute('consolidatedOrderId', '');
    } 
}