<?php

namespace App\Services\Accounting;
use DB;

class ArchiveLog
{
	private string $content = '';

	private array $userList = [];

	public function logs($orders, $invoices): string
	{
		$this->orderLogs($orders);
		$this->invoiceLogs($invoices);

		return $this->content;
	}

	// Order logs
	private function orderLogs($orders)
	{
		if(empty($orders)) return;
		$idList = collect($orders)->pluck('id')->toArray();
		if(empty($orders)) return;
		
		$this->logsById($idList);
	}

	// Invoice Logs
	private function invoiceLogs($invoices)
	{
		if(empty($invoices)) return;
		$idList = collect($invoices)->pluck('id')->toArray();
		if(empty($idList)) return;

		DB::table('accounting_invoice_logs')->whereIntegerInRaw('invoice_id', $idList)
		->orderBy('id', 'asc')
		->chunk(100, function($chunk) {

			$chunk->each(function($item) {
				$this->formatInvoiceLog($item);
			});
		});
	}

	public function logsById($ordersId): string
	{		
		DB::table('order_logs')->whereIntegerInRaw('order_id', $ordersId)
		->orderBy('id')
		->chunk(100, function($logs) {
			$logs->each(function($item) {
				$item->payload = json_decode($item->payload, true);
				$this->formatMessage($item);
			});
		});

		return $this->content;
	}



	private function formatInvoiceLog($item)
	{
		$this->content .= strip_tags("Invoice ID: {$item->invoice_id}\r\nMessage: {$item->log}\r\nUser: {$item->change_by}\r\n\n\n");
	}

	private function formatMessage($item)
	{
		$log = $item->payload;
		$userName = !empty($log['action_by']) ? $this->userName($log['action_by']) : '';
		$this->content .= strip_tags("Order ID: {$item->order_id}\r\nMessage: {$log['message']}\r\nUser: {$item->userName}\r\n\n\n");
	}


	private function userName($id)
	{
		if(empty($id)) return '';

		if(!isset($this->userList[$id])) {
			$this->userList[$id] = DB::table('cms_users')->where('id', $id)->value('name');
		}

		return (string)$this->userList[$id];
	}
}