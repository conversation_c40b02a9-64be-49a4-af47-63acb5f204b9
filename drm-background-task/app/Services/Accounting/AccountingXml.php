<?php

namespace App\Services\Accounting;

use App\UpcomingInvoice;
use App\NewOrder;
use SimpleXMLElement;
use ZipArchive;
use Carbon\Carbon;
use Exception;
use File;

final class AccountingXml extends AccountingXmlFormatter
{	
	private $invoices;
	private $orders;

    private static $instance;

	private function __construct(array $data)
	{
        $this->invoices = !empty($data['invoice']) ? UpcomingInvoice::with(['supplier', 'invoice_category'])->whereIntegerInRaw('id', $data['invoice'])->get() : collect();
        $this->orders = !empty($data['order']) ? NewOrder::with(['customer', 'client'])->whereIntegerInRaw('id', $data['order'])->get() : collect();
	}


    public static function make(array $data)
    {
        if(!self::$instance)
        {
            self::$instance = new self($data);
        }

        return self::$instance;
    }

    //Download ZIP
	public function downloadZip($user)
	{
		$payable = $this->getPayableLedger();
		$receivable = $this->getReceivableLedger();

		$zip = new ZipArchive(); // Load zip library

        $date = Carbon::now()->format('Y_m_d');
        $unix_time = Carbon::now()->unix();
        $filename = 'accounting_xml_'.$date . "_" . $user->id . '_' . $unix_time.'.zip';


        $path = public_path().'/accounting_xml_archives';
        File::makeDirectory($path, 0777, true, true);

        $zip_name = "accounting_xml_archives/{$filename}"; // Zip name

        try {

            if ($zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE)) {

                if($this->invoices->isNotEmpty())
                {
                    $zip->addFromString("accounts-payable-ledger-{$date}.xml", $payable->asXML());
                }

                if($this->orders->isNotEmpty())
                {
                    $zip->addFromString("accounts-receivable-ledger-{$date}.xml", $receivable->asXML());
                }

            	$zip->close();

            	if(!file_exists($zip_name)) throw new Exception('Sorry, Failed to download file.');

				ob_end_clean();
				ob_start();
				$response = response(file_get_contents(realpath($zip_name)), 200);
				$response->header('Content-Type', 'application/zip');
				$response->header('Content-Disposition', 'attachment; filename="'.$filename.'"');

				return $response;

            }else {
            	throw new Exception('Sorry, Failed to download your file.');
            }
            
        }finally {
        	@unlink(realpath($zip_name));
        }
	}


    //Get payable ledger
	public function getPayableLedger(): SimpleXMLElement
	{
		$xml = new SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><LedgerImport></LedgerImport>');
        $xml->addAttribute('xmlns', 'http://xml.datev.de/bedi/tps/ledger/v050');
        $xml->addAttribute('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');
        $xml->addAttribute('xsi:schemaLocation', 'http://xml.datev.de/bedi/tps/ledger/v050 Belegverwaltung_online_ledger_import_v050.xsd');
        $xml->addAttribute('version', '5.0');
        $xml->addAttribute('generator_info', 'DATEV');
        $xml->addAttribute('generating_system', 'DATEV-Musterdaten');
        $xml->addAttribute('xml_data', 'Kopie nur zur Verbuchung berechtigt nicht zum Vorsteuerabzug');

        //consolidate
        $consolidate = $xml->addChild('consolidate');

        //Invoices
        if($this->invoices->isNotEmpty())
        {
            $total_amount = $this->invoices->sum('amount');
            $this->consolidateAttributes($consolidate, ['total_amount' => $total_amount]);

            foreach($this->invoices as $invoice)
            {
                $data = $this->invoiceFormattedData($invoice);
                $ledger = $consolidate->addChild('accountsPayableLedger');
                $this->payableLedgerChildren($ledger, $data);
            }
        }

        return $xml;
	}


    //Get receivable ledger
	public function getReceivableLedger(): SimpleXMLElement
	{
		$xml = new SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><LedgerImport></LedgerImport>');
        $xml->addAttribute('xmlns', 'http://xml.datev.de/bedi/tps/ledger/v050');
        $xml->addAttribute('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');
        $xml->addAttribute('xsi:schemaLocation', 'http://xml.datev.de/bedi/tps/ledger/v050 Belegverwaltung_online_ledger_import_v050.xsd');
        $xml->addAttribute('version', '5.0');
        $xml->addAttribute('generator_info', 'DATEV');
        $xml->addAttribute('generating_system', 'DATEV-Musterdaten');
        $xml->addAttribute('xml_data', 'Kopie nur zur Verbuchung berechtigt nicht zum Vorsteuerabzug');

        //consolidate
        $consolidate = $xml->addChild('consolidate');

        //Orders
        if($this->orders->isNotEmpty())
        {
            $total_amount = $this->orders->sum('eur_total');
            $this->consolidateAttributes($consolidate, ['total_amount' => $total_amount]);

            foreach($this->orders as $order)
            {
                $data = $this->orderFormattedData($order);
                $ledger = $consolidate->addChild('accountsReceivableLedger');
                $this->reciavableLedgerChildren($ledger, $data);
            }  
        }

        return $xml;
	}
}