<?php

namespace App\Services\DateTime;

class DateTime
{
    public static function getRemainDays($start_date,$end_date){
      $now = strtotime($start_date);
      $end_date = strtotime($end_date);
      $datediff = $end_date - $now;
      $days = round($datediff / (60 * 60 * 24));
      return $days;
    }

    public static function getTrialRemaining($start_date,$days){
      $date = strtotime($start_date);
      $end_date = date("Y-m-d", strtotime("+".$days." days", $date));
      $start_date = date("Y-m-d");
      $days = DateTime::getRemainDays($start_date,$end_date);
      return $days;
    }
    public static function shopInfo($data){
      dd($data);
    }
}
