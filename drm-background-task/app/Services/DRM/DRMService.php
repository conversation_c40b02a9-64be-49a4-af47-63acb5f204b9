<?php
namespace App\Services\DRM;

use App\Services\BaseService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use RuntimeException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\NewCustomer;
use App\DropfunnelCustomerTag;
use App\User;

class DRMService extends BaseService
{

	//Key account users id
	public function keyAccountUsersId($key_id){
		$cahe_key = 'key_manager_id_'.$key_id;
		return Cache::rememberForever($cahe_key, function() use ($key_id) {
			return DB::table('user_manager')->where('manager_id', '=', $key_id)->where('status', '=', 1)->pluck('user_id')->toArray();
		});
	}

	//key managers all users id
	public function keyAccountAllUsersId($key_id){
		$cahe_key = 'key_manager_all_id_'.$key_id;
		return Cache::rememberForever($cahe_key, function() use ($key_id) {
			return DB::table('user_manager')->where('manager_id', '=', $key_id)->pluck('status', 'user_id')->toArray();
		});
	}

	//Due payment invoice
	public function duePaymentInvoiceList()
	{
		$user_id = CRUDBooster::myParentId();
		$data =  Cache::remember('user_inkasso_invoice_list_'.$user_id, now()->addMinutes(10), function () use ($user_id) {
            return DB::table('new_orders')
            ->whereNull('deleted_at')
            ->where('status', 'inkasso')
            ->where('cms_client', $user_id)
            ->select('id', 'eur_total', 'insert_type', 'order_history', 'marketplace_order_ref')
            ->orderBy('id', 'ASC')
            ->get()
            ->map(function($item) {
				$history = @json_decode($item->order_history, true) ?? [];
				$time = @collect($history)->reverse()->firstWhere('status', 'inkasso')['time'];

            	return [
            		'id' => $item->id,
            		'description' => $description,
            		'time' => $time,
            		'type' => $item->insert_type,
            		'eur_total' => $item->eur_total,
            		'marketplace_order_ref' => $item->marketplace_order_ref,
            	];
            })
            ->toArray();
        });

        return collect($data)->map(function($item) {
        	$day = \Carbon\Carbon::now()->diffInDays($item['time']);

        	$day_str =  ($day == 0)? 'today' : ( ($day === 1)? '1 day' : $day.' days');
        	$id = $item['id'];
        	$amount = $item['eur_total'];
        	$amount_str = number_format($amount, 2, ',', '.') . ' DRMService.php' .formatCurrency('EUR');
        	$order_id = $id; //$item['type'] == 8? $item['marketplace_order_ref'] : $item['id'];

        	$url = CRUDBooster::adminPath("invoice-payment/$id");
        	$url_str = "<a href=\"$url\" class=\"btn btn-xs btn-drm\">pay now!</a>";
        	$item['message'] = "Bestellung $order_id in Höhe von $amount_str ist seit $day_str überfällig. <a href=\"$url\" class=\"btn btn-xs btn-drm\">Jetzt bezahlen</a>.";
        	return $item;
        })
        ->pluck('message')
        ->toArray();

	}

	//Due MP payment
	public function dueMpOrderCount()
	{
		$user_id = CRUDBooster::myParentId();
		// return Cache::remember('user_mp_due_invoice_count_'.$user_id, now()->addMinutes(10), function () use ($user_id) {
			$count = \App\NewOrder::where('cms_user_id', 2455)
			->whereNotNull('cms_client')
			->where('cms_client', '=', $user_id)
			->whereNotNull('due_mp_block')
			->count();
return true;
			return $count >= 3;
        // });
	}

	//DRM page
	public function pageContent($page, $default = '')
	{
		if(is_numeric($page) || empty($page))
		{
			return $default;
		}

		$cache_key = 'drm_pages_'.$page;

		$data = Cache::rememberForever($cache_key, function() use($page){
			return DB::table('drm_pages')->where('page_name', $page)->select('page_content')->value('page_content');
		});

		return $data?? $default;
	}

	//Drm user status array
	public function orderAllStatus($user_id)
	{
		$system_status = config('order_status.statuses')?? [];
		$skip_status = config('order_status.skip_status')?? [];

		$manual_status = DB::table('user_order_statuses')->where('user_id', $user_id)->pluck('value')->toArray();
		$all_status = array_unique(array_merge($system_status, $manual_status));
		return array_diff($all_status, $skip_status);
	}

	//User all mail templates
	public function userTemplates($user_id, $channel = NULL)
	{
		$data = [];

		// if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
			if(!empty($channel)){
				$data['status_templates'] = DB::table('order_mail_templates')->where('user_id', $user_id)->where('channel', $channel)->select('id','auto_mail', 'order_status', 'send_invoice', 'sender_email')->get()->keyBy('order_status')->toArray();
			}else{
				$data['status_templates'] = DB::table('order_mail_templates')->where('user_id', $user_id)->whereNull('channel')->select('id','auto_mail', 'order_status', 'send_invoice', 'sender_email')->get()->keyBy('order_status')->toArray();
			}
		// }else{
		// $data['status_templates'] = DB::table('order_mail_templates')->where('user_id', $user_id)->select('id','auto_mail', 'order_status', 'send_invoice', 'sender_email')->get()->keyBy('order_status')->toArray();
		// }

		// if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
			$data['drm_order_mail'] = findSystemMail('drm_order_mail', $user_id, $channel);
			$data['combine_status_email_setting'] = findSystemMail('combine_status_email_settings', $user_id, $channel);
		// }else{
		// $data['drm_order_mail'] = DB::table('drm_order_mail')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		// $data['combine_status_email_setting'] = DB::table('combine_status_email_settings')->where('cms_user_id', $user_id)->select('id','auto_mail', 'send_invoice', 'sender_email')->first();
		// }

		$data['droptienda_order_confirmation_template'] = DB::table('droptienda_order_confirmation_template')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();

		// if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
			$data['remainder_email_settings'] = findSystemMail('remainder_email_settings', $user_id, $channel);
		// }else{
		// $data['remainder_email_settings'] = DB::table('remainder_email_settings')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		// }

		// if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
			$data['marketing_email_settings'] = findSystemMail('marketing_email_settings', $user_id, $channel);
			$data['drm_supplier_mail'] = findSystemMail('drm_supplier_mail', $user_id, $channel);
		// }else{
		// $data['marketing_email_settings'] = DB::table('marketing_email_settings')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		// $data['drm_supplier_mail'] = DB::table('drm_supplier_mail')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		// }

		$data['customer_email_verification'] = DB::table('customer_email_verification')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();

		//Drm offer mail
		$data['drm_offer_mail'] = DB::table('drm_offer_mail')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email', 'payment_link')->first();
		$data['drm_offer_remainder_mail'] = DB::table('drm_offer_remainder_mail')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();

		//Droptienda stock mail
		$data['drm_dt_stock_mail'] = DB::table('drm_dt_stock_mail')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		$data['drm_dt_stock_mail_entry'] = DB::table('drm_dt_stock_mail_entry')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();

		$data['dt_subscription_mail'] = DB::table('dt_subscription_mails')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		$data['dt_subscription_cancel_mail'] = DB::table('subscription_cancel_mail')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();

		// if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
			$data['mp_chat_email_template'] = findSystemMail('mp_chat_email_template', $user_id, $channel);
		// }else{
		// $data['mp_chat_email_template'] = DB::table('mp_chat_email_template')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		// }
		$data['df_csv_email_template'] = DB::table('df_csv_email_template')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();

		$data['order_tracking_emails'] = DB::table('order_tracking_emails')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		$appointmentEmailSetting = DB::table('appointment_email_settings')->select('auto_mail', 'id', 'sender_email', 'status')->where('cms_user_id', $user_id)->orderBy('status', 'desc')->get();
        $data['appointment_cancel_email_settings'] = $data['appointment_email_settings'] = null;
		if ($appointmentEmailSetting->isNotEmpty()) {
		    foreach ($appointmentEmailSetting as $setting) {
		        if (empty($setting->status)) {
                    $data['appointment_cancel_email_settings'] = $setting;
                } else {
                    $data['appointment_email_settings'] = $setting;
                }
            }
        }

        foreach(['drm_email_templete_mp_return', 'drm_email_templete_return_received', 'drm_email_templete_return_shipped', 'drm_email_templete_mp_rejected', 'drm_email_templete_stock_unavailable'] as $t)
        {
        	$data[$t] = DB::table($t)->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
        }

		$data['payment_progress_email_setting'] = findSystemMail('payment_progress_email_settings', $user_id, $channel);

		return $data;
	}

	//User mail templates by Channel
	public function userTemplatesByChannel($user_id, $channel)
	{
		$data = [];

		$data['drm_order_mail'] = DB::table('drm_order_mail_by_channels')->where('cms_user_id', $user_id)->where('channel', $channel)->select('auto_mail', 'id', 'sender_email')->first();


		$data['remainder_email_settings'] = DB::table('remainder_email_setting_by_channels')->where('cms_user_id', $user_id)->where('channel', $channel)->select('auto_mail', 'id', 'sender_email')->first();
		$data['marketing_email_settings'] = DB::table('marketing_email_setting_by_channels')->where('cms_user_id', $user_id)->where('channel', $channel)->select('auto_mail', 'id', 'sender_email')->first();
		$data['mp_chat_email_template'] = DB::table('mp_chat_email_template_by_channels')->where('cms_user_id', $user_id)->where('channel', $channel)->select('auto_mail', 'id', 'sender_email')->first();
		$data['drm_supplier_mail'] = DB::table('drm_supplier_mail_by_channels')->where('cms_user_id', $user_id)->where('channel', $channel)->select('auto_mail', 'id', 'sender_email')->first();
		$data['combine_status_email_setting'] = DB::table('combine_status_email_setting_by_channels')->where('cms_user_id', $user_id)->where('channel', $channel)->select('id','auto_mail', 'send_invoice', 'sender_email')->first();

		//Order Status Email
		$data['status_templates'] = DB::table('order_mail_template_by_channels')->where('user_id', $user_id)->where('channel', $channel)->select('id','auto_mail', 'order_status', 'send_invoice', 'sender_email')->get()->keyBy('order_status')->toArray();

		//Drm offer mail
		$data['drm_offer_mail'] = DB::table('drm_offer_mail_by_channels')->where('cms_user_id', $user_id)->where('channel', $channel)->select('auto_mail', 'id', 'sender_email', 'payment_link')->first();
		$data['drm_offer_remainder_mail'] = DB::table('drm_offer_remainder_mail_by_channels')->where('cms_user_id', $user_id)->where('channel', $channel)->select('auto_mail', 'id', 'sender_email')->first();

		$data['order_tracking_emails'] = DB::table('order_tracking_email_by_channels')->where('cms_user_id', $user_id)->where('channel', $channel)->select('auto_mail', 'id', 'sender_email')->first();

		//Droptienda mail
		$data['droptienda_order_confirmation_template'] = DB::table('droptienda_order_confirmation_template')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		$data['drm_dt_stock_mail'] = DB::table('drm_dt_stock_mail')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		$data['drm_dt_stock_mail_entry'] = DB::table('drm_dt_stock_mail_entry')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		$data['dt_subscription_mail'] = DB::table('dt_subscription_mails')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();
		$data['dt_subscription_cancel_mail'] = DB::table('subscription_cancel_mail')->where('cms_user_id', $user_id)->select('auto_mail', 'id', 'sender_email')->first();

		return $data;
	}

	//Can be delete status
	public function isSystemStatus($order_status)
	{
		$system_status = config('order_status.statuses')?? [];

		$filter = array_filter($system_status, function($item) use($order_status){
			return strtolower($item) === strtolower($order_status);
		});

		return !empty($filter);
	}


	//VOD customers id array
	public function vodCustomerIdArray($user_id)
	{
       	$customer_id = NewCustomer::where('cc_user_id', $user_id)->where('user_id', 2454)->value('id');
        $vod_customers = DB::table('vod_customers')->where('status', 1)->where('user_id', $user_id)->pluck('customer_id')->toArray();
       	$vod_customers[] = $customer_id;
        return array_unique($vod_customers);
	}

	//Most watched vod tags
	public function mostWatchedVodTags()
	{
		return Cache::rememberForever('mostWatchedVodTags', function() {
			return DB::table('dropfunnel_customer_tags')->join('dropfunnel_tags', function ($join) {
	            $join->on('dropfunnel_customer_tags.tag_id', '=', 'dropfunnel_tags.id')
	            ->where('dropfunnel_customer_tags.insert_type', '=', 3)
	            ->whereNull('dropfunnel_customer_tags.deleted_at')
	            ->where('dropfunnel_tags.user_id', '=', 2454);
	        })->selectRaw('COUNT(*) as count, dropfunnel_tags.tag as tag')
	        ->groupBy('dropfunnel_customer_tags.tag_id')
	        ->orderBy('count', 'desc')
	        ->limit(10)
	        ->get()
	        ->map(function($item){
	        	return [
	        		'video' => trim(str_replace('+ Favourite', '', $item->tag)),
	        		'label' => $item->tag,
	        		'count' => $item->count
	        	];
	        })->toArray();
    	});
	}

	//Most video watchers
	public function mostVideoWatchers()
	{
		return Cache::rememberForever('mostVideoWatchers', function() {
			$vod_limit = CRUDBooster::getSetting('vod_limit')?? 1;
	        return DB::table('new_customers')->join('dropfunnel_customer_tags', function ($join) {
	            $join->on('new_customers.id', '=', 'dropfunnel_customer_tags.customer_id')
	            ->where('dropfunnel_customer_tags.insert_type', '=', 3)
	            ->whereNull('dropfunnel_customer_tags.deleted_at');
	        })
	        ->where('new_customers.user_id', '=', 2454)
	        ->selectRaw('COUNT(*) as count, new_customers.full_name, new_customers.email, new_customers.id as id')
            ->groupBy('new_customers.id')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->map(function($item) use($vod_limit) {

            	$total = $item->count;
            	$percentage = (($total * 100) / $vod_limit);
            	$percentage = ($percentage >= 100) ? 100 : round($percentage, 2);

	        	return [
	        		'id' => $item->id,
	        		'name' => $item->full_name,
	        		'email' => $item->email,
	        		'total' => $total,
	        		'percentage' => $percentage
	        	];
        	})->toArray();
        });
	}


	//vod interval finding
	public function vod_interval_str($number, $range = 5)
	{
		$number = $number >= 100 ? 100 : $number;
		$low = ($number / $range);
		$c = (int)$low;
		$c = ($number % $range === 0)? $c : (++$c);

		$h = $c * $range;
		$l = $h - $range + 1;
		return $l.'-'.$h.'% watch';
	}

	//insert VOD Watch progress tag
	public function insertVodWatchProgressTag($customer_id, $user_id)
	{
		$vod_limit = \CRUDBooster::getSetting('vod_limit')?? 1;
		$vod_limit = intval($vod_limit);

        $total = DropfunnelCustomerTag::where('customer_id', '=', $customer_id)->where('insert_type', '=', 3)->count() ?? 0;
        if($total < 1) return;

        $percentage = intval(($total * 100) / $vod_limit);
        $percentage = $percentage >= 100 ? 100 : $percentage;

        //Insert VOD video progress
        $i = 5;
        do{
        	$tag = $this->vod_interval_str($i);
			DropfunnelCustomerTag::insertTag($tag, $user_id, $customer_id, 13);
			$i += 5;
        }while($percentage >= $i);
        //END VOD progress tag
	}

	/*=========================================
	============== Stripe keys ================
	==========================================*/
	//stripe keys
	public function stripeKeys($id)
	{
		$slug =  (strpos($id, 'stripe_key_') !== false)? $id : 'stripe_key_'.$id;
		return Cache::rememberForever($slug, function() use($slug){
			$stripe = DB::table('stripe_keys')->where('slug', $slug)->select('public_key', 'secret_key')->first();
			return empty($stripe)? [] : ['public' => $this->stringEncryption($stripe->public_key, true), 'secret' => $this->stringEncryption($stripe->secret_key, true)];
		});
	}

	public function stripePublicKey($id)
	{
		$keys = $this->stripeKeys($id);
		return empty($keys)? null : $keys['public'];
	}

	public function stripeSecretKey($id)
	{
		$keys = $this->stripeKeys($id);
		return empty($keys)? null : $keys['secret'];
	}
	//Stripe keys end


	public function stringEncryption($text, $decrypt = false)
	{
		$ciphering = "AES-128-CTR"; //Cipher method
		$iv_length = openssl_cipher_iv_length($ciphering); // Use OpenSSl Encryption method
		$options = 0;
		$encryption_iv = '1234567891011121'; // Non-NULL Initialization Vector for encryption
		$encryption_key = "mcfRqcTOFmG"; //encryption key
		return $decrypt? openssl_decrypt ($text, $ciphering, $encryption_key, $options, $encryption_iv) : openssl_encrypt($text, $ciphering, $encryption_key, $options, $encryption_iv);
	}

	public function textEncryption($text, $decrypt = false)
	{
		$ciphering = "AES-128-CTR"; //Cipher method
		$iv_length = openssl_cipher_iv_length($ciphering); // Use OpenSSl Encryption method
		$options = 0;
		$encryption_iv = '1234567341011121'; // Non-NULL Initialization Vector for encryption
		$encryption_key = "mcXaqcTOFqG"; //encryption key
		return $decrypt? openssl_decrypt ($text, $ciphering, $encryption_key, $options, $encryption_iv) : openssl_encrypt($text, $ciphering, $encryption_key, $options, $encryption_iv);
	}


	//Sidebar notifications
	public function sidebarNotificationPositionHooks(){
		return Cache::rememberForever('notification_trigger_cache', function() {
			$positions = array_keys(config('global.notification_positions')?? []);
			return DB::table('notification_settings')
	        ->join('notification_trigger as nt', 'notification_settings.trigger_id', '=', 'nt.id')
	        ->whereIntegerInRaw('notification_settings.position', $positions)
	        ->select('notification_settings.position as position', 'nt.hook as notification_hook')
	        ->get()
	        ->groupBy('position')
	        ->map(function($item){
	        	return collect($item)->pluck('notification_hook')->unique()->toArray();
	        })
	        ->toArray();
		});
	}

	//get all sidebar notification count
	public function sidebarAllNotificationCounts(){
		//Get hooks
		$position_hooks = $this->sidebarNotificationPositionHooks() ?? [];
		if(empty($position_hooks)) return [];

		//Nestesd to single array
		$hooks = collect($position_hooks)->flatten();

		$user_id = CRUDBooster::myParentId() == 2455 ? CRUDBooster::myParentId() : CRUDBooster::myId();

		$data = \App\Notification::where('notifiable_type', 'App\User')
		->where('notifiable_id', $user_id)
		->whereIn('data->hook', $hooks)
		->whereNull('read_at')
		->select('data->hook as hook')
		->get()
		->groupBy('hook')
		->map(function($item){
			return count($item);
		})
		->toArray();

		if(empty($data)){
			return array_fill_keys(array_keys($position_hooks), 0);
		}

		return collect($position_hooks)->map(function($phk) use($data){
			$count = 0;
			foreach($phk as $h){
				if(!empty($data[$h])){
					$count += $data[$h];
				}
			}
			return $count;
		});
	}



	//Tax.php number checker
	public function checkTaxNumber($vatid, $isSkipCheck = false)
	{
		try {

			if(empty($vatid)) return ['success' => false, 'message' => 'Vat Id can\'t be empty!']; //Empty vat id check

			if($isSkipCheck) return ['success' => true, 'message' => 'VAT ID: '.$vatid];

		    $vatid = preg_replace("/[^a-zA-Z0-9]]/", "", $vatid); // remove non alphanum characters

		    // a valid vat id consists of an alpha country code and up to 12 alphanumeric characters
		    $vatidRegex = "/^[a-z]{2}[a-z0-9]{0,12}$/i";

		    if (preg_match($vatidRegex, $vatid) !== 1) {
		    	return ['success' => false, 'message' => 'Invalid Vat Id Format'];
		    }

		    $client = new \SoapClient("https://ec.europa.eu/taxation_customs/vies/checkVatService.wsdl");

		    // using checkVatApprox() as it tends to return a bit more information than checkVat()
		    $params = [
		        'countryCode' => substr($vatid, 0, 2),
		        'vatNumber' => substr($vatid, 2),
		    ];
		    $result = $client->checkVatApprox($params);

		    if ($result->valid) {
		        return ['success' => true, 'message' => $vatid.' is valid.'];
		    } else {
		        // VAT ID is NOT valid
		        return ['success' => false, 'message' => $vatid.' is not valid.'];
		    }
		} catch (\Exception $e) {

			if(DB::table('new_customers')->whereNotNull('vat_number')->where('vat_number', $vatid)->exists())
			{
				return ['success' => true, 'message' => $vatid];
			}

		    return ['success' => false, 'message' => $e->getMessage()];
		}
	}


	//Stripe automatic payment
	public function stripeAutoOrderPayment($order_id, $service_id)
	{
		$order = \App\NewOrder::where('eur_total', '>', 0)->whereNotNull('cms_client')->find($order_id);
		if(empty($order)) throw new \Exception("Invalid data", 1);

		$setupCard = new \App\Services\Stripe\SetupCard('stripe_key_2455');
		$res = $setupCard->payment($order->cms_client, $service_id, $order->eur_total, $order->id);

		if(isset($res['intend_id']))
		{
			$order->update([
				'intend_id' => $res['intend_id'],
				'payment_type' => 'Stripe',
				'payment_date' => date('Y-m-d H:i:s'),
			]);
		}

		if($res['status'] == 'succeeded'){
			$message = ' (Payment by Stripe)';
			call_user_func('\App\Helper\OrderHelper' . '::callerOfTrigger', 'paid', $order, ['message' => $message]);
			return $res;
		}

		throw new \Exception("Payment failed", 2);
	}

	//Call non defined methods
	public function __call($method, $args)
	{

		if(function_exists($method))
		{
			return $method(...$args);
		}

        throw new RuntimeException('Function not exists!');
	}

	public function updateSuppliersInfo ($userId)
	{
		try {
			$user = \App\User::findOrFail($userId);
			$data = [
				'name' 		=> $user->name,
				'email'		=> $user->email,
				'profile_picture' => $user->photo,
			];
			$deliveryCompany = \App\DeliveryCompany::where('supplier_id', $userId);
			if ( $deliveryCompany->exists() ) {
				$deliveryCompany->first()->update($data);
			}
		} catch (\Exception $e){};
	}


	public function generateNewVersionLoginToken($user_id, $email)
	{
	    $payload = json_encode(['id' => $user_id, 'key' => \Str::random(9), 'email' => $email]);
	    $token = $this->stringEncryption($payload);
	    return base64_encode($token);
	}

	public function getActiveOrderStatus($allOrderStatus)
	{
		$user_id = CRUDBooster::myParentId();

		// if( (isLocal() || in_array($user_id, [212, 2592])) ){
			$allOrderStatus = array_diff($allOrderStatus, config('order_status.combine_status'));
		// }

		// if(!in_array($user_id, [179, 212, 2592])){
		// 	unset($allOrderStatus[17]);
		// }

		if(!CRUDBooster::isSuperadmin()){
			unset($allOrderStatus[16]);
		}

		return $allOrderStatus;
	}

	public function getAllSystemEmailLink()
	{
		$user_id = CRUDBooster::myParentId();

		$all_links = [
			"default" => [
				url('admin/drm_all_orders/email-setting'),
				url('admin/drm_all_orders/offer-email-setting'),
				url('admin/drm_all_orders/offer-remainder-email-setting'),
				url('admin/email_marketings/marketing-email-settings'),
				url('admin/drm_all_orders/remainder-email-setting'),
				url('admin/appointment_booking/appointment-email-settings'),
				url('admin/appointment_booking/appointment-cancel-email-settings'),
				url('admin/email_marketings/order-query-email-template'),
				url('admin/email_marketings/df-csv-interval-email-template'),
				url('admin/delivery_companies/supplier-email-setting'),
				url('admin/email_marketings/combine-status-email-setting'),

			],
			"channel" => [
				'all_channel' => [
					url('admin/drm_all_orders/email-setting-by-channel'),
					// url('admin/drm_all_orders/offer-email-setting-by-channel'),
					// url('admin/drm_all_orders/offer-remainder-email-setting-by-channel'),
					url('admin/drm_all_orders/remainder-email-setting-by-channel'),
					url('admin/email_marketings/marketing-email-settings-by-channel'),
					url('admin/email_marketings/order-query-email-template-by-channel'),
					url('admin/delivery_companies/supplier-email-setting-by-channel'),
					url('admin/email_marketings/combine-status-email-setting-by-channel'),
				],
				'dt_channel' => [
					url('admin/email_marketings/droptienda-order-confirmation-template'),
					url('admin/email_marketings/droptienda-stock-entry-email-setting'),
					url('admin/email_marketings/droptienda-stock-email-setting'),
					url('admin/email_marketings/dt-subscription-email-setting'),
					url('admin/email_marketings/dt-subscription-cancel-email-setting')
				],
			]
		];

		$status_templates = $this->getActiveOrderStatus( $this->orderAllStatus($user_id) );

		// if( (isLocal() || in_array($user_id, [212, 2592])) ){
		// 	$status_templates = array_diff($status_templates, config('order_status.combine_status'));
		// }

		// if(!in_array($user_id, [179, 212, 2592])){
		// 	unset($status_templates[17]);
		// }

		// if(!CRUDBooster::isSuperadmin()){
		// 	unset($status_templates[16]);
		// }

		if(!empty($status_templates)){
			foreach($status_templates as $status){
				$all_links['default'][] = url('admin/email_marketings/status-email-setting/'.$status);
				$all_links['channel']['all_channel'][] = url('admin/email_marketings/status-email-setting-by-channel/'.$status);
			}
		}

		$all_links['default'][] = url('admin/email_marketings/customer-email-verification');

		return $all_links;
	}

}
