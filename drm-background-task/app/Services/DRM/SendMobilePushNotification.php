<?php
namespace App\Notifications\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class FirebaseChannel
{
	/**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        $message = $notification->toFirebase($notifiable);

        try{
            $devices = DB::table('user_firebase_device_tokens')->where('user_id', $notifiable->id)->pluck('token')->unique()->toArray();
            if(empty($devices)) return [];

            //API URL of FCM
            $url = 'https://fcm.googleapis.com/fcm/send';

            //api_key
            $api_key = 'AAAAdkXpTXA:APA91bEuxtSypTwpYYNqfCZYjeTgFG3_CIIx29gOp0ylmCT0rzAc-u5FfrAIxEGEpjeX8vrWNK_2s-RrikDhKe_X-PcZ6wkiRqqlu53Cz-mdzlqMRMBP_gidDkXuXzKV8H6V_0L21Hn3';
                 


            //Actions
			$actions = [];
			if(!empty($message['url'])){
				 $actions = [
				    "title" => "Go to DRM", // button name
				    "action" => "browser", // action type
				    "url" => $message['url'] // action additional information
				];
			}

            //Fields        
            $fields = [
                'registration_ids' => $devices,
                'notification' => [
                    "body" => $message['message'],
                    "title" => $message['subject'],
                    "vibrate" => 1,
                    "sound" => 1
                ],
                "actions" => $actions
            ];

            //header includes Content type and api key
            $headers = array(
                'Content-Type:application/json',
                'Authorization:key='.$api_key
            );
                        
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
            $result = curl_exec($ch);
            if ($result === FALSE) {
                throw new \Exception('FCM Send Error: ' . curl_error($ch));
            }
            curl_close($ch);
            return [
                'success' => true,
                'status' => 200,
                'message' => 'Send to firebase!',
                'data' => $result
            ];
        }catch(\Exception $e){
            return [
                'success' => false,
                'status' => 400,
                'message' => $e->getMessage()
            ];
        }
    }
}