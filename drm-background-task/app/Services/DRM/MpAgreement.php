<?php
namespace App\Services\DRM;
use Illuminate\Support\Facades\DB;

class MpAgreement
{
	private $active = false;
	private $minAmount = 0;
	private $maxAmount = 0;

	private $consumedAmount = 0;

	public function __construct($user_id)
	{
		$agreement = DB::table('mp_payment_agreements')
		->where('user_id', '=', $user_id)
		->where('type', '=', 1)
		->select('max_limit', 'min_limit')
		->first();

		if(!empty($agreement))
		{
			$this->active = true;
			$this->minAmount = $agreement->min_limit;
			$this->maxAmount = $agreement->max_limit;

			$this->consumedAmount = DB::table('new_orders')
			->where('cms_user_id', '=', 2455)
			->where('cms_client', $user_id)
			->where('marketplace_paid_status', '<>', 1)
			->whereNotNull('mp_payment_agreement_at')
			->whereNull('credit_ref')
			->sum('eur_total');
		}
	}

	public function isActive(): bool
	{
		return $this->active;
	}

	public function getAvailableAmount()
	{
		return $this->maxAmount - $this->consumedAmount;
	}

	public function getMinAmount()
	{
		return $this->minAmount;
	}

	public function getMaxAmount()
	{
		return $this->maxAmount;
	}

	public function canSendImmediately($amount): bool
	{
		return $this->isActive() && $amount >= $this->getMinAmount() && $this->getAvailableAmount() >= $amount;
	}

	public function testMPAgreement()
	{
		$client_id = 212;

		$amount = 20;


		$mp_supplier_list = config('global.mp_api_suppliers', []);
		$mp_supplier_id = $mp_supplier_list[rand(0,3)];

        $carts = '[{"id":1,"product_name":"Test product","description":"Test product description","qty":1,"rate":'.$amount.',"tax":0,"product_discount":0,"amount":'.$amount.',"mp_supplier_id":'.$mp_supplier_id.'}]';


		$order_info = [
            'user_id' => 2455,
            'cms_client' => $client_id,
            'order_date' => now(),
            'total' => $amount,
            'sub_total' => $amount,
            'discount' => 0,
            'discount_type' => 'fixed',
            'total_tax' => 0,
            'payment_type' => null,
            'status' => "nicht_bezahlt",
            'currency' => "EUR",
            'adjustment' => 0,
            'insert_type' => 8,
            'shop_id' => 440,
            'order_id_api' => rand(23, 9999999),
            'invoice_date' => now(),
            'carts' => json_decode($carts),
            'marketplace_order_ref' => 23,
            'tax_rate' => 0,
            'vat_number' => 'BBPDC',
            'tax_version' => 1,
        ];


        $method = new \ReflectionMethod(\App\Http\Controllers\AdminDrmAllOrdersController::class, "insertMarketplaceOrder");
        $method->setAccessible(true);
        return $method->invoke(new \App\Http\Controllers\AdminDrmAllOrdersController, $order_info, $client_id);
	}
}