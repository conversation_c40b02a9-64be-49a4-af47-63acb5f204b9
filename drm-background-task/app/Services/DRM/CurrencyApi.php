<?php
namespace App\Services\DRM;

use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Exception;
use DateTime;

class CurrencyApi extends BaseService
{
	//Currency data
	private function getCurrencyRate()
	{

        $old_rates = DB::table('currency_rates')->orderBy('id', 'DESC')->first();
        $fetched_at = new DateTime($old_rates->updated_at ?? now());
        $difference = $fetched_at->diff(now());
        $hours = $difference->format('%h');

        if(!$old_rates || $hours > 6){
            $now = now();
            $date = $now->format('Y-m-d');
    
            $cache_date = $now->format('Y_m_d');
            $cahe_key = 'currency_rates_base_'.$cache_date.'_eur';
    
    
            if(Cache::has($cahe_key)) {
                if($res = Cache::get($cahe_key))
                {
                    return json_decode($res, true); 
                }
            }
    
            $res = DB::table('currency_rates')->where('date', $date)->value('rates');
            if($res) {
                Cache::put($cahe_key, $res, now()->addHours(12));
                return json_decode($res, true);
            }
    
            $curl = curl_init();
            curl_setopt_array($curl, array(
              CURLOPT_URL => "https://api.apilayer.com/fixer/latest?base=EUR",
              CURLOPT_HTTPHEADER => array(
                "Content-Type: text/plain",
                "apikey: CUTiQigW8Lb96wbYOUl2uwbvZwIExQvq"
              ),
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_ENCODING => "",
              CURLOPT_MAXREDIRS => 10,
              CURLOPT_TIMEOUT => 0,
              CURLOPT_FOLLOWLOCATION => true,
              CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
              CURLOPT_CUSTOMREQUEST => "GET"
            ));
    
            $curl_txt = curl_exec($curl);
            curl_close($curl);
            $response = json_decode($curl_txt, true);

            if($response['success'])
            {
                $rates = $response['rates'];

                DB::table('currency_rates')
                ->updateOrInsert([
                    'date' => $date,
                ],
                [
                    'fetching_date' => $response['date'],
                    'rates' => json_encode($rates),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                return $rates;
            }
        }

        else{
            return json_decode($old_rates->rates, true);
        }

		return [];
	}


	public function convertToEUR($amount, $currency = 'USD')
	{
		$rates = $this->getCurrencyRate();
		if(!isset($rates[$currency])) return 0;

		$currency_rate = removeCommaPrice($rates[$currency]);
		return $amount / $currency_rate;
	}
}
