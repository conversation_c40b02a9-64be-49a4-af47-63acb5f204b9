<?php
namespace App\Services\DRM;

use App\Traits\EmailCampaignTrait;
use App\EmailMarketing;
use Illuminate\Support\Request;


class CampaignService
{
	use EmailCampaignTrait;

	public function sendTestCampaign(array $payload)
	{
		$campaign = EmailMarketing::where('id', $payload['campaign'])->first();
		$stepId = $payload['step'] ?? null;
		$email = $payload['email'];

		if( isset($payload['full_test']) && $payload['full_test'] === 'yes' )
		{
			$this->sendCampaignEmailTest($email, $campaign);
			$this->sendCampaignStepEmailTest($email, $campaign);

		} elseif($stepId) {
			$this->sendCampaignStepEmailTest($email, $campaign, $stepId);

		} else {
			$this->sendCampaignEmailTest($email, $campaign);
		}

		return ["message" => "Campaign sent successfully!"];
	}
}