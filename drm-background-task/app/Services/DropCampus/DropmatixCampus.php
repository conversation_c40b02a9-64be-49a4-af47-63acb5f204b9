<?php
namespace App\Services\DropCampus;
use App\Services\DropCampus\DropCampusTariffUsers;

class DropmatixCampus extends DropCampusTariffUsers
{
	protected $endpoint = "https://masterclass.dropmatix.com/api/custom-date-wise-plan-activate";
	protected $reactivationEndpoint = "https://masterclass.dropmatix.com/api/custom-date-wise-plan-reactivate/";
	protected $deactivationEndpoint = "https://masterclass.dropmatix.com/api/custom-date-wise-plan-deactivate/";
	protected $deleteEndpoint = "https://masterclass.dropmatix.com/api/delete-custom-plan-activate-user/";
	protected $loginEndpoint = "https://masterclass.dropmatix.com/login";
	protected $column_name = 'drm_campus_user_id';

	public function createDropmatixCampusUser($user){
        $this->createDropCampusUser($user, $this->column_name, $this->endpoint, $this->reactivationEndpoint, $this->loginEndpoint);
		return true;
    }

	public function deactivateDropmatixCampusUser($user){
		$this->deactivateDropCampusUser($user, $this->column_name, $this->deactivationEndpoint);
		return true;
    }

	public function reactivateDropmatixCampusUser($user){
		$this->reactivateDropCampusUser($user, $this->column_name, $this->reactivationEndpoint);
		return true;
    }

	public function deleteDropmatixCampusUser($user){
		$this->deleteDropCampusUser($user, $this->column_name, $this->deleteEndpoint);
		return true;
    }
}
