<?php

namespace App\Services\DropCampus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Mail\DRMSEndMail;
use Illuminate\Support\Facades\Mail;

abstract class DropCampusTariffUsers
{

    protected $api_key = "nlTVg7qkqRizvCQ97uke";

    public function createDropCampusUser($user, $column_name, $endpoint, $reactivationEndpoint, $loginEndpoint){
        $id_exists = DB::table('drop_campus_tariff_user')->where('drm_user_id', $user->id)->whereNotNull($column_name)->value($column_name);
		if($id_exists){
			$this->reactivateDropCampusUser($user, $column_name, $reactivationEndpoint);
			return true;
		}
		$languageId = app('App\Services\UserService')->getProductCountry($user->id);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $password = Str::random(10);

		$deadline = DB::table('purchase_import_plans')->select('end_date')->where('cms_user_id', $user->id)->orderBy('id', 'desc')->first();

		$data = [
			"secret_api_key" => $this->api_key,
			"plan_id" => 42,
			"first_name" => $user->billing_detail->first_name ?? $user->name ?? "N/A",
			"last_name" => $user->billing_detail->last_name ?? $user->last_name ?? "N/A",
			"username" => $user->billing_detail->first_name ?? $user->name ?? "N/A",
			"email" => $user->email,
			"password" => $password,
			"country_code" => $lang,
			"mobile" => $user->billing_detail->phone ?? "+880",
			"address" => $user->billing_detail->address ?? "N/A",
			"state" => $user->billing_detail->city ?? "N/A",
			"zip" => $user->billing_detail->zip ?? 23823,
			"country" => "DE",
			"city" => $user->billing_detail->city ?? "N/A",
		];

		$headers = array();
		$headers[] = "Content-Type: application/json";

		$ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
		
		$json = curl_exec($ch);
        curl_close($ch);
		$response = json_decode($json);

		$expertise_campus_id = $response->user->id;

		DB::table('drop_campus_tariff_user')->insert([
			'drm_user_id' => $user->id,
			$column_name => $expertise_campus_id,
			'is_blocked' => 0,
			'created_at' => now(),
			'updated_at' => now()
		]);

		$tags = [
			'user_name' =>  $user->name,
			'password' =>  $password,
			'logo' => '<img id="display_logo" width="150" src="https://drm.software/images/branding/logo.png">',
			'login_link' => $loginEndpoint
		];

		$slug = 'dropcampus_account_activation_mail';
		$mail_data = DRMParseMailTemplate($tags, $slug, $lang);

		if(!isLocal()){
			app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));
		}

		return true;
    }

    public function deactivateDropCampusUser($user, $column_name, $deactivationEndpoint){
        $id_exists = DB::table('drop_campus_tariff_user')->where('drm_user_id', $user->id)->whereNotNull($column_name)->first();
        if($id_exists){
			$column = $column_name;

			DB::table('drop_campus_tariff_user')->where([
					'drm_user_id' => $user->id,
					$column_name => $id_exists->$column,
				])
				->update([
					'is_blocked' => 1,
					'updated_at' => now()
				]);

			$deactivationEndpoint = $deactivationEndpoint . $id_exists->$column . "?secret_api_key=" . $this->api_key . "&status=0";

			$headers = array();
			$headers[] = "Content-Type: application/json";

			$ch = curl_init($deactivationEndpoint);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	
			$json = curl_exec($ch);
			curl_close($ch);
        }
		return true;
    }

    public function reactivateDropCampusUser($user, $column_name, $reactivationEndpoint){
        $id_exists = DB::table('drop_campus_tariff_user')->where('drm_user_id', $user->id)->whereNotNull($column_name)->first();
        if($id_exists){
			$deadline = DB::table('purchase_import_plans')->select('end_date')->where('cms_user_id', $user->id)->orderBy('id', 'desc')->first();
			$column = $column_name;

			DB::table('drop_campus_tariff_user')->where([
					'drm_user_id' => $user->id,
					$column_name => $id_exists->$column,
				])
				->update([
					'is_blocked' => 0,
					'updated_at' => now()
				]);

			$reactivationEndpoint = $reactivationEndpoint . $id_exists->$column . "?secret_api_key=" . $this->api_key . "&status=1";

			$headers = array();
			$headers[] = "Content-Type: application/json";

			$ch = curl_init($reactivationEndpoint);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	
			$json = curl_exec($ch);
			curl_close($ch);
        }
		return true;
    }

	public function deleteDropCampusUser($user, $column_name, $deleteEndpoint){
        $id_exists = DB::table('drop_campus_tariff_user')->where('drm_user_id', $user->id)->whereNotNull($column_name)->first();
        if($id_exists){
			$column = $column_name;

			$deletionEndpoint = $deleteEndpoint . $id_exists->$column . "?secret_api_key=" . $this->api_key;

			DB::table('drop_campus_tariff_user')
				->where('drm_user_id', $user->id)
				->where($column_name, $id_exists->$column)
				->whereNotNull($column_name)
				->delete();

			$headers = array();
			$headers[] = "Content-Type: application/json";

			$ch = curl_init($deletionEndpoint);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
			curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	
			$json = curl_exec($ch);
			curl_close($ch);
        }
		return true;
    }
}
