<?php
namespace App\Services\DropCampus;
use App\Services\DropCampus\DropCampusTariffUsers;

class DroptiendaCampus extends DropCampusTariffUsers
{
	protected $endpoint = "https://vm7.droptienda.eu/api/custom-date-wise-plan-activate";
	protected $reactivationEndpoint = "https://vm7.droptienda.eu/api/custom-date-wise-plan-reactivate/";
	protected $deactivationEndpoint = "https://vm7.droptienda.eu/api/custom-date-wise-plan-deactivate/";
	protected $loginEndpoint = "https://vm7.droptienda.eu/login";
	protected $column_name = 'dt_campus_user_id';

	public function createDroptiendaCampusUser($user){
        $this->createDropCampusUser($user, $this->column_name, $this->endpoint, $this->reactivationEndpoint, $this->loginEndpoint);
		return true;
    }

	public function deactivateDroptiendaCampusUser($user){
		$this->deactivateDropCampusUser($user, $this->column_name, $this->deactivationEndpoint);
		return true;
    }

	public function reactivateDroptiendaCampusUser($user){
		$this->reactivateDropCampusUser($user, $this->column_name, $this->reactivationEndpoint);
		return true;
    }
}
