<?php
namespace App\Services\DropCampus;
use App\Services\DropCampus\DropCampusTariffUsers;

class ExpertiseCampus extends DropCampusTariffUsers
{
	protected $endpoint = "https://masterclass.expertise.rocks/api/custom-date-wise-plan-activate";
	protected $reactivationEndpoint = "https://masterclass.expertise.rocks/api/custom-date-wise-plan-reactivate/";
	protected $deactivationEndpoint = "https://masterclass.expertise.rocks/api/custom-date-wise-plan-deactivate/";
	protected $deleteEndpoint = "https://masterclass.expertise.rocks/api/delete-custom-plan-activate-user/";
	protected $loginEndpoint = "https://masterclass.expertise.rocks/login";
	protected $column_name = 'expertise_campus_user_id';

	public function createExpertiseCampusUser($user){
        $this->createDropCampusUser($user, $this->column_name, $this->endpoint, $this->reactivationEndpoint, $this->loginEndpoint);
		return true;
    }

	public function deactivateExpertiseCampusUser($user){
		$this->deactivateDropCampusUser($user, $this->column_name, $this->deactivationEndpoint);
		return true;
    }

	public function reactivateExpertiseCampusUser($user){
		$this->reactivateDropCampusUser($user, $this->column_name, $this->reactivationEndpoint);
		return true;
    }

	public function deleteExpertiseCampusUser($user){
		$this->deleteDropCampusUser($user, $this->column_name, $this->deleteEndpoint);
		return true;
    }
}
