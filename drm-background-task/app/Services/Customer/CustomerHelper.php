<?php

namespace App\Services\Customer;

use App\NewOrder as Order;
// use app\Services\Order\OrderService;
// use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\DropfunnelCustomerTag;
// use DRM;
use App\NewCustomer as Customer;
use Illuminate\Support\Facades\DB;

class CustomerHelper
{
    //update billing / shipping address json
    // public static function updateBillingShippingAddress($new_value, $old_value)
    // {
    //     if (drmIsJSON($new_value) && drmIsJSON($old_value)) {
    //         $json_data = [];
    //         $new_json = json_decode($new_value, true);
    //         $old_json = json_decode($old_value, true);

    //         foreach ($new_json as $key => $value) {
    //             $json_data[$key] = ((is_null($new_json[$key]) || ($new_json[$key] == '')) && isset($old_json[$key])) ? $old_json[$key] : $new_json[$key];
    //         }
    //         foreach ($old_json as $key => $value) {
    //             if (!isset($json_data[$key])) $json_data[$key] = $old_json[$key];
    //         }
    //         return json_encode($json_data);
    //     }
    //     return drmIsJSON($new_value) ? $new_value : (drmIsJSON($old_value) ? $old_value : self::billingInfoJson([]));
    // }

    // //Return customer info as json
    // public static function customerInfoJson($customer_info, $json = true)
    // {
    //     $country = isset($customer_info['country']) ? $customer_info['country'] : null;
    //     $data = [
    //         'name' => isset($customer_info['full_name']) ? $customer_info['full_name'] : null,
    //         'company' => isset($customer_info['company_name']) ? $customer_info['company_name'] : null,
    //         'address' => isset($customer_info['address']) ? $customer_info['address'] : null,
    //         'zip_code' => isset($customer_info['zip_code']) ? $customer_info['zip_code'] : null,
    //         'city' => isset($customer_info['city']) ? $customer_info['city'] : null,
    //         'state' => isset($customer_info['state']) ? $customer_info['state'] : null,
    //         'country' => $country,
    //     ];

    //     return $json ? json_encode($data) : $data;
    // }

    // public static function billingInfoJson($customer_info, $json = true)
    // {
    //     $country = isset($customer_info['country_billing']) ? $customer_info['country_billing'] : (isset($customer_info['country']) ? $customer_info['country'] : null);
    //     $data = [
    //         'name' => isset($customer_info['billing_name']) ? $customer_info['billing_name'] : (isset($customer_info['full_name']) ? $customer_info['full_name'] : null),
    //         'company' => isset($customer_info['billing_company']) ? $customer_info['billing_company'] : (isset($customer_info['company_name']) ? $customer_info['company_name'] : null),
    //         'street' => isset($customer_info['street_billing']) ? $customer_info['street_billing'] : (isset($customer_info['address']) ? $customer_info['address'] : null),
    //         'address' => isset($customer_info['address_billing']) ? $customer_info['address_billing'] : null,
    //         'zip_code' => isset($customer_info['zipcode_billing']) ? $customer_info['zipcode_billing'] : (isset($customer_info['zip_code']) ? $customer_info['zip_code'] : null),
    //         'city' => isset($customer_info['city_billing']) ? $customer_info['city_billing'] : (isset($customer_info['city']) ? $customer_info['city'] : null),
    //         'state' => isset($customer_info['state_billing']) ? $customer_info['state_billing'] : (isset($customer_info['state']) ? $customer_info['state'] : null),
    //         'country' => $country,
    //     ];

    //     return $json ? json_encode($data) : $data;
    // }

    // public static function shippingInfoJson($customer_info, $json = true)
    // {
    //     $country = isset($customer_info['country_shipping']) ? $customer_info['country_shipping'] : (isset($customer_info['country']) ? $customer_info['country'] : null);

    //     $data = [
    //         'name' => isset($customer_info['shipping_name']) ? $customer_info['shipping_name'] : (isset($customer_info['full_name']) ? $customer_info['full_name'] : null),
    //         'company' => isset($customer_info['shipping_company']) ? $customer_info['shipping_company'] : (isset($customer_info['company_name']) ? $customer_info['company_name'] : null),
    //         'street' => isset($customer_info['street_shipping']) ? $customer_info['street_shipping'] : (isset($customer_info['address']) ? $customer_info['address'] : null),
    //         'address' => isset($customer_info['address_shipping']) ? $customer_info['address_shipping'] : null,
    //         'zip_code' => isset($customer_info['zipcode_shipping']) ? $customer_info['zipcode_shipping'] : (isset($customer_info['zip_code']) ? $customer_info['zip_code'] : null),
    //         'city' => isset($customer_info['city_shipping']) ? $customer_info['city_shipping'] : (isset($customer_info['city']) ? $customer_info['city'] : null),
    //         'state' => isset($customer_info['state_shipping']) ? $customer_info['state_shipping'] : (isset($customer_info['state']) ? $customer_info['state'] : null),
    //         'country' => $country,
    //     ];

    //     return $json ? json_encode($data) : $data;
    // }

    //Format order customer field data from customer
    // public static function orderCustomerFormat(Customer $customer): array
    // {
    //     $billing_json = $customer->billing;
    //     $shipping_json = $customer->shipping;

    //     $customer_info = [
    //         'name' => $customer->full_name,
    //         'company' => $customer->company_name,
    //         'street' => $customer->address,
    //         'zip_code' => $customer->zip_code,
    //         'city' => $customer->city,
    //         'state' => $customer->state,
    //         'country' => $customer->country,
    //         'vat_number' => $customer->vat_number,
    //     ];

    //     $billing = json_decode($billing_json, true) ?? [];
    //     $shipping = json_decode($shipping_json, true) ?? [];

    //     $address_structure = [
    //         "name" => '',
    //         "company" => '',
    //         "street" => '',
    //         "address" => '',
    //         "zip_code" => '',
    //         "city" => '',
    //         "state" => '',
    //         "country" => "Germany"
    //     ];

    //     $billing = array_merge($address_structure, $billing);
    //     $shipping = array_merge($address_structure, $shipping);

    //     return [
    //         'customer_info' => $customer_info,
    //         'billing' => $billing,
    //         'shipping' => $shipping,
    //     ];
    // }

    //Format customer field data
    // public static function customerFormat(Customer $customer): array
    // {
    //     $billing_json = $customer->billing;
    //     $shipping_json = $customer->shipping;

    //     $customer_info = [
    //         'name' => $customer->full_name,
    //         'company' => $customer->company_name,
    //         'street' => $customer->address,
    //         'zip_code' => $customer->zip_code,
    //         'city' => $customer->city,
    //         'state' => $customer->state,
    //         'country' => $customer->country,

    //         'email' => $customer->email,
    //         'phone' => $customer->phone,
    //         'vat_number' => $customer->vat_number,
    //         'website' => $customer->website,

    //         'currency' => $customer->currency,
    //         'default_language' => $customer->default_language,
    //         'note' => $customer->note ?? null,
    //     ];

    //     $billing = json_decode($billing_json, true) ?? [];
    //     $shipping = json_decode($shipping_json, true) ?? [];

    //     $address_structure = [
    //         "name" => '',
    //         "company" => '',
    //         "street" => '',
    //         "address" => '',
    //         "zip_code" => '',
    //         "city" => '',
    //         "state" => '',
    //         "country" => "Germany"
    //     ];


    //     $billing = array_merge($address_structure, $billing);
    //     $shipping = array_merge($address_structure, $shipping);

    //     return [
    //         'customer_info' => $customer_info,
    //         'billing' => $billing,
    //         'shipping' => $shipping,
    //     ];
    // }

    /**
     * Update from order edit
     * @deprecated
     */
    // public static function updateCustomer($customer_info, $id)
    // {
    //     $customer = Customer::find($id);
    //     if ($customer) {
    //         $customer_data = [];
    //         $customer_data['full_name'] = $customer_info['full_name'];
    //         $customer_data['company_name'] = $customer_info['company_name'] ?? null;
    //         $customer_data['country'] = $customer_info['country'];
    //         $customer_data['phone'] = $customer_info['phone'] ?? null;
    //         $customer_data['website'] = $customer_info['website'] ?? null;
    //         $customer_data['city'] = $customer_info['city'];
    //         $customer_data['zip_code'] = $customer_info['zip_code'];
    //         $customer_data['state'] = $customer_info['state'] ?? null;
    //         $customer_data['currency'] = $customer_info['currency'] ?? null;
    //         $customer_data['default_language'] = $customer_info['default_language'] ?? 'DE';
    //         $customer_data['address'] = $customer_info['address'];

    //         if (isset($customer_info['vat_number'])) {
    //             $customer_data['vat_number'] = $customer_info['vat_number'];
    //         }

    //         $shipping = self::updateBillingShippingAddress(self::shippingInfoJson($customer_info), $customer->shipping);
    //         $billing = (isset($customer_info['is_same_address'])) ? $shipping : self::updateBillingShippingAddress(self::billingInfoJson($customer_info), $customer->billing);

    //         $customer_data['billing'] = $billing;
    //         $customer_data['shipping'] = $shipping;

    //         // customer filter empty value
    //         $customer_data = array_filter($customer_data);
    //         $customer->update($customer_data);

    //         app(OrderService::class)->updateOrderCustomerInfo($customer);

    //         return $id;
    //     }
    //     return null;
    // }



    //Billing address format
    public static function addressStr(Order $order, $type = 'billing', $one_line = false): string
    {
        $address = $type === 'billing' ? $order->billing : $order->shipping;

        if (drmIsJSON($address) && $address_json = json_decode($address)) {

            $html = '';

            $country_name = ($address_json->country) ? $address_json->country : null;

            if (isset($address_json->name)) {
                $html .= ($address_json->name) ? '<b>' . $address_json->name . '</b>' : '';
                $html .= ($address_json->name) ? $one_line ? ', ' : '<br>' : '';
            }

            if (isset($address_json->company)) {
                $html .= ($address_json->company) ? '<b>' . $address_json->company . '</b>' : '';
                $html .= ($address_json->company) ? $one_line ?  ', ' : '<br>' : '';
            }


            if ($type === 'billing' && $vat_number = $order->vat_number) {
                $html .= ($vat_number) ? 'Umsatzsteuernummer: <b>' . $vat_number . '</b>' : '';
                $html .= ($vat_number) ? $one_line ?  ', ' : '<br>' : '';
            }

            $html .= ($address_json->street) ? $address_json->street : '';
            $html .= ($address_json->street) ? $one_line ?  ', ' : '<br>' : '';
            $html .= ($address_json->zip_code) ? $address_json->zip_code . ' ' : '';
            $html .= ($address_json->city) ? $address_json->city : '';
            $html .= ($address_json->city) ? $one_line ? ', ' : '<br>' : '';

            $html .= ($country_name) ? $country_name : 'Germany';

            if($type === 'billing' && ($country_name == 'Spain' || str_starts_with($order->vat_number ?? '', 'ES')) && $tax_number = $order->tax_number)
            {
                $html .= ($tax_number) ? '<br>' . 'Steuernummer / NIF: <b>' . $tax_number . '</b>' : '';
            }

            $html .= '<br>';
            return $html;
        }

        return '';
    }


    //Customer country all
    // private static function customerCountryAll(Customer $customer): array
    // {
    //     $billing = json_decode($customer->billing, true) ?? [];
    //     $shipping = json_decode($customer->shipping, true) ?? [];

    //     $data = [];
    //     if (isset($billing['country'])) {
    //         $data[] = $billing['country'];
    //     }

    //     if (isset($shipping['country'])) {
    //         $data[] = $shipping['country'];
    //     }

    //     $data[] = $customer->country;

    //     return array_unique(array_filter($data));
    // }

    // Customer country list
    // public static function customerCountryList(Customer $customer): array
    // {
    //     $all_country = self::customerCountryAll($customer);
    //     return DRM::taxCountryList($all_country);
    // }

    /**
     * Insert lead or customer tag
     */
    public static function insertLeadOrCustomerTag($userId, $customerId): void
    {
        if (static::customerHasOrder($userId, $customerId)) {
            static::storeCustomerType($userId, $customerId, DropfunnelCustomerTag::CUSTOMER);
        } else {
            static::storeCustomerType($userId, $customerId, DropfunnelCustomerTag::LEAD);
        }
    }

    /**
     * Customer has order
     */
    public static function customerHasOrder($userId, $customerId): bool
    {
        if (is_null($userId) || is_null($customerId)) {
            return false;
        }

        $order = Order::with('customer:id,billing')
            ->whereNull('new_orders.deleted_at')
            ->where('new_orders.invoice_number', '!=', -1)
            ->where('new_orders.test_order', '!=', 1)
            ->where('new_orders.credit_number', '=', 0)
            ->whereNull('new_orders.credit_ref')
            ->where('new_orders.eur_total', '>', 0)
            ->where('new_orders.cms_user_id', $userId)
            ->where('new_orders.drm_customer_id', $customerId)
            ->select('id', 'drm_customer_id')
            ->first();

        return $order && $order->id && CustomerHelper::hasCompleteBilling($order->customer->billing ?? null);
    }

    /**
     * Has billing
     */
    public static function hasCompleteBilling(?string $address): bool
    {
        if (! $address || ! drmIsJSON($address)) {
            return false;
        }

        $row = json_decode($address, true);
        if (
            empty($row) ||
            empty($row['name']) ||
            empty($row['street']) ||
            empty($row['zip_code']) ||
            empty($row['city']) ||
            empty($row['country'])
        ) {
            return false;
        }

        return true;
    }

    /**
     * Store customer type
     */
    public static function storeCustomerType($userId, $customerId, $customerType): void
    {
        DB::table('new_customers')
            ->where('user_id', $userId)
            ->where('id', $customerId)
            ->where('customer_type', '<>', $customerType)
            ->update(['customer_type' => $customerType]);
    }
}
