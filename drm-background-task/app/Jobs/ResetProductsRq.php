<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ResetProductsRq implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id)
    {
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // recalculate rq
        $order_users = \App\NewOrder::where([
                ['cms_user_id', '=', $this->user_id],
                ['test_order', '!=', 1],
                ['invoice_number', '>', 0],
            ])
            ->whereNull('offer_number')
            ->get()
            ->groupBy('cms_user_id');

        foreach($order_users as $user_id => $user_orders) {
            foreach($user_orders as $index => $order_data) {
                $carts = json_decode($order_data->cart);

                if(!empty($carts))
                {
                    $shop_id = $order_data->shop_id;
                    $shop = \App\Shop::where('id', $shop_id)->select('channel', 'lang')->first();
                    $channel = $shop->channel;
                    $lang = $shop->lang ?? 'de';
            
                    foreach ($carts as $key => $cart) {
                        $cart = (object) $cart;
                        $ean = $cart->ean ?? null;
                        $product_name = $cart->product_name ?? null;
                        if(empty($product_name)) continue;
            
                        if(!empty($shop)) {
                            // 1 Total calcled order
                            $total_cancled_order = \App\NewOrder::where('shop_id', $shop_id)
                                ->where('test_order', '!=', 1)
                                ->where('invoice_number', '>', 0)
                                ->whereNull('offer_number')
                                ->where(function ($query) {
                                    $query->whereIn('status', ['mp_return', 'Retoure eingegangen'])
                                        ->orWhere(function ($q) {
                                            $q->where('insert_type', 9)->where('order_id_api', 'like', 'mp-return%');
                                        });
                                })
                                ->where('cms_user_id', $user_id)
                                ->where(function($q) use ($ean, $product_name) {
                                    if($ean) {
                                        $q->whereJsonContains('cart', ['ean' => $ean])
                                        ->orWhereJsonContains('cart', ['product_name' => $product_name]);
                                    }else{
                                        $q->whereJsonContains('cart', ['product_name' => $product_name]);
                                    }
                                })
                            ->count();

                            if($total_cancled_order) {
                                // 2 user all shop product order
                                $total_prod_order = \App\NewOrder::where('cms_user_id', $user_id)
                                    ->where('shop_id', $shop_id)
                                    ->where('test_order', '!=', 1)
                                    ->where('invoice_number', '>', 0)
                                    ->whereNull('offer_number')
                                    ->where('credit_number', 0)
                                    ->whereNotIn('status', ['mp_return', 'Retoure eingegangen'])
                                    ->where(function ($query) {
                                        $query->where('insert_type', '<>', 9)->where('order_id_api', 'not like', 'mp-return%');
                                    })
                                    ->where(function($q) use ($ean, $product_name) {
                                        if($ean) {
                                            $q->whereJsonContains('cart', ['ean' => $ean])
                                            ->orWhereJsonContains('cart', ['product_name' => $product_name]);
                                        }else{
                                            $q->whereJsonContains('cart', ['product_name' => $product_name]);
                                        }
                                    })
                                ->count();

                                //Calculate rq
                                $rq = 100;
                                if($total_prod_order) {
                                    $total_order = $total_cancled_order + $total_prod_order;
                                    $rq = round(( ($total_cancled_order / $total_order) * 100), 2 );
                                }

                                // 3 Update channel products RQ
                                DB::table('channel_products')
                                    ->where('user_id', $user_id)
                                    ->where('shop_id', $shop_id)
                                    ->where(function($q) use ($ean, $product_name, $lang) {
                                        if($ean) {
                                            $q->where('ean', $ean)
                                            ->orWhereJsonContains('title', [$lang => $product_name]);
                                        }else{
                                            $q->whereJsonContains('title', [$lang => $product_name]);
                                        }
                                    })
                                    ->update(['rq' => $rq]);
                            }
                        }
                
                        // 4 Total calcled order
                        $total_cancled_order = \App\NewOrder::whereNotNull('shop_id')
                            ->where('test_order', '!=', 1)
                            ->where('invoice_number', '>', 0)
                            ->whereNull('offer_number')
                            ->where(function ($query) {
                                $query->whereIn('status', ['mp_return', 'Retoure eingegangen'])
                                    ->orWhere(function ($q) {
                                        $q->where('insert_type', 9)->where('order_id_api', 'like', 'mp-return%');
                                    });
                            })
                            ->where('cms_user_id', $user_id)
                            ->where(function($q) use ($ean, $product_name) {
                                if($ean) {
                                    $q->whereJsonContains('cart', ['ean' => $ean])
                                    ->orWhereJsonContains('cart', ['product_name' => $product_name]);
                                }else{
                                    $q->whereJsonContains('cart', ['product_name' => $product_name]);
                                }
                            })
                        ->count();

                        if(!$total_cancled_order) continue;

                        // 5 Total product order
                        $total_prod_order = \App\NewOrder::where('cms_user_id', $user_id)
                            ->whereNotNull('shop_id')
                            ->where('test_order', '!=', 1)
                            ->where('invoice_number', '>', 0)
                            ->whereNull('offer_number')
                            ->where('credit_number', 0)
                            ->whereNotIn('status', ['mp_return', 'Retoure eingegangen'])
                            ->where(function ($query) {
                                $query->where('insert_type', '<>', 9)->where('order_id_api', 'not like', 'mp-return%');
                            })
                            ->where(function($q) use ($ean, $product_name) {
                                if($ean) {
                                    $q->whereJsonContains('cart', ['ean' => $ean])
                                    ->orWhereJsonContains('cart', ['product_name' => $product_name]);
                                }else{
                                    $q->whereJsonContains('cart', ['product_name' => $product_name]);
                                }
                            })
                        ->count();

                        //Calculate rq
                        $rq = 100;
                        if($total_prod_order) {
                            $total_order = $total_cancled_order + $total_prod_order;
                            $rq = round(( ($total_cancled_order / $total_order) * 100), 2 );
                        }
            
                        // 6 Update drm products RQ
                        DB::table('drm_products')
                            ->where('user_id', $user_id)
                            ->where(function($q) use ($ean, $product_name, $lang) {
                                if($ean) {
                                    $q->where('ean', $ean)
                                    ->orWhereJsonContains('title', [$lang => $product_name]);
                                }else{
                                    $q->whereJsonContains('title', [$lang => $product_name]);
                                }
                            })
                            ->update(['rq' => $rq]);
                    }
                }
            }
        }
    }
}
