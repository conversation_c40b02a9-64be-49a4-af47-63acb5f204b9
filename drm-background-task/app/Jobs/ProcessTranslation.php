<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Helper\GooglTranslationApi;

class ProcessTranslation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $import_id;
    protected $source;
    protected $target;
    public $tries = 1;
    public function __construct($import_id,$source,$target=array())
    {  
        $this->import_id=$import_id;
        $this->source=$source;
        $this->target=$target;
    }

    public function handle()
    {   
        try {
        $import_id= $this->import_id;
        $source= $this->source;
        $target= $this->target;
        
        $translationApi=new GooglTranslationApi();
        $translationApi->Dotranslation($import_id,$source,$target);

        } catch(\Exception $e) {}

    }
}
