<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use League\Csv\Writer;
use App\Traits\EmailCampaignTrait;

class DropfunnelCsvEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, EmailCampaignTrait;

    public $connectionName = 'database-long-running';
    public $timeout = 7200;
    public $tries = 2;
    public $retryAfter = 7280;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        ini_set("memory_limit",-1);
        DB::table('email_marketings')->whereNotNull('csv_email')->whereNotNull('csv_interval')
        ->selectRaw("id, name, user_id, csv_email")
        ->where(function($q) {
            $q->whereRaw("DATEDIFF(NOW(), csv_last_sent_time) >= csv_interval")
            ->orWhereNull('csv_last_sent_time');
        })
        ->cursor()
        ->each(function($campaign) {
            $this->sendCsvEmail($campaign);
        });

        } catch(\Exception $e) {}
    }

    public function sendCsvEmail($campaign){
        
        try{

            $user_id = $campaign->user_id; // User id
            $tags = [
                'campaign' => $campaign->name,
            ];

            $customers = $this->findCustomersByCampaignId($campaign->id);

            //Has purchase APP
            if(!DrmUserHasPurchasedApp($user_id, 92))
            {
                $customers = $customers->slice(0, 25);
            }

            $file = new \SplTempFileObject();
            $file->setFlags(\SplFileObject::READ_CSV);
            $file->setCsvControl(';');
            $csv = Writer::createFromFileObject($file);

            foreach($customers as $customer)
            {
                $csv->insertOne([$customer->email]);
            }

            $content = $csv->getContent();

            $template = DRMParseDtProductStockTemplate($tags, $user_id, 'df_csv_email_template');
            $data['email_to'] = $campaign->csv_email;
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];
            $data['subject'] = $template['subject'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new \Exception("Something Wrong! Mail Not Sent!.");
            }
            
            app('drm.mailer')->getMailer($user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $content) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }

                $messages->attachData($content, 'campaign_email.csv', [
                    'mime' => 'text/csv',
                ]);
            });

            DB::table('email_marketings')->where('id', '=', $campaign->id)->update(['csv_last_sent_time' => now()]);

        }catch(\Exception $e){}
    }
}
