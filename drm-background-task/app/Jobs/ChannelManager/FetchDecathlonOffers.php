<?php

namespace App\Jobs\ChannelManager;

use App\Services\Modules\Export\Decathlon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FetchDecathlonOffers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $user_id;
    protected int $shop_id;
    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(int $shop_id, int $user_id)
    {
        $this->shop_id = $shop_id;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle()
    {
//        $service = new Decathlon($this->shop_id,$this->user_id);
//        $service->transferShopOffers();
    }

    public function tags()
    {

    }
}
