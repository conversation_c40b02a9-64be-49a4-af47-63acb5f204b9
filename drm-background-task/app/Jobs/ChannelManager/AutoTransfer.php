<?php

namespace App\Jobs\ChannelManager;

use App\Enums\Channel;
use App\Shop;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AutoTransfer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected string $lang;
    protected int $user_id;
    protected array $shops;

    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $user_id, $lang = 'de', $shops = array())
    {
        $this->ids = $ids;
        $this->lang = $lang;
        $this->user_id = $user_id;
        $this->shops = $shops;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        if(empty($this->shops)){
            $channels = Shop::where([
                'user_id' =>  $this->user_id,
                'auto_transfer' => true
            ])->select('id','channel')->get();
        }else{
            $channels = Shop::whereIn('id',$this->shops)->select('id','channel')->get();
        }

        $fields = [
            'title',
            'item_number',
            'ean',
            'description',
            'short_description',
            'images',
            'ek_price',
            'stock',
            'category',
            'status',
            'gender',
            'user_id',
            'delivery_company_id',
            'country_id',
            'item_weight',
            'item_unit',
            'item_color',
            'note',
            'production_year',
            'materials',
            'brand',
            'tags',
            'item_size',
            'delivery_days',
            'uvp',
            'industry_template_data',
            'shipping_cost',
            'tax_type',
            // 'rq_limit',
            'offer_options'
        ];

        foreach ($channels as $channel) {
            if($channel == Channel::DROPTIENDA)
            {
                TransferProductBeta::dispatch(
                    $this->ids,
                    $channel->channel,
                    $fields,
                    $this->lang,
                    true,
                    [
                        'stock_percentage' => 0,
                        'shop_id' => $channel->id
                    ]
                );
            }else{
                TransferProduct::dispatch(
                    $this->ids,
                    $channel->channel,
                    $fields,
                    $this->lang,
                    "create",
                    true,
                    0,
                    [
                        'shop_id' => $channel->id
                    ]
                );
            }
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {

    }
}
