<?php

namespace App\Jobs\ChannelManager;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TransferProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected array $fields;
    protected string $lang;
    protected string $event;
    protected bool $force_reset;
    protected int $stock_percentage;
    protected int $channel_id;
    protected array $data;
    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $channel, $fields, $lang, $event, $force_reset, $stock_percentage, $data)
    {
        $this->ids = $ids;
        $this->channel_id = $channel;
        $this->fields = $fields;
        $this->lang = $lang;
        $this->event = $event;
        $this->force_reset = $force_reset;
        $this->stock_percentage = $stock_percentage;
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        foreach ($this->ids as $productId) {
            app('\App\Services\ChannelProductService')->transferProduct(
                $productId,
                $this->channel_id,
                $this->fields,
                $this->lang,
                $this->event,
                $this->force_reset,
                $this->stock_percentage,
                $this->data
            );
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {

    }
}
