<?php

namespace App\Jobs\ChannelManager;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Services\ChannelProductService;

class ProductsCalculationUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ids;
    public $timeout = 0;
    public $tries = 3;
    public $calculation;

    /**
     * Create a new job instance.
     *
     * @param $calculation
     */
    public function __construct($calculation)
    {
        $this->calculation = $calculation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(ChannelProductService::class)->updateProductCalculation($this->calculation);

        } catch(\Exception $e) {}
    }

    public function tags()
    {

    }
}
