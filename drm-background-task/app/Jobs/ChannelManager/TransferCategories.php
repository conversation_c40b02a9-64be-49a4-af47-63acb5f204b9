<?php

namespace App\Jobs\ChannelManager;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TransferCategories implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected array $fields;
    protected string $lang;
    protected string $event;
    protected int $shop_id;
    protected int $user_id;
    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($product_ids, $user_id, $shop_id, $lang = 'de')
    {
        $this->ids = $product_ids;
        $this->user_id = $user_id;
        $this->shop_id = $shop_id;
        $this->lang = $lang;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app('\App\Services\ChannelProductService')->transferProductCategories(
            $this->ids,
            $this->user_id,
            $this->shop_id,
            $this->lang
        );

        } catch(\Exception $e) {}
    }

    public function tags()
    {

    }
}
