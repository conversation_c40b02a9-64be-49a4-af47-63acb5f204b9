<?php

namespace App\Jobs\ChannelManager;

use App\Enums\CreditType;
use App\MarketplaceProducts;
use App\Models\ChannelProduct;
use App\Models\Marketplace\UserAccess;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class CalculateMpTopSaleProductsTurnover implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $user_id;

    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id)
    {
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $user_id = $this->user_id;
            
            $user_access_categories = UserAccess::where('user_id', $user_id)->value('accessable_categories');
            $user_access_categories = !empty($user_access_categories) ? $user_access_categories : [];

            // get products
            $all_product_ids = MarketplaceProducts::where('status', 1)
                ->whereIn('category_id', $user_access_categories)
                ->whereNotExists(function ($query) use ($user_id) {
                    $query->select(DB::raw(1))->from('mp_core_drm_transfer_products')
                        ->where('user_id', $user_id)
                        ->whereRaw('marketplace_products.id = mp_core_drm_transfer_products.marketplace_product_id');
                })
                ->where(function($query) {
                    $query->where([
                        ['shipping_method', 1],
                        ['stock', '>', 0]
                    ])->orWhere([
                        ['shipping_method', 2],
                        ['internel_stock', '>', 0]
                    ]);
                })
                ->orderBy('id', 'asc')
                ->pluck('id')
                ->toArray();
                
            if (!empty($all_product_ids)) {
                // calculate top sale products
                $top_36_sales = $this->calculateTop36SaleProducts($all_product_ids);

                if (!empty($top_36_sales)) {
                    // store mp top sale products turnover
                    $this->store_mp_top_sale_products($user_id, $top_36_sales);
                }
            }
        } catch(\Exception $e) {
            Log::channel('command')->error("CalculateMpTopSaleProductsTurnover Job Failed");
            Log::channel('command')->error($e->getMessage());
        } finally {

        }
    }

    public function calculateTop36SaleProducts($all_product_ids) {
        $top_36_sales = [];

        foreach (array_chunk($all_product_ids, 900) as $chunk_product_ids) {
            $chunk_sale_products = DB::connection('marketplace')->table('marketplace_product_sales_information')
                ->where('status', 1)
                ->where('created_at', '>=', Carbon::now()->subDays(30))
                ->whereIn('marketplace_product_id', $chunk_product_ids)
                ->select('marketplace_product_id', DB::raw('sum(sales_amount) as sales_turnover'))
                ->groupBy('marketplace_product_id')
                ->orderBy('sales_turnover', 'desc')
                ->take(36)
                ->pluck('sales_turnover', 'marketplace_product_id')
                ->filter()
                ->toArray();

            if (!empty($chunk_sale_products)) {
                $top_36_sales = $top_36_sales + $chunk_sale_products;
            }
        }

        $top_36_sales = array_unique($top_36_sales);

        arsort($top_36_sales);
        $top_36_sales = array_slice($top_36_sales, 0, 36, true);

        return $top_36_sales;
    }

    public function store_mp_top_sale_products($user_id, $top_36_sales) {
        DB::table('mp_top_sale_products')
            ->where('user_id', $user_id)
            ->whereNotIn('product_id', array_keys($top_36_sales))
            ->delete();

        if (!empty($top_36_sales)) {
            foreach ($top_36_sales as $product_id => $turnover) {
                DB::table('mp_top_sale_products')
                    ->updateOrInsert([
                        'user_id' => $user_id,
                        'product_id' => $product_id,
                    ], 
                    [
                        'turnover' => $turnover,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
            }
        }
    }
}
