<?php

namespace App\Jobs\ChannelManager;

use App\Enums\CreditType;
use App\Models\ChannelProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class UpdateCheapestPriceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $user_id;
    protected int $interval_duration;
    protected array $items;

    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id, $interval_duration, $items)
    {
        $this->user_id           = $user_id;
        $this->interval_duration = $interval_duration;
        $this->items             = $items;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if(isset(\App\Enums\V2UserAccess::USERS[$this->user_id])) return;

        try {
            foreach ($this->items as $index => $channel_product) {
                $cheapest_price = $re_vk_price = ChannelProduct::join('analysis_products', function ($join) {
                    $join->on('channel_products.drm_product_id', '=', 'analysis_products.product_id')
                        ->orOn('channel_products.marketplace_product_id', '=', 'analysis_products.product_id');
                    })
                    ->where([
                        'channel_products.id' => $channel_product['id'],
                    ])
                    ->whereNull('analysis_products.deleted_at')
                    ->select(
                        DB::raw("(CASE WHEN channel=4 THEN analysis_products.ebay_price
                                        WHEN channel=5 THEN analysis_products.amazon_price
                                        ELSE analysis_products.google_price
                                END) as analysis_price")
                    )
                    ->value('analysis_price');

                $min_price = $channel_product['min_price'];
                $max_price = $channel_product['max_price'];
                if (empty($cheapest_price) || ($cheapest_price == 0.00)) {
                    $cheapest_price = $re_vk_price = $channel_product['max_price'];
                } else {
                    if ($cheapest_price < $min_price) {
                        $cheapest_price = $re_vk_price = $min_price;
                    } else if ($cheapest_price > $max_price) {
                        $cheapest_price = $re_vk_price = $max_price;
                    } else {
                        $repricing_price_diff = $channel_product['repricing_price_diff'];
                        if ($repricing_price_diff > 0) {
                            if ($channel_product['repricing_price_diff_type'] == 0) { // amount
                                $re_vk_price = $re_vk_price - $repricing_price_diff;
                            } else { // percentage
                                $re_vk_price = $re_vk_price - ($re_vk_price * ($repricing_price_diff / 100));
                            }
                        }
                    }
                }

                $cheapest_price = ($cheapest_price < $min_price) ? $min_price : $cheapest_price;
                $re_vk_price    = ($re_vk_price < $min_price) ? $min_price : $re_vk_price;
                $current_time   = \Carbon\Carbon::now();

                $product                      = ChannelProduct::find($channel_product['id']);
                $product->cheapest_price      = $cheapest_price;
                $product->job_token           = null; // token exists, release now
                $product->repricing_last_sync = $current_time;

                $channel_price_history = [
                    'channel_products_id' => $product->id,
                    'vk_price_before'     => $product->vk_price,
                    'vk_price_after'      => $re_vk_price,
                ];

                if ($channel_price_history['vk_price_before'] != $channel_price_history['vk_price_after']) {
                    $product->vk_price            = $re_vk_price;
                    $product->vk_price_updated_at = $current_time;
                    \App\ChannelPriceHistory::create($channel_price_history);
                }

                $product->save();
            }

            (new \App\Services\Tariff\Credit\ChargeCredit)->charge($this->user_id, count($this->items), \App\Services\Tariff\Credit\CreditType::CHEAPEST_PRICE_CALCULATION);
        } catch(\Exception $e) {
            Log::channel('command')->error("UpdateCheapestPriceJob");
            Log::channel('command')->error($e->getMessage());
        } finally {

        }
    }
}
