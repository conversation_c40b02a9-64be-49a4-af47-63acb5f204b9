<?php

namespace App\Jobs\ChannelManager;

use App\Country;
use App\Models\ChannelProduct;
use App\Shop;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PrepareSyncConnectionStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 0;
    public int $tries = 3;
    private int $user_id;
    private int $channel;
    private array $product_ids;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($product_ids,$channel,$user_id)
    {
        $this->channel = $channel;
        $this->user_id = $user_id;
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $shop = Shop::where([
            'channel' => $this->channel,
            'user_id' => $this->user_id
        ])->select(['lang','country_id'])->first();

        $products = ChannelProduct::where([
            'channel' => $this->channel,
            'user_id' => $this->user_id
        ])->whereIn('id',$this->product_ids)->get();

        foreach ($this->product_ids as $product_id) {
            $product = $products->where('id',$product_id)->first();

            ChangeChannelProductConnectionStatus::dispatch(
                $product_id,
                [],
                [
                    'lang' => $shop->lang,
                    'country' => $shop->country_id,
                ],
                true
            );
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {

    }
}
