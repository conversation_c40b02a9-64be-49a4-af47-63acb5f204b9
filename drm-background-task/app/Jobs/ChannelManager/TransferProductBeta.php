<?php

namespace App\Jobs\ChannelManager;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TransferProductBeta implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected array $fields;
    protected string $lang;
    protected bool $force_reset;
    protected int $channel_id;
    public int $timeout = 0;
    public int $tries = 3;
    protected array $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $channel, $fields, $lang, $force_reset, $data)
    {
        $this->ids = $ids;
        $this->channel_id = $channel;
        $this->fields = $fields;
        $this->lang = $lang;
        $this->force_reset = $force_reset;
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app('\App\Services\ChannelProductService')->transferProductBeta(
            $this->ids,
            $this->channel_id,
            $this->data,
            $this->fields,
            $this->lang,
        );

        } catch(\Exception $e) {}
    }

    public function tags()
    {

    }
}
