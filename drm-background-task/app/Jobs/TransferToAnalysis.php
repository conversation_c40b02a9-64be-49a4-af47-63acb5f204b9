<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TransferToAnalysis implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected array $product_ids;
    protected int $user_id;
    protected array $eans;
    protected array $meta;

    public function __construct(array $product_ids, array $eans, int $user_id, array$meta = array())
    {
        $this->product_ids = $product_ids;
        $this->eans = $eans;
        $this->user_id = $user_id;
        $this->meta = $meta;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app('App\Services\CPService')->transferToEasyPricing(
            $this->user_id,
            $this->product_ids,
            $this->eans
        );

        if(isset($meta['calculation']))
        {
            app('App\Services\ChannelProductService')->updateEasyPrice($meta['calculation'],$meta['channel_products']);
        }

        } catch(\Exception $e) {}
    }

}
