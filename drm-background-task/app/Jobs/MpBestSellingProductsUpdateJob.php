<?php

namespace App\Jobs;

use App\Models\Marketplace\MpBestSellingProducts;
use App\Models\Marketplace\Product;
use App\NewOrder;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MpBestSellingProductsUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $getAllSoldProducts = $this->getMpSoldProducts();
        $takeHundredBestProducts = $this->mpSoldProductsFormat($getAllSoldProducts, 100);

        $this->bestSellingProductsUpdate($takeHundredBestProducts);

        } catch(\Exception $e) {}
    }

    public function getMpSoldProducts()
    {
        $mp_order_data = [];
        $all_ordered_products = [];
        $ordered_mp_product_id = [];

        NewOrder::where('new_orders.insert_type', 8)
        ->where('new_orders.cms_user_id', 2455)
        ->where('new_orders.test_order', '<>', 1)
        ->where('new_orders.invoice_number', '>', 0)
        ->whereDate('order_date', '>=', Carbon::now()->subYear()) // Last 1 year mp sold product get
        ->select('new_orders.id', 'new_orders.cart')
        ->chunk(1000, function($mp_orders) use(&$ordered_mp_product_id, &$all_ordered_products){
            foreach ($mp_orders as $order) {
            if (!is_null($order->products)) {
                foreach ($order->products as $product) {
                    unset($product->id);
                    unset($product->description);
                    unset($product->rate);
                    unset($product->tax);
                    unset($product->product_discount);
                    unset($product->item_number);
                    unset($product->delivery_days);
                    unset($product->ean);
                    unset($product->shipping_cost);
                    unset($product->delivery_days);

                    $product->order_id = $order->id;
                    $all_ordered_products[] = (array) $product;

                    if(!in_array($product->marketplace_product_id, $ordered_mp_product_id)){
                        $ordered_mp_product_id[] = $product->marketplace_product_id;
                    }
                }
            }
            }
        });

        $mp_order_data['sold_product_mp'] = $all_ordered_products;
        $mp_order_data['sold_product_mp_ids'] = $ordered_mp_product_id;

        return $mp_order_data;
    }

    public function mpSoldProductsFormat($allsoldOrders, $limit)
    {
        $array_product = $allsoldOrders['sold_product_mp'];
        $ordered_mp_product_id = $allsoldOrders['sold_product_mp_ids'];

        $mp_existing_products = $this->checkOrderedMpProductStoreExists($ordered_mp_product_id); //Filtering if mp sold product still at mp and return mp_product model collection

        $mp_existing_product_ids = array_column($mp_existing_products->toArray(), 'id');

    //   $drm_transferred_ids = $this->checkOrderedMpProductDrmExists($ordered_mp_product_id, $user_id); //Check for if product already transferred to main level
    //   $mp_existing_product_ids = array_diff($mp_existing_product_ids, $drm_transferred_ids);

        $products_collection = collect($array_product);
        $products_collection = $products_collection->whereIn('marketplace_product_id', $mp_existing_product_ids)->groupBy('marketplace_product_id');

        $top_product_collection = [];

        foreach ($products_collection as $product) {
            $count = count($product);
            $qty = $product->sum('qty');

            $item = reset($product);

            if (isset($item[0])) {
                
                // $mp_pro_id = $item[0]['marketplace_product_id'];
                // $mp_pro_vk_price = $mp_existing_products->where('id', $mp_pro_id)->pluck('vk_price')->first();

                $item[0]['sell_count'] = $count; // Total how many times orders the products
                $item[0]['sell_qty'] = $qty; // Total how much unit sold of the products
                // $item[0]['vk_price'] = $mp_pro_vk_price;

                unset($item[0]['product_name']);
                unset($item[0]['product_id']);
                unset($item[0]['supplier_id']);
                unset($item[0]['mp_supplier_id']);
                unset($item[0]['order_id']);
                unset($item[0]['shipping_method']);
                unset($item[0]['qty']);
                unset($item[0]['amount']);
                unset($item[0]['image']);

                $top_product_collection[] = $item[0];
            }
        }

        $best_mp_selling_products = collect($top_product_collection ?? [])
        ->sortByDesc('sell_qty')
        ->take($limit)
        ->values();

        return $best_mp_selling_products;
    }

    public function checkOrderedMpProductStoreExists($ordered_product_id)
    {
        $existing_products = collect();
        
        Product::select('id', 'category_id', 'vk_price')
        ->chunk(1000, function($products) use($ordered_product_id, &$existing_products){

            foreach ($products as $product){
                if(in_array($product->id, $ordered_product_id)) $existing_products->push($product);
            }

        });

        return $existing_products;
    }

    // public function checkOrderedMpProductDrmExists($ordered_product_id, $user_id)
    // {
    //     $expire_time = 60*60*24*7; //Cache for 7 dyas

    //     return Cache::remember('mp_ordered_drm_products_exists', $expire_time, function () use($ordered_product_id, $user_id){
    //         $existing_drm_products = [];

    //         DrmProduct::where('user_id', $user_id) //Check for if product already transferred to main level
    //         // ->whereIn('marketplace_product_id', $mp_existing_product_ids)
    //         // ->pluck('marketplace_product_id')
    //         // ->toArray();
    //         ->chunk(1000, function($products) use($ordered_product_id, &$existing_drm_products){

    //         foreach ($products as $product){
    //             if(in_array($product->marketplace_product_id, $ordered_product_id)){
    //                 $existing_drm_products[] = $product->marketplace_product_id;  
    //             }
    //         }

    //         });

    //         return $existing_drm_products;
    //     });
    // }

    public function bestSellingProductsUpdate($bestSoldProducts)
    {
        if($bestSoldProducts->isNotEmpty()){
            
            foreach($bestSoldProducts as $key => $item){
                $mp_id = $item['marketplace_product_id'];
                $existing_position = MpBestSellingProducts::where('mp_product_id', $mp_id)->select('position', 'old_position')->first();

                $db_new_position = $existing_position->position;
                $db_old_position = $existing_position->old_position;
                $current_new_position = ($key + 1);

                MpBestSellingProducts::updateOrCreate(
                    [
                        'mp_product_id' => $mp_id
                    ],
                    [
                        'sale_quantity' => $item['sell_qty'],
                        'position' => $current_new_position,
                        'old_position' => ($db_new_position != $current_new_position) ? $db_new_position : $db_old_position,
                    ]
                );
            }

            $bestSoldIds = $bestSoldProducts->pluck('marketplace_product_id')->toArray();
            $dbSoldProductIds = MpBestSellingProducts::pluck('mp_product_id')->toArray();

            $mpNotExistsProducts = array_diff($dbSoldProductIds, $bestSoldIds);

            if(!empty($mpNotExistsProducts)){
                MpBestSellingProducts::whereIn('mp_product_id', $mpNotExistsProducts)->delete();    //Mp not exists products delete from "mp_best_selling_products" table
            }
        }
    }
}
