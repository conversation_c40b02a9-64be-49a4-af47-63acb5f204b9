<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class MpCoreTableEanInsert<PERSON><PERSON> implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    private $core_product = [];

    public function __construct($records)
    {
        $this->core_product = $records;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $products = $this->core_product->groupBy('marketplace_product_id');
        
        foreach($products as $mp_product_id => $mp_products){
            $mp_product_ean = DB::connection('marketplace')->table('marketplace_products')->where('id', $mp_product_id)->value('ean');
            $updateable_product_id = $mp_products->pluck('id')->toArray();

            DB::connection('marketplace')->table('mp_core_drm_transfer_products')->whereIn('id', $updateable_product_id)->update([
                'mp_product_ean' => $mp_product_ean
            ]);
        }

        } catch(\Exception $e) {}
    }
}
