<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class OrderSendToTelegram implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;
    public $order_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($order_id)
    {
        $this->order_id = $order_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        ini_set("memory_limit",-1);
        app('App\Http\Controllers\NewShopSyncController')->orderSendToTelegramAppQueue($this->order_id);

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Telegram new order Notification. #ORD: '.$this->order_id];
    }
}
