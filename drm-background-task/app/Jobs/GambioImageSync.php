<?php

namespace App\Jobs;

use App\Helper\GambioApi;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use App\Services\Modules\Export\Gambio\GambioApiService;

class GambioImageSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ids;
    protected $shop_details;
    public $timeout = 0;
    public $tries = 4;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $shop_details)
    {
        $this->ids = $ids;
        $this->shop_details = $shop_details;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        if(isLocal() || \in_array($this->shop_details->user_id,[62])){
            GambioApiService::GambioImageUploadArray($this->shop_details, $this->ids);
        }
        else {
            GambioApi::GambioImageUploadArray($this->shop_details, $this->ids);
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        $user = DB::table('cms_users')->where('id', $this->shop_details->user_id)->first();

        return ['Image Sync: ' . $this->shop_details->shop_name . ' Shop - ' . $user->name];
    }
}
