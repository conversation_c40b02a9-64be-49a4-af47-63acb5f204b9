<?php

namespace App\Jobs;

use App\AnalysisCategory;
use App\Models\Product\AnalysisProduct;
use App\Models\Product\CPAnalysisRequest;
use App\Models\Product\CPAnalysisUserRequests;
use App\Services\ProductApi\Services\Countdown;
use App\Services\ProductApi\Services\GoogleShopping;
use App\Services\ProductApi\Services\Rainforest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ProcessCPTransfer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $user_id;
    protected $analysisService;

    public function __construct($user_id)
    {
        $this->user_id = $user_id;
        $this->analysisService = [
            Countdown::class,
            Rainforest::class,
            GoogleShopping::class,
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $category_ids = AnalysisCategory::where('user_id', $this->user_id)->get();

            $analysisService = $this->analysisService;
            foreach ($analysisService as $service) {
                $analysisApi = new $service;

                $column = $analysisApi->driver();
                $column_name = $column . "_collection_ids";
                $user_requests = CPAnalysisUserRequests::select($column_name)->where('user_id', $this->user_id)->first();
                $user_request_collections = unserialize($user_requests->$column_name);

                if (!empty($user_request_collections)) {
                    foreach ($user_request_collections as $collection) {
                        $analysisApi->deleteCollection($collection);
                    }
                }
            }

            foreach ($category_ids as $category) {
                $category_products = AnalysisProduct::select('id', 'ean')
                    ->where('user_id', $this->user_id)
                    ->where('archived', '!=', 1)
                    ->where('category_id', $category->id)
                    ->get();


                if ($category_products->isNotEmpty()) {
                    $chunkSize = sizeof($category_products);

                    foreach ($analysisService as $service) {
                        $analysisApi = new $service;
                        $collectionIds = collect();

                        if ($chunkSize >= 15000) {
                            $category_products->chunk(15000)->each(function ($products) use ($category, $analysisApi, &$collectionIds) {

                                $_getCollectionId = $this->getIntervalCollectionId($category, $analysisApi);
                                $collectionIds->push($_getCollectionId);
                                $this->pushProductsForSearch($category, $analysisApi, $products, $_getCollectionId);
                            });
                        } else {
                            $_getCollectionId = $this->getIntervalCollectionId($category, $analysisApi);
                            $collectionIds->push($_getCollectionId);
                            $this->pushProductsForSearch($category, $analysisApi, $category_products, $_getCollectionId);
                        }

                        CPAnalysisUserRequests::updateOrCreate(
                            [
                                'user_id' => $this->user_id
                            ],
                            [
                                $analysisApi->driver() . '_collection_ids' => serialize($collectionIds->toArray()),
                                'cat_id' => $category->id
                            ]
                        );

                    }
                }
            }

        } catch (\Exception $e) {
        }

    }

    private function pushProductsForSearch($category, $api, $products, $collection_id)
    {
        $cp_request_model_id = CPAnalysisRequest::create([
            'user_id' => $this->user_id,
            'default' => 0,
            'easy_pricing' => $category->easy_pricing,
            'total_products' => $products->count(),
            $api->collectionColumnName() => $collection_id,
            $api->driver() . '_last_sync' => now()
        ])->id;

        $chunckProducts = $products->chunk(1000);
        foreach ($chunckProducts as $apiProducts) {
            AnalysisProduct::whereIn('id', $apiProducts->pluck('id'))
                ->update([$api->driver() . '_id' => $cp_request_model_id]);

            $apiProductChunkArray = $apiProducts->map(function ($product) {
                $product->item_number = trim($product->ean) . "-" . $this->user_id;
                return [
                    'id' => $product->id,
                    'user_id' => $this->user_id,
                    'ean' => $product->ean,
                    'item_number' => $product->item_number
                ];
            })->toArray();

            $api->addProducts($apiProductChunkArray, $collection_id);
        }
    }


    private function getIntervalCollectionId($category, $analysisApi)
    {
        $intervalMapping = [
            'oneTime' => [
                'schedule_type' => 'manual',
            ],
            'hours' => [
                'schedule_type' => 'daily',
                'schedule_hours' => $category->duration,
            ],
            'minutes' => [
                'schedule_type' => 'minutes',
                'schedule_hours' => $category->duration,
            ],
        ];

        $collection_interval_data = $intervalMapping[$category->type] ?? [];

        $collection_body = [
            'name' => $category->name,
            'enabled' => true,
            'priority' => 'normal',
            'notification_as_csv' => true,
            'notification_as_json' => true,
            'include_html' => false,
        ];
        $collectionBody = array_merge($collection_body, $collection_interval_data);
        return $analysisApi->createCollection($collectionBody);
    }


}
