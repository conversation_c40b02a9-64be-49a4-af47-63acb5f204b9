<?php

namespace App\Jobs;

use App\AnalysisCategory;
use App\Models\Product\AnalysisProduct;
use App\Models\Product\CPAnalysisRequest;
use App\Services\ProductApi\Services\Countdown;
use App\Services\ProductApi\Services\GoogleShopping;
use App\Services\ProductApi\Services\Rainforest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessEasyTransfer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $user_id;
    protected $category_id;
    protected $eanArray;
    protected $analysisService;

    public function __construct($user_id, $category_id, $eanArray)
    {
        $this->user_id = $user_id;
        $this->category_id = $category_id;
        $this->eanArray = $eanArray;
        $this->analysisService = [
            Countdown::class,
            Rainforest::class,
            GoogleShopping::class,
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $category_id = AnalysisCategory::where('id', $this->category_id)->first();

        $analysisService = $this->analysisService;
        foreach($analysisService as $service){
            $analysisApi = new $service;
            $collection_column_name = $analysisApi->collectionColumnName();
            if(!empty($this->eanArray)){
                $products = [];
                foreach($this->eanArray as $ean){
                    $item_number = trim($ean)."-".$this->user_id;
                    $products[] = ['ean' => $ean, 'item_number' => $item_number];
                }
                if(!empty($products)){
                    $fIndex = 0;
                    $limit = 1000;
                    if(sizeof($products)>1000){
                        for($i = 0; $i <= (sizeof($products)/1000); $i++){
                            $products = array_slice($products, $fIndex, $limit);
                            $fIndex += 1000;
                            $analysisApi->addProducts($products, $category_id->$collection_column_name);
                            unset($products);
                        }
                    }
                    else{
                        $analysisApi->addProducts($products, $category_id->$collection_column_name);
                    }
                }
            }
        }

        } catch(\Exception $e) {}
    }
}
