<?php

namespace App\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\tariffController;

class LongTermPlanCreditResetJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $newPlanId = [25, 26, 27]; // Credit related plan only

        $planGreaterThanOneMonth = DB::table('purchase_import_plans')
        ->join('import_plans', 'purchase_import_plans.import_plan_id', '=', 'import_plans.id')
        ->whereIn('import_plan_id', $newPlanId)
        ->whereRaw("DATEDIFF(end_date, start_date)  >= 60")
        ->where('end_date', '>=', Carbon::now())
        ->select('cms_user_id', 'start_date', 'end_date', 'import_plans.credit')
        ->get();
        
        if($planGreaterThanOneMonth->isNotEmpty()){
            foreach($planGreaterThanOneMonth as $user){
                $planDaysTillNow = date_diff(Carbon::parse($user->start_date), Carbon::now())->format('%a');

                // Credit reset end of the plan
                // Plan duration day, if divisible by 30 (exam: 60, 90, 120 ...) then reset credit. Meaning end of the month credit reset
                if(!empty($planDaysTillNow) && $planDaysTillNow % 30 == 0){
                    $userId = $user->cms_user_id;

                    // app('App\Http\Controllers\tariffController')->creditReset($userId, $user->start_date);

                    //credit re assign
                    $credit   = $user->credit;
					$message = 'Import Plan Credit Assigned by Admin from Job';
					$type = \App\Enums\CreditType::PLAN_PURCHASE;
					$status = \App\Enums\CreditType::CREDIT_ADD;
					
					// app('App\Http\Controllers\tariffController')->drmUserCreditAdd($userId, $credit, $message, $type, $status);
					// app('App\Http\Controllers\tariffController')->CreditUpdate($userId, $credit, 'credit_add');

                    (new \App\Services\Tariff\Credit\RefillCredit)->resetTariffCredit($userId, (float) $credit, \App\Services\Tariff\Credit\CreditType::IMPORT_PLAN_CREDIT_BY_JOB);
                    //credit re assign END
                }

            }
        }

        } catch(\Exception $e) {}
    }
}
