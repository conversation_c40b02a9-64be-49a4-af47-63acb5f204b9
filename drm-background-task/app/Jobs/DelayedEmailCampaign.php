<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DelayedEmailCampaign implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $connectionName = 'dropfunnel';
    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;
    public $id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            
        app('App\Http\Controllers\NewShopSyncController')->campaignMailJob($this->id);

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Campaign ID: '.$this->id];
    }
}
