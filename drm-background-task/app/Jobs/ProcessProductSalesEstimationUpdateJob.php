<?php

namespace App\Jobs;

use Carbon\Carbon;
use App\ProductPriceApi;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\SerializesModels;
use App\Models\Product\AnalysisProduct;
use App\Services\ProductApi\ProductApi;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessProductSalesEstimationUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $product_list;

    public function __construct($product_list)
    {
        $this->product_list = $product_list;
    }

    public function handle()
    {
        try {
        $domain = "amazon.de";

        $productApi = new ProductApi($domain);

        foreach($this->product_list as $product){

            $asin = $product->amazon_product_number ?? $product->asin;

            if($asin == null && $product->amazon_last_sync == null){
                $asin = $this->updateProduct($product->ean, 'amazon.de');
            }

            if($product->amazon_last_sync == null || $product->ebay_product_sold != null){
                $this->updateProduct($product->ean, 'ebay.de');
            }

            $estimatedData = $productApi->salesEstimate($asin, "sales_estimation")->fetchSalesEstimationFromASIN();

            if($estimatedData->estimatedData()){
                $estimatedData = $estimatedData->estimatedData();
                if($estimatedData->has_sales_estimation == true){
                    $weekly_estimate = $estimatedData->weekly_sales_estimate;
                    $monthly_estimate = $estimatedData->monthly_sales_estimate;
                    AnalysisProduct::where('ean', $product->ean)->update([
                        'amazon_weekly_sales_estimate' => $weekly_estimate,
                        'amazon_monthly_sales_estimate' => $monthly_estimate
                    ]);
                }
            }
        }

        } catch(\Exception $e) {}
    }

    public function updateProduct($ean, $domain){

        $domain_column = (strpos($domain, 'ebay') !== false) ? 'ebay' : 'amazon';
        $price_column = $domain_column.'_price';
        $rating_column = $domain_column.'_rating';
        $rating_count_column = $domain_column.'_rating_count';
        $product_number_column = $domain_column.'_product_number';
        $last_sync_column = $domain_column.'_last_sync';
        $also_bought_column = $domain_column.'_also_bought';

        $productApi = new ProductApi($domain);
        $product = $productApi->search('ean', $ean)->fetch();

        $price = 0;
        $rating = 0;
        $isPrime = false;

        if($product->price() || $product->offerPrice() || $product->rating() || $product->productNumber() || $product->ratingCount() || $product->title() || $product->sellerName() || $product->sellerLink() || $product->isPrime() || $product->getOtherSeller()){

            if($product->price()){
                $price = $product->price();
            }

            if($product->title()){
                $title = $product->title();
            }

            // elseif($product->offerPrice()){
            //     $price = $product->price();
            // }

            if($product->rating()){
                $rating = $product->rating();
            }

            if($product->productNumber()){
                $productNumber = $product->productNumber();
            }

            if($product->ratingCount()){
                $ratingCount = $product->ratingCount();
            }

            if($product->image()){
                $image = $product->image();
            }

            if($product->sellerName()){
                $sellerName = $product->sellerName();
            }

            if($product->sellerLink()){
                $sellerLink = $product->sellerLink();
            }

            if($product->isPrime()){
                $isPrime = $product->isPrime();
            }

            if($product->getOtherSeller()){
                $otherSeller = $product->getOtherSeller();
            }
            // $price = (float)$price / 100;

            // if($domain_column === 'amazon')
            // {
            //     $price = $product->offerPrice();
            // }

            if($product->getPeopleAlsoBought()){
                $alsoBought = $product->getPeopleAlsoBought();
            }

            if($product->getProductSold()){
                $productSold = $product->getProductSold();
            }

            $analysis_product = AnalysisProduct::where('ean', $ean)->first();

            if(strlen($productNumber) > 0){
                DB::table('amazon_asin_collections')->updateOrInsert(
                    [
                        'ean' => $ean,
                        'domain' => 3
                    ],
                    [
                        'asin' => $productNumber
                    ]
                );
            }
            if($domain == 'amazon.de'){
                AnalysisProduct::where('ean', $ean)->update([
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    'amazon_seller_name' => $sellerName,
                    'amazon_seller_link' => $sellerLink,
                    'is_prime' => $isPrime,
                    'amazon_other_sellers' => $otherSeller,
                    $also_bought_column => $alsoBought,
                    $last_sync_column => now(),
                    'manual_update' => now(),
                ]);
            }else{
                AnalysisProduct::where('ean', $ean)->update([
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    'ebay_product_sold' => $productSold,
                    $last_sync_column => now(),
                    'manual_update' => now(),
                ]);
            }

            if($analysis_product->image == null){
                AnalysisProduct::where('ean', $ean)->update([
                    'image' => json_encode([$image])
                ]);
            }
            if($analysis_product->title == null){
                AnalysisProduct::where('ean', $ean)->update([
                    'title' => $title
                ]);
            }
            $addPrices[] = [
                'ean' => $ean,
                'source' => $domain,
                'title' => $title,
                'price' => $price,
                'rating' => $rating,
                'rating_count' => $ratingCount,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
            ProductPriceApi::insert($addPrices);
        }
        else{
            AnalysisProduct::where('ean', $ean)->update([
                $last_sync_column => now(),
                'manual_update' => now(),
            ]);
        }

        return $productNumber;
    }
}
