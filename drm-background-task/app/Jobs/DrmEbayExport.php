<?php

namespace App\Jobs;

use App\Helper\EbayApi;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use DB;
use App\Events\ExportProgressEvent;
use Illuminate\Support\Facades\Session;

class DrmEbayExport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $ids;
    protected $user_id;
    protected $shop_details;
    protected $lang;
    protected $shipping_title;
    protected $total_products;
    protected $export_process_id;
    public $timeout = 0;
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $shop_details, $lang, $user_id, $shipping_title = NULL, $export_process_id = NULL, $total_product = NULL)
    {
        $this->ids = $ids;
        $this->shop_details = $shop_details;
        $this->lang = $lang;
        $this->user_id = $user_id;
        $this->shipping_title = $shipping_title;
        $this->total_products = $total_product;
        $this->export_process_id = $export_process_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $ebay_shop = new EbayApi($this->ids, $this->shop_details, $this->lang, $this->user_id, $this->shipping_title);
        $ebay_shop->drmProduct();

        $update_table = DB::table('export_process')->where('id',$this->export_process_id)->update(['progress' => 1,'updated_at' => now()]);
        $total = $this->total_products;
        $count = DB::table('export_process')->where(['user_id' => $this->user_id, 'job_name' => 'EbayExport','progress'=>1])->sum('count');

        $end = 0;
        if((int)$count>=$total){
            $end = 1;
            DB::table('export_process')->where(['user_id' => $this->user_id,'job_name' => 'EbayExport'])->delete();
        }

        if($total != 0){
            $message = ['name'=>$this->shop_details->shop_name,'count' => $count, 'total' => $total, 'user_id' => $this->user_id, 'end' => $end ,'percent' => round(($count/$total)*100,2)];
            event(new ExportProgressEvent($message));
        }

        } catch(\Exception $e) {}

    }

    public function tags()
    {
        $user = DB::table('cms_users')->where('id', $this->shop_details->user_id)->first();

        return [$this->shop_details->shop_name . ' Shop - ' . $user->name];
    }
}
