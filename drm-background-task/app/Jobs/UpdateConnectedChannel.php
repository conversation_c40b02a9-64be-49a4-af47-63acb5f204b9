<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;

use App\Http\Controllers\AdminDrmExportsController;
use App\Services\Modules\Export\ExportServices;

class UpdateConnectedChannel implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ids;
    protected $user_id;
    protected $lang;
    public $timeout = 0;
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids,$user_id,$lang = "de")
    {
        $this->ids = $ids;
        $this->user_id = $user_id;
        $this->lang = $lang;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        AdminDrmExportsController::updateAnyConnectedShopProduct($this->ids,$this->user_id,$this->lang);
        ExportServices::updateConnected($this->ids,$this->user_id);

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        $user = DB::table('cms_users')->where('id',$this->user_id)->first();
        return ['Update Connected Channel: ' . $user->name];
    }
}
