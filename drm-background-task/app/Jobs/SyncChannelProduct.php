<?php

namespace App\Jobs;

use App\Services\ChannelProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncChannelProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $productId;
    private $channel;
    private $fields;
    private $lang;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($productId, $channel, $fields = [], $lang = 'de')
    {
        $this->productId = $productId;
        $this->channel = $channel;
        $this->fields = $fields;
        $this->lang = $fields;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(ChannelProductService::class)->transfer($this->productId, $this->channel, $this->fields, $this->lang);

        } catch(\Exception $e) {}
    }
}
