<?php

namespace App\Jobs;

use App\MarketplaceProducts;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ProcessKeepaCSVProductsColumnUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    public function __construct()
    {
        
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        DB::table('keepa_csv_products')->whereNull('deleted_at')->orderBy('id')->chunk(500, function($rows) {
            foreach($rows as $row){
                $mp_available = MarketplaceProducts::where('ean', $row->ean)->exists();
                if($mp_available){
                    $row->mp_available = 1;
                }
                else{
                    $row->mp_available = 0;
                }
                DB::table('keepa_csv_products')->where('id', $row->id)->update([
                    'mp_available' => $row->mp_available
                ]);
            }
        });

        } catch(\Exception $e) {}
    }
}
