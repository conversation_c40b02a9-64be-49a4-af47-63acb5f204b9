<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\DropfunnelCustomerTag;

class CustomerBulkRemoveTagJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $customer_ids;
    protected $tags;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($customer_ids, $tags)
    {
        $this->customer_ids = $customer_ids;
        $this->tags = $tags;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            foreach($this->customer_ids as $id){
                DropfunnelCustomerTag::where('customer_id', $id)->whereIn('tag_id', $this->tags)->delete();
            }
        }catch(Exception $e) {}
    }
}
