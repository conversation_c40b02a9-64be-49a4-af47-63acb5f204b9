<?php

namespace App\Jobs\UniversalExport;

use App\Services\Modules\Import\UserImportSync;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;


class ImportSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 0;
    public $tries = 3;
    protected $users;

    /**
     * Create a new job instance.
     *
     * @param $user_id
     * @param $feed_id
     */
    public function __construct($users)
    {
        $this->users = $users;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        if(!is_array($this->users) || empty($this->users)) return;
        
        UserImportSync::syncAllExportFeeds($this->users);

        } catch(\Exception $e) {}
    }
}
