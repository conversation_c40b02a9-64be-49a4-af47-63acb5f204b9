<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\NewCustomer;
use App\NewOrder;
use App\DropfunnelCustomerTag;

class CustomerMergeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $customer_ids;
    protected $primary_account;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($customer_ids, $primary_account)
    {
        $this->customer_ids = $customer_ids;
        $this->primary_account = $primary_account;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        return;
        
        try {
            NewOrder::where('id', '!=', $this->primary_account)->whereIn('id', $this->customer_ids)->update([
                'drm_customer_id' => $this->primary_account
            ]);

            foreach($this->customer_ids as $id){
                $tags = DropfunnelCustomerTag::where('customer_id', $id)->get();
                foreach($tags as $tag){
                    $primaryCustomerTag = DropfunnelCustomerTag::where('customer_id', $this->primary_account)->where('tag_id', $tag->tag_id)->first();
                    if(!$primaryCustomerTag){
                        $history = $tag->history ?? [];
                        $history[] = [
                            'time' => now(),
                            'insert_type' => 4,
                            'score' => $tag->score,
                        ];

                        DropfunnelCustomerTag::where('id', $tag->id)->update([
                            'customer_id' => $this->primary_account,
                            'history' => $history
                        ]);
                    }
                    else{
                        $total_score = $primaryCustomerTag->score + $tag->score;

                        $history = $primaryCustomerTag->history ?? [];
                        $history[] = [
                            'time' => now(),
                            'insert_type' => 4,
                            'score' => $total_score,
                        ];

                        DropfunnelCustomerTag::where('id', $primaryCustomerTag->id)->update([
                            'score' => $total_score,
                            'history' => $history
                        ]);
                    }
                }
            }

            $cc_user_id = NewCustomer::whereIn('id', $this->customer_ids)->whereNotNull('cc_user_id')->first();
            NewCustomer::where('id', '!=', $this->primary_account)->whereIn('id', $this->customer_ids)->delete();
            NewCustomer::where('id', $this->primary_account)->update([
                'cc_user_id' => $cc_user_id->cc_user_id
            ]);
        }catch(Exception $e) {
            dd($e);
        }
    }
}
