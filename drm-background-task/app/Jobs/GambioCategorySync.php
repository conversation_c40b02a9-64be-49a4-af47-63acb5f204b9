<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Controllers\AdminCategoriesController;
use DB;

class GambioCategorySync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    protected $shop;
    public $timeout = 0;
    public $tries = 2;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shop)
    {
        $this->shop = $shop;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        if($this->shop->id!=116){
            $Categoriescontroller = new AdminCategoriesController();
            $Categoriescontroller->getGambioCategory($this->shop, 'background');
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        $user = DB::table('cms_users')->where('id', $this->shop->user_id)->first();

        return ['Sync Gambio Shop Category : ' . $this->shop->shop_name . ' Shop - ' . $user->name];
    }

}
