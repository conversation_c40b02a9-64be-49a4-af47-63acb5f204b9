<?php

namespace App\Jobs;

use App\Models\Product\AnalysisProduct;
use Carbon\Carbon;
use DateTime;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use App\MarketplaceProducts;

class ProcessMPRatingUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $product_ids;

    public function __construct($product_ids)
    {
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        AnalysisProduct::whereIn('id', $this->product_ids)->chunk(1000, function($rows) {
            foreach($rows as $row){
                try{
                    $total_rating = ($row->ebay_rating ?? 0) + ($row->amazon_rating ?? 0) + ($row->google_rating ?? 0);
                    $available = 0;
                    $available = ($row->ebay_rating ? 1 : 0) + ($row->amazon_rating ? 1 : 0) + ($row->google_rating ? 1 : 0);
                    if($available < 1){
                        $available = 1;
                    }
                    $avg_rating = $total_rating / $available;
                    MarketplaceProducts::where('ean', $row->ean)->update([
                        'product_avg_rating' => $avg_rating
                    ]);
                } catch(Exception $e){

                }
            }
        });

        } catch(\Exception $e) {}
    }
}
