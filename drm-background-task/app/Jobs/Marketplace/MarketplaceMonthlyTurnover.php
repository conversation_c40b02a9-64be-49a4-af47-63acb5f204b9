<?php

namespace App\Jobs\Marketplace;

use App\Mail\DRMSEndMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class MarketplaceMonthlyTurnover implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $lastMonth = now()->subMonth();
        // Fetch the sum totals of shipped and canceled orders for each client
        $orders = \App\NewOrder::select('cms_client', \DB::raw('SUM(CASE WHEN status = "Shipped" THEN total ELSE 0 END) as shipped_total'),
                \DB::raw('SUM(CASE WHEN status = "Canceled" THEN total ELSE 0 END) as canceled_total'))
                ->where('insert_type', 8)
                ->whereYear('created_at', $lastMonth->year)
                ->whereMonth('created_at', $lastMonth->month)
                ->whereNotNull('cms_client')
                ->groupBy('cms_client')
                ->get();
    

        $type = \App\Enums\CreditType::MARKETPLACE_ORDER;
        $tariffController = app(\App\Http\Controllers\TariffController::class);
        $adminDrmController = app(\App\Http\Controllers\AdminDrmImportsController::class);
        foreach ($orders as $order) {
            $turnover_amount = $order->shipped_total - $order->canceled_total;
            $checkUserTariff = $adminDrmController->checkIsProfessionalOrEnterprice($order->cms_client);
    
            if ($turnover_amount > 0 && isset($checkUserTariff) && $checkUserTariff->import_plan_id == 26) {
                $credit_amount = max($order->shipped_total - 1000, 1);

                // $tariffController->CreditUpdate($order->cms_client, $credit_amount, 'credit_add');
                // $tariffController->drmUserCreditAdd($order->cms_client, $credit_amount, 'MP monthly turnover credit add', $type, \App\Enums\CreditType::CREDIT_ADD);

                (new \App\Services\Tariff\Credit\RefillCredit)->refillMarketplaceBonusCredit($order->cms_client, (float) $credit_amount, \App\Services\Tariff\Credit\CreditType::MP_MONTHLY_TURNOVER_CREDIT_ADD);
            }
        }
    }
}
