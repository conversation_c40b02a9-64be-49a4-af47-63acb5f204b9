<?php

namespace App\Jobs\Marketplace;

use App\Mail\DRMSEndMail;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SupplierMonthlyReportSendJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $email;
    protected $pdf;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($email, $pdf)
    {
        $this->email = $email;
        $this->pdf = $pdf;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $slug = "monthly_revenue_report_email";
            $tags = [
                'revenue_file' =>  $this->pdf,
            ];

            $mail_data = DRMParseMailTemplate($tags, $slug); //Generated html
            app('drm.mailer')->getMailer()->to($this->email)->send(new DRMSEndMail($mail_data)); //Send

        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }
}
