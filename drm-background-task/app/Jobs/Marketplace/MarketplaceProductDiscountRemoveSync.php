<?php

namespace App\Jobs\Marketplace;

use App\DrmProduct;
use Illuminate\Bus\Queueable;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\Log;
use App\Models\Marketplace\Category;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Marketplace\MpCoreDrmTransferProduct;


class MarketplaceProductDiscountRemoveSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */


    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $discount = 0;
        $category_discount = 0;
        $marketPlaceProducts = Product::with('mainCategory:id,is_offer_active,start_date,end_date,discount_percentage', 'core_products')
            ->where('is_offer_active', 1)
            ->where('offer_end_date', '<', now())
            ->select('id', 'ean', 'category_id', 'is_offer_active', 'vk_price', 'supplier_id', 'api_id')
            ->get();

        foreach($marketPlaceProducts as $marketPlaceProduct){
            $country_id = app('App\Services\UserService')->getProductCountry($marketPlaceProduct->supplier_id);
            $lang = app('App\Services\UserService')->getProductLanguage($country_id);

            if(!blank($marketPlaceProduct->mainCategory) && $marketPlaceProduct->mainCategory->is_offer_active == 1){
                $category_discount = $marketPlaceProduct->mainCategory->discount_percentage ?? 0.0;
            }
            $discount += $category_discount;

            if(!blank($marketPlaceProduct->core_products)){
                foreach($marketPlaceProduct->core_products as $drmProduct){    
                    $updateableColumns = [];
                    $update_status = json_decode($drmProduct->update_status, 1);
                    
                    if ( isset($update_status['ek_price']) && $update_status['ek_price'] == 1 ) {
                        $new_ek_price = userWiseVkPriceCalculate($marketPlaceProduct->vk_price ?? 0.0, $drmProduct->user_id, false, $category_discount, $marketPlaceProduct->api_id ?? null);
                        if(round($new_ek_price, 2) != round($drmProduct->ek_price, 2)){
                            $updateableColumns['ek_price'] = $new_ek_price;      
                        }          
                    }

                    if($discount != $drmProduct->mp_category_offer){
                        $updateableColumns['mp_category_offer'] = $discount;     
                    }
                    
                    if(!blank($updateableColumns)){
                        app(\App\Services\DRMProductService::class)->update($drmProduct->id, $updateableColumns, $lang); 
                    }
                }
            }

            $marketPlaceProduct->is_offer_active = 0;
            $marketPlaceProduct->update();
        }

    //     $marketPlaceProducts = Product::select('id','category_id','is_offer_active','vk_price','supplier_id')->where('is_offer_active', 1)->whereDate('offer_end_date', '<', date('Y-m-d'))->get();
    //     foreach($marketPlaceProducts as $marketPlaceProduct){
    //         $checkExistInDrm = MpCoreDrmTransferProduct::where('marketplace_product_id',$marketPlaceProduct->id)->get();
    //         if(count($checkExistInDrm) > 0){
    //             $country_id = app('App\Services\UserService')->getProductCountry($marketPlaceProduct->supplier_id);
    //             $lang = app('App\Services\UserService')->getProductLanguage($country_id);
    //             $category_discount = Category::where('id', $marketPlaceProduct->category_id)->where('is_offer_active', 1)->whereDate('start_date', '<=', date('Y-m-d'))->whereDate('end_date', '>=', date('Y-m-d'))->select('discount_percentage')->first()->discount_percentage ?? 0.0;
    //             foreach($checkExistInDrm as $product){    
    //                 $drmProduct = DrmProduct::select('id','update_status','user_id')->where('id', $product->drm_product_id)->first()->toArray();
    //                 $updateableColumns = [];
    //                 $update_status = json_decode($drmProduct['update_status'], 1);
                    
    //                 if ( isset($update_status['ek_price']) && $update_status['ek_price'] == 1 ) {
    //                     $updateableColumns['ek_price'] = userWiseVkPriceCalculate($marketPlaceProduct->vk_price ?? 0.0, $drmProduct['user_id'], false, $category_discount);                
    //                 }
    //                 $updateableColumns['mp_category_offer'] = 0.0 + $category_discount;                
    //                 app(\App\Services\DRMProductService::class)->update($drmProduct['id'], $updateableColumns, $lang); 
    //             }        
    //         }
    //         $marketPlaceProduct->is_offer_active = 0;
    //         $marketPlaceProduct->update();
    //     }

    }
}
