<?php

namespace App\Jobs\Marketplace;

use App\User;
use App\Mail\DRMSEndMail;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class FulfilmentProductAcceptedMail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public  $supplierId;

    public function __construct($supplierId)
    {
        $this->supplierId = $supplierId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        
        try {
            $slug = 'fulfilment_product_accepted';
            $supplier = User::where('id',$this->supplierId)->first();
            $tags = [
            'name' => $supplier->name,
            ];
            $mail_data = DRMParseMailTemplate($tags, $slug);
            app('drm.mailer')->getMailer()->to($supplier->email)->send(new DRMSEndMail($mail_data)); //Send
        } catch (\Exception $e) {
            Log::info('!ops Error fulfilment product accepted mail'.$e->getMessage());
        }
    }
}
