<?php

namespace App\Jobs\Marketplace;

use App\Mail\DRMSEndMail;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SendMailProductListIntoInternel implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $cloudFile_url;

    public function __construct($cloudFile_url)
    {
        $this->cloudFile_url = $cloudFile_url;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            $slug = "internel_send_product_list";
            $tags = [
                'product_file' => $this->cloudFile_url,
            ];
            $mail_data = DRMParseMailTemplate($tags, $slug);
            app('drm.mailer')->getMailer()->to('<EMAIL>')->send(new DRMSEndMail($mail_data)); //Send
        }catch(\Exception $e){
            Log::info('!ops Error'.$e->getMessage());
        }
    }
}
