<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Controllers\AdminMarketplaceProductsController;

class UpdateUserCategoryAccess implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $productIds;
    protected $mainCategory;

    public function __construct($productIds,$mainCategory)
    {
        $this->productIds = $productIds;
        $this->mainCategory = $mainCategory;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        app(AdminMarketplaceProductsController::class)
        ->updateUserCategoryAccess($this->productIds,$this->mainCategory);
    }
}
