<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\Category;
use App\DrmProduct;
use Carbon\Carbon;

class MarketplaceProductDiscountSyncToDrm implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $products;

    public function __construct($products)
    {
        $this->products = $products;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach($this->products as $product){
            $marketPlaceProduct = Product::select('vk_price','supplier_id','offer_start_date','offer_end_date','is_offer_active','discount_percentage','category_id', 'api_id')->where('id', $product->marketplace_product_id)->first()->toArray();
            $drmProduct = DrmProduct::select('id','update_status','user_id')->where('id', $product->drm_product_id)->first()->toArray();
            $category_discount = Category::where('id', $marketPlaceProduct['category_id'])->where('is_offer_active', 1)->whereDate('start_date', '<=', date('Y-m-d'))->whereDate('end_date', '>=', date('Y-m-d'))->select('discount_percentage')->first()->discount_percentage ?? 0.0;
            $product_discount = 0.0;
            if($marketPlaceProduct['offer_start_date'] <= Carbon::now() && $marketPlaceProduct['offer_end_date'] >= Carbon::now() && $marketPlaceProduct['is_offer_active'] == 1){
                $product_discount = $marketPlaceProduct['discount_percentage'] ? ($marketPlaceProduct['discount_percentage'] - $marketPlaceProduct['discount_percentage'] / 4) : 0.0;
            }
            $updateableColumns = [];
            $country_id = app('App\Services\UserService')->getProductCountry($marketPlaceProduct['supplier_id']);
            $lang = app('App\Services\UserService')->getProductLanguage($country_id);            
            $update_status = json_decode($drmProduct['update_status'], 1);
            
            if ( isset($update_status['ek_price']) && $update_status['ek_price'] == 1 ) {
                $updateableColumns['ek_price'] = userWiseVkPriceCalculate($marketPlaceProduct['vk_price'] ?? 0.0, $drmProduct['user_id'], false, ($product_discount + $category_discount), $marketPlaceProduct['api_id'] ?? null);                
            }
            $updateableColumns['mp_category_offer'] = $product_discount + $category_discount;                
            app(\App\Services\DRMProductService::class)->update($drmProduct['id'], $updateableColumns, $lang);

        }
    }
}
