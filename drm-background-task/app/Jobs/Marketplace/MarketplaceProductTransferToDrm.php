<?php

namespace App\Jobs\Marketplace;

use Exception;
use Illuminate\Support\Str;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\MessageBroker\ProductTransferToDrmJob;

class MarketplaceProductTransferToDrm implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $tranferable_ids;
    protected $category_id;
    protected $is_trail;
    protected $user_id;
    protected $country_id;
    public function __construct($tranferable_ids = [], $user_id, $category_id = null, $is_trail = false, $country_id = 1)
    {
        $this->tranferable_ids = $tranferable_ids;
        $this->category_id = $category_id;
        $this->is_trail = $is_trail;
        $this->user_id = $user_id;
        $this->country_id = $country_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if (!isLocal() && hasV2Access($this->user_id)) {
            $this->productTransferToDropmatixV9();
        } else {
            if ($this->is_trail) {
                app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferUnlimitedProductsToDrm($this->tranferable_ids, $this->user_id, $this->category_id);
            } else {
                app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferTarifLimitedProductsToDrm($this->tranferable_ids, $this->user_id, $this->category_id);
            }
        }
    }

    private function productTransferToDropmatixV9()
    {
        if (empty($this->user_id)) return;

        try {
            $queueJob = new ProductTransferToDrmJob;
            $batchId  = Str::uuid()->toString();
            $queueJob->publish([
                'batchId'       => $batchId,
                'userId'        => $this->user_id,
                'countryId'     => $this->country_id,
                'productsId'    => $this->tranferable_ids,
                'countryInfo'   => [],
            ]);
        } catch (Exception $e) {
            info("MP Product transfer error");
        }
    }
}
