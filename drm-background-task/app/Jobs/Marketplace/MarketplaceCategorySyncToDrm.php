<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\Category;
use App\DrmProduct;
use App\Models\DrmCategory;


class MarketplaceCategorySyncToDrm implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $products;

    public function __construct($products)
    {
        $this->products = $products;

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach($this->products as $product){
            $marketPlaceProduct = Product::select('supplier_id','category_id')->where('id', $product->marketplace_product_id)->first();
            $drmProduct = DrmProduct::select('id','update_status','user_id')->where('id', $product->drm_product_id)->first();
            if($marketPlaceProduct && $drmProduct){
                $marketPlaceProduct = $marketPlaceProduct->toArray();
                $drmProduct = $drmProduct->toArray();
                
                $updateableColumns = [];
                $country_id = app('App\Services\UserService')->getProductCountry($marketPlaceProduct['supplier_id']);
                $lang = app('App\Services\UserService')->getProductLanguage($country_id);
                
                $update_status = json_decode($drmProduct['update_status'], 1);
                
                if ( isset($update_status['category']) && $update_status['category'] == 1 ) {
                    $marketPlaceProductCategory = Category::find($marketPlaceProduct['category_id']);
                    if(in_array($marketPlaceProduct['supplier_id'], [2817])){
                        $category = \DB::table('channel_user_categories')->where('id', $marketPlaceProduct['category_id'])->first();
                        $drmCategory = DrmCategory::where('category_name_'.$lang, $category->category_name)->where('user_id',$drmProduct['user_id'])->first();
                        if ( !$drmCategory ) {
                            $drmCategory = DrmCategory::create([
                                'category_name_'.$lang => $category->category_name,
                                'user_id' => $drmProduct['user_id'],
                                'country_id' => $country_id,
                            ]);
                        }
                    } else {
                        $drmCategory = DrmCategory::where('category_name_'.$lang, $marketPlaceProductCategory->name)->where('user_id', $drmProduct['user_id'])->first();
                        if ( !$drmCategory ) {
                            $drmCategory = DrmCategory::create([
                                'category_name_'.$lang => $marketPlaceProductCategory->name,
                                'user_id'          => $drmProduct['user_id'],
                                'country_id'       => $country_id,
                            ]);
                        }
                    }
                    $updateableColumns['category'] = [$drmCategory->id];
                    app(\App\Services\DRMProductService::class)->update($drmProduct['id'], $updateableColumns, $lang); 
                }
            }
        }
    }
}
