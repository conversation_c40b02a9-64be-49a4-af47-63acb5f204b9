<?php

namespace App\Jobs\Marketplace;

use App\Jobs\DestroyProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Marketplace\UserAccess;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CustomerBulkParentCategoryRemove implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $categoryIds;
    protected $user_id;

    public function __construct(array $categoryIds, $user_id)
    {
        $this->categoryIds = $categoryIds;
        $this->user_id =  $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    /*
     * This job is for update the status of all synced Orders
     * */
    public function handle()
    {
       $languageId = app('App\Services\UserService')->getProductCountry($this->user_id);
       $lang = app('App\Services\UserService')->getProductLanguage($languageId);
        try{
            $mp_product_id =  DB::connection('marketplace')->table('marketplace_products')
                                ->whereIn('category_id', $this->categoryIds)->pluck('id')->toArray();
            $drm_product_id = DB::connection('marketplace')->table('mp_core_drm_transfer_products')
                                ->whereIn('marketplace_product_id', $mp_product_id)
                                ->where('user_id',$this->user_id)->pluck('drm_product_id')->toArray();                         
            if (!empty($drm_product_id)) {
                foreach (array_chunk($drm_product_id, 500) as $chunkIds) {
                    DestroyProduct::dispatch($chunkIds, $this->user_id, $lang, $languageId);
                }
            }
        }catch(\Exception $e){
            Log::info([
                'message'=> '!ops customer bulk category product remove failed',
                'categoryIds'=>$this->categoryIds,
                'error'=>$e->getMessage()
            ]);
        }
    }
}
