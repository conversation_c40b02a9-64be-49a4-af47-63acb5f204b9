<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use App\DropmatixProductBrand;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\DrmProduct;
use DB;

class VidaxlProductsBrandUpdateToDrm implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $marketplace_ids;
    protected $brand_name;

    public function __construct($marketplace_ids = [], $brand_name)
    {
        $this->marketplace_ids = $marketplace_ids;
        $this->brand_name = $brand_name;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        info('Start Product Brand Update...');
            $drm_products = DrmProduct::select('id', 'user_id', 'update_status', 'brand')->whereIn('marketplace_product_id', $this->marketplace_ids)->get();
            foreach($drm_products as $drm_product){
                $user_id = $drm_product->user_id;
                $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper($this->brand_name) . '%')->where('user_id', $user_id)->first() ?? DropmatixProductBrand::create([
                    'brand_name' => $this->brand_name,
                    'user_id' => $user_id,
                    'brand_logo' => ["https:\/\/drm.software\/Marketing_assets\/img\/no-product-image.jpg"]
                ]);

                $update_status = json_decode($drm_product->update_status, 1);

                if($update_status['brand'] == 1 && $drm_product->brand != $drmBrand->id){
                    $updateableColumn['brand'] = $drmBrand->id;
                    app(\App\Services\DRMProductService::class)->update($drm_product->id, $updateableColumn);
                }
            }
        info('End Product Brand Update...');

    }
}
