<?php

namespace App\Jobs\Marketplace;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Marketplace\MpCoreDrmTransferProduct;

class CategoryAccessProductDeleteJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $product_ids;
    protected $user_id;
   

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($product_ids,$user_id)
    {
        $this->product_ids = $product_ids;
        $this->user_id = $user_id;
     
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // $drm_product_id = MpCoreDrmTransferProduct::whereIn('marketplace_product_id', $this->product_ids)
        // ->where('user_id', $this->user_id)->pluck('drm_product_id')->toArray();
        // if(count($drm_product_id) > 0){
        //     app(\App\Services\DRMProductService::class)->destroy($drm_product_id, $this->user_id);
        // }
        return app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->marketplaceProductRemoveFromDrm($this->product_ids, $this->user_id);

    }
}
