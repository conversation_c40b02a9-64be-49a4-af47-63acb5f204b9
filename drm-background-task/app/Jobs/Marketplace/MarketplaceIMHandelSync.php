<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Marketplace\Product;


class MarketplaceIMHandelSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $product_ids;
    protected $im_percentage;


    public function __construct($product_ids, $im_percentage)
    {
        $this->product_ids = $product_ids;
        $this->im_percentage = $im_percentage;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $marketPlaceProduct = Product::select('id', 'ek_price','vk_price', 'im_handel')
            ->whereIn('id', $this->product_ids)
            ->get();
        foreach ($marketPlaceProduct as $product) {
            $product->im_handel = $this->im_percentage > 0 ? $product->vk_price + ($product->vk_price * $this->im_percentage / 100) : 0;
            $product->update();
        }
    }
}
