<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class ProductBrandChangeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $product_ids;
    protected $selected_brand_id;
    protected $selected_brand_name;
    protected $selected_brand_logo;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($productId,$selected_brand_id,$selected_brand_name,$selected_brand_logo)
    {
        $this->product_ids          = $productId;
        $this->selected_brand_id    = $selected_brand_id;
        $this->selected_brand_name  = $selected_brand_name;
        $this->selected_brand_logo  = $selected_brand_logo;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        info('Start Product Brand ChangeJob.............');

        app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->ProductBrandUpdate($this->product_ids,$this->selected_brand_id,$this->selected_brand_name,$this->selected_brand_logo);
    }
}
