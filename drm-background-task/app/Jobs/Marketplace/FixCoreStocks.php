<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Marketplace\CoreSyncReport;
use App\Models\Marketplace\Product;

class FixCoreStocks implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        return false;
        $count = 0;
        $deletedCount = 0;
        $reports = CoreSyncReport::where('fixed', 0)
            ->where(function ($q) {
                $q->whereRaw('CAST(marketplace_price as SIGNED) != CAST(drm_price as SIGNED)')
                ->orWhereRaw('CAST(marketplace_stock as SIGNED) != CAST(drm_stock AS SIGNED)');
            })->cursor();

            foreach($reports->chunk(1000) as $chunk){
                $mpProducts = Product::whereIn('id', $chunk->pluck('marketplace_product_id')->toArray())
                ->select(
                    'id',
                    'stock',
                    'vk_price as ek_price'
                )->cursor();

                foreach ($chunk as $product) {
                    $mpProduct = $mpProducts->where('id', $product->marketplace_product_id)->first();

                    if($mpProduct)
                    {
                        $mpProduct = $mpProduct->toArray();
                        unset($mpProduct['id']);
                        app('App\Services\DRMProductService')->update(
                            $product->drm_product_id,
                            $mpProduct
                        );
                        // echo "\r Processed " . $count++ . " Products";
                    }
                    else{
                        app('App\Services\DRMProductService')->destroy(
                            [$product->drm_product_id],
                            $product->user_id
                        );
                        // echo "\r Deleted " . $deletedCount++ . " Products";
                    }

                    $product->fixed = true;
                    $product->save();
                }
            }
    }
}
