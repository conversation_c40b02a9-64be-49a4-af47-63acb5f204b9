<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MarketplaceReviewAndRating implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $rows;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($rows)
    {
        $this->rows = $rows;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $processedData = array_map(function ($row) {
                return array_map(fn ($ean) => [
                    "rating" => $row['Reviews: Rating'] ?? '',
                    "review_count" => $row['Reviews: Review Count'] ?? '',
                    "ean" => $ean,
                    'created_at' => now(),
                ], $this->getEans($row));
            }, $this->rows);

            $uniqueData = $this->getUniqueEans($processedData);
            $eans = array_column($uniqueData, 'ean');

            $reviewAndRating = \App\Models\Marketplace\MpReviewAndRating::whereIn('ean', $eans)->get()->keyBy('ean');
            info('mp review insert start');
            $insertReviewAndRating = [];
            foreach ($uniqueData as $value) {
                if ($review = $reviewAndRating->get($value['ean'])) {
                    $review->update([
                        'rating' => $value['rating'],
                        'review_count' => $value['review_count'],
                    ]);
                } else {
                    $insertReviewAndRating[] = $value;
                }
            }
            if (!empty($insertReviewAndRating)) {
                \App\Models\Marketplace\MpReviewAndRating::insert($insertReviewAndRating);
            }
            info('mp review insert end');
        } catch (\Exception $e) {
            info('!ops Review import error' . $e->getMessage());
        }
        return true;
    }

    private function getEans($row)
    {
        $eans = array_filter(array_map('trim', explode(',', $row['Product Codes: EAN'] ?? '')));
        $paddedEans = array_map(fn ($ean) => str_pad($ean, 13, '0', STR_PAD_LEFT), $eans);
        return $paddedEans;
    }

    private function getUniqueEans($processedData)
    {
        return array_values(array_reduce(array_merge(...$processedData), function ($carry, $item) {
            $carry[$item['ean']] = $item;
            return $carry;
        }, []));
    }
}
