<?php

namespace App\Jobs;

use App\Models\Product\AnalysisProduct;
use Carbon\Carbon;
use DateTime;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ProcessAnalysisArchiveDeleteJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    public function __construct()
    {
        
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $products = AnalysisProduct::where('archived', 1)->get();
        
        foreach($products as $product){
            $archived_at = new DateTime($product->archived_at);
            $archived_for = $archived_at->diff(now());
            $days = $archived_for->format('%a');
            if($days >= 30){
                AnalysisProduct::where('id', $product->id)->delete();
            }
        }

        } catch(\Exception $e) {}
    }
}
