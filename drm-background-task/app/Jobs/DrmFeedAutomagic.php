<?php

namespace App\Jobs;

use App\Models\TrackAutomagic;
use App\Services\Keepa\Automagic;
use App\Services\Keepa\Keepa;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DrmFeedAutomagic implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // public $tries = 4;

    protected $user_id;
    protected $product_ids;
    protected $track_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id, $product_ids, $track_id)
    {
        $this->user_id = $user_id;
        $this->product_ids = $product_ids;
        $this->track_id = $track_id;

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $keepa = new Keepa( env('KEEPA_API_KEY') );
        $tokens = (object) $keepa->tokens();
        $token_required = count($this->product_ids) * 2;

        if($tokens->tokens > $token_required){
            $automagic = new Automagic($this->user_id);
            $automagic->setLabel('drm')->magicAutocomplete( $this->product_ids );
            TrackAutomagic::where(['id' => $this->track_id])->delete();
        }else{
            if ($this->attempts() > 3) {
                throw new Exception("Maximul attempts for feed automagic reached.");
            }
            $this->release($tokens->refill_time + 300); #add extra 5 minutes with token refill time
        }

        } catch(\Exception $e) {}
    }
}
