<?php

namespace App\Jobs;

use App\DrmProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use Exception;
use Carbon\Carbon;
use Log;

class DRMStockHistoryStore implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            DrmProduct::groupBy('user_id')
                ->select('user_id')
                ->selectRaw("SUM(stock) as stock_sum")
                ->selectRaw("count(stock) as total_stock")
                ->get()
                ->each(function ($item) {

                    $stocks = DB::table('drm_products')->where('user_id', $item->user_id)
                        ->select('id','stock')
                        ->get();
                    $in_stock = $stocks->where('stock', '>', 0)->count('id');
                    $stock_out = $stocks->where('stock', '<=', 0)->count('id');
                    if($item->user_id){
                        DB::table('drm_stock_history')->insert([
                            "user_id" => $item->user_id,
                            "total" => $item->total_stock,
                            "in_stock" => $in_stock,
                            "stock_out" => $stock_out,
                            "stock_sum" => $item->stock_sum,
                            "created_at" => now(),
                            "updated_at" => now()
                        ]);
                    }
                });
        } catch (Exception $e) {
            Log::channel('command')->info("DRMStockHistoryStore");
            Log::channel('command')->info($e);
        }
    }
}
