<?php

namespace App\Jobs;

use App\Models\Product\AnalysisProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\MarketplaceProducts;

class ProcessCPToCoreTransferToCustomer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $user_id;
    protected $product_ids;

    public function __construct($user_id, $product_ids)
    {
        $this->user_id = $user_id;
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $drm_product_ids = AnalysisProduct::whereIn('id', $this->product_ids)->whereIntegerInRaw('source', [2, 3])->pluck('product_id')->toArray();
        $mp_products = MarketplaceProducts::whereIn('id', $drm_product_ids)->pluck('id')->toArray();

        if(empty($mp_products)) return;

        app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferAllFilteredProductsToDrm($mp_products, [], null, $this->user_id);

        } catch(\Exception $e) {}
    }
}
