<?php

namespace App\Jobs;

use App\Services\Modules\Export\Mirakl\MiraklChannelManager;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessDecathlonColumnUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

     protected $shop_id;
     protected $product_ids;
     protected $user_id;

    public function __construct($product_ids, $user_id, $shop_id){
//        $this->uri = "https://marketplace-decathlon-eu.mirakl.net/api/products";
//        $this->token = "bb58b9b2-aa8e-43cc-8535-5ed791f181f8";
        $this->product_ids = $product_ids;
        $this->user_id = $user_id;
        $this->shop_id = $shop_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $channelManager = new MiraklChannelManager();
        $service = $channelManager($this->shop_id);
        $service->syncWithMiraklAPI($this->product_ids);
    }
}
