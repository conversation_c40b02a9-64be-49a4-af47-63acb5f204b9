<?php

namespace App\Jobs;

use App\KeepaProduct;
use App\Services\Keepa\Keepa;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class FetchKeepaProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $retryAfter = 5400;

    protected $parsable_list;
    protected $keepa_category;
    protected $key;

    /**
     * Create a new job instance.
     *
     * @param $keepa_category 
     * @param $parsable_list
     * @param $key
     */
    public function __construct($keepa_category, $parsable_list, $key)
    {
        $this->parsable_list = $parsable_list;
        $this->keepa_category = $keepa_category;
        $this->key = $key;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        ini_set("memory_limit",-1);
        ini_set('max_execution_time', 1800);
        $keepa = new Keepa( env('KEEPA_API_KEY') );
        $tokens = (object) $keepa->tokens();
        $token_required = count($this->parsable_list) * 2;

        _log("Started: ------------- KEY: $this->key  |  Cat: $this->keepa_category");
        _log("Tok Left: $tokens->tokens");
        _log("Tok Left: $token_required");
        _log("Ended -------------------------");
        if($tokens->tokens > $token_required){
            $this->fetchProducts();
        }else{
            _log("Released For (MIN): ".$tokens->refill_time/60);
            $this->release($tokens->refill_time); #add extra 5 minutes with token refill time
        }

        } catch(\Exception $e) {}

    }

    public function retryUntil()
    {
        return now()->addMinutes(60*24*8); // 8 days
    }

    public function fetchProducts(){

        $keepa_category = (object) $this->keepa_category;
        $product_param = [
            'stats' => 1,
            'rating' => 1,
        ];    
        # fetche keepa products by asin list
        $keepa = new Keepa(env('KEEPA_API_KEY'));
        $keepa_products = $keepa->asin($this->parsable_list, $product_param);


        $drm_app_id = isset($keepa_category->drm_app_id['drm_app_id']) ? $keepa_category->drm_app_id['drm_app_id'] : null;
        print("Fetching App Data: $drm_app_id\n");
        
        if(!$keepa_products->responseJSON() || !$drm_app_id) return;
        
        #store json data to storate
        $json_path = "keepa_products/".date('Y-m')."/$drm_app_id/$this->key.json";

        Storage::disk('spaces')->put($json_path, $keepa_products->responseJSON(), 'public');

        $exists_products = KeepaProduct::whereIn('asin', $keepa_category->asin_list)->pluck('asin')->toArray();

        $insert = [];
        foreach ($this->parsable_list as $chunk_idx => $asin) {

            $keepa_product = $keepa_products->product($asin, 'asin');

            if(empty($keepa_product->asin)) continue;

            if(\in_array($keepa_product->asin, $exists_products)){
                $update = [
                    'category_id' => $keepa_category->category_id,
                    'asin' => $keepa_product->asin,
                    'ean' => json_encode($keepa_product->eanList),
                    'title' => $keepa_product->title,
                    'images' => $keepa_product->imagesCSV,
                ];
                KeepaProduct::where(['asin' => $keepa_product->asin])->update($update);
            }else{
                $insert[] = [
                    'category_id' => $keepa_category->category_id,
                    'asin' => $keepa_product->asin,
                    'ean' => json_encode($keepa_product->eanList),
                    'title' => $keepa_product->title,
                    'images' => $keepa_product->imagesCSV,
                ];
            }
        }
        #insert each category chunks once
        KeepaProduct::insert($insert);
    }
}
