<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;
use GuzzleHttp\Client;
use App\Jobs\ProcessCustomerSync;
use Illuminate\Support\Facades\Log;

class SplitFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user_id;
    protected $counter;
    protected $slices;
    protected $last_sync_date;
    protected $totalSubscribers;
    protected $offset;

    public function __construct($user_id, $counter, $slices, $last_sync_date, $totalSubscribers, $offset)
    {
        $this->user_id = $user_id;
        $this->counter = $counter;
        $this->slices = $slices;
        $this->last_sync_date = $last_sync_date;
        $this->totalSubscribers = $totalSubscribers;
        $this->offset = $offset;
    }

    public function handle()
    {
        ini_set("memory_limit",-1);
        // Redis::throttle('split-customer-file')->block(0)->allow(1)->every(5)->then(function () {
            try {
                $client_filtered = new Client();
                $url_filtered = "http://64.227.124.51/customers/".$this->user_id."/subscribers?offset=".$this->offset."&limit=100";
                $response_filtered  = $client_filtered->get($url_filtered);
                $result_filtered = json_decode($response_filtered->getBody()->getContents(), TRUE);
                ProcessCustomerSync::dispatch($result_filtered, $this->user_id, $this->slices, $this->counter, $this->last_sync_date, $this->totalSubscribers);
            } catch (\Exception $e) {
                    User::find($this->user_id)->notify(new DRMNotification('Your KlickTipp Subscribers can not be synced. Please Contact Administrator!', 'CustomerOnSync'));
            }
        // }, function () {

        //     return $this->release(5);
        // });
    }

    public function tags()
    {
        return ['Chunking Json File for - '.$this->user_id];
    }

    public function failed(\Exception $exception)
    {
        Log::channel('command')->info("SplitFile");
        Log::channel('command')->info($exception);
    }
}
