<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use DB;
use Log;
use App\Shop;
use App\Services\DateTime\DateTime;

class UserTrialEndCheckJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        
        try{
            //get only DT Users
            DB::table('user_group_relations')
                // ->join('cms_users','cms_users.id','user_group_relations.user_id')
                ->where('user_group_relations.user_id','>',config('global.greater_user_id'))
                ->where('user_group_relations.group_id',2)
                ->select('user_group_relations.user_id')
                ->orderBy('user_group_relations.user_id')
                ->chunk(100, function ($users) {

                foreach ($users as $user) {
                    
                   $user_id = $user->user_id;

                   //check purchase plan & Trial ends
                   $tariff = DB::table('dt_tariff_purchases')
                   ->where('user_id', $user_id)
                   ->selectRaw('DATEDIFF(end_date, NOW()) as end_days') // If end date and today date same DATEDIFF function return 0
                   ->first();

                   $tariffDays = !empty($tariff) ? $tariff->end_days : -1;
                   if($tariffDays >= 0) continue;

                   $trial = $this->checkImportTrial($user_id);
                   if($trial && $trial > 0) continue;

                    // $dueDays = max($trial, $tariffDays);
                    // $dueDays = abs($trial);

                    //log
                    DB::table('deletable_trial_dt_users_logs')->updateOrInsert(['user_id'=>$user_id],[
                        'user_id' => $user_id,
                        "created_at" => now()
                    ]);

                    //DT Account Entry
                    if($trial <= 0){
                        DB::table('cms_users')->where('id',$user_id)->whereNotNull('status')->update([
                            'status'=> null
                        ]);

                        app('App\Services\DeactiveUser\UserRemove')->allShopDeleteEntry($user_id);

                        //log
                        DB::table('deletable_trial_dt_users_logs')->where('user_id',$user_id)->update([
                        'blocked_user' => 1,
                        ]);
                    }

                    //Block DRM & DT Access between 14days to 30days
                    // if($dueDays > 27)
                    // {

                    //     DB::table('cms_users')->where('id',$user_id)->whereNotNull('status')->update([
                    //         'status'=> null
                    //     ]);

                    //     $shopId = Shop::where('user_id', $user_id)
                    //         ->where('channel', 10)
                    //         ->orderBy('id')
                    //         ->get('id');

                    //      //log
                    //      DB::table('deletable_trial_dt_users_logs')->where('user_id',$user_id)->update([
                    //         'shops' => json_encode($shopId),
                    //     ]);


                    //     // Remove users
                    //     app('App\Services\DeactiveUser\UserRemove')->remove($user_id);

                    // }else 

                    // if($dueDays > 13) {

                    //     DB::table('cms_users')->where('id',$user_id)->whereNotNull('status')->update([
                    //         'status'=> null
                    //     ]);

                    //     app('App\Services\DeactiveUser\UserRemove')->blockDtAceess($user_id);

                    //      //log
                    //      DB::table('deletable_trial_dt_users_logs')->where('user_id',$user_id)->update([
                    //         'blocked_user' => 1,
                    //     ]);
                    // }

                    //Block DRM & DT Access between 14days to 30days
                    // if($dueDays > 27)
                    // {

                    //     DB::table('cms_users')->where('id',$user_id)->whereNotNull('status')->update([
                    //         'status'=> null
                    //     ]);

                    //     $shopId = Shop::where('user_id', $user_id)
                    //         ->where('channel', 10)
                    //         ->orderBy('id')
                    //         ->get('id');

                    //      //log
                    //      DB::table('deletable_trial_dt_users_logs')->where('user_id',$user_id)->update([
                    //         'shops' => json_encode($shopId),
                    //     ]);


                    //     // Remove users
                    //     app('App\Services\DeactiveUser\UserRemove')->remove($user_id);

                    // }else if($dueDays > 13) {

                    //     DB::table('cms_users')->where('id',$user_id)->whereNotNull('status')->update([
                    //         'status'=> null
                    //     ]);

                    //     app('App\Services\DeactiveUser\UserRemove')->blockDtAceess($user_id);

                    //      //log
                    //      DB::table('deletable_trial_dt_users_logs')->where('user_id',$user_id)->update([
                    //         'blocked_user' => 1,
                    //     ]);
                    // }

                }
            });

        }catch(\Exception $e){
            Log::channel('command')->info('Trial End == > failed'.$e->getMessage());
        }
        
    }

    public function checkImportTrial($user_id)
    {
        $trial = DB::table('app_trials')
        ->selectRaw('DATEDIFF(DATE_ADD(start_date,INTERVAL 14 DAY), NOW()) as end_days')
        ->where(['user_id' => $user_id, 'app_id' => 0])
        ->first();

        return !empty($trial) ? $trial->end_days : -30;
    }
}
