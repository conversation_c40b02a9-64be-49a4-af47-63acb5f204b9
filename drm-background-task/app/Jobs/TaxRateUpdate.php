<?php

namespace App\Jobs;

use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TaxRateUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tax;
    public $shop;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($tax, $shop)
    {
        $this->tax = $tax;
        $this->shop = $shop;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $tax = $this->tax;
        $shop = $this->shop;
        $shop_url = rtrim($this->shop->url, '/');
        $url = $shop_url."/api/v1/dt_tax_add";
        $req = [
            'country' => $tax->country,
            'charge' => $tax->charge,
            'alpha_three' => $tax->alpha_three,
            'lang_kod' => $tax->lang_kod,
            'country_code' => $tax->country_code,
            'country_de' => $tax->country_de,
            'drm_ref_id' => $tax->id,
            'userToken' => $shop->username,
            'userPassToken' => $shop->password,
        ];

        $client = new Client();
        $client->request('POST', $url, [
            'form_params' => $req
        ]);

        } catch(\Exception $e) {}
    }
}
