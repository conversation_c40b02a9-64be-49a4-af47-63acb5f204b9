<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Controllers\AdminCategoriesController;

class DtLicenseRenewJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    public $timeout = 0;
    public $tries = 2;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {

            \Illuminate\Support\Facades\Http::withOptions([
                'verify' => false,
            ])
            ->withHeaders([
                'token' => 'Zd6tQv8Cvd',
            ])
            ->get(config('app.drm_url').'/api/droptienda/dt-license-renew');

        } catch (\Exception $e) {}
    }

    public function tags()
    {
        return ['DT License renew'];
    }

}
