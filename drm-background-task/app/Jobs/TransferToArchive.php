<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Product\AnalysisProduct;
use App\Services\ProductApi\Services\GoogleShopping;
use Illuminate\Support\Facades\DB;

class TransferToArchive implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */


    protected $product_ids;
    protected $type;
    protected $user_id;
    protected $category_id;
    protected $analysisService;

    public function __construct($product_ids, $type, $user_id, $category_id)
    {
        $this->product_ids = $product_ids;
        $this->type = $type;
        $this->user_id = $user_id;
        $this->category_id = $category_id;
        $this->analysisService = [
            Countdown::class,
            Rainforest::class,
            GoogleShopping::class,
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $transferred_ids = [];
        if($this->type == 1){
            foreach($this->product_ids as $product){
                $id = DB::table('analysis_products')->select('ean', 'amazon_id', 'google_id', 'ebay_id', 'amazon_request_id', 'google_request_id', 'ebay_request_id')->where('user_id', $this->user_id)->where('id', $product)->get();
                $item_number = trim($id[0]->ean)."-".$this->user_id;

                $product_detail['ean'] = $id[0]->ean;
                $product_detail['item_number'] = trim($id[0]->ean)."-".$this->user_id;

                $analysisService = $this->analysisService;
                foreach($analysisService as $service){
                    $analysisApi = new $service;
                    $collection_id_column = $analysisApi->collectionColumnName();
                    $column_prefix = $analysisApi->columnPrefix();
                    $column_name = $column_prefix."id";

                    $current_collection_id = DB::table('c_p_analysis_requests')->select($collection_id_column)->where('id', $id[0]->$column_name)->get();
                    if($this->category_id != null){
                        $target_collection = DB::table('analysis_category')->select($collection_id_column)->where('id', $this->category_id)->get();
                        $target_collection_id = $target_collection[0]->$collection_id_column;
                    }
                    else{
                        $target_collection = DB::table('c_p_analysis_requests')->select($collection_id_column)->where('user_id', $this->user_id)->whereNotNull($collection_id_column)->where('default', 1)->orderBy('id', 'desc')->first();
                        // dd($target_collection);
                        $target_collection_id = $target_collection->$collection_id_column;
                    }
                    $final_id = DB::table('c_p_analysis_requests')->select('id')->where($collection_id_column, $target_collection_id)->get();
                    // dd($collection_id_column);
                    $request_id_column = $column_prefix."request_id";

                    $request_id = $id[0]->$request_id_column ? $id[0]->$request_id_column : $analysisApi->findRequest($current_collection_id[0]->$collection_id_column, $item_number);

                    $response = $analysisApi->deleteRequest($current_collection_id[0]->$collection_id_column, $request_id);

                    if($response["request_info"]['success'] == true){
                        $transferred_ids[] = $product;
                    }
                }
            }
        }

        else if($this->type == 0){
            foreach($this->product_ids as $product){
                $id = DB::table('analysis_products')->select('ean', 'item_number', 'amazon_id', 'google_id', 'ebay_id')->where('user_id', $this->user_id)->where('id', $product)->get();
                $item_number = $id[0]->item_number;

                $product_detail['ean'] = $id[0]->ean;
                $product_detail['item_number'] = $id[0]->item_number;
                // dd($request);

                $analysisService = $this->analysisService;
                foreach($analysisService as $service){
                    $analysisApi = new $service;
                    $collection_id_column = $analysisApi->collectionColumnName();
                    $column_prefix = $analysisApi->columnPrefix();
                    $column_name = $column_prefix."id";

                    $current_collection_id = DB::table('c_p_analysis_requests')->select($collection_id_column)->where('id', $id[0]->$column_name)->get();
                    if($this->category_id != null){
                        $target_collection = DB::table('analysis_category')->select($collection_id_column)->where('id', $this->category_id)->get();
                        $target_collection_id = $target_collection[0]->$collection_id_column;
                    }
                    else{
                        $target_collection = DB::table('c_p_analysis_requests')->select($collection_id_column)->where('user_id', $this->user_id)->whereNotNull($collection_id_column)->where('default', 1)->orderBy('id', 'desc')->first();
                        // dd($target_collection);
                        $target_collection_id = $target_collection->$collection_id_column;
                    }
                    $final_id = DB::table('c_p_analysis_requests')->select('id')->where($collection_id_column, $target_collection_id)->get();
                    // dd($collection_id_column);

                    $res = $analysisApi->addProducts([$product_detail], $target_collection_id);
                    $request_id_column = $column_prefix."request_id";
                    if($res){
                        $transferred_ids[] = $product;
                        DB::table('analysis_products')->where('id', $product)->update(
                            [
                                $request_id_column => null
                            ]
                        );
                    }
                }
            }
        }


        $updateProduct = AnalysisProduct::whereIn('id',$transferred_ids)
            ->where('user_id', $this->user_id)
            ->update(['archived' => $this->type]);

        } catch(\Exception $e) {}
    }
}
