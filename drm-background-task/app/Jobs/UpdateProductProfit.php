<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use App\Services\Modules\Import\ProfitMarginService;

class UpdateProductProfit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $import_id;
    protected $profit;
    protected $user_id;
    
    public $timeout = 0;
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($profit,$import_id,$user_id)
    {
        $this->profit = $profit;
        $this->import_id = $import_id;
        $this->user_id =   $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $service = new ProfitMarginService($this->import_id,$this->profit,$this->user_id);
        $service->updateProductsProfit();

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Update profit calculation : ' .$this->import_id];
    }
}
