<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Exception;
use App\Enums\Marketplace\ProductStatus;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\Log;

class MarketplaceAllProductStatusUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
   protected $product_ids;
   protected $productService;

    public function __construct($product_ids)
    {
        $this->product_ids = $product_ids;
        $this->productService = new \App\Services\Marketplace\ProductService();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {   
        try{
            $product_ids = $this->product_ids;
            $products = Product::with('mainCategory:id,minimum_price')->whereIn('id', $product_ids)->where('vk_price', '<>', 0)->get();
            $all_products = [];

            foreach($products as $product) {
                $category_min_price = $product->mainCategory ? $product->mainCategory->minimum_price : 0;
                if($category_min_price < $product->ek_price) {
                    $all_products[] = $product->id;
                    if ($product->is_dublicate == 1) {
                        $all_products = array_diff($all_products, [$product->id]);
                        $this->productService->bulkDuplicateApproved($product->id);
                    }
                }
                $this->productService->otherCountryDuplicateStatusChange([$product->ean], 1);
            }

            if(count($all_products) > 0){
                Product::whereIn('id',$all_products)->update(['status' => 1]);
                Log::info('Products status update successfully');
            }

        } catch(\Exception $e) {
            Log::info("Products status not updated ", ['Exception' => $e]);
        }
       
    }
}