<?php

namespace App\Jobs;

use App\AnalysisCategory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Product\AnalysisProduct;
use App\Models\Product\CPAnalysisRequest;
use Illuminate\Support\Facades\DB;

class TransferToMerchant implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $product_ids;
    protected $category_id;
    protected $user_id;
    protected $analysisService;

    public function __construct($product_ids, $category_id, $user_id)
    {
        $this->product_ids = $product_ids;
        $this->category_id = $category_id;
        $this->user_id = $user_id;
        $this->analysisService = [
            Countdown::class,
            Rainforest::class,
            GoogleShopping::class,
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $category_ids = DB::table('analysis_category')->select('id', 'name', 'duration', 'type', 'google_collection_id', 'ebay_collection_id', 'amazon_collection_id')->where('user_id', $this->user_id)->get();

        $analysisService = $this->analysisService;
        foreach($analysisService as $service){
            $analysisApi = new $service;
            $collection_id_column = $analysisApi->collectionColumnName();
            $column_prefix = $analysisApi->columnPrefix();
            $column = $analysisApi->driver();
            $column_name = $column_prefix."id";
            $default_collection = DB::table('c_p_analysis_requests')->select($collection_id_column)->where('user_id', $this->user_id)->whereNotNull($collection_id_column)->where('default', 1)->orderBy('id', 'desc')->first();

            $analysisApi->deleteCollection($default_collection->$collection_id_column);

            $product = [];
            $products = AnalysisProduct::select('id', 'ean')->where('user_id', $this->user_id)->where('category_id', null)->get();

            $collection_interval_data = [
                "schedule_type" => "manual",
            ];

            $collection_body = [
                "name" => "Collection - $this->user_id - " . date('Y-m-d'),
                "enabled" => True,
                "priority" => "normal",
                "notification_as_csv" => True,
                "notification_as_json" => True,
                "include_html" => 'False'
            ];

            $collectionBody =  array_merge($collection_body, $collection_interval_data);
            $collection_id = $analysisApi->createCollection($collectionBody);

            $cp_request_model = CPAnalysisRequest::create([
                'user_id' => $this->user_id,
                $collection_id_column => $collection_id,
                'default' => 1,
            ]);

            AnalysisProduct::whereIn('id', $product)->update([
                $column.'_id' => $cp_request_model->id,
            ]);

            if(!empty($products)){
                foreach($products as $key){
                    $key->item_number = trim($key->ean)."-".$this->user_id;
                    $product[] = ['id' => $key->id, 'user_id' => $this->user_id, 'ean' => $key->ean, 'item_number' => $key->item_number];
                }
                if(!empty($product)){
                    $fIndex = 0;
                    $limit = 1000;
                    if(sizeof($product)>1000){
                        for($i = 0; $i <= (sizeof($product)/1000); $i++){
                            $products = array_slice($product, $fIndex, $limit);
                            $fIndex += 1000;
                            $analysisApi->addProducts($products, $collection_id);
                            unset($products);
                        }
                    }
                    else{
                        $analysisApi->addProducts($product, $collection_id);
                    }
                }
            }

            foreach($category_ids as $category){
                $category_product = [];
                $category_products =[];
                $category_products = AnalysisProduct::select('id', 'ean')->where('user_id', $this->user_id)->where('category_id', $category->id)->get();

                $analysisApi->deleteCollection($category->$collection_id_column);

                $collection_interval_data = [
                    "schedule_type" => "manual",
                ];

                $collection_body = [
                    "name" => $category->name,
                    "enabled" => True,
                    "priority" => "normal",
                    "notification_as_csv" => True,
                    "notification_as_json" => True,
                    "include_html" => 'False'
                ];

                $collectionBody =  array_merge($collection_body, $collection_interval_data);
                $collection_id = $analysisApi->createCollection($collectionBody);
                AnalysisCategory::where('id', $category->id)->update([
                    $collection_id_column => $collection_id,
                ]);

                $cp_request_model = CPAnalysisRequest::create([
                    'user_id' => $this->user_id,
                    $collection_id_column => $collection_id,
                    'default' => 0,
                ]);

                if(!empty($category_products)){
                    foreach($category_products as $product){
                        $product->item_number = trim($product->ean)."-".$this->user_id;
                        $category_product[] = ['id' => $product->id, 'user_id' => $this->user_id, 'ean' => $product->ean, 'item_number' => $product->item_number];
                    }
                    if(!empty($category_product)){
                        $fIndex = 0;
                        $limit = 1000;
                        if(sizeof($category_product)>1000){
                            for($i = 0; $i <= (sizeof($category_product)/1000); $i++){
                                $products = array_slice($category_product, $fIndex, $limit);
                                $fIndex += 1000;
                                $analysisApi->addProducts($products, $collection_id);
                                unset($products);
                            }
                        }
                        else{
                            $analysisApi->addProducts($category_product, $collection_id);
                        }
                    }
                }
            }
        }

        } catch(\Exception $e) {}












        // foreach($this->product_ids as $product){
        //     $id = DB::table('analysis_products')->select('ean', 'item_number', 'amazon_id', 'google_id', 'ebay_id', 'amazon_request_id', 'google_request_id', 'ebay_request_id')->where('user_id', $this->user_id)->where('id', $product)->get();
        //     $item_number = trim($id[0]->ean)."-".$this->user_id;

        //     $product_detail['ean'] = $id[0]->ean;
        //     $product_detail['item_number'] = $item_number;

        //     foreach($analysisService as $service){
        //         $analysisApi = new $service;
        //         $collection_id_column = $analysisApi->collectionColumnName();
        //         $column_prefix = $analysisApi->columnPrefix();
        //         $column_name = $column_prefix."id";

        //         $current_collection_id = DB::table('c_p_analysis_requests')->select($collection_id_column)->where('id', $id[0]->$column_name)->get();
        //         if($this->category_id != null){
        //             $target_collection = DB::table('analysis_category')->select($collection_id_column)->where('id', $this->category_id)->get();
        //             $target_collection_id = $target_collection[0]->$collection_id_column;
        //         }
        //         else{
        //             $target_collection = DB::table('c_p_analysis_requests')->select($collection_id_column)->where('user_id', $this->user_id)->whereNotNull($collection_id_column)->where('default', 1)->orderBy('id', 'desc')->first();
        //             $target_collection_id = $target_collection->$collection_id_column;
        //         }
        //         $final_id = DB::table('c_p_analysis_requests')->select('id')->where($collection_id_column, $target_collection_id)->get();
        //         $request_id_column = $column_prefix."request_id";

        //         $request_id = $id[0]->$request_id_column ? $id[0]->$request_id_column : $analysisApi->findRequest($current_collection_id[0]->$collection_id_column, $item_number);

        //         if($request_id){
        //             $response = $analysisApi->deleteRequest($current_collection_id[0]->$collection_id_column, $request_id);
        //             $res = $analysisApi->addProducts([$product_detail], $target_collection_id);
        //             if($res){
        //                 $transferred_ids[] = $product;
        //                 DB::table('analysis_products')->where('id', $product)->update(
        //                     [
        //                         $column_name => $final_id[0]->id,
        //                         $request_id_column => null
        //                     ]
        //                 );
        //             }
        //         }
        //     }
        // }

        // $updateProduct = AnalysisProduct::whereIn('id',$transferred_ids)
        //     ->where('user_id', $this->user_id)
        //     ->update(['category_id' => $this->category_id]);

        // return response()->json([
        //     'success' => true,
        //     'message' => 'Product transfer successfully!'
        // ]);

        // if($updateProduct){
        //     return response()->json([
        //         'success' => true,
        //         'message' => 'Product transfer successfully!'
        //     ]);
        // }else{
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'Something went wrong!'
        //     ]);
        // }
    }
}
