<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CampaignMailJobAfterInserting implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $connectionName = 'dropfunnel';
    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;

    protected int $tag_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($tag_id)
    {
        $this->tag_id = $tag_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app('App\Http\Controllers\NewShopSyncController')->sendCampaignMailJobAfterInserting($this->tag_id);

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Campaign Mail Job After User Registration for Tag: ' . $this->tag_id];
    }

}
