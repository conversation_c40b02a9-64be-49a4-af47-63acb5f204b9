<?php

namespace App\Jobs;

use App\Enums\CreditType;
use App\Models\Product\AnalysisProduct;
use App\ProductPriceApi;
use App\Services\ProductApi\ProductApi;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class CpProductUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $productIds;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($productIds)
    {
        $this->productIds = $productIds;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {

        foreach ($this->productIds as $productId) {
            // dd('coming',$productId);
            $product = AnalysisProduct::find($productId);
            $this->manualUpdateProductBulk($product->ean,$product->user_id);

        }

        } catch(\Exception $e) {}
    }

    private function getSelectedDomains($user_id): array
    {
        $columns = $this->getSelectedColumns($user_id);

        $domainMapping = [
            'ebay' => 'ebay.de',
            'amazon' => 'amazon.de',
            'googleshopping' => 'google.de'
        ];
        return array_values(array_intersect_key($domainMapping, array_flip($columns)));
    }

    private function getSelectedColumns($user_id)
    {
        $columns = DB::table('drm_user_saved_columns')->where([
            'user_id' => $user_id,
            'table_name' => 'analysis_products'
        ])->value('columns');
        return json_decode($columns,true) ?? array();
    }

    public function manualUpdateProductBulk($ean, $user_id)
    {
        $domains = $this->getSelectedDomains($user_id);
        foreach($domains as $domain){
            $this->updateProduct($ean,$domain);
        }
        $this->updateEKPriceQuantities($ean,$user_id);
        $msg = 'manual analysis product update';
        $this->creditDeductionForAnalysis($user_id, 1, $msg);
    }

    public function updateProduct($ean, $domain){
        $domain_column = (strpos($domain, 'ebay') !== false) ? 'ebay' : 'amazon';
        $price_column = $domain_column.'_price';
        $rating_column = $domain_column.'_rating';
        $rating_count_column = $domain_column.'_rating_count';
        $product_number_column = $domain_column.'_product_number';
        $last_sync_column = $domain_column.'_last_sync';
        $also_bought_column = $domain_column.'_also_bought';

        $productApi = new ProductApi($domain);
        $product = $productApi->search('ean', $ean)->fetch();

        $price = 0;
        $rating = 0;
        $isPrime = false;

        if($product->price() || $product->offerPrice() || $product->rating() || $product->productNumber() || $product->ratingCount() || $product->title() || $product->sellerName() || $product->sellerLink() || $product->isPrime() || $product->getOtherSeller()){

            if($product->price()){
                $price = $product->price();
            }

            if($product->title()){
                $title = $product->title();
            }

            // elseif($product->offerPrice()){
            //     $price = $product->price();
            // }

            if($product->rating()){
                $rating = $product->rating();
            }

            if($product->productNumber()){
                $productNumber = $product->productNumber();
            }

            if($product->ratingCount()){
                $ratingCount = $product->ratingCount();
            }

            if($product->image()){
                $image = $product->image();
            }

            if($product->sellerName()){
                $sellerName = $product->sellerName();
            }

            if($product->sellerLink()){
                $sellerLink = $product->sellerLink();
            }

            if($product->isPrime()){
                $isPrime = $product->isPrime();
            }

            if($product->getOtherSeller()){
                $otherSeller = $product->getOtherSeller();
            }
            // $price = (float)$price / 100;

            // if($domain_column === 'amazon')
            // {
            //     $price = $product->offerPrice();
            // }

            if($product->getPeopleAlsoBought()){
                $alsoBought = $product->getPeopleAlsoBought();
            }

            if($product->getProductSold()){
                $productSold = $product->getProductSold();
            }

            $analysis_product = AnalysisProduct::where('ean', $ean)->first();

            if(strlen($productNumber) > 0){
                DB::table('amazon_asin_collections')->updateOrInsert(
                    [
                        'ean' => $ean,
                        'domain' => 3
                    ],
                    [
                        'asin' => $productNumber
                    ]
                );
            }
            if($domain == 'amazon.de'){
                AnalysisProduct::where('ean', $ean)->update([
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    'amazon_seller_name' => $sellerName,
                    'amazon_seller_link' => $sellerLink,
                    'is_prime' => $isPrime,
                    'amazon_other_sellers' => $otherSeller,
                    $also_bought_column => $alsoBought,
                    $last_sync_column => now(),
                ]);
            }else{
                AnalysisProduct::where('ean', $ean)->update([
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    'ebay_product_sold' => $productSold,
                    $last_sync_column => now(),
                ]);
            }

            if($analysis_product->image == null){
                AnalysisProduct::where('ean', $ean)->update([
                    'image' => json_encode([$image])
                ]);
            }
            if($analysis_product->title == null){
                AnalysisProduct::where('ean', $ean)->update([
                    'title' => $title
                ]);
            }
            $addPrices[] = [
                'ean' => $ean,
                'source' => $domain,
                'title' => $title,
                'price' => $price,
                'rating' => $rating,
                'rating_count' => $ratingCount,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
            ProductPriceApi::insert($addPrices);
        }
        else{
            AnalysisProduct::where('ean', $ean)->update([
                $last_sync_column => now(),
            ]);
        }

        return $productNumber;
    }

    private function updateEKPriceQuantities($ean,$user_id)
    {

        $drm_product = DB::table('drm_products')->where('ean',$ean)->where('user_id',$user_id)->first();

        if($drm_product){
            $ek_price = $drm_product->ek_price;


            DB::table('analysis_products')->where('ean',$ean)->where('user_id',$user_id)->update([
                'purchase_price' => $ek_price,
                'manual_update' => now()
            ]);
        }else{

            DB::table('analysis_products')->where('ean',$ean)->where('user_id',$user_id)->update([
                'manual_update' => now()
            ]);

        }
    }

    public function creditDeductionForAnalysis($user_id, $total_credit_deduction, $msg) {
        $status = \App\Enums\CreditType::CREDIT_REMOVE;

        // app('App\Http\Controllers\tariffController')->CreditUpdate($user_id, $total_credit_deduction, 'credit_deduct');
        // app('App\Http\Controllers\tariffController')->drmUserCreditAdd($user_id, $total_credit_deduction, $msg, CreditType::PRODUCT_ANALYSIS, $status);

        (new \App\Services\Tariff\Credit\ChargeCredit)->charge($user_id, $total_credit_deduction, \App\Services\Tariff\Credit\CreditType::MANUAL_ANALYSIS_PRODUCT_UPDATE);
    }


}
