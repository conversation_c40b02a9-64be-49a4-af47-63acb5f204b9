<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\User;

use App\Notifications\DRMNotification;
use Carbon\Carbon;
use DB;

class OverdueInvoiceNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $invoices = DB::table('upcoming_invoices')->join('cms_users', 'cms_users.id', '=', 'upcoming_invoices.user_id')->where('upcoming_invoices.status', 'unpaid')->whereDate('upcoming_invoices.due_date', '<', Carbon::now())->select('cms_users.name as user_name', 'cms_users.email as user_email', 'upcoming_invoices.*')->get();

        if($invoices){
            foreach($invoices as $invoice){

                // checking for pro app is purchased or not
                if(checkUserHasAppBoolean(36, $invoice->user_id)){
                    User::find($invoice->user_id)->notify(new DRMNotification('We hereby inform you that invoice '. $invoice->invoice_number .' is overdue. It was deposited with the due date '. date('Y-m-d H:i:s', strtotime($invoice->due_date)) .'. Please check this and arrange for payment.','ORDER_MODULE'));                    
                }
            }
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {   
        return ['Invoice overdue - Notification'];
    }
}
