<?php

namespace App\Jobs\SuspendUser;

use App\Models\ChannelProduct;
use App\Services\ChannelProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemoveProducts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 7200;
    public $tries = 2;
    public $retryAfter = 7280;
    public $ids = [];
    public $user;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $shopProducts = ChannelProduct::where([
            'user_id' => $this->user,
        ])->get()->groupBy('shop_id');

        foreach ($shopProducts as $shop_id => $products) {
            foreach ($products->chunk(500) as $items) {
                if ($shop_id){
                    app(ChannelProductService::class)->queueDeleteChannelProducts($items->pluck('id')->toArray(),$shop_id,$this->user);
                }
            }
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Removing products due to tariff expiry. User ID: '.$this->user];
    }
}
