<?php

namespace App\Jobs\SuspendUser;

use Exception;
use Illuminate\Bus\Queueable;
use App\Models\ChannelProduct;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\SerializesModels;
use App\Services\ChannelProductService;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\ProductApi\TransferProduct;

class DisconnectProducts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 7200;
    public $tries = 2;
    public $retryAfter = 7280;
    public int $user;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        try {
        $shopProducts = ChannelProduct::where([
            'user_id' => $this->user,
            'is_connected' => true
        ])->get()->groupBy('shop_id');

        foreach ($shopProducts as $shop_id => $products) {
            if($shop_id){
                foreach ($products->chunk(500) as $items) {
                    app(ChannelProductService::class)->deleteShopProducts($items->pluck('id')->toArray(),$this->user,$shop_id);
                }
            }
        }

        // Remove group products from Countdown,Rainforest,GoogleShopping
        $user_requests = DB::table('cp_analysis_user_requests')->where('user_id',$this->user)->get();
        if($user_requests->count()){
            foreach($user_requests as $req){
                app(TransferProduct::class)->deleteIntervals($req->id, $this->user);
            }
        }

        } catch(\Exception $e) {}
    }

    public function tags(): array
    {
        return ['Disconnecting products due to tariff expiry. User ID: '.$this->user];
    }
}
