<?php

namespace App\Jobs\Supplier;

use App\Services\Modules\Import\ImportService;
use App\Services\SupplierService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DeleteImportFeed implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $feed_id;
    protected int $user_id;

    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($feed_id,$user_id)
    {
        $this->user_id = $user_id;
        $this->feed_id = $feed_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(ImportService::class)->destroy($this->feed_id,$this->user_id);

        } catch(\Exception $e) {}
    }

    public function tags(): array
    {
        return ['Import feed delete'];
    }
}
