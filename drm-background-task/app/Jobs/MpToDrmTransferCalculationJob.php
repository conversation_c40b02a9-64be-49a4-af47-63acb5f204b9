<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\DB;

class MpToDrmTransferCalculationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    { 
        try {
        Product::join('mp_core_drm_transfer_products', 'marketplace_products.id', '=', 'mp_core_drm_transfer_products.marketplace_product_id')
        ->select('marketplace_products.id', 'marketplace_products.total_no_transferred', 'marketplace_products.old_total_no_transferred', 'mp_core_drm_transfer_products.marketplace_product_id', DB::raw('count(mp_core_drm_transfer_products.marketplace_product_id) as total_transfer_count'))
        ->groupBy('mp_core_drm_transfer_products.marketplace_product_id')
        ->chunk(1000, function($products){
            foreach($products as $product){
                
                $current_value = $product->total_no_transferred;
                $total_count = $product->total_transfer_count;

                if($current_value != $total_count){
                    $product->total_no_transferred = $total_count;
                    $product->old_total_no_transferred = $current_value;

                    unset($product->marketplace_product_id); // For update "marketplace_product" table remove column from "mp_core_drm_transfer_products" table
                    unset($product->total_transfer_count); // For update "marketplace_product" table remove column from "mp_core_drm_transfer_products" table

                    $product->save();
                }

            }
        });

        } catch(\Exception $e) {}
    }
}
