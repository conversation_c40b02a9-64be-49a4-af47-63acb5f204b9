<?php

namespace App\Jobs\DeactiveUser;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\DeactiveUser\BackupUserData;
use App\Services\DeactiveUser\ReportTrait;

class BackupCustomerData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ReportTrait;

    public $connectionName = 'database-long-running';
    public $timeout = 7200;
    public $tries = 2;
    public $retryAfter = 7280;

    //user id
    protected $user_id;
    protected $uuid;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id, $uuid)
    {
        $this->user_id = $user_id;
        $this->uuid = $uuid;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{

            $backup = new BackupUserData($this->user_id, $this->uuid);
            $backup->storeToCloud();
            
        }catch(\Exception $e){
            $this->sendReport($this->user_id, $this->uuid, $e->getMessage(), 0);
        }
    }

    //Tags
    public function tags()
    {
        return ['Backup user data. ID: '.$this->user_id.' UUID'. $this->uuid];
    }
}
