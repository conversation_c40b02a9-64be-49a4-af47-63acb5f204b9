<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use App\Services\Modules\Product\ProductBundle;

class UpdateProductBundle implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $product_ids;
    protected $user_id;
    
    public $timeout = 0;
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($product_ids,$user_id)
    {
        $this->product_ids = $product_ids ?? [];
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $service = new ProductBundle();

        foreach($this->product_ids as $id)
        {
            $service->updateProductBundles($id,$this->user_id);
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Update product bundle'];
    }
}
