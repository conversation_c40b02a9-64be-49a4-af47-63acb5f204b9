<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use Exception;
use Carbon\Carbon;
use Log;

class NotificationClearHistory implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $is_clear = DB::table('notifications')->delete();
            if($is_clear){
                DB::table('notification_clear_history')->insert([
                    "last_clear" => now(),
                    "created_at" => now(),
                    "updated_at" => now()
                ]);
            }

        } catch (Exception $e) {
            Log::channel('command')->info("NotificationClearHistory");
            Log::channel('command')->info($e);
        }
    }
}
