<?php

namespace App\Jobs;

use App\Enums\ProductType;
use App\KeepaProduct;
use App\MarketplaceProducts;
use App\Services\ProductTransferService;
use App\TrendCategories;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TrendImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 7190;
    public $tries = 5;
    public $retryAfter = 6;
    protected $user_id;
    protected $keepa_cat_id;
    protected $package_limit;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id, $keepa_cat_id, $package_limit)
    {
        $this->user_id = $user_id;
        $this->keepa_cat_id = $keepa_cat_id;
        $this->package_limit = $package_limit;
        _log("TI: INIT-----");
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
//        ini_set("memory_limit", -1);
//
//        // marketplace_ct_id
//        $products = KeepaProduct::select('category_id', 'ean')
//        ->whereNotNull('ean')
//        ->where('ean', '!=', 'null')
//        ->where('category_id', $this->keepa_cat_id)
//        ->take($this->package_limit)
//        ->get()->toArray();
//        $ean_list = array_column($products, 'ean');
//        $ean_list = array_merge(...$ean_list);
//
//        #match keepa ean with marketplace
//        $marketplace_products = MarketplaceProducts::select('id', 'name', 'category_id')
//        ->whereIn('ean', $ean_list)
//        ->take($this->package_limit)
//        ->get();
//
//        $match_count = count($marketplace_products);
//        $remaining = ( $this->package_limit - $match_count ) < 0 ? 0 : ($this->package_limit - $match_count);
//
//        #remaining product will be imported from similar category
//        $trendProducts = TrendCategories::select('marketplace_cat_id', 'keepa_cat_id')
//        ->where('keepa_cat_id', $this->keepa_cat_id)->first();
//
//        $mpProducts = MarketplaceProducts::select('id', 'name', 'category_id')
//        ->where('category_id', $trendProducts->marketplace_cat_id)
//        ->limit($remaining)->get();
//
//
//        $country_id = app('App\Services\UserService')->getProductCountry($mpProducts[0]->supplier_id);
//        $lang = app('App\Services\UserService')->getProductLanguage($country_id);
//
//        $vars = [
//            'country_id' => $country_id,
//            'lang' => $lang,
//            'marketplace_cat_id' => $trendProducts->marketplace_cat_id,
//        ];
//
//        $data_override = [
//            'product_type' => ProductType::TREND_IMPORT,
//            'user_id' => $this->user_id
//        ];
//        $PTS = new ProductTransferService();
//
//        if(!empty($marketplace_products)) {
//            $PTS->toDrm($marketplace_products, $this->user_id, $data_override, $vars);
//            // foreach ($marketplace_products as $key => $product) {
//                // _log("In keepa matched product loop.... $product->id");
//                // _log(".......... $product->id");
//                // if($import){
//                    // TrendImportTrack::where([
//                    //     'user_id' => $this->user_id,
//                    //     'keepa_cat_id' => $this->keepa_cat_id,
//                    // ])->increment('fetched');
//                // }
//            // }
//        }
//
//        if(!empty($mpProducts)){
//            $PTS->toDrm($mpProducts, $this->user_id, $data_override, $vars);
//            // foreach ($mpProducts as $key => $product) {
//            //     _log("In CAT Match Product loop.... $product->id");
//            //     _log(".......... $product->id");
//                // if($import){
//                //     TrendImportTrack::where([
//                //         'user_id' => $this->user_id,
//                //         'keepa_cat_id' => $this->keepa_cat_id,
//                //     ])->increment('fetched');
//                // }
//            // }
//        }
    }
}
