<?php

namespace App\Jobs;

use DB;
use App\NewOrder;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\ChannelProduct;
use App\ProductMigration\MigrationHelper;

class UpdateProductRqNew
{
    public $carts;
    public $user_id;
    public $shop_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($carts, $user_id, $shop_id)
    {
        $this->carts = $carts;
        $this->user_id = $user_id;
        $this->shop_id = $shop_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $rq_enabled = @get_option_drm('rq_enabled', 'rq', $this->user_id);
        if( $rq_enabled ) {
            if( $rq_enabled->option_value == 0 ) return false;
        }


        if(!empty($this->carts))
        {
            $shop = \App\Shop::where('id', $this->shop_id)->select('channel', 'country_id')->first();
            if(empty($shop)) return;

            $channel = $shop->channel;
            $countryId = $shop->country_id;

            foreach ($this->carts as $key => $cart) {
                $cart = (object) $cart;
                $ean = $cart->ean ?? null;
                $product_name = $cart->product_name ?? null;
                if(empty($product_name)) continue;

                $normalizeName = MigrationHelper::normalizeString($product_name);

                if(!empty($shop)) {
                    // 1 Total calcled order
                    $total_cancled_order = NewOrder::where('shop_id', $this->shop_id)
                        ->where('test_order', '!=', 1)
                        ->where('invoice_number', '>', 0)
                        ->whereNull('offer_number')
                        ->where(function ($query) {
                            $query->whereIn('status', ['mp_return', 'Retoure eingegangen'])
                                ->orWhere(function ($q) {
                                    $q->where('insert_type', 9)->where('order_id_api', 'like', 'mp-return%'); // shipcloud return label purchased
                                });
                        })
                        ->where('cms_user_id', $this->user_id)
                        ->where(function($q) use ($ean, $product_name) {
                            if($ean) {
                                $q->whereJsonContains('cart', ['ean' => $ean])
                                ->orWhereJsonContains('cart', ['product_name' => $product_name]);
                            }else{
                                $q->whereJsonContains('cart', ['product_name' => $product_name]);
                            }
                        })
                    ->count();
                    
                    if($total_cancled_order) {
                        // 2 user total shop product order
                        $total_prod_order = NewOrder::where('cms_user_id', $this->user_id)
                            ->where('shop_id', $this->shop_id)
                            ->where('test_order', '!=', 1)
                            ->where('invoice_number', '>', 0)
                            ->whereNull('offer_number')
                            ->where('credit_number', 0)
                            ->whereNotIn('status', ['mp_return', 'Retoure eingegangen'])
                            ->where(function ($query) {
                                $query->where('insert_type', '<>', 9)->where('order_id_api', 'not like', 'mp-return%');
                            })
                            ->where(function($q) use ($ean, $product_name) {
                                if($ean) {
                                    $q->whereJsonContains('cart', ['ean' => $ean])
                                    ->orWhereJsonContains('cart', ['product_name' => $product_name]);
                                }else{
                                    $q->whereJsonContains('cart', ['product_name' => $product_name]);
                                }
                            })
                        ->count();

                        //Calculate rq
                        $rq = 100;
                        if ($total_prod_order) {
                            $total_order = $total_cancled_order + $total_prod_order;
                            $rq = round(( ($total_cancled_order / $total_order) * 100), 2 );
                        }

                        // 3 Update channel products RQ
                        DB::table('shop_products')
                            ->leftJoin('channel_product_localizations as locale', function ($join) {
                                $join->on('shop_products.id', '=', 'locale.channel_product_id');
                            })
                            ->where('shop_products.user_id', $this->user_id)
                            ->where('shop_products.shop_id', $this->shop_id)
                            ->where(function($q) use ($ean, $normalizeName) {
                                if($ean) {
                                    $q->where('shop_products.ean', $ean)
                                    ->orWhere('locale.title_normalized', $normalizeName);
                                }else{
                                    $q->where('locale.title_normalized', $normalizeName);
                                }
                            })
                            ->update(['shop_products.rq' => $rq, 'shop_products.updated_at' => now()]);
                    }
                }

                // 4 Total calcled order
                $total_cancled_order = NewOrder::where('shop_id', $this->shop_id)
                    ->where('test_order', '!=', 1)
                    ->where('invoice_number', '>', 0)
                    ->whereNull('offer_number')
                    ->where(function ($query) {
                        $query->whereIn('status', ['mp_return', 'Retoure eingegangen'])
                            ->orWhere(function ($q) {
                                $q->where('insert_type', 9)->where('order_id_api', 'like', 'mp-return%');
                            });
                    })
                    ->where('cms_user_id', $this->user_id)
                    ->where(function($q) use ($ean, $product_name) {
                        if($ean) {
                            $q->whereJsonContains('cart', ['ean' => $ean])
                            ->orWhereJsonContains('cart', ['product_name' => $product_name]);
                        }else{
                            $q->whereJsonContains('cart', ['product_name' => $product_name]);
                        }
                    })
                ->count();

                if(!$total_cancled_order) continue;

                // 5 Total product order
                $total_prod_order = NewOrder::where('cms_user_id', $this->user_id)
                    ->where('shop_id', $this->shop_id)
                    ->where('test_order', '!=', 1)
                    ->where('invoice_number', '>', 0)
                    ->whereNull('offer_number')
                    ->where('credit_number', 0)
                    ->whereNotIn('status', ['mp_return', 'Retoure eingegangen'])
                    ->where(function ($query) {
                        $query->where('insert_type', '<>', 9)->where('order_id_api', 'not like', 'mp-return%');
                    })
                    ->where(function($q) use ($ean, $product_name) {
                        if($ean) {
                            $q->whereJsonContains('cart', ['ean' => $ean])
                            ->orWhereJsonContains('cart', ['product_name' => $product_name]);
                        }else{
                            $q->whereJsonContains('cart', ['product_name' => $product_name]);
                        }
                    })
                ->count();
                
                //Calculate rq
                $rq = 100;
                if ($total_prod_order) {
                    $total_order = $total_cancled_order + $total_prod_order;
                    $rq = round(( ($total_cancled_order / $total_order) * 100), 2 );
                }

                // 6 Update drm products RQ
                DB::table('drm_products_new')
                    ->leftJoin('drm_product_localizations as locale', function ($join) {
                        $join->on('drm_products_new.id', '=', 'locale.drm_product_id');
                    })
                    ->where('drm_products_new.user_id', $this->user_id)
                    ->where('drm_products_new.country_id', $countryId)
                    ->where(function($q) use ($ean, $normalizeName) {
                        if($ean) {
                            $q->where('drm_products_new.ean', $ean)
                            ->orWhere('locale.title_normalized', $normalizeName);
                        }else{
                            $q->where('locale.title_normalized', $normalizeName);
                        }
                    })
                    ->update(['drm_products_new.rq' => $rq, 'drm_products_new.updated_at' => now()]);
            }

        }

        } catch(\Exception $e) {}
    }
}
