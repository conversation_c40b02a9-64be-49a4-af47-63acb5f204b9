<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UserTemplateDelete implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $custom_ind_temp_id;

    public function __construct($temp_id)
    {
        $this->custom_ind_temp_id = $temp_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        app('App\Services\CustomIndustryTemplateService')->userTemplateDelete($this->custom_ind_temp_id);
    }

    public function tags()
    {
        return ['Main Custom Industry Template Delete, User - '.$this->custom_ind_temp_id];
    }
}
