<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OrderSyncMail extends Mailable
{
    use Queueable, SerializesModels;

    public $status;
    public $subject;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($order_type, $status)
    {
        $this->subject = 'DRM '.$order_type.' Sync';
        $this->status = $status;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject($this->subject)->view('emails.sync_order');
    }
}
