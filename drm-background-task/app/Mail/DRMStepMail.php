<?php

namespace App\Mail;

use App\DropfunnelCustomerTag;
use App\DropfunnelStepMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use App\DropfunnelTag;
use App\EmailMarketing;
use Illuminate\Support\Facades\DB;

use App\Services\Mailgun\MailgunOptions;
use App\Services\Mailgun\Dropfunel;
use App\MailGunWebHookHistory;
use Illuminate\Container\Container;
use Illuminate\Contracts\Mail\Factory as MailFactory;
use Exception;
use Illuminate\Support\Facades\Log;

class DRMStepMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels, MailgunOptions, Dropfunel;

    public $connectionName = 'dropfunnel';
    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $body;
    public $subject;
    public $mail_from;
    protected $info;
    protected $lastStep = false;
    
    public function __construct($info)
    {
        $this->body = $info['body'];
        $this->subject = $info['subject'];
        $this->mail_from = (isset($info['from']) && $info['from']) ? $info['from'] : null;

        $this->info = !empty($info['history']) ? $info['history'] : [];
        $this->lastStep = isset($info['last_step']) && $info['last_step'] ? $info['last_step'] : $this->lastStep;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->subject($this->subject);

        $data = $this->info;

        //Tracking email open and clicks
        $this->df_track();

        //add email tag
        $this->df_tags('drm_dropfunnel_email');

        $newData = [
            'user_id' => $data['user_id'],
            'customer_id' => $data['customer_id'],
            'step_mail_id' => $data['step_mail_id'],
            'campaign_id' => $data['campaign_id'],
            'campaign_name' => $this->subject,
            'email' => $data['email'],
        ];

        if ($this->lastStep) {
            $newData['last_step'] = 'step_mail';
        }
        //send variables
        $this->df_variables($newData);

        // if ($this->mail_from) {
        //     $this->from($this->mail_from);
        // }

        $customSmtp = false;
        // CSA SMTP
        if( $this->isCsaSender($this->mail_from) )
        {
            $this->from($this->mail_from);
            $this->mailer('campaign');
            $this->setHeader('X-CSA-Complaints', '<EMAIL>');
            $customSmtp = true;

        }else if($this->isDtSender($this->mail_from)){

            $this->from(config('mail.dt'));
        }
        else if($this->mail_from && in_array($data['user_id'], [3721, 212])) { // Franz: dubai-rendite.com
            $this->from($this->mail_from);
            $this->mailer('dropmatix_mailgun');
        }
        else{
    
            $this->from(config('mail.drm'));
        }

        if($customSmtp)
        {
            if(isset($data['unsubscribe_url']) && $unsubscribe_url = $data['unsubscribe_url'])
            {
                $this->setHeader('List-Unsubscribe-Post', 'List-Unsubscribe=One-Click');
                $this->setHeader('List-Unsubscribe', '<'.$unsubscribe_url.'>'); 
            }

            $this->body .= $this->trackingImage($newData);
        }

        return $this->html($this->body);
    }


    /**
     * Send the message using the given mailer.
     *
     * @param \Illuminate\Contracts\Mail\Factory|\Illuminate\Contracts\Mail\Mailer $mailer
     * @return void
     */
    public function send($mailer)
    {
        // Check mail validation
        if(!$this->emailValidate($this->info ?? [])) {
            dump('invalid', $this->info['email']);
            return;
        }

        try {
            $checkStepProcess = DB::table('temp_step_processes')
                ->where([
                    'campaign_id' => $this->info['campaign_id'],
                    'step_mail_id' => $this->info['step_mail_id'],
                    'customer_id' => $this->info['customer_id'],
                    'user_id' => $this->info['user_id'],
                ])
                ->exists();
        
            if ($checkStepProcess) {
                return;
            } else {
                $this->insertTempStepData();
            }

            return $this->withLocale($this->locale, function () use ($mailer) {
                Container::getInstance()->call([$this, 'build']);

                $mailer = $mailer instanceof MailFactory
                    ? $mailer->mailer($this->mailer)
                    : $mailer;

                $callback = $mailer->send($this->buildView(), $this->buildViewData(), function ($message) {
                    $this->buildFrom($message)
                        ->buildRecipients($message)
                        ->buildSubject($message)
                        ->runCallbacks($message)
                        ->buildAttachments($message);
                });

                $this->campaignHistoryData();
                $this->updateTempStepData();

                return $callback;
            });
        } catch (Exception $e) {
            Log::channel('command')->error('Step Mail Error Log Start');
            Log::channel('command')->error($e);
            Log::channel('command')->error('Step Mail Error Log End');
        }
    }

    // Tracking email
    private function campaignHistoryData()
    {
        $loginsert = 'step mail log inserting';
        try {
            $campaign_data = $history = [];
            $campaign_data['campaign_id'] = $this->info['campaign_id'];
            $campaign_data['customer_id'] = $this->info['customer_id'];
            $campaign_data['step_mail_id'] = $this->info['step_mail_id'];
            $campaign_data = array_filter($campaign_data);

            $step_has_history = DB::table('step_mail_sent_history')->where($campaign_data)->exists();
            if (!$step_has_history) {
                $campaign_data['user_id']    = $this->info['user_id'];
                $campaign_data['created_at'] = now();
                $campaign_data['updated_at'] = now();

                $history[] = $campaign_data;
                DB::table('step_mail_sent_history')->insert($history);
            } else {
                DB::table('step_mail_sent_history')
                    ->where([
                        'campaign_id'  => $this->info['campaign_id'],
                        'step_mail_id' => $this->info['step_mail_id'],
                        'customer_id'  => $this->info['customer_id'],
                        'user_id'      => $this->info['user_id'],
                    ])
                    ->update([
                        'updated_at' => Carbon::now(),
                    ]);
            }

        } catch (Exception $e) {
            $loginsert = 'step mail log exception inserting';
            Log::channel('command')->error('Step Mail History insert Log Start');
            Log::channel('command')->error($e);
            Log::channel('command')->error('Step Mail History insert Log End');
        }

        $incrementExp = 'Step mail complate increment';
        try {
            $step = DropfunnelStepMail::find($this->info['step_mail_id']);
            $incValue = empty($step->complete) ? 0 : $step->complete;
            $step->complete =  $incValue + 1;
            $step->save();

            if (!empty($step)) {
                $incrementExp = 'Step Mail Found With ID = '. $step->id.' After increment Complate is = '.$step->complete;
            }
            // DB::table('dropfunnel_step_mails')->where('id', $this->info['step_mail_id'])->increment('complete');
        } catch (Exception $e) {
            $incrementExp = 'Step Mail Complate increment error Log Start';
            Log::channel('command')->error('Step Mail Complate increment Log Start');
            Log::channel('command')->error($e);
            Log::channel('command')->error('Step Mail Complate increment Log End');
        }

        // Mailer Campaign
        try {
            if($this->mailer === 'campaign')
            {
                $payload = $this->info;
                $insert_data = [
                    'delivered' => now()->format('Y-m-d H:i:s'),
                    'customer_email' => $payload['email'],
                ];
                $check_arr = [
                    'user_id' => $payload['user_id'],
                    'customer_id' => $payload['customer_id'],
                    'campaign_id' => $payload['campaign_id'],
                    'step_id' => $payload['step_mail_id'],
                ];
                if(!(MailGunWebHookHistory::where($check_arr)->exists()))
                {
                    MailGunWebHookHistory::create(array_merge($check_arr, $insert_data));
                }
            }
        } catch(Exception $e) {}
    }


    public function tags()
    {
        $data = $this->info;
        $user_id = $data['user_id'];
        $customer_id = $data['customer_id'];
        $step_mail_id = $data['step_mail_id'];
        $campaign_id = $data['campaign_id'];
        return ['Campaign ID: ' . $campaign_id, 'User ID: ' . $user_id, 'Customer ID:' . $customer_id, 'Step Campaign ID: ' . $step_mail_id];
    }

    public function insertTempStepData(){
        try {
            DB::table('temp_step_processes')->insert([
                'campaign_id' => $this->info['campaign_id'],
                'step_mail_id' => $this->info['step_mail_id'],
                'customer_id' => $this->info['customer_id'],
                'user_id' => $this->info['user_id'],
                'status' => 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

        } catch(Exception $e) {
           
        }
    }

    public function updateTempStepData() {
        try {
            DB::table('temp_step_processes')
                ->where([
                    'campaign_id' => $this->info['campaign_id'],
                    'step_mail_id' => $this->info['step_mail_id'],
                    'customer_id' => $this->info['customer_id'],
                    'user_id' => $this->info['user_id'],
                ])
                ->update([
                    'status' => 1,
                    'updated_at' => Carbon::now(),
                ]);
        } catch(Exception $e) {

        }
    }
}
