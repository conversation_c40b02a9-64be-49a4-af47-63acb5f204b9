<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use App\DropfunnelTag;
use App\EmailMarketing;
use Illuminate\Support\Facades\DB;

use App\Services\Mailgun\MailgunOptions;
use App\Services\Mailgun\Dropfunel;
use App\MailGunWebHookHistory;
use Illuminate\Container\Container;
use Illuminate\Contracts\Mail\Factory as MailFactory;

class EmailMarketingTestMailable extends Mailable
{
    use Queueable, SerializesModels, MailgunOptions, Dropfunel;

    public $connectionName = 'dropfunnel';
    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $body;
    public $subject;
    public $mail_from;
    protected $info;
    protected $campaign_id;
    protected $lastStep = false;

    public function __construct($info)
    {
        $this->body=$info['body'];
        $this->subject=$info['subject'];
        $this->mail_from= (isset($info['from']) && $info['from'])? $info['from'] : null;
        $this->lastStep = (isset($info['last_step']) && $info['last_step']) ? $info['last_step'] : $this->lastStep;
        $this->info = $info;
        $this->campaign_id = $info['campaign_id'];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->subject($this->subject);

        $data = $this->info;

        //Tracking email open and clicks
        $this->df_track();

        //add email tag
        $this->df_tags('drm_dropfunnel_email');

        $newData = [
            'user_id' => $data['user_id'],
            'customer_id' => $data['customer_id'],
            'campaign_name' => $data['campaign_name'],
            'campaign_id' => $data['campaign_id'],
            'email' => $data['email'],
        ];

        //send variables
        $this->df_variables($newData);

        if($this->mail_from){
            $this->from($this->mail_from);
        }

        // CSA SMTP
        if( $this->isCsaSender($this->mail_from) )
        {
            $this->mailer('campaign');
            $this->setHeader('X-CSA-Complaints', '<EMAIL>');

            if(isset($data['unsubscribe_url']) && $unsubscribe_url = $data['unsubscribe_url'])
            {
                $this->setHeader('List-Unsubscribe-Post', 'List-Unsubscribe=One-Click');
                $this->setHeader('List-Unsubscribe', '<'.$unsubscribe_url.'>'); 
            }
        }

        return $this->html($this->body);
    }


    /**
     * Send the message using the given mailer.
     *
     * @param  \Illuminate\Contracts\Mail\Factory|\Illuminate\Contracts\Mail\Mailer  $mailer
     * @return void
     */
    public function send($mailer)
    {
        //Check already send
        $data = $this->info;
        $customer_id = $data['customer_id'];
        $campaign_id = $data['campaign_id'];

        \Log::channel('command')->info('Before sending email');

        // Check mail validation
        if(!$this->emailValidate($data, false)) {
            \Log::channel('command')->info('Invalid email');
            \Log::channel('command')->info($data);
            return;
        }

        usleep(100);

        \Log::channel('command')->info('Before sending email - API');
        return $this->withLocale($this->locale, function () use ($mailer) {
            Container::getInstance()->call([$this, 'build']);

            $mailer = $mailer instanceof MailFactory
                ? $mailer->mailer($this->mailer)
                : $mailer;

            $callback = $mailer->send($this->buildView(), $this->buildViewData(), function ($message) {
                $this->buildFrom($message)
                    ->buildRecipients($message)
                    ->buildSubject($message)
                    ->runCallbacks($message)
                    ->buildAttachments($message);
            });

            return $callback;
        });
    }
}
