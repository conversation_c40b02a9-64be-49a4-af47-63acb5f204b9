<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class DRMSEndMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $body;
    public $subject;
    public $mail_from;

    public function __construct($info)
    {
        $this->body = $info['body'];
        $this->subject = $info['subject'];
        $this->mail_from = (isset($info['from']) && $info['from']) ? $info['from'] : null;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if ($this->mail_from) {
            return $this->from($this->mail_from)->subject($this->subject)->html($this->body);
        } else {
            return $this->subject($this->subject)->html($this->body);
        }
    }
}
