<?php

namespace App\Listeners;

use App\Events\Progress;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class Progress
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  Progress  $event
     * @return void
     */
    public function handle(Progress $event)
    {
        //
    }
}
