<?php

namespace App\Providers;

use App\Services\DRM\DRMService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Queue;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobProcessing;
use App\NewCustomer;
use App\Observers\CustomerObserver;
use App\Services\MailSender\MailSender;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if(request()->notification_id!=null && request()->notification_id!=""){
            DB::table("notifications")->where("id",request()->notification_id)->update(['read_at'=>now()]);
        }

        if(request()->ref_id!=null && request()->ref_id!=""){
            session()->put('ref_id',request()->ref_id);
        }

        Schema::defaultStringLength(191);

        Blade::directive('jsvar', function ($expression) {
            return "<?php echo json_encode($expression); ?>";
        });

        //Newcustomer observer
        NewCustomer::observe(CustomerObserver::class);

    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('drm', function(){
            return new DRMService;
        });

        $this->app->singleton('drm.mailer', function(){
            return new MailSender;
        });
    }
}
