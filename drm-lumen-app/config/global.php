<?php

return [

//    'tax_for_invoice' => 21, //all stripe invoice
//    'csv_interval_app_id' => 45, // CSV app interval ID
    'interval_app_id' => 44, //Interval app id
    'interval_bronze_plan' => 12, //Interval Bronze plan id
    'interval_silver_plan' => 13, //Interval Silver plan id
    'interval_gold_plan' => 14, //Interval Gold plan id
    'interval_platinum_plan' => 15, //Interval Platinum plan id
    'zapier_app_id' => 54, //  zapier  App ID
//    'fabian_daily_account_id' => 98, //Fabian Daily account id
//    'channels' => [1, 2, 3, 4, 5, 6, 7, 8, 9],
//    'inbox_app_id' => 34,
//    'paywall_due_date' => 7,
//    'notification_positions' => [
//        1 => 'Kunden & Bestellungen',
//        2 => 'Products',
//        3 => 'Lieferanten',
//    ],
//    'invoice_translate_app_id' => 48, // CSV Invoice Translate App ID
//    'recuring_invoice_app_id' => 49, // Recuring Invoice App ID
//    'email_marketing_app_id' => 50, // Email marketing App ID
//    'upcoming_invoice_pro_app_id' => 36,
//    'image_backup_app' => 51,
//    'os' => [
//        '/windows phone 8/i' => 'Windows Phone 8',
//        '/windows phone os 7/i' => 'Windows Phone 7',
//        '/windows nt 10/i' => 'Windows 10',
//        '/windows nt 6.3/i' => 'Windows 8.1',
//        '/windows nt 6.2/i' => 'Windows 8',
//        '/windows nt 6.1/i' => 'Windows 7',
//        '/windows nt 6.0/i' => 'Windows Vista',
//        '/windows nt 5.2/i' => 'Windows Server 2003/XP x64',
//        '/windows nt 5.1/i' => 'Windows XP',
//        '/windows xp/i' => 'Windows XP',
//        '/windows nt 5.0/i' => 'Windows 2000',
//        '/windows me/i' => 'Windows ME',
//        '/win98/i' => 'Windows 98',
//        '/win95/i' => 'Windows 95',
//        '/win16/i' => 'Windows 3.11',
//        '/iphone/i' => 'iPhone',
//        '/ipod/i' => 'iPod',
//        '/ipad/i' => 'iPad',
//        '/android/i' => 'Android',
//        '/blackberry/i' => 'BlackBerry',
//        '/webos/i' => 'Mobile',
//        '/macintosh|mac os x/i' => 'Mac OS X',
//        '/mac_powerpc/i' => 'Mac OS 9',
//        '/linux/i' => 'Linux',
//        '/ubuntu/i' => 'Ubuntu',
//    ],
//    'browsers' => [
//        '/msie/i' => 'Internet Explorer',
//        '/firefox/i' => 'Firefox',
//        '/chrome/i' => 'Chrome',
//        '/opera/i' => 'Opera',
//        '/netscape/i' => 'Netscape',
//        '/maxthon/i' => 'Maxthon',
//        '/konqueror/i' => 'Konqueror',
//        '/safari/i' => 'Safari',
//        '/mobile/i' => 'Handheld Browser'
//    ],
//    'languages' => [
//        'ab' => 'Abkhazian',
//        'aa' => 'Afar',
//        'af' => 'Afrikaans',
//        'ak' => 'Akan',
//        'sq' => 'Albanian',
//        'am' => 'Amharic',
//        'ar' => 'Arabic',
//        'an' => 'Aragonese',
//        'hy' => 'Armenian',
//        'as' => 'Assamese',
//        'av' => 'Avaric',
//        'ae' => 'Avestan',
//        'ay' => 'Aymara',
//        'az' => 'Azerbaijani',
//        'bm' => 'Bambara',
//        'ba' => 'Bashkir',
//        'eu' => 'Basque',
//        'be' => 'Belarusian',
//        'bn' => 'Bengali',
//        'bh' => 'Bihari languages',
//        'bi' => 'Bislama',
//        'bs' => 'Bosnian',
//        'br' => 'Breton',
//        'bg' => 'Bulgarian',
//        'my' => 'Burmese',
//        'ca' => 'Catalan, Valencian',
//        'km' => 'Central Khmer',
//        'ch' => 'Chamorro',
//        'ce' => 'Chechen',
//        'ny' => 'Chichewa, Chewa, Nyanja',
//        'zh' => 'Chinese',
//        'cu' => 'Church Slavonic, Old Bulgarian, Old Church Slavonic',
//        'cv' => 'Chuvash',
//        'kw' => 'Cornish',
//        'co' => 'Corsican',
//        'cr' => 'Cree',
//        'hr' => 'Croatian',
//        'cs' => 'Czech',
//        'da' => 'Danish',
//        'dv' => 'Divehi, Dhivehi, Maldivian',
//        'nl' => 'Dutch, Flemish',
//        'dz' => 'Dzongkha',
//        'en' => 'English',
//        'eo' => 'Esperanto',
//        'et' => 'Estonian',
//        'ee' => 'Ewe',
//        'fo' => 'Faroese',
//        'fj' => 'Fijian',
//        'fi' => 'Finnish',
//        'fr' => 'French',
//        'ff' => 'Fulah',
//        'gd' => 'Gaelic, Scottish Gaelic',
//        'gl' => 'Galician',
//        'lg' => 'Ganda',
//        'ka' => 'Georgian',
//        'de' => 'German',
//        'ki' => 'Gikuyu, Kikuyu',
//        'el' => 'Greek (Modern)',
//        'kl' => 'Greenlandic, Kalaallisut',
//        'gn' => 'Guarani',
//        'gu' => 'Gujarati',
//        'ht' => 'Haitian, Haitian Creole',
//        'ha' => 'Hausa',
//        'he' => 'Hebrew',
//        'hz' => 'Herero',
//        'hi' => 'Hindi',
//        'ho' => 'Hiri Motu',
//        'hu' => 'Hungarian',
//        'is' => 'Icelandic',
//        'io' => 'Ido',
//        'ig' => 'Igbo',
//        'id' => 'Indonesian',
//        'ia' => 'Interlingua (International Auxiliary Language Association)',
//        'ie' => 'Interlingue',
//        'iu' => 'Inuktitut',
//        'ik' => 'Inupiaq',
//        'ga' => 'Irish',
//        'it' => 'Italian',
//        'ja' => 'Japanese',
//        'jv' => 'Javanese',
//        'kn' => 'Kannada',
//        'kr' => 'Kanuri',
//        'ks' => 'Kashmiri',
//        'kk' => 'Kazakh',
//        'rw' => 'Kinyarwanda',
//        'kv' => 'Komi',
//        'kg' => 'Kongo',
//        'ko' => 'Korean',
//        'kj' => 'Kwanyama, Kuanyama',
//        'ku' => 'Kurdish',
//        'ky' => 'Kyrgyz',
//        'lo' => 'Lao',
//        'la' => 'Latin',
//        'lv' => 'Latvian',
//        'lb' => 'Letzeburgesch, Luxembourgish',
//        'li' => 'Limburgish, Limburgan, Limburger',
//        'ln' => 'Lingala',
//        'lt' => 'Lithuanian',
//        'lu' => 'Luba-Katanga',
//        'mk' => 'Macedonian',
//        'mg' => 'Malagasy',
//        'ms' => 'Malay',
//        'ml' => 'Malayalam',
//        'mt' => 'Maltese',
//        'gv' => 'Manx',
//        'mi' => 'Maori',
//        'mr' => 'Marathi',
//        'mh' => 'Marshallese',
//        'ro' => 'Moldovan, Moldavian, Romanian',
//        'mn' => 'Mongolian',
//        'na' => 'Nauru',
//        'nv' => 'Navajo, Navaho',
//        'nd' => 'Northern Ndebele',
//        'ng' => 'Ndonga',
//        'ne' => 'Nepali',
//        'se' => 'Northern Sami',
//        'no' => 'Norwegian',
//        'nb' => 'Norwegian Bokmål',
//        'nn' => 'Norwegian Nynorsk',
//        'ii' => 'Nuosu, Sichuan Yi',
//        'oc' => 'Occitan (post 1500)',
//        'oj' => 'Ojibwa',
//        'or' => 'Oriya',
//        'om' => 'Oromo',
//        'os' => 'Ossetian, Ossetic',
//        'pi' => 'Pali',
//        'pa' => 'Panjabi, Punjabi',
//        'ps' => 'Pashto, Pushto',
//        'fa' => 'Persian',
//        'pl' => 'Polish',
//        'pt' => 'Portuguese',
//        'qu' => 'Quechua',
//        'rm' => 'Romansh',
//        'rn' => 'Rundi',
//        'ru' => 'Russian',
//        'sm' => 'Samoan',
//        'sg' => 'Sango',
//        'sa' => 'Sanskrit',
//        'sc' => 'Sardinian',
//        'sr' => 'Serbian',
//        'sn' => 'Shona',
//        'sd' => 'Sindhi',
//        'si' => 'Sinhala, Sinhalese',
//        'sk' => 'Slovak',
//        'sl' => 'Slovenian',
//        'so' => 'Somali',
//        'st' => 'Sotho, Southern',
//        'nr' => 'South Ndebele',
//        'es' => 'Spanish, Castilian',
//        'su' => 'Sundanese',
//        'sw' => 'Swahili',
//        'ss' => 'Swati',
//        'sv' => 'Swedish',
//        'tl' => 'Tagalog',
//        'ty' => 'Tahitian',
//        'tg' => 'Tajik',
//        'ta' => 'Tamil',
//        'tt' => 'Tatar',
//        'te' => 'Telugu',
//        'th' => 'Thai',
//        'bo' => 'Tibetan',
//        'ti' => 'Tigrinya',
//        'to' => 'Tonga (Tonga Islands)',
//        'ts' => 'Tsonga',
//        'tn' => 'Tswana',
//        'tr' => 'Turkish',
//        'tk' => 'Turkmen',
//        'tw' => 'Twi',
//        'ug' => 'Uighur, Uyghur',
//        'uk' => 'Ukrainian',
//        'ur' => 'Urdu',
//        'uz' => 'Uzbek',
//        've' => 'Venda',
//        'vi' => 'Vietnamese',
//        'vo' => 'Volap_k',
//        'wa' => 'Walloon',
//        'cy' => 'Welsh',
//        'fy' => 'Western Frisian',
//        'wo' => 'Wolof',
//        'xh' => 'Xhosa',
//        'yi' => 'Yiddish',
//        'yo' => 'Yoruba',
//        'za' => 'Zhuang, Chuang',
//        'zu' => 'Zulu'
//    ],
//    'countries' => [
//        "AF" => "Afghanistan",
//        "AL" => "Albania",
//        "DZ" => "Algeria",
//        "AS" => "American Samoa",
//        "AD" => "Andorra",
//        "AO" => "Angola",
//        "AI" => "Anguilla",
//        "AQ" => "Antarctica",
//        "AG" => "Antigua and Barbuda",
//        "AR" => "Argentina",
//        "AM" => "Armenia",
//        "AW" => "Aruba",
//        "AU" => "Australia",
//        "AT" => "Austria",
//        "AZ" => "Azerbaijan",
//        "BS" => "Bahamas",
//        "BH" => "Bahrain",
//        "BD" => "Bangladesh",
//        "BB" => "Barbados",
//        "BY" => "Belarus",
//        "BE" => "Belgium",
//        "BZ" => "Belize",
//        "BJ" => "Benin",
//        "BM" => "Bermuda",
//        "BT" => "Bhutan",
//        "BO" => "Bolivia",
//        "BA" => "Bosnia and Herzegovina",
//        "BW" => "Botswana",
//        "BV" => "Bouvet Island",
//        "BR" => "Brazil",
//        "IO" => "British Indian Ocean Territory",
//        "BN" => "Brunei Darussalam",
//        "BG" => "Bulgaria",
//        "BF" => "Burkina Faso",
//        "BI" => "Burundi",
//        "KH" => "Cambodia",
//        "CM" => "Cameroon",
//        "CA" => "Canada",
//        "CV" => "Cape Verde",
//        "KY" => "Cayman Islands",
//        "CF" => "Central African Republic",
//        "TD" => "Chad",
//        "CL" => "Chile",
//        "CN" => "China",
//        "CX" => "Christmas Island",
//        "CC" => "Cocos (Keeling) Islands",
//        "CO" => "Colombia",
//        "KM" => "Comoros",
//        "CG" => "Congo",
//        "CD" => "Congo, the Democratic Republic of the",
//        "CK" => "Cook Islands",
//        "CR" => "Costa Rica",
//        "CI" => "Cote D'Ivoire",
//        "HR" => "Croatia",
//        "CU" => "Cuba",
//        "CY" => "Cyprus",
//        "CZ" => "Czech Republic",
//        "DK" => "Denmark",
//        "DJ" => "Djibouti",
//        "DM" => "Dominica",
//        "DO" => "Dominican Republic",
//        "EC" => "Ecuador",
//        "EG" => "Egypt",
//        "SV" => "El Salvador",
//        "GQ" => "Equatorial Guinea",
//        "ER" => "Eritrea",
//        "EE" => "Estonia",
//        "ET" => "Ethiopia",
//        "FK" => "Falkland Islands (Malvinas)",
//        "FO" => "Faroe Islands",
//        "FJ" => "Fiji",
//        "FI" => "Finland",
//        "FR" => "France",
//        "GF" => "French Guiana",
//        "PF" => "French Polynesia",
//        "TF" => "French Southern Territories",
//        "GA" => "Gabon",
//        "GM" => "Gambia",
//        "GE" => "Georgia",
//        "DE" => "Germany",
//        "GH" => "Ghana",
//        "GI" => "Gibraltar",
//        "GR" => "Greece",
//        "GL" => "Greenland",
//        "GD" => "Grenada",
//        "GP" => "Guadeloupe",
//        "GU" => "Guam",
//        "GT" => "Guatemala",
//        "GN" => "Guinea",
//        "GW" => "Guinea-Bissau",
//        "GY" => "Guyana",
//        "HT" => "Haiti",
//        "HM" => "Heard Island and Mcdonald Islands",
//        "VA" => "Holy See (Vatican City State)",
//        "HN" => "Honduras",
//        "HK" => "Hong Kong",
//        "HU" => "Hungary",
//        "IS" => "Iceland",
//        "IN" => "India",
//        "ID" => "Indonesia",
//        "IR" => "Iran, Islamic Republic of",
//        "IQ" => "Iraq",
//        "IE" => "Ireland",
//        "IL" => "Israel",
//        "IT" => "Italy",
//        "JM" => "Jamaica",
//        "JP" => "Japan",
//        "JO" => "Jordan",
//        "KZ" => "Kazakhstan",
//        "KE" => "Kenya",
//        "KI" => "Kiribati",
//        "KP" => "Korea, Democratic People's Republic of",
//        "KR" => "Korea, Republic of",
//        "KW" => "Kuwait",
//        "KG" => "Kyrgyzstan",
//        "LA" => "Lao People's Democratic Republic",
//        "LV" => "Latvia",
//        "LB" => "Lebanon",
//        "LS" => "Lesotho",
//        "LR" => "Liberia",
//        "LY" => "Libyan Arab Jamahiriya",
//        "LI" => "Liechtenstein",
//        "LT" => "Lithuania",
//        "LU" => "Luxembourg",
//        "MO" => "Macao",
//        "MK" => "Macedonia, the Former Yugoslav Republic of",
//        "MG" => "Madagascar",
//        "MW" => "Malawi",
//        "MY" => "Malaysia",
//        "MV" => "Maldives",
//        "ML" => "Mali",
//        "MT" => "Malta",
//        "MH" => "Marshall Islands",
//        "MQ" => "Martinique",
//        "MR" => "Mauritania",
//        "MU" => "Mauritius",
//        "YT" => "Mayotte",
//        "MX" => "Mexico",
//        "FM" => "Micronesia, Federated States of",
//        "MD" => "Moldova, Republic of",
//        "MC" => "Monaco",
//        "MN" => "Mongolia",
//        "MS" => "Montserrat",
//        "MA" => "Morocco",
//        "MZ" => "Mozambique",
//        "MM" => "Myanmar",
//        "NA" => "Namibia",
//        "NR" => "Nauru",
//        "NP" => "Nepal",
//        "NL" => "Netherlands",
//        "AN" => "Netherlands Antilles",
//        "NC" => "New Caledonia",
//        "NZ" => "New Zealand",
//        "NI" => "Nicaragua",
//        "NE" => "Niger",
//        "NG" => "Nigeria",
//        "NU" => "Niue",
//        "NF" => "Norfolk Island",
//        "MP" => "Northern Mariana Islands",
//        "NO" => "Norway",
//        "OM" => "Oman",
//        "PK" => "Pakistan",
//        "PW" => "Palau",
//        "PS" => "Palestinian Territory, Occupied",
//        "PA" => "Panama",
//        "PG" => "Papua New Guinea",
//        "PY" => "Paraguay",
//        "PE" => "Peru",
//        "PH" => "Philippines",
//        "PN" => "Pitcairn",
//        "PL" => "Poland",
//        "PT" => "Portugal",
//        "PR" => "Puerto Rico",
//        "QA" => "Qatar",
//        "RE" => "Reunion",
//        "RO" => "Romania",
//        "RU" => "Russian Federation",
//        "RW" => "Rwanda",
//        "SH" => "Saint Helena",
//        "KN" => "Saint Kitts and Nevis",
//        "LC" => "Saint Lucia",
//        "PM" => "Saint Pierre and Miquelon",
//        "VC" => "Saint Vincent and the Grenadines",
//        "WS" => "Samoa",
//        "SM" => "San Marino",
//        "ST" => "Sao Tome and Principe",
//        "SA" => "Saudi Arabia",
//        "SN" => "Senegal",
//        "CS" => "Serbia and Montenegro",
//        "SC" => "Seychelles",
//        "SL" => "Sierra Leone",
//        "SG" => "Singapore",
//        "SK" => "Slovakia",
//        "SI" => "Slovenia",
//        "SB" => "Solomon Islands",
//        "SO" => "Somalia",
//        "ZA" => "South Africa",
//        "GS" => "South Georgia and the South Sandwich Islands",
//        "ES" => "Spain",
//        "LK" => "Sri Lanka",
//        "SD" => "Sudan",
//        "SR" => "Suriname",
//        "SJ" => "Svalbard and Jan Mayen",
//        "SZ" => "Swaziland",
//        "SE" => "Sweden",
//        "CH" => "Switzerland",
//        "SY" => "Syrian Arab Republic",
//        "TW" => "Taiwan, Province of China",
//        "TJ" => "Tajikistan",
//        "TZ" => "Tanzania, United Republic of",
//        "TH" => "Thailand",
//        "TL" => "Timor-Leste",
//        "TG" => "Togo",
//        "TK" => "Tokelau",
//        "TO" => "Tonga",
//        "TT" => "Trinidad and Tobago",
//        "TN" => "Tunisia",
//        "TR" => "Turkey",
//        "TM" => "Turkmenistan",
//        "TC" => "Turks and Caicos Islands",
//        "TV" => "Tuvalu",
//        "UG" => "Uganda",
//        "UA" => "Ukraine",
//        "AE" => "United Arab Emirates",
//        "GB" => "United Kingdom",
//        "US" => "United States",
//        "UM" => "United States Minor Outlying Islands",
//        "UY" => "Uruguay",
//        "UZ" => "Uzbekistan",
//        "VU" => "Vanuatu",
//        "VE" => "Venezuela",
//        "VN" => "Viet Nam",
//        "VG" => "Virgin Islands, British",
//        "VI" => "Virgin Islands, U.s.",
//        "WF" => "Wallis and Futuna",
//        "EH" => "Western Sahara",
//        "YE" => "Yemen",
//        "ZM" => "Zambia",
//        "ZW" => "Zimbabwe"
//    ]

    "drm_base_url" => env('DRM_BASE_URL', 'https://drm.team')
];
