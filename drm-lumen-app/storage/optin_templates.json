[{"id": "11", "image": "https://drm.software/images/5.png", "source": "<style type=\"text/css\">iframe#_hjRemoteVarsFrame {display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;} .form-control{ margin-bottom: 10px !important;}.ktv2-form-chicklet{text-align:center;color:white;}</style><div id=\"formbuilder-preview\">\r\n                                            <div id=\"form-223115-wrapper\" class=\"ktv2-form style-gradient size-medium has-chicklet resizable\" style=\"width: 300px;\">\r\n                                                <div class=\"ktv2-form-body resizers\" id=\"ktv2-form-body\" style=\"background: linear-gradient(rgb(78, 107, 29) 0%, rgba(17, 187, 0, 0) 100%) transparent;\">\r\n                                                    <div class=\"ktv2-form-body-bg\" id=\"ktv2-form-body-bg\" style=\"padding: 20px;margin-top:45px; background: linear-gradient(rgb(128, 157, 79) 0%, rgb(78, 107, 29) 100%) transparent;\">\r\n                                                        <div class=\"ktv2-form-body-border\">\r\n                                                            <div class=\"ktv2-form-element\">\r\n                                                                <label style=\"display: none;\" for=\"FormField_EmailAddress_1\"></label>\r\n                                                                <input type=\"text\" id=\"FormField_EmailAddress_1\"  class=\"form-control\" name=\"opln[email]\" value=\"\" class=\"ktv2-form-element-textfield form-control\" placeholder=\"E-Mail-Adresse\" data-type=\"email\">\r\n                                                                                                                  <input id=\"FormField_EmailAddress_3\" value=\"\" class=\"ktv2-form-element-textfield form-control\" name=\"opln[name]\"  class=\"form-control\" type=\"text\" placeholder=\"Your phone please\" data-type=\"text\">      <input id=\"FormField_EmailAddress_3\" value=\"\" class=\"ktv2-form-element-textfield form-control\" name=\"opln[name]\"  class=\"form-control\" type=\"text\" placeholder=\"Your name please\" data-type=\"text\"></div>\r\n                                                        </div>\r\n                                                        <div id=\"ktv2-form-dsgvocheckbox\" style=\"display: none; text-align: left; color: #fff;\">\r\n                                                            <input type=\"checkbox\" id=\"DSGVOCheckbox\" name=\"opln[DSGVOCheckbox]\" style=\"position: absolute;\">\r\n                                                            <label for=\"DSGVOCheckbox\" id=\"DSGVOCheckboxL\" style=\"\"></label>\r\n                                                        </div>\r\n\r\n                                                        <div class=\"ktv2-submit-element\">\r\n                                                            <div class=\"ktv2-submit-element-bg button-image\">\r\n \r\n    <input class=\"form-control\" type=\"submit\" id=\"itemId6\" name=\"FormSubmit\" value=\"Absenden\" style=\"\r\n    background-color: #fd6500;\r\n  text-align:center;\r\n    color: white;\r\n    border: navajowhite;\r\n    margin-top: 8px;\r\n\">                                                       </div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div class=\"resizer bottom-right\" id=\"currentResizer\"></div>\r\n                                                </div>\r\n                                                <div class=\"ktv2-form-footer\" style=\"background: rgb(48, 77, 0);\">\r\n                                                    <div class=\"ktv2-form-chicklet\" id=\"ktv2-form-chicklet\"><span class=\"ktchicklet\">0</span> Eintragungen</div>\r\n                                                </div>\r\n                                                \r\n                                                \r\n                                                \r\n                                                \r\n                                            </div>\r\n                                        </div>"}, {"id": "2", "image": "https://drm.software/images/1.png", "source": "<style>body {\r\n  align-items: center;\r\n  background-color: #000;\r\n  display: flex;\r\n  justify-content: center;\r\n  height: 100vh;\r\n}\r\n\r\n.form {\r\n  background-color: #15172b;\r\n  border-radius: 20px;\r\n  box-sizing: border-box;\r\n  height: 500px;\r\n  padding: 20px;\r\n  width: 320px;\r\n}\r\n\r\n.title {\r\n  color: #eee;\r\n  font-family: sans-serif;\r\n  font-size: 36px;\r\n  font-weight: 600;\r\n  margin-top: 30px;\r\n}\r\n\r\n.subtitle {\r\n  color: #eee;\r\n  font-family: sans-serif;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  margin-top: 10px;\r\n}\r\n\r\n.input-container {\r\n  height: 50px;\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.ic1 {\r\n  margin-top: 40px;\r\n}\r\n\r\n.ic2 {\r\n  margin-top: 30px;\r\n}\r\n\r\n.input {\r\n  background-color: #303245;\r\n  border-radius: 12px;\r\n  border: 0;\r\n  box-sizing: border-box;\r\n  color: #eee;\r\n  font-size: 18px;\r\n  height: 100%;\r\n  outline: 0;\r\n  padding: 4px 20px 0;\r\n  width: 100%;\r\n}\r\n\r\n.cut {\r\n  background-color: #15172b;\r\n  border-radius: 10px;\r\n  height: 20px;\r\n  left: 20px;\r\n  position: absolute;\r\n  top: -20px;\r\n  transform: translateY(0);\r\n  transition: transform 200ms;\r\n  width: 76px;\r\n}\r\n\r\n.cut-short {\r\n  width: 50px;\r\n}\r\n\r\n.input:focus ~ .cut,\r\n.input:not(:placeholder-shown) ~ .cut {\r\n  transform: translateY(8px);\r\n}\r\n\r\n.placeholder {\r\n  color: #65657b;\r\n  font-family: sans-serif;\r\n  left: 20px;\r\n  line-height: 14px;\r\n  pointer-events: none;\r\n  position: absolute;\r\n  transform-origin: 0 50%;\r\n  transition: transform 200ms, color 200ms;\r\n  top: 20px;\r\n}\r\n\r\n.input:focus ~ .placeholder,\r\n.input:not(:placeholder-shown) ~ .placeholder {\r\n  transform: translateY(-30px) translateX(10px) scale(0.75);\r\n}\r\n\r\n.input:not(:placeholder-shown) ~ .placeholder {\r\n  color: #808097;\r\n}\r\n\r\n.input:focus ~ .placeholder {\r\n  color: #dc2f55;\r\n}\r\n\r\n.submit {\r\n  background-color: #08d;\r\n  border-radius: 12px;\r\n  border: 0;\r\n  box-sizing: border-box;\r\n  color: #eee;\r\n  cursor: pointer;\r\n  font-size: 18px;\r\n  height: 50px;\r\n  margin-top: 38px;\r\n  // outline: 0;\r\n  text-align: center;\r\n  width: 100%;\r\n}\r\n\r\n.submit:active {\r\n  background-color: #06b;\r\n}\r\n  </style> \r\n <div class=\"form\">\r\n      <div class=\"title\">Welcome</div>\r\n      <div class=\"subtitle\">Let's create your account!</div>\r\n      <div class=\"input-container ic1\">\r\n        <input id=\"firstname\" class=\"input\" type=\"text\" placeholder=\" \" />\r\n        <div class=\"cut\"></div>\r\n        <label for=\"firstname\" class=\"placeholder\">First name</label>\r\n      </div>\r\n      <div class=\"input-container ic2\">\r\n        <input id=\"lastname\" class=\"input\" type=\"text\" placeholder=\" \" />\r\n        <div class=\"cut\"></div>\r\n        <label for=\"lastname\" class=\"placeholder\">Last name</label>\r\n      </div>\r\n      <div class=\"input-container ic2\">\r\n        <input id=\"email\" class=\"input\" type=\"text\" placeholder=\" \" />\r\n        <div class=\"cut cut-short\"></div>\r\n        <label for=\"email\" class=\"placeholder\">Email</>\r\n      </div>\r\n      <button type=\"text\" class=\"submit\">submit</button>\r\n    </div>"}, {"id": "3", "image": "https://drm.software/images/3.png", "source": "<style>@import url(\"https://fonts.googleapis.com/css?family=Fjalla+One&display=swap\");\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\nbody {\r\n  background: url(\"https://s3-us-west-2.amazonaws.com/s.cdpn.io/38816/image-from-rawpixel-id-2210775-jpeg.jpg\") center center no-repeat;\r\n  background-size: cover;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  display: grid;\r\n  align-items: center;\r\n  justify-items: center;\r\n}\r\n\r\n.contact-us {\r\n  background: #f8f4e5;\r\n  padding: 50px 100px;\r\n  border: 2px solid black;\r\n  box-shadow: 15px 15px 1px #ffa580, 15px 15px 1px 2px black;\r\n}\r\n\r\ninput {\r\n  display: block;\r\n  width: 100%;\r\n  font-size: 14pt;\r\n  line-height: 28pt;\r\n  font-family: \"Fjalla One\";\r\n  margin-bottom: 28pt;\r\n  border: none;\r\n  border-bottom: 5px solid black;\r\n  background: #f8f4e5;\r\n  min-width: 250px;\r\n  padding-left: 5px;\r\n  outline: none;\r\n  color: black;\r\n}\r\n\r\ninput:focus {\r\n  border-bottom: 5px solid #ffa580;\r\n}\r\n\r\nbutton {\r\n  display: block;\r\n  margin: 0 auto;\r\n  line-height: 28pt;\r\n  padding: 0 20px;\r\n  background: #ffa580;\r\n  letter-spacing: 2px;\r\n  transition: 0.2s all ease-in-out;\r\n  outline: none;\r\n  border: 1px solid black;\r\n  box-shadow: 3px 3px 1px #95a4ff, 3px 3px 1px 1px black;\r\n}\r\nbutton:hover {\r\n  background: black;\r\n  color: white;\r\n  border: 1px solid black;\r\n}\r\n\r\n::selection {\r\n  background: #ffc8ff;\r\n}\r\n\r\ninput:-webkit-autofill,\r\ninput:-webkit-autofill:hover,\r\ninput:-webkit-autofill:focus {\r\n  border-bottom: 5px solid #95a4ff;\r\n  -webkit-text-fill-color: #2A293E;\r\n  -webkit-box-shadow: 0 0 0px 1000px #f8f4e5 inset;\r\n  transition: background-color 5000s ease-in-out 0s;\r\n}</style>\r\n<div class=\"contact-us\">\r\n  <form>\r\n    <input placeholder=\"Name\" required=\"\" type=\"text\" /><input name=\"customerEmail\" placeholder=\"Email\" type=\"email\" /><input name=\"customerPhone\" pattern=\"[0-9]{3}-[0-9]{3}-[0-9]{4}\" placeholder=\"Phone\" type=\"tel\" /><button type=\"button\">SIGN UP</button>\r\n  </form>\r\n</div>"}, {"id": "4", "image": "https://drm.software/images/4.png", "source": "<style>#customerOrder{margin-top:5px;}.container{width:auto!important}input{margin-bottom: 2pt !important;}@import url(\"https://fonts.googleapis.com/css?family=Spartan&display=swap\");\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\nbody {\r\n  background: url(\"https://s3-us-west-2.amazonaws.com/s.cdpn.io/38816/image-from-rawpixel-id-2044837-jpeg.jpg\") center center no-repeat;\r\n  background-size: cover;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  display: grid;\r\n  align-items: center;\r\n  justify-items: center;\r\n  font-size: 12pt;\r\n  font-family: \"Spartan\";\r\n  color: #2A293E;\r\n}\r\n\r\n.contact-us {\r\n  background: #f8f4e5;\r\n  padding: 50px 100px;\r\n  border-top: 10px solid #f45702;\r\n}\r\n\r\nlabel, input, textarea {\r\n  display: block;\r\n  width: 100%;\r\n  font-size: 12pt;\r\n  line-height: 24pt;\r\n  font-family: \"Spartan\";\r\n}\r\n\r\ninput {\r\n  margin-bottom: 24pt;\r\n}\r\n\r\nh3 {\r\n  font-weight: normal;\r\n  font-size: 10pt;\r\n  line-height: 24pt;\r\n  font-style: italic;\r\n  margin: 0 0 0.5em 0;\r\n}\r\n\r\nspan {\r\n  font-size: 8pt;\r\n}\r\n\r\nem {\r\n  color: #f45702;\r\n  font-weight: bold;\r\n}\r\n\r\ninput, textarea {\r\n  border: none;\r\n  border: 1px solid rgba(0, 0, 0, 0.1);\r\n  border-radius: 2px;\r\n  background: #f8f4e5;\r\n  padding-left: 5px;\r\n  outline: none;\r\n}\r\n\r\ninput:focus, textarea:focus {\r\n  border: 1px solid #6bd4b1;\r\n}\r\n\r\ntextarea {\r\n  resize: none;\r\n}\r\n\r\nbutton {\r\n  display: block;\r\n  float: right;\r\n  line-height: 24pt;\r\n  padding: 0 20px;\r\n  border: none;\r\n  background: #f45702;\r\n  color: white;\r\n  letter-spacing: 2px;\r\n  transition: 0.2s all ease-in-out;\r\n  border-bottom: 2px solid transparent;\r\n  outline: none;\r\n}\r\nbutton:hover {\r\n  background: inherit;\r\n  color: #f45702;\r\n  border-bottom: 2px solid #f45702;\r\n}\r\n\r\n::selection {\r\n  background: #ffc7b8;\r\n}\r\n\r\ninput:-webkit-autofill,\r\ninput:-webkit-autofill:hover,\r\ninput:-webkit-autofill:focus,\r\ntextarea:-webkit-autofill,\r\ntextarea:-webkit-autofill:hover,\r\ntextarea:-webkit-autofill:focus {\r\n  border: 1px solid #6bd4b1;\r\n  -webkit-text-fill-color: #2A293E;\r\n  -webkit-box-shadow: 0 0 0px 1000px #f8f4e5 inset;\r\n  transition: background-color 5000s ease-in-out 0s;\r\n}</style>\r\n<div class=\"contact-us\">\r\n  <form action=\"#\">\r\n    <label for=\"customerName\">NAME <em>&#x2a;</em></label><input id=\"customerName\" name=\"customerName\" required=\"\" type=\"text\" /><label for=\"customerEmail\">EMAIL <em>&#x2a;</em></label><input id=\"customerEmail\" name=\"customerEmail\" required=\"\" type=\"email\" />\r\n    <label for=\"customerName\">Phone <em>&#x2a;</em></label><input id=\"customerName\" name=\"customerName\" type=\"text\" /><button style=\"margin-top:8px;\" id=\"customerOrder\">SUBMIT</button>\r\n  </form>\r\n</div>"}, {"id": "6", "image": "https://drm.software/images/6.png", "source": "<style>@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;900&display=swap');\r\n\r\nbody {\r\n  margin: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: #ecf0f3;\r\n  display: flex;\r\n  align-items: center;\r\n  text-align: center;\r\n  justify-content: center;\r\n  place-items: center;\r\n  overflow: hidden;\r\n  font-family: poppins;\r\n}\r\n\r\n.container {\r\n  position: relative;\r\n  width: 350px;\r\n  height: 500px;\r\n  border-radius: 20px;\r\n  padding: 40px;\r\n  box-sizing: border-box;\r\n  background: #ecf0f3;\r\n  box-shadow: 14px 14px 20px #cbced1, -14px -14px 20px white;\r\n}\r\n\r\n.brand-logo {\r\n  height: 100px;\r\n  width: 100px;\r\n  background: url(\"https://img.icons8.com/color/100/000000/twitter--v2.png\");\r\n  margin: auto;\r\n  border-radius: 50%;\r\n  box-sizing: border-box;\r\n  box-shadow: 7px 7px 10px #cbced1, -7px -7px 10px white;\r\n}\r\n\r\n.brand-title {\r\n  margin-top: 10px;\r\n  font-weight: 900;\r\n  font-size: 1.8rem;\r\n  color: #1DA1F2;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.inputs {\r\n  text-align: left;\r\n  margin-top: 30px;\r\n}\r\n\r\nlabel, input, button {\r\n  display: block;\r\n  width: 100%;\r\n  padding: 0;\r\n  border: none;\r\n  outline: none;\r\n  box-sizing: border-box;\r\n}\r\n\r\nlabel {\r\n  margin-bottom: 4px;\r\n}\r\n\r\nlabel:nth-of-type(2) {\r\n  margin-top: 12px;\r\n}\r\n\r\ninput::placeholder {\r\n  color: gray;\r\n}\r\n\r\ninput {\r\n  background: #ecf0f3;\r\n  padding: 10px;\r\n  padding-left: 20px;\r\n  height: 50px;\r\n  font-size: 14px;\r\n  border-radius: 50px;\r\n  box-shadow: inset 6px 6px 6px #cbced1, inset -6px -6px 6px white;\r\n}\r\n\r\nbutton {\r\n  margin-top: 20px;\r\n  background: #1DA1F2;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  cursor: pointer;\r\n  font-weight: 900;\r\n  box-shadow: 6px 6px 6px #cbced1, -6px -6px 6px white;\r\n  transition: 0.5s;\r\n}\r\n\r\nbutton:hover {\r\n  box-shadow: none;\r\n}\r\n\r\na {\r\n  position: absolute;\r\n  font-size: 8px;\r\n  bottom: 4px;\r\n  right: 4px;\r\n  text-decoration: none;\r\n  color: black;\r\n  background: yellow;\r\n  border-radius: 10px;\r\n  padding: 2px;\r\n}\r\n\r\nh1 {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}</style>\r\n<div class=\"containerr\">\r\n  <div class=\"brand-title\">Form Title</div>\r\n  <div class=\"inputs\">\r\n    <label>EMAIL</label>\r\n    <input type=\"email\" placeholder=\"<EMAIL>\" />\r\n    <label>Nmae</label>\r\n    <input type=\"text\" placeholder=\"Enter Name\" />\r\n    <label>Phone</label>\r\n    <input type=\"text\" placeholder=\"Enter Phone\" />\r\n  <button type=\"submit\">Sbumit</button>\r\n  </div>\r\n  </div>"}, {"id": "7", "image": "https://drm.software/images/7.png", "source": "<style>.container{width:auto!important}\r\n@import url(\"https://fonts.googleapis.com/css2?family=Sansita+Swashed:wght@600&display=swap\");\r\nbody {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100vh;\r\n  background: linear-gradient(45deg, greenyellow, dodgerblue);\r\n  font-family: \"Sansita Swashed\", cursive;\r\n}\r\n.center {\r\n  position: relative;\r\n  padding: 50px 50px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n}\r\n.center h1 {\r\n  font-size: 2em;\r\n  border-left: 5px solid dodgerblue;\r\n  padding: 10px;\r\n  color: #000;\r\n  letter-spacing: 5px;\r\n  margin-bottom: 60px;\r\n  font-weight: bold;\r\n  padding-left: 10px;\r\n}\r\n.center .inputbox {\r\n  position: relative;\r\n  width: 300px;\r\n  height: 50px;\r\n  margin-bottom: 25px;\r\n}\r\n.center .inputbox input {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  border: 2px solid #000;\r\n  outline: none;\r\n  background: none;\r\n  padding: 10px;\r\n  border-radius: 10px;\r\n  font-size: 1.2em;\r\n}\r\n.center .inputbox:last-child {\r\n  margin-bottom: 0;\r\n}\r\n.center .inputbox span {\r\n  position: absolute;\r\n  top: 14px;\r\n  left: 20px;\r\n  font-size: 1em;\r\n  transition: 0.6s;\r\n  font-family: sans-serif;\r\n}\r\n.center .inputbox input:focus ~ span,\r\n.center .inputbox input:valid ~ span {\r\n  transform: translateX(-13px) translateY(-35px);\r\n  font-size: 1em;\r\n}\r\n.center .inputbox [type=\"button\"] {\r\n  width: 50%;\r\n  background: dodgerblue;\r\n  color: #fff;\r\n  border: #fff;\r\n}\r\n.center .inputbox:hover [type=\"button\"] {\r\n  background: linear-gradient(45deg, greenyellow, dodgerblue);\r\n}\r\n\r\n</style>\r\n<div class=\"center\">\r\n  <h1>Form Title</h1>\r\n  <form>\r\n    <div class=\"inputbox\">\r\n      <input type=\"text\" required=\"required\">\r\n      <span>Email</span>\r\n    </div>\r\n     <div class=\"inputbox\">\r\n      <input type=\"text\" required=\"required\">\r\n      <span>Name</span>\r\n    </div>\r\n     <div class=\"inputbox\">\r\n      <input type=\"text\" required=\"required\">\r\n      <span>Phone</span>\r\n    </div>\r\n    <div class=\"inputbox\">\r\n      <input type=\"button\" value=\"submit\">\r\n    </div>\r\n  </form>\r\n</div>"}, {"id": "8", "image": "https://drm.software/images/8.png", "source": "<style>@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');\r\n\r\n*, *:after, *:before {\r\n\tbox-sizing: border-box;\r\n}\r\n\r\nbody {\r\n\tfont-family: \"DM Sans\", sans-serif;\r\n\tline-height: 1.5;\r\n\tbackground-color: #f1f3fb;\r\n\tpadding: 0 2rem;\r\n}\r\n\r\nimg {\r\n\tmax-width: 100%;\r\n\tdisplay: block;\r\n}\r\n\r\n\r\n// iOS Reset \r\ninput {\r\n\tappearance: none;\r\n\tborder-radius: 0;\r\n}\r\n\r\n.card {\r\n\tmargin: 2rem auto;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\twidth: 100%;\r\n\tmax-width: 425px;\r\n\tbackground-color: #FFF;\r\n\tborder-radius: 10px;\r\n\tbox-shadow: 0 10px 20px 0 rgba(#999, .25);\r\n\tpadding: .75rem;\r\n}\r\n\r\n.card-image {\r\n\tborder-radius: 8px;\r\n\toverflow: hidden;\r\n\tpadding-bottom: 65%;\r\n\tbackground-image: url('https://assets.codepen.io/285131/coffee_1.jpg');\r\n\tbackground-repeat: no-repeat;\r\n\tbackground-size: 150%;\r\n\tbackground-position: 0 5%;\r\n\tposition: relative;\r\n}\r\n\r\n.card-heading {\r\n\tposition: absolute;\r\n\tleft: 10%;\r\n\ttop: 15%;\r\n\tright: 10%;\r\n\tfont-size: 1.75rem;\r\n\tfont-weight: 700;\r\n\tcolor: #735400;\r\n\tline-height: 1.222;\r\n\tsmall {\r\n\t\tdisplay: block;\r\n\t\tfont-size: .75em;\r\n\t\tfont-weight: 400;\r\n\t\tmargin-top: .25em;\r\n\t}\r\n}\r\n\r\n.card-form {\r\n\tpadding: 2rem 1rem 0;\r\n}\r\n\r\n.input {\r\n\tdisplay: flex;\r\n\tflex-direction: column-reverse;\r\n\tposition: relative;\r\n\tpadding-top: 1.5rem;\r\n\t&+.input {\r\n\t\tmargin-top: 1.5rem;\r\n\t}\r\n}\r\n\r\n.input-label {\r\n\tcolor: #8597a3;\r\n\tposition: absolute;\r\n\ttop: 1.5rem;\r\n\ttransition: .25s ease;\r\n}\r\n\r\n.input-field {\r\n\tborder: 0;\r\n\tz-index: 1;\r\n\tbackground-color: transparent;\r\n\tborder-bottom: 2px solid #eee; \r\n\tfont: inherit;\r\n\tfont-size: 1.125rem;\r\n\tpadding: .25rem 0;\r\n\t&:focus, &:valid {\r\n\t\toutline: 0;\r\n\t\tborder-bottom-color: #6658d3;\r\n\t\t&+.input-label {\r\n\t\t\tcolor: #6658d3;\r\n\t\t\ttransform: translateY(-1.5rem);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.action {\r\n\tmargin-top: 2rem;\r\n}\r\n\r\n.action-button {\r\n\tfont: inherit;\r\n\tfont-size: 1.25rem;\r\n\tpadding: 1em;\r\n\twidth: 100%;\r\n\tfont-weight: 500;\r\n\tbackground-color: #6658d3;\r\n\tborder-radius: 6px;\r\n\tcolor: #FFF;\r\n\tborder: 0;\r\n\t&:focus {\r\n\t\toutline: 0;\r\n\t}\r\n}\r\n\r\n.card-info {\r\n\tpadding: 1rem 1rem;\r\n\ttext-align: center;\r\n\tfont-size: .875rem;\r\n\tcolor: #8597a3;\r\n\ta {\r\n\t\tdisplay: block;\r\n\t\tcolor: #6658d3;\r\n\t\ttext-decoration: none;\r\n\t}\r\n}\r\n\r\n\r\n\r\n\r\n</style>\r\n<div class=\"container\">\r\n\t<!-- code here -->\r\n\t<div class=\"card\">\r\n\t\t<div class=\"card-image\">\t\r\n\t\t\t<h2 class=\"card-heading\">\r\n\t\t\t\tGet started\r\n\t\t\t\t<small>Let us create your account</small>\r\n\t\t\t</h2>\r\n\t\t</div>\r\n\t\t<form class=\"card-form\">\r\n\t\t\t<div class=\"input\">\r\n\t\t\t\t<input type=\"text\" class=\"input-field\"/>\r\n\t\t\t\t<label class=\"input-label\">Full name</label>\r\n\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"input\">\r\n\t\t\t\t<input type=\"text\" class=\"input-field\" required/>\r\n\t\t\t\t<label class=\"input-label\">Email</label>\r\n\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"input\">\r\n\t\t\t\t<input type=\"text\" class=\"input-field\"/>\r\n\t\t\t\t<label class=\"input-label\">Phone</label>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"action\">\r\n\t\t\t\t<button class=\"action-button\">Get started</button>\r\n\t\t\t</div>\r\n\t\t</form>\r\n\t\t<div class=\"card-info\">\r\n\t\t\t<p>By signing up you are agreeing to our </p>\r\n\t\t</div>\r\n\t</div>\r\n</div>\r\n"}, {"id": "14", "image": "https://drm.software/images/14.png", "source": "<style>\r\n\r\n\r\n@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600,700,800,900');\r\n\r\nbody{\r\n\tfont-family: 'Poppins', sans-serif;\r\n\tfont-weight: 300;\r\n\tfont-size: 15px;\r\n\tline-height: 1.7;\r\n\tcolor: #c4c3ca;\r\n\tbackground-color: #1f2029;\r\n\toverflow-x: hidden;\r\n}\r\na {\r\n\tcursor: pointer;\r\n  transition: all 200ms linear;\r\n}\r\na:hover {\r\n\ttext-decoration: none;\r\n}\r\n.link {\r\n  color: #c4c3ca;\r\n}\r\n.link:hover {\r\n  color: #ffeba7;\r\n}\r\np {\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  line-height: 1.7;\r\n}\r\nh4 {\r\n  font-weight: 600;\r\n}\r\nh6 span{\r\n  padding: 0 20px;\r\n  text-transform: uppercase;\r\n  font-weight: 700;\r\n}\r\n.section{\r\n  position: relative;\r\n  width: 100%;\r\n  display: block;\r\n}\r\n.full-height{\r\n  min-height: 100vh;\r\n}\r\n[type=\"checkbox\"]:checked,\r\n[type=\"checkbox\"]:not(:checked){\r\n  position: absolute;\r\n  left: -9999px;\r\n}\r\n.checkbox:checked + label,\r\n.checkbox:not(:checked) + label{\r\n  position: relative;\r\n  display: block;\r\n  text-align: center;\r\n  width: 60px;\r\n  height: 16px;\r\n  border-radius: 8px;\r\n  padding: 0;\r\n  margin: 10px auto;\r\n  cursor: pointer;\r\n  background-color: #ffeba7;\r\n}\r\n.checkbox:checked + label:before,\r\n.checkbox:not(:checked) + label:before{\r\n  position: absolute;\r\n  display: block;\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 50%;\r\n  color: #ffeba7;\r\n  background-color: #102770;\r\n  font-family: 'unicons';\r\n  content: '\\eb4f';\r\n  z-index: 20;\r\n  top: -10px;\r\n  left: -10px;\r\n  line-height: 36px;\r\n  text-align: center;\r\n  font-size: 24px;\r\n  transition: all 0.5s ease;\r\n}\r\n.checkbox:checked + label:before {\r\n  transform: translateX(44px) rotate(-270deg);\r\n}\r\n\r\n\r\n.card-3d-wrap {\r\n  position: relative;\r\n  width: 440px;\r\n  max-width: 100%;\r\n  height: 400px;\r\n  -webkit-transform-style: preserve-3d;\r\n  transform-style: preserve-3d;\r\n  perspective: 800px;\r\n  margin-top: 60px;\r\n}\r\n.card-3d-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  position:absolute;    \r\n  top: 0;\r\n  left: 0;  \r\n  -webkit-transform-style: preserve-3d;\r\n  transform-style: preserve-3d;\r\n  transition: all 600ms ease-out; \r\n}\r\n.card-front, .card-back {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #2a2b38;\r\n  background-image: url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/1462889/pat.svg');\r\n  background-position: bottom center;\r\n  background-repeat: no-repeat;\r\n  background-size: 300%;\r\n  position: absolute;\r\n  border-radius: 6px;\r\n  left: 0;\r\n  top: 0;\r\n  -webkit-transform-style: preserve-3d;\r\n  transform-style: preserve-3d;\r\n  -webkit-backface-visibility: hidden;\r\n  -moz-backface-visibility: hidden;\r\n  -o-backface-visibility: hidden;\r\n  backface-visibility: hidden;\r\n}\r\n.card-back {\r\n  transform: rotateY(180deg);\r\n}\r\n.checkbox:checked ~ .card-3d-wrap .card-3d-wrapper {\r\n  transform: rotateY(180deg);\r\n}\r\n.center-wrap{\r\n  position: absolute;\r\n  width: 100%;\r\n  padding: 0 35px;\r\n  top: 50%;\r\n  left: 0;\r\n  transform: translate3d(0, -50%, 35px) perspective(100px);\r\n  z-index: 20;\r\n  display: block;\r\n}\r\n\r\n\r\n.form-group{ \r\n  position: relative;\r\n  display: block;\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n.form-style {\r\n  padding: 13px 20px;\r\n  padding-left: 55px;\r\n  height: 48px;\r\n  width: 100%;\r\n  font-weight: 500;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n  line-height: 22px;\r\n  letter-spacing: 0.5px;\r\n  outline: none;\r\n  color: #c4c3ca;\r\n  background-color: #1f2029;\r\n  border: none;\r\n  -webkit-transition: all 200ms linear;\r\n  transition: all 200ms linear;\r\n  box-shadow: 0 4px 8px 0 rgba(21,21,21,.2);\r\n}\r\n.form-style:focus,\r\n.form-style:active {\r\n  border: none;\r\n  outline: none;\r\n  box-shadow: 0 4px 8px 0 rgba(21,21,21,.2);\r\n}\r\n.input-icon {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 18px;\r\n  height: 48px;\r\n  font-size: 24px;\r\n  line-height: 48px;\r\n  text-align: left;\r\n  color: #ffeba7;\r\n  -webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n}\r\n\r\n.form-group input:-ms-input-placeholder  {\r\n  color: #c4c3ca;\r\n  opacity: 0.7;\r\n  -webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n}\r\n.form-group input::-moz-placeholder  {\r\n  color: #c4c3ca;\r\n  opacity: 0.7;\r\n  -webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n}\r\n.form-group input:-moz-placeholder  {\r\n  color: #c4c3ca;\r\n  opacity: 0.7;\r\n  -webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n}\r\n.form-group input::-webkit-input-placeholder  {\r\n  color: #c4c3ca;\r\n  opacity: 0.7;\r\n  -webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n}\r\n.form-group input:focus:-ms-input-placeholder  {\r\n  opacity: 0;\r\n  -webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n}\r\n.form-group input:focus::-moz-placeholder  {\r\n  opacity: 0;\r\n  -webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n}\r\n.form-group input:focus:-moz-placeholder  {\r\n  opacity: 0;\r\n  -webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n}\r\n.form-group input:focus::-webkit-input-placeholder  {\r\n  opacity: 0;\r\n  -webkit-transition: all 200ms linear;\r\n    transition: all 200ms linear;\r\n}\r\n\r\n.btn{  \r\n  border-radius: 4px;\r\n  height: 44px;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  -webkit-transition : all 200ms linear;\r\n  transition: all 200ms linear;\r\n  padding: 0 30px;\r\n  letter-spacing: 1px;\r\n  display: -webkit-inline-flex;\r\n  display: -ms-inline-flexbox;\r\n  display: inline-flex;\r\n  -webkit-align-items: center;\r\n  -moz-align-items: center;\r\n  -ms-align-items: center;\r\n  align-items: center;\r\n  -webkit-justify-content: center;\r\n  -moz-justify-content: center;\r\n  -ms-justify-content: center;\r\n  justify-content: center;\r\n  -ms-flex-pack: center;\r\n  text-align: center;\r\n  border: none;\r\n  background-color: #ffeba7;\r\n  color: #102770;\r\n  box-shadow: 0 8px 24px 0 rgba(255,235,167,.2);\r\n}\r\n.btn:active,\r\n.btn:focus{  \r\n  background-color: #102770;\r\n  color: #ffeba7;\r\n  box-shadow: 0 8px 24px 0 rgba(16,39,112,.2);\r\n}\r\n.btn:hover{  \r\n  background-color: #102770;\r\n  color: #ffeba7;\r\n  box-shadow: 0 8px 24px 0 rgba(16,39,112,.2);\r\n}\r\n\r\n\r\n\r\n\r\n.logo {\r\n\tposition: absolute;\r\n\ttop: 30px;\r\n\tright: 30px;\r\n\tdisplay: block;\r\n\tz-index: 100;\r\n\ttransition: all 250ms linear;\r\n}\r\n.logo img {\r\n\theight: 26px;\r\n\twidth: auto;\r\n\tdisplay: block;\r\n}</style>\r\n\r\n\t<div class=\"section\">\r\n\t\t<div class=\"container\">\r\n\t\t\t<div class=\"row full-height justify-content-center\">\r\n\t\t\t\t<div class=\"col-12 text-center align-self-center py-5\">\r\n\t\t\t\t\t<div class=\"section pb-5 pt-5 pt-sm-2 text-center\">\r\n\t\t\t\r\n\t\t\t\t\t\t<div class=\"card-3d-wrap mx-auto\">\r\n\t\t\t\t\t\t\t<div class=\"card-3d-wrapper\">\r\n\t\t\t\t\t\t\t\t<div class=\"card-front\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"center-wrap\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"section text-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<h4 class=\"mb-4 pb-3\">Form Title</h4>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"email\" name=\"logemail\" class=\"form-style\" placeholder=\"Your Email\" id=\"logemail\" autocomplete=\"off\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"input-icon uil uil-at\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\t\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"form-group mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"tedt\" name=\"logpass\" class=\"form-style\" placeholder=\"Your Name\" id=\"logpass\" autocomplete=\"off\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"input-icon uil uil-lock-alt\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n                      <div class=\"form-group mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"tedt\" name=\"logpass\" class=\"form-style\" placeholder=\"Your Phone\" id=\"logpass\" autocomplete=\"off\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"input-icon uil uil-lock-alt\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<input type=\"submit\" value=\"Submit\" class=\"btn mt-4\"/>\r\n                            \t\t\t\r\n\t\t\t\t      \t\t\t\t\t</div>\r\n\t\t\t      \t\t\t\t\t</div>\r\n\t\t\t      \t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"card-back\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"center-wrap\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"section text-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<h4 class=\"mb-4 pb-3\">Sign Up</h4>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"text\" name=\"logname\" class=\"form-style\" placeholder=\"Your Full Name\" id=\"logname\" autocomplete=\"off\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"input-icon uil uil-user\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\t\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"form-group mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"email\" name=\"logemail\" class=\"form-style\" placeholder=\"Your Email\" id=\"logemail\" autocomplete=\"off\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"input-icon uil uil-at\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\t\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"form-group mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<input type=\"password\" name=\"logpass\" class=\"form-style\" placeholder=\"Your Password\" id=\"logpass\" autocomplete=\"off\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<i class=\"input-icon uil uil-lock-alt\"></i>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<input class=\"btn mt-4\" type=\"submit\" value=\"Submit\">\r\n\t\t\t\t      \t\t\t\t\t</div>\r\n\t\t\t      \t\t\t\t\t</div>\r\n\t\t\t      \t\t\t\t</div>\r\n\t\t\t      \t\t\t</div>\r\n\t\t\t      \t\t</div>\r\n\t\t\t      \t</div>\r\n\t\t      \t</div>\r\n\t      \t</div>\r\n\t    </div>\r\n\t</div>"}, {"id": "9", "image": "https://drm.software/images/9.png", "source": "<style>.container{margin-top:50px}\"\"\r\n@import url(\"https://fonts.googleapis.com/css?family=Playfair+Display:400,400i,700,700i,900,900i|Poppins:300,400,500,600,700,800,900\");\r\n@import url(\"https://fonts.googleapis.com/css?family=Forum\");\r\n@import url(\"https://fonts.googleapis.com/css?family=Cinzel:400,700,900|Josefin+Slab:100,100i,300,300i,400,400i,600,600i,700,700i|<PERSON><PERSON>|Nanum+Myeongjo:400,700,800|Old+Standard+TT:400,400i,700|Prata|Vidaloka\");\r\nbody {\r\n  background: #F0F0F0;\r\n  margin: 0;\r\n  color: white;\r\n}\r\n\r\n.subscribe-box {\r\n  background: #2bb24c;\r\n  font-family: \"Gothic A1\", serif;\r\n  padding: 6em 0;\r\n  text-align: center;\r\n}\r\n.subscribe-box h2 {\r\n  margin: 0 0 0.85em 0;\r\n  font-weight: 100;\r\n  font-size: 30px;\r\n  font-family: \"Marcellus\", serif;\r\n}\r\n.subscribe-box .subscribe {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  margin: auto;\r\n}\r\n.subscribe-box .subscribe input {\r\n  width: 100%;\r\n  background: transparent;\r\n  border: 0;\r\n  border-bottom: 1px solid;\r\n  padding: 1em 0 0.8em;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  font-family: inherit;\r\n  font-weight: 300;\r\n  line-height: 1.5;\r\n  color: inherit;\r\n  outline: none;\r\n}\r\n.subscribe-box .subscribe input::-moz-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n}\r\n.subscribe-box .subscribe input:-ms-input-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n}\r\n.subscribe-box .subscribe input::placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n}\r\n.subscribe-box .subscribe button {\r\n  all: unset;\r\n  margin-top: 2.4em;\r\n  background: transparent;\r\n  border: 2px solid white;\r\n  padding: 1em 4em;\r\n  border-radius: 50px;\r\n  cursor: pointer;\r\n  display: inline-block;\r\n  font-weight: 700;\r\n  position: relative;\r\n  transition: all 300ms ease;\r\n}\r\n.subscribe-box .subscribe button span {\r\n  display: inline-block;\r\n  transition: all 300ms ease;\r\n}\r\n.subscribe-box .subscribe button:before, .subscribe-box .subscribe button:after {\r\n  content: \"\";\r\n  display: block;\r\n  position: absolute;\r\n  transition: all 300ms ease;\r\n  opacity: 0;\r\n}\r\n.subscribe-box .subscribe button:before {\r\n  height: 7px;\r\n  width: 7px;\r\n  background: transparent;\r\n  border-right: 2px solid;\r\n  border-top: 2px solid;\r\n  right: 30px;\r\n  top: 21px;\r\n  transform: rotate(45deg);\r\n}\r\n.subscribe-box .subscribe button:after {\r\n  background: white;\r\n  height: 2px;\r\n  width: 50px;\r\n  left: 0;\r\n  top: 1.49em;\r\n}\r\n.subscribe-box .subscribe button:hover span {\r\n  transform: translateX(-10px);\r\n}\r\n.subscribe-box .subscribe button:hover:before {\r\n  opacity: 1;\r\n}\r\n.subscribe-box .subscribe button:hover:after {\r\n  width: 14px;\r\n  opacity: 1;\r\n  transform: translateX(160px);\r\n}\r\n</style>\r\n<div class=\"subscribe-box\"> \r\n  <h2>Subscribe to our mailing list</h2>\r\n  <form class=\"subscribe\">\r\n    <input type=\"text\" placeholder=\"Enter Name\" autocomplete=\"off\" required=\"required\"/>\r\n    <input type=\"email\" placeholder=\"Enter Email Address\" autocomplete=\"off\" required=\"required\"/>\r\n    <input type=\"text\" placeholder=\"Enter Phone\" autocomplete=\"off\" required=\"required\"/>\r\n    <button type=\"submit\"> <span>Subscribe</span></button>\r\n  </form>\r\n</div>"}, {"id": "10", "image": "https://drm.software/images/10.png", "source": "<style>html {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\nbody {\r\n  background: linear-gradient(45deg, rgba(66, 183, 245, 0.8) 0%, rgba(66, 245, 189, 0.4) 100%);\r\n  color: rgba(0, 0, 0, 0.6);\r\n  font-family: \"Roboto\", sans-serif;\r\n  font-size: 14px;\r\n  line-height: 1.6em;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n.overlay, .form-panel.one:before {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  display: none;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.form {\r\n  z-index: 15;\r\n  position: relative;\r\n  background: #FFFFFF;\r\n  width: 600px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);\r\n  box-sizing: border-box;\r\n  margin: 100px auto 10px;\r\n  overflow: hidden;\r\n}\r\n.form-toggle {\r\n  z-index: 10;\r\n  position: absolute;\r\n  top: 60px;\r\n  right: 60px;\r\n  background: #FFFFFF;\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 100%;\r\n  transform-origin: center;\r\n  transform: translate(0, -25%) scale(0);\r\n  opacity: 0;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n.form-toggle:before, .form-toggle:after {\r\n  content: \"\";\r\n  display: block;\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 30px;\r\n  height: 4px;\r\n  background: #4285F4;\r\n  transform: translate(-50%, -50%);\r\n}\r\n.form-toggle:before {\r\n  transform: translate(-50%, -50%) rotate(45deg);\r\n}\r\n.form-toggle:after {\r\n  transform: translate(-50%, -50%) rotate(-45deg);\r\n}\r\n.form-toggle.visible {\r\n  transform: translate(0, -25%) scale(1);\r\n  opacity: 1;\r\n}\r\n.form-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  margin: 0 0 20px;\r\n}\r\n.form-group:last-child {\r\n  margin: 0;\r\n}\r\n.form-group label {\r\n  display: block;\r\n  margin: 0 0 10px;\r\n  color: rgba(0, 0, 0, 0.6);\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  line-height: 1;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.2em;\r\n}\r\n.two .form-group label {\r\n  color: #FFFFFF;\r\n}\r\n.form-group input {\r\n  outline: none;\r\n  display: block;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  width: 100%;\r\n  border: 0;\r\n  border-radius: 4px;\r\n  box-sizing: border-box;\r\n  padding: 12px 20px;\r\n  color: rgba(0, 0, 0, 0.6);\r\n  font-family: inherit;\r\n  font-size: inherit;\r\n  font-weight: 500;\r\n  line-height: inherit;\r\n  transition: 0.3s ease;\r\n}\r\n.form-group input:focus {\r\n  color: rgba(0, 0, 0, 0.8);\r\n}\r\n.two .form-group input {\r\n  color: #FFFFFF;\r\n}\r\n.two .form-group input:focus {\r\n  color: #FFFFFF;\r\n}\r\n.form-group button {\r\n  outline: none;\r\n  background: #4285F4;\r\n  width: 100%;\r\n  border: 0;\r\n  border-radius: 4px;\r\n  padding: 12px 20px;\r\n  color: #FFFFFF;\r\n  font-family: inherit;\r\n  font-size: inherit;\r\n  font-weight: 500;\r\n  line-height: inherit;\r\n  text-transform: uppercase;\r\n  cursor: pointer;\r\n}\r\n.two .form-group button {\r\n  background: #FFFFFF;\r\n  color: #4285F4;\r\n}\r\n.form-group .form-remember {\r\n  font-size: 12px;\r\n  font-weight: 400;\r\n  letter-spacing: 0;\r\n  text-transform: none;\r\n}\r\n.form-group .form-remember input[type=checkbox] {\r\n  display: inline-block;\r\n  width: auto;\r\n  margin: 0 10px 0 0;\r\n}\r\n.form-group .form-recovery {\r\n  color: #4285F4;\r\n  font-size: 12px;\r\n  text-decoration: none;\r\n}\r\n.form-panel {\r\n  padding: 60px calc(5% + 60px) 60px 60px;\r\n  box-sizing: border-box;\r\n}\r\n.form-panel.one:before {\r\n  content: \"\";\r\n  display: block;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: 0.3s ease;\r\n}\r\n.form-panel.one.hidden:before {\r\n  display: block;\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n.form-panel.two {\r\n  z-index: 5;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 95%;\r\n  background: #4285F4;\r\n  width: 100%;\r\n  min-height: 100%;\r\n  padding: 60px calc(10% + 60px) 60px 60px;\r\n  transition: 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n.form-panel.two:before, .form-panel.two:after {\r\n  content: \"\";\r\n  display: block;\r\n  position: absolute;\r\n  top: 60px;\r\n  left: 1.5%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  height: 30px;\r\n  width: 2px;\r\n  transition: 0.3s ease;\r\n}\r\n.form-panel.two:after {\r\n  left: 3%;\r\n}\r\n.form-panel.two:hover {\r\n  left: 93%;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\r\n}\r\n.form-panel.two:hover:before, .form-panel.two:hover:after {\r\n  opacity: 0;\r\n}\r\n.form-panel.two.active {\r\n  left: 10%;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\r\n  cursor: default;\r\n}\r\n.form-panel.two.active:before, .form-panel.two.active:after {\r\n  opacity: 0;\r\n}\r\n.form-header {\r\n  margin: 0 0 40px;\r\n}\r\n.form-header h1 {\r\n  padding: 4px 0;\r\n  color: #4285F4;\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  text-transform: uppercase;\r\n}\r\n.two .form-header h1 {\r\n  position: relative;\r\n  z-index: 40;\r\n  color: #FFFFFF;\r\n}\r\n.pen-footer {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: space-between;\r\n  width: 600px;\r\n  margin: 20px auto 100px;\r\n}\r\n.pen-footer a {\r\n  color: #FFFFFF;\r\n  font-size: 12px;\r\n  text-decoration: none;\r\n  text-shadow: 1px 2px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n.pen-footer a .material-icons {\r\n  width: 12px;\r\n  margin: 0 5px;\r\n  vertical-align: middle;\r\n  font-size: 12px;\r\n}\r\n\r\n.cp-fab {\r\n  background: #FFFFFF !important;\r\n  color: #4285F4 !important;\r\n}</style>\r\n<div class=\"form\">\r\n  <div class=\"form-toggle\"></div>\r\n  <div class=\"form-panel one\">\r\n    <div class=\"form-header\">\r\n      <h1>Form Title</h1>\r\n    </div>\r\n    <div class=\"form-content\">\r\n      <form>\r\n        <div class=\"form-group\">\r\n          <label for=\"username\">Full Name</label>\r\n          <input type=\"text\" name=\"name\"/>\r\n        </div>\r\n        <div class=\"form-group\">\r\n          <label for=\"email\">Email</label>\r\n          <input type=\"email\" name=\"email\" required=\"required\"/>\r\n        </div>\r\n         <div class=\"form-group\">\r\n          <label for=\"phone\">Phone</label>\r\n          <input type=\"text\" name=\"phone\"/>\r\n        </div>\r\n        <div class=\"form-group\">\r\n          <button type=\"submit\">Submit</button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>"}, {"id": "12", "image": "https://drm.software/images/12.png", "source": "\r\n<style>.sign-up-modal .email-input{margin-bottom:23px !important;margin-top:6px !important;}.submit_btn{text-align:center;padding:0px !important}@import url(https://fonts.googleapis.com/css?family=Roboto:700,300);\r\nbody {\r\n\t\tbackground-color: #333;\r\n}\r\n\r\n.sign-up-modal p,\r\n.sign-up-modal .form-checkbox label {\r\n\t\tfont-size: 1em;\r\n\t\tfont-weight: 300;\r\n}\r\n\r\n.sign-up-modal .form-checkbox label {\r\n\t\tcursor: pointer;\r\n}\r\n\r\n.logo {\r\n\t\tfill: #FEFEFE;\r\n}\r\n\r\n.logo-container {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 50px;\r\n\t\tposition: relative;\r\n\t\ttext-align: center;\r\n}\r\n\r\n.sign-up-modal {\r\n\t\tfont-family: \"Roboto\", arial, sans-serif;\r\n\t\tcolor: #fefefe;\r\n\t\tborder-style: solid;\r\n\t\tborder-width: 10px;\r\n\t\tborder-color: rgb(254, 254, 254);\r\n\t\tborder-radius: 10px 10px 10px 10px;\r\n\t\tbackground-image: -moz-linear-gradient( -90deg, rgb(236, 111, 102) 0%, rgb(243, 161, 131) 100%);\r\n\t\tbackground-image: -webkit-linear-gradient( -90deg, rgb(236, 111, 102) 0%, rgb(243, 161, 131) 100%);\r\n\t\tbackground-image: -ms-linear-gradient( -90deg, rgb(236, 111, 102) 0%, rgb(243, 161, 131) 100%);\r\n\t\twidth: 90%;\r\n\t\tmin-width: 400px;\r\n\t\tmax-width: 700px;\r\n\t\tmargin: 0 auto;\r\n\t\tpadding: 25px;\r\n}\r\n\r\n.sign-up-modal form {\r\n\t\tmargin: 0 auto;\r\n\t\ttext-align: center;\r\n}\r\n\r\n.sign-up-modal input::-webkit-input-placeholder {\r\n\t\tcolor: #fefefe;\r\n\t\topacity: 1;\r\n}\r\n\r\n.sign-up-modal input:focus::-webkit-input-placeholder {\r\n\t\tcolor: #111;\r\n\t\topacity: .5;\r\n\t\ttransition: all 0.3s ease;\r\n}\r\n\r\n.sign-up-modal .input-container {\r\n\t\tmargin: 10px;\r\n}\r\n\r\n.sign-up-modal input[type=\"email\"],\r\n.sign-up-modal input[type=\"text\"],\r\n.sign-up-modal input[type=\"password\"],\r\n.sign-up-modal input[type=\"checkbox\"]:focus,\r\n.sign-up-modal input[type=\"submit\"]:focus {\r\n\t\toutline: 0;\r\n}\r\n\r\n.sign-up-modal input[type=\"email\"],\r\n.sign-up-modal input[type=\"text\"],\r\n.sign-up-modal input[type=\"password\"] {\r\n\t\tfont-weight: 700;\r\n\t\tfont-size: 1.4em;\r\n\t\tpadding: 10px;\r\n\t\tborder-width: 2px;\r\n\t\tborder-color: rgba(247, 247, 247, .3);\r\n\t\tborder-style: solid;\r\n\t\tbackground: url('transparent');\r\n}\r\n\r\n.sign-up-modal input[type=\"email\"]:focus,\r\n.sign-up-modal input[type=\"text\"]:focus,\r\n.sign-up-modal input[type=\"password\"]:focus {\r\n\t\tbackground: white;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tcolor: #222;\r\n}\r\n\r\n.sign-up-modal input[type=\"checkbox\"] {\r\n\t\t-webkit-appearance: none;\r\n\t\tbackground-color: #fefefe;\r\n\t\tdisplay: inline-block;\r\n\t\tposition: relative;\r\n\t\tpadding: 6px;\r\n\t\tmargin-left: -6px;\r\n\t\tmargin-top: 25px;\r\n\t\tcursor: pointer;\r\n}\r\n\r\n.sign-up-modal input[type=\"checkbox\"]:checked:after {\r\n\t\tcontent: '\\2714';\r\n\t\tfont-size: 13px;\r\n\t\tposition: absolute;\r\n\t\ttop: -2px;\r\n\t\tleft: 2px;\r\n\t\tcolor: #111;\r\n}\r\n\r\n.sign-up-modal a {\r\n\t\tcolor: #fefefe;\r\n\t\ttext-decoration: underline;\r\n}\r\n\r\n.sign-up-modal a:hover {\r\n\t\tcolor: #d26960;\r\n}\r\n\r\n.sign-up-modal input[type=\"submit\"] {\r\n\t\tfont-weight: 700;\r\n\t\tfont-size: 1.8em;\r\n\t\tcolor: #111;\r\n\t\tbackground: #fefefe;\r\n\t\tbox-shadow: 0px 4px 0px 0px #d26a60;\r\n\t\tborder-style: none;\r\n\t\tpadding: 10px 50px;\r\n\t\tmargin: 25px 0 15px 0;\r\n\t\tposition: relative;\r\n\t\tdisplay: inline-block;\r\n\t\ttransition: all .1s linear;\r\n}\r\n\r\n.sign-up-modal input[type=\"submit\"]:active {\r\n\t\tbox-shadow: 0 2px 0 #d26a60;\r\n\t\ttransform: translateY(3px);\r\n\t\t-webkit-transform: translateY(3px);\r\n\t\t-ms-transform: translateY(3px);\r\n}\r\n\r\n.sign-up-modal #close-modal-button {\r\n\t\tborder-radius: 50%;\r\n\t\twidth: 50px;\r\n\t\theight: 50px;\r\n\t\tbackground-color: #d26960;\r\n\t\tfloat: right;\r\n\t\tposition: relative;\r\n\t\ttop: -55px;\r\n\t\tleft: 50px;\r\n\t\tcursor: pointer;\r\n}\r\n\r\n.sign-up-modal #close-modal-button:before {\r\n\t\tcontent: '\\2716';\r\n\t\tfont-size: 1.5em;\r\n\t\tposition: relative;\r\n\t\ttop: 10px;\r\n\t\tleft: 17px;\r\n}\r\n\r\n@media only screen and (min-width: 768px) {\r\n\t\t.sign-up-modal .form-checkbox {\r\n\t\t\t\ttext-align: left;\r\n\t\t}\r\n\t\t.sign-up-modal .password-input {\r\n\t\t\t\twidth: 47.5%;\r\n\t\t\t\tmargin-left: -11.5%;\r\n\t\t}\r\n\t\t.sign-up-modal .username-input {\r\n\t\t\t\twidth: 47.5%;\r\n\t\t}\r\n\t\t.sign-up-modal .email-input {\r\n\t\t\t\tmargin-bottom: 20px;\r\n\t\t\t\tmargin-top: 50px;\r\n\t\t}\r\n\t\t.sign-up-modal form {\r\n\t\t\t\twidth: 70%;\r\n\t\t}\r\n\t\t.submit_btn{\r\n\t\t\tfont-weight: 700;\r\n    font-size: 1.8em;\r\n    color: #111;\r\n    background: #fefefe;\r\n    box-shadow: 0px 4px 0px 0px #d26a60;\r\n    border-style: none;\r\n    padding: 10px 50px;\r\n    margin: 25px 0 15px 0;\r\n    position: relative;\r\n    display: inline-block;\r\n    transition: all .1s linear;\r\n\t\t}\r\n}</style>\r\n<br>\r\n<br>\r\n<div class=\"sign-up-modal\">\r\n\t\t\r\n\r\n\t\t<div class=\"logo-container\">\r\n\t\t\t\t<svg class=\"logo\" width=\"94.4px\" height=\"56px\">\r\n\t\t\t\t\t\t<g>\r\n\t\t\t\t\t\t\t\t<polygon points=\"49.3,56 49.3,0 0,28 \t\" />\r\n\t\t\t\t\t\t\t\t<path d=\"M53.7,3.6v46.3l40.7-23.2L53.7,3.6z M57.7,10.6l28.4,16.2L57.7,42.9V10.6z\" />\r\n\t\t\t\t\t\t</g>\r\n\t\t\t\t</svg>\r\n\t\t</div>\r\n\r\n\t\t<form class=\"details\">\r\n\t\t\t\t<div class=\"input-container\">\r\n\t\t\t\t\t\t<input class=\"col-sm-12 email-input with-placeholder\" type=\"email\" placeholder=\"Email\" />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"input-container\">\r\n\t\t\t\t\t\t<input class=\"col-sm-12 email-input with-placeholder\" type=\"text\" placeholder=\"Full Name\" />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"input-container\">\r\n\t\t\t\t\t\t<input class=\"col-sm-12 email-input with-placeholder\" type=\"text\" placeholder=\"Phone\" />\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<input class=\"submit_btn\" id=\"sign-up-button\" type=\"submit\" value=\"Submit\">\r\n\r\n\r\n\r\n\t\t</form>\r\n</div>"}, {"id": "16", "image": "https://drm.software/images/16.png", "source": "<html lang=\"en\" class=\"\"><head>\r\n\r\n  <meta charset=\"UTF-8\">\r\n  <title>CodePen Demo</title>\r\n\r\n  <meta name=\"robots\" content=\"noindex\">\r\n\r\n  <link rel=\"shortcut icon\" type=\"image/x-icon\" href=\"https://cpwebassets.codepen.io/assets/favicon/favicon-aec34940fbc1a6e787974dcd360f2c6b63348d4b1f4e06c77743096d55480f33.ico\">\r\n  <link rel=\"mask-icon\" type=\"\" href=\"https://cpwebassets.codepen.io/assets/favicon/logo-pin-8f3771b1072e3c38bd662872f6b673a722f4b3ca2421637d5596661b4e2132cc.svg\" color=\"#111\">\r\n  <link rel=\"canonical\" href=\"https://codepen.io/adam2326/pen/VYMOdx/\">\r\n\r\n  \r\n  \r\n\r\n  <style class=\"INLINE_PEN_STYLESHEET_ID\">\r\n    @import url(https://fonts.googleapis.com/css?family=Indie+Flower);\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  background-color: #E74C3C;\r\n}\r\n\r\n.middle {\r\n  width: 460px;\r\n  margin: 0 auto;\r\n}\r\n\r\nh1 {\r\n  margin: 50px 0 0 0;\r\n  color: #fff;\r\n  font-family: \"Indie Flower\", cursive;\r\n  font-size: 48px;\r\n  font-weight: normal;\r\n  text-align: center;\r\n}\r\n\r\n#fancy-inputs {\r\n  float: left;\r\n  width: 100%;\r\n  margin: 0 0 50px 0;\r\n}\r\n#fancy-inputs label.input {\r\n  float: left;\r\n  width: 460px;\r\n  height: 42px;\r\n  margin: 50px 0 0 0;\r\n  position: relative;\r\n  clear: both;\r\n}\r\n#fancy-inputs label.input span {\r\n  width: 100%;\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: absolute;\r\n  left: 0;\r\n  cursor: text;\r\n}\r\n#fancy-inputs label.input span span {\r\n  position: absolute;\r\n  top: 0;\r\n  z-index: 1;\r\n  font-family: \"Indie Flower\", cursive;\r\n  font-size: 22px;\r\n  color: #fff;\r\n  text-indent: 10px;\r\n  transition: 0.3s;\r\n  -webkit-transition: 0.3s;\r\n  -moz-transition: 0.3s;\r\n}\r\n#fancy-inputs label.input span:before {\r\n  content: \"\";\r\n  width: 0%;\r\n  height: 3px;\r\n  background-color: #2C3E50;\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: 99;\r\n  transition: 0.3s;\r\n  -webkit-transition: 0.3s;\r\n  -moz-transition: 0.3s;\r\n}\r\n#fancy-inputs label.input span:after {\r\n  content: \"\";\r\n  width: 0%;\r\n  height: 3px;\r\n  background-color: #2C3E50;\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  z-index: 99;\r\n  transition: 0.3s;\r\n  -webkit-transition: 0.3s;\r\n  -moz-transition: 0.3s;\r\n}\r\n#fancy-inputs input {\r\n  float: left;\r\n  width: 460px;\r\n  height: 40px;\r\n  padding: 0 10px;\r\n  border: 0;\r\n  border-bottom: 3px solid #fff;\r\n  background-color: transparent;\r\n  color: #fff;\r\n  font-family: \"Indie Flower\", cursive;\r\n  font-size: 22px;\r\n  position: relative;\r\n  z-index: 99;\r\n}\r\n#fancy-inputs input:focus {\r\n  outline: 0;\r\n}\r\n#fancy-inputs input.white {\r\n  background-color: #E74C3C;\r\n}\r\n#fancy-inputs input:focus + span span {\r\n  cursor: initial;\r\n  position: absolute;\r\n  top: -35px;\r\n  color: #2C3E50;\r\n}\r\n#fancy-inputs input:focus + span:before {\r\n  width: 50%;\r\n}\r\n#fancy-inputs input:focus + span:after {\r\n  width: 50%;\r\n}\r\n#fancy-inputs span.fixed span {\r\n  top: -35px;\r\n}\r\n\r\n#fancy-radio {\r\n  float: left;\r\n  width: 100%;\r\n  margin: 0 0 30px 0;\r\n}\r\n#fancy-radio label.radio {\r\n  float: left;\r\n  width: auto;\r\n  line-height: 30px;\r\n  margin: 0 0 10px 0;\r\n  color: #fff;\r\n  font-family: \"Indie Flower\", cursive;\r\n  font-size: 22px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  clear: both;\r\n  transition: 0.3s;\r\n  -webkit-transition: 0.3s;\r\n  -moz-transition: 0.3s;\r\n}\r\n#fancy-radio label.radio:before {\r\n  content: \"\";\r\n  float: left;\r\n  width: 29px;\r\n  height: 29px;\r\n  margin: 0 5px 0 0;\r\n  background-color: #fff;\r\n  border-radius: 100%;\r\n  font-size: 22px;\r\n  text-indent: 40px;\r\n}\r\n#fancy-radio label.radio:after {\r\n  content: \"\";\r\n  width: 5px;\r\n  height: 5px;\r\n  background-color: #2C3E50;\r\n  border-radius: 100%;\r\n  position: absolute;\r\n  top: 12px;\r\n  left: 12px;\r\n  opacity: 0;\r\n  transition: 0.2s;\r\n  -webkit-transition: 0.2s;\r\n  -moz-transition: 0.2s;\r\n}\r\n#fancy-radio label.radio.selected:after {\r\n  width: 15.5px;\r\n  height: 15.5px;\r\n  top: 7px;\r\n  left: 7px;\r\n  opacity: 1;\r\n}\r\n#fancy-radio label.radio:hover {\r\n  text-indent: 5px;\r\n}\r\n\r\n.btn {\r\n  float: left;\r\n  width: 100%;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  margin: 0 0 50px 0;\r\n  background-color: #2C3E50;\r\n  color: #fff;\r\n  font-family: \"Indie Flower\", cursive;\r\n  font-size: 22px;\r\n  text-decoration: none;\r\n  text-align: center;\r\n  transition: 0.3s;\r\n  -webkit-transition: 0.3s;\r\n  -moz-transition: 0.3s;\r\n}\r\n.btn:hover {\r\n  background-color: #374b60;\r\n}\r\n  </style>\r\n\r\n  \r\n<script src=\"https://cpwebassets.codepen.io/assets/editor/iframe/iframeConsoleRunner-d8236034cc3508e70b0763f2575a8bb5850f9aea541206ce56704c013047d712.js\"></script>\r\n<script src=\"https://cpwebassets.codepen.io/assets/editor/iframe/iframeRefreshCSS-4793b73c6332f7f14a9b6bba5d5e62748e9d1bd0b5c52d7af6376f3d1c625d7e.js\"></script>\r\n<script src=\"https://cpwebassets.codepen.io/assets/editor/iframe/iframeRuntimeErrors-4f205f2c14e769b448bcf477de2938c681660d5038bc464e3700256713ebe261.js\"></script>\r\n</head>\r\n\r\n<body>\r\n  <div class=\"middle\">\r\n  <h1> Form Title </h1>\r\n  \r\n  <!--  Fancy inputs  -->\r\n  <div id=\"fancy-inputs\">\r\n    <label class=\"input\">\r\n      <input type=\"text\">\r\n      <span><span>Full Name</span></span>\r\n    </label>\r\n\r\n    <label class=\"input\">\r\n      <input type=\"text\">\r\n      <span><span>Email</span></span>\r\n    </label>\r\n    \r\n       <label class=\"input\">\r\n      <input type=\"text\">\r\n      <span><span>Phone</span></span>\r\n    </label>\r\n  </div>\r\n <input type=\"submit\"  value=\"Submit\" class=\"btn\"/>\r\n</div>\r\n  \r\n<script src=\"https://cpwebassets.codepen.io/assets/common/stopExecutionOnTimeout-8216c69d01441f36c0ea791ae2d4469f0f8ff5326f00ae2d00e4bb7d20e24edb.js\"></script>\r\n<script src=\"https://cdnjs.cloudflare.com/ajax/libs/jquery/2.1.3/jquery.min.js\"></script>\r\n  <script src=\"https://cdpn.io/cp/internal/boomboom/pen.js?key=pen.js-6fa81910-5730-5280-7196-c60498c42795\" crossorigin=\"\"></script>\r\n\r\n\r\n</body></html>"}, {"id": "17", "image": "https://drm.software/images/17.png", "source": "<html lang=\"en\" class=\"\"><head>\r\n\r\n  <meta charset=\"UTF-8\">\r\n  <title>CodePen Demo</title>\r\n\r\n  <meta name=\"robots\" content=\"noindex\">\r\n\r\n  <link rel=\"shortcut icon\" type=\"image/x-icon\" href=\"https://cpwebassets.codepen.io/assets/favicon/favicon-aec34940fbc1a6e787974dcd360f2c6b63348d4b1f4e06c77743096d55480f33.ico\">\r\n  <link rel=\"mask-icon\" type=\"\" href=\"https://cpwebassets.codepen.io/assets/favicon/logo-pin-8f3771b1072e3c38bd662872f6b673a722f4b3ca2421637d5596661b4e2132cc.svg\" color=\"#111\">\r\n  <link rel=\"canonical\" href=\"https://codepen.io/Tbgse/pen/JXrJGX/\">\r\n\r\n  <meta name=\"viewport\" content=\"width=device-width\">\r\n\r\n  \r\n\r\n  <style class=\"INLINE_PEN_STYLESHEET_ID\">\r\n    @import url(https://fonts.googleapis.com/css?family=Roboto:400,300,100,500);\r\nbody,\r\nhtml {\r\n  margin: 0;\r\n  height: 100%;\r\n}\r\n\r\ninput {\r\n  border: none;\r\n}\r\n\r\nbutton:focus {\r\n  outline: none;\r\n}\r\n\r\n::-webkit-input-placeholder {\r\n  color: rgba(255, 255, 255, 0.65);\r\n}\r\n\r\n::-webkit-input-placeholder .input-line:focus +::input-placeholder {\r\n  color: #fff;\r\n}\r\n\r\n.highlight {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: 400;\r\n  cursor: pointer;\r\n  transition: color .2s ease;\r\n}\r\n\r\n.highlight:hover {\r\n  color: #fff;\r\n  transition: color .2s ease;\r\n}\r\n\r\n.spacing {\r\n  -webkit-box-flex: 1;\r\n  -webkit-flex-grow: 1;\r\n  -ms-flex-positive: 1;\r\n  flex-grow: 1;\r\n  height: 120px;\r\n  font-weight: 300;\r\n  text-align: center;\r\n  margin-top: 10px;\r\n  color: rgba(255, 255, 255, 0.65)\r\n}\r\n\r\n.input-line:focus {\r\n  outline: none;\r\n  border-color: #fff;\r\n  -webkit-transition: all .2s ease;\r\n  transition: all .2s ease;\r\n}\r\n\r\n.ghost-round {\r\n  cursor: pointer;\r\n  background: none;\r\n  border: 1px solid rgba(255, 255, 255, 0.65);\r\n  border-radius: 25px;\r\n  color: rgba(255, 255, 255, 0.65);\r\n  -webkit-align-self: flex-end;\r\n  -ms-flex-item-align: end;\r\n  align-self: flex-end;\r\n  font-size: 19px;\r\n  font-size: 1.2rem;\r\n  font-family: roboto;\r\n  font-weight: 300;\r\n  line-height: 2.5em;\r\n  margin-top: auto;\r\n  margin-bottom: 25px;\r\n  -webkit-transition: all .2s ease;\r\n  transition: all .2s ease;\r\n}\r\n\r\n.ghost-round:hover {\r\n  background: rgba(255, 255, 255, 0.15);\r\n  color: #fff;\r\n  -webkit-transition: all .2s ease;\r\n  transition: all .2s ease;\r\n}\r\n\r\n.input-line {\r\n  background: none;\r\n  margin-bottom: 10px;\r\n  line-height: 2.4em;\r\n  color: #fff;\r\n  font-family: roboto;\r\n  font-weight: 300;\r\n  letter-spacing: 0px;\r\n  letter-spacing: 0.02rem;\r\n  font-size: 19px;\r\n  font-size: 1.2rem;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.65);\r\n  -webkit-transition: all .2s ease;\r\n  transition: all .2s ease;\r\n}\r\n\r\n.full-width {\r\n  width: 100%;\r\n}\r\n\r\n.input-fields {\r\n  margin-top: 25px;\r\n}\r\n\r\n.container {\r\n  display: -webkit-box;\r\n  display: -webkit-flex;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-align: center;\r\n  -webkit-align-items: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  -webkit-box-pack: center;\r\n  -webkit-justify-content: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  background: #eee;\r\n  height: 100%;\r\n}\r\n\r\n.content {\r\n  padding-left: 25px;\r\n  padding-right: 25px;\r\n  display: -webkit-box;\r\n  display: -webkit-flex;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-flex-flow: column;\r\n  -ms-flex-flow: column;\r\n  flex-flow: column;\r\n  z-index: 5;\r\n}\r\n\r\n.welcome {\r\n  font-weight: 200;\r\n  margin-top: 75px;\r\n  text-align: center;\r\n  font-size: 40px;\r\n  font-size: 2.5rem;\r\n  letter-spacing: 0px;\r\n  letter-spacing: 0.05rem;\r\n}\r\n\r\n.subtitle {\r\n  text-align: center;\r\n  line-height: 1em;\r\n  font-weight: 100;\r\n  letter-spacing: 0px;\r\n  letter-spacing: 0.02rem;\r\n}\r\n\r\n.menu {\r\n  background: rgba(0, 0, 0, 0.2);\r\n  width: 100%;\r\n  height: 50px;\r\n}\r\n\r\n.window {\r\n  z-index: 100;\r\n  color: #fff;\r\n  font-family: roboto;\r\n  position: relative;\r\n  display: -webkit-box;\r\n  display: -webkit-flex;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-flex-flow: column;\r\n  -ms-flex-flow: column;\r\n  flex-flow: column;\r\n  box-shadow: 0px 15px 50px 10px rgba(0, 0, 0, 0.2);\r\n  box-sizing: border-box;\r\n  height: 560px;\r\n  width: 360px;\r\n  background: #fff;\r\n  background: url('https://pexels.imgix.net/photos/27718/pexels-photo-27718.jpg?fit=crop&w=1280&h=823') top left no-repeat;\r\n}\r\n\r\n.overlay {\r\n  background: -webkit-linear-gradient(#8CA6DB, #B993D6);\r\n  background: linear-gradient(#8CA6DB, #B993D6);\r\n  opacity: 0.85;\r\n  filter: alpha(opacity=85);\r\n  height: 560px;\r\n  position: absolute;\r\n  width: 360px;\r\n  z-index: 1;\r\n}\r\n\r\n.bold-line {\r\n  background: #e7e7e7;\r\n  position: absolute;\r\n  top: 0px;\r\n  bottom: 0px;\r\n  margin: auto;\r\n  width: 100%;\r\n  height: 360px;\r\n  z-index: 1;\r\n  opacity:0.1;\r\n    background: url('https://pexels.imgix.net/photos/27718/pexels-photo-27718.jpg?fit=crop&w=1280&h=823') left no-repeat;\r\n  background-size:cover;\r\n}\r\n\r\n@media (max-width: 500px) {\r\n  .window {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  .overlay {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n}\r\n  </style>\r\n\r\n  \r\n<script src=\"https://cpwebassets.codepen.io/assets/editor/iframe/iframeConsoleRunner-d8236034cc3508e70b0763f2575a8bb5850f9aea541206ce56704c013047d712.js\"></script>\r\n<script src=\"https://cpwebassets.codepen.io/assets/editor/iframe/iframeRefreshCSS-4793b73c6332f7f14a9b6bba5d5e62748e9d1bd0b5c52d7af6376f3d1c625d7e.js\"></script>\r\n<script src=\"https://cpwebassets.codepen.io/assets/editor/iframe/iframeRuntimeErrors-4f205f2c14e769b448bcf477de2938c681660d5038bc464e3700256713ebe261.js\"></script>\r\n</head>\r\n\r\n<body>\r\n  <div class=\"bold-line\"></div>\r\n<div class=\"container\">\r\n  <div class=\"window\">\r\n    <div class=\"overlay\"></div>\r\n    <div class=\"content\">\r\n      <div class=\"welcome\">Form Title</div>\r\n    \r\n      <div class=\"input-fields\">\r\n        <input type=\"text\" placeholder=\"Full Name\" class=\"input-line full-width\">\r\n        <input type=\"email\" placeholder=\"Email\" class=\"input-line full-width\">\r\n        <input type=\"text\" placeholder=\"Phone\" class=\"input-line full-width\">\r\n\r\n      </div>\r\n      <div><button class=\"ghost-round full-width\">Submit</button></div>\r\n    </div>\r\n  </div>\r\n</div>\r\n  \r\n<script src=\"https://cpwebassets.codepen.io/assets/common/stopExecutionOnTimeout-8216c69d01441f36c0ea791ae2d4469f0f8ff5326f00ae2d00e4bb7d20e24edb.js\"></script>\r\n  <script src=\"https://cdpn.io/cp/internal/boomboom/pen.js?key=pen.js-cb372aa6-4b58-b4b3-3af7-93711b99d0fd\" crossorigin=\"\"></script>\r\n\r\n\r\n</body></html>"}, {"id": "18", "image": "https://drm.software/images/18.png", "source": "<style>.btn{outline: 0;background: #EF3B3A;width: 100%;border: 0;padding: 15px;border-top-left-radius: 3px;border-top-right-radius: 3px;border-bottom-left-radius: 3px;border-bottom-right-radius: 3px;color: #FFFFFF;font-size: 14px;transition: all 0.3 ease;cursor: pointer;}/* Form */\r\n.form {\r\n  position: relative;\r\n  z-index: 1;\r\n  background: #FFFFFF;\r\n  max-width: 300px;\r\n  margin: 0 auto 100px;\r\n  padding: 30px;\r\n  border-top-left-radius: 3px;\r\n  border-top-right-radius: 3px;\r\n  border-bottom-left-radius: 3px;\r\n  border-bottom-right-radius: 3px;\r\n  text-align: center;\r\n}\r\n.form .thumbnail {\r\n  background: #EF3B3A;\r\n  width: 150px;\r\n  height: 150px;\r\n  margin: 0 auto 30px;\r\n  padding: 50px 30px;\r\n  border-top-left-radius: 100%;\r\n  border-top-right-radius: 100%;\r\n  border-bottom-left-radius: 100%;\r\n  border-bottom-right-radius: 100%;\r\n  box-sizing: border-box;\r\n}\r\n.form .thumbnail img {\r\n  display: block;\r\n  width: 100%;\r\n}\r\n.form input {\r\n  outline: 0;\r\n  background: #f2f2f2;\r\n  width: 100%;\r\n  border: 0;\r\n  margin: 0 0 15px;\r\n  padding: 15px;\r\n  border-top-left-radius: 3px;\r\n  border-top-right-radius: 3px;\r\n  border-bottom-left-radius: 3px;\r\n  border-bottom-right-radius: 3px;\r\n  box-sizing: border-box;\r\n  font-size: 14px;\r\n}\r\n.form button {\r\n  outline: 0;\r\n  background: #EF3B3A;\r\n  width: 100%;\r\n  border: 0;\r\n  padding: 15px;\r\n  border-top-left-radius: 3px;\r\n  border-top-right-radius: 3px;\r\n  border-bottom-left-radius: 3px;\r\n  border-bottom-right-radius: 3px;\r\n  color: #FFFFFF;\r\n  font-size: 14px;\r\n  transition: all 0.3 ease;\r\n  cursor: pointer;\r\n}\r\n.form .message {\r\n  margin: 15px 0 0;\r\n  color: #b3b3b3;\r\n  font-size: 12px;\r\n}\r\n.form .message a {\r\n  color: #EF3B3A;\r\n  text-decoration: none;\r\n}\r\n.form .register-form {\r\n  display: none;\r\n}\r\n\r\n.container {\r\n  position: relative;\r\n  z-index: 1;\r\n  max-width: 300px;\r\n  margin: 0 auto;\r\n}\r\n.container:before, .container:after {\r\n  content: \"\";\r\n  display: block;\r\n  clear: both;\r\n}\r\n.container .info {\r\n  margin: 50px auto;\r\n  text-align: center;\r\n}\r\n.container .info h1 {\r\n  margin: 0 0 15px;\r\n  padding: 0;\r\n  font-size: 36px;\r\n  font-weight: 300;\r\n  color: #1a1a1a;\r\n}\r\n.container .info span {\r\n  color: #4d4d4d;\r\n  font-size: 12px;\r\n}\r\n.container .info span a {\r\n  color: #000000;\r\n  text-decoration: none;\r\n}\r\n.container .info span .fa {\r\n  color: #EF3B3A;\r\n}\r\n\r\n/* END Form */\r\n/* Demo Purposes */\r\nbody {\r\n  background: #ccc;\r\n  font-family: \"Roboto\", sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\nbody:before {\r\n  content: \"\";\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  display: block;\r\n  background: rgba(255, 255, 255, 0.8);\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n#video {\r\n  z-index: -99;\r\n  position: fixed;\r\n  top: 50%;\r\n  left: 50%;\r\n  min-width: 100%;\r\n  min-height: 100%;\r\n  width: auto;\r\n  height: auto;\r\n  transform: translateX(-50%) translateY(-50%);\r\n}</style>\r\n\r\n<div class=\"container\">\r\n  <div class=\"info\">\r\n    <h1>Form Title</h1>\r\n  </div>\r\n</div>\r\n<div class=\"form\">\r\n  <div class=\"thumbnail\"><img src=\"https://s3-us-west-2.amazonaws.com/s.cdpn.io/169963/hat.svg\"/></div>\r\n  <form class=\"login-form\">\r\n    <input type=\"text\" placeholder=\"Full Name\"/>\r\n    <input type=\"email\" placeholder=\"Enter Email\"/>\r\n    <input type=\"text\" placeholder=\"Enter Phone\"/>\r\n <input style=\"outline: 0;background: #EF3B3A;width: 100%;border: 0;padding: 15px;border-top-left-radius: 3px;border-top-right-radius: 3px;border-bottom-left-radius: 3px;border-bottom-right-radius: 3px;color: #FFFFFF;font-size: 14px;transition: all 0.3 ease;cursor: pointer;\" type=\"submit\" value=\"Submit\" class=\"btn\"/>\r\n  </form>\r\n</div>"}, {"id": "19", "image": "https://drm.software/images/19.png", "source": "<style>.b$font-family:   \"Roboto\";\r\n$font-size:     14px;\r\n\r\n$color-primary: #ABA194;\r\n\r\n* {\r\n    margin: 0;\r\n    padding: 0;\r\n    box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n    font-family: $font-family;\r\n    font-size: $font-size;\r\n    background-size: 200% 100% !important;\r\n    animation: move 10s ease infinite;\r\n    transform: translate3d(0, 0, 0);\r\n    background: linear-gradient(45deg, #49D49D 10%, #A2C7E5 90%);\r\n    height: 100vh\r\n}\r\n\r\n.user {\r\n    width: 90%;\r\n    max-width: 340px;\r\n    margin: 10vh auto;\r\n}\r\n\r\n.user__header {\r\n    text-align: center;\r\n    opacity: 0;\r\n    transform: translate3d(0, 500px, 0);\r\n    animation: arrive 500ms ease-in-out 0.7s forwards;\r\n}\r\n\r\n.user__title {\r\n    font-size: $font-size;\r\n    margin-bottom: -10px;\r\n    font-weight: 500;\r\n    color: white;\r\n}\r\n\r\n.form {\r\n    margin-top: 40px;\r\n    border-radius: 6px;\r\n    overflow: hidden;\r\n    opacity: 0;\r\n    transform: translate3d(0, 500px, 0);\r\n    animation: arrive 500ms ease-in-out 0.9s forwards;\r\n}\r\n\r\n.form--no {\r\n    animation: NO 1s ease-in-out;\r\n    opacity: 1;\r\n    transform: translate3d(0, 0, 0);\r\n}\r\n\r\n.form__input {\r\n    display: block;\r\n    width: 100%;\r\n    padding: 20px;\r\n    font-family: $font-family;\r\n    -webkit-appearance: none;\r\n    border: 0;\r\n    outline: 0;\r\n    transition: 0.3s;\r\n    \r\n    &:focus {\r\n        background: darken(#fff, 3%);\r\n    }\r\n}\r\n\r\n.btn {\r\n    display: block;\r\n    width: 100%;\r\n    padding: 20px;\r\n    font-family: $font-family;\r\n    -webkit-appearance: none;\r\n    outline: 0;\r\n    border: 0;\r\n    color: white;\r\n    background: $color-primary;\r\n    transition: 0.3s;\r\n    \r\n    &:hover {\r\n        background: darken($color-primary, 5%);\r\n    }\r\n}\r\n\r\n@keyframes NO {\r\n  from, to {\r\n    -webkit-transform: translate3d(0, 0, 0);\r\n    transform: translate3d(0, 0, 0);\r\n  }\r\n\r\n  10%, 30%, 50%, 70%, 90% {\r\n    -webkit-transform: translate3d(-10px, 0, 0);\r\n    transform: translate3d(-10px, 0, 0);\r\n  }\r\n\r\n  20%, 40%, 60%, 80% {\r\n    -webkit-transform: translate3d(10px, 0, 0);\r\n    transform: translate3d(10px, 0, 0);\r\n  }\r\n}\r\n\r\n@keyframes arrive {\r\n    0% {\r\n        opacity: 0;\r\n        transform: translate3d(0, 50px, 0);\r\n    }\r\n    \r\n    100% {\r\n        opacity: 1;\r\n        transform: translate3d(0, 0, 0);\r\n    }\r\n}\r\n\r\n@keyframes move {\r\n    0% {\r\n        background-position: 0 0\r\n    }\r\n\r\n    50% {\r\n        background-position: 100% 0\r\n    }\r\n\r\n    100% {\r\n        background-position: 0 0\r\n    }\r\n}</style><div class=\"user\">\r\n    <header class=\"user__header\">\r\n        <h1 class=\"user__title\">Form Title</h1>\r\n    </header>\r\n    \r\n    <form class=\"form\">\r\n        <div class=\"form__group\">\r\n            <input type=\"text\" placeholder=\"Username\" class=\"form__input\" />\r\n        </div>\r\n        \r\n        <div class=\"form__group\">\r\n            <input type=\"email\" placeholder=\"Email\" class=\"form__input\" />\r\n        </div>\r\n        \r\n        <div class=\"form__group\">\r\n            <input type=\"password\" placeholder=\"Password\" class=\"form__input\" />\r\n        </div>\r\n        \r\n        <button class=\"btn\" type=\"button\" style=\"    display: block;width: 100%;padding: 20px;font-family: Roboto;-webkit-appearance: none;outline: 0;border: 0;color: white;background: #ABA194;transition: 0.3s;\">Submit</button>\r\n    </form>\r\n</div>"}, {"id": "21", "image": "https://drm.software/images/21.png", "source": "<style>@import url(\"https://fonts.googleapis.com/css?family=SourceSansPro\");\r\n* {\r\n  -webkit-box-sizing: border-box;\r\n  -moz-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n}\r\nhtml,\r\nbody {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f2f1ef;\r\n}\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\np,\r\na,\r\ninput,\r\nbutton {\r\n  font-family: 'Source Sans Pro', sans-serif;\r\n}\r\na {\r\n  text-decoration: none;\r\n}\r\ninput {\r\n  width: 100%;\r\n  border: none;\r\n  margin: 0;\r\n  padding: 20px 30px;\r\n  font-size: 18px;\r\n  font-weight: 200;\r\n  outline: none;\r\n  background: none;\r\n}\r\n.login-form {\r\n  width: 360px;\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  margin: 100px 0 0 -180px;\r\n}\r\n.login-form header {\r\n  background: #22313f;\r\n  color: #fff;\r\n      padding: 6px;\r\n    height: 126px;\r\n  overflow: hidden;\r\n  text-align: center;\r\n}\r\n.login-form header .photo,\r\n.login-form header .user-info {\r\n  width: 100%;\r\n}\r\n.login-form header .photo {\r\n  margin-top: 30px;\r\n}\r\n.login-form header img {\r\n  border-radius: 50%;\r\n}\r\n.login-form header .user-info {\r\n  padding-top: 20px;\r\n}\r\n.login-form header .user-info h3,\r\n.login-form header .user-info h5 {\r\n  font-weight: 200;\r\n}\r\n.login-form header .user-info h3 {\r\n  font-size: 36px;\r\n}\r\n.login-form header .user-info h5 {\r\n  font-size: 12px;\r\n  letter-spacing: 0.05em;\r\n}\r\n.login-form section .input,\r\n.login-form section .password {\r\n  border-bottom: 1px solid #d2d7d3;\r\n  background: #fff;\r\n}\r\n.login-form section .password {\r\n  position: relative;\r\n}\r\n.login-form section .password .toggle-password {\r\n  font-size: 12px;\r\n  color: #d2d7d3;\r\n  border: 1px solid #d2d7d3;\r\n  border-radius: 4px;\r\n  padding: 4px 8px;\r\n  position: absolute;\r\n  right: 30px;\r\n  top: 20px;\r\n}\r\n.login-form section .password .toggle-password:hover {\r\n  background: #d2d7d3;\r\n  color: #fff;\r\n}\r\n.login-form section .confirm-password,\r\n.login-form section .inner {\r\n  -webkit-transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n  -moz-transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n  -o-transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n  -ms-transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n  transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n}\r\n.login-form section .confirm-password {\r\n  position: relative;\r\n  -webkit-perspective: 1000px;\r\n  -moz-perspective: 1000px;\r\n  -ms-perspective: 1000px;\r\n  perspective: 1000px;\r\n  height: 64px;\r\n}\r\n.login-form section .inner {\r\n  height: 0px;\r\n  -webkit-transform-origin: top;\r\n  -moz-transform-origin: top;\r\n  -o-transform-origin: top;\r\n  -ms-transform-origin: top;\r\n  transform-origin: top;\r\n  -webkit-transform: rotateX(-90deg);\r\n  -moz-transform: rotateX(-90deg);\r\n  -o-transform: rotateX(-90deg);\r\n  -ms-transform: rotateX(-90deg);\r\n  transform: rotateX(-90deg);\r\n  background-color: #d2d7d3;\r\n}\r\n.login-form footer {\r\n  background: #fff;\r\n  overflow: hidden;\r\n  -webkit-transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n  -moz-transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n  -o-transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n  -ms-transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n  transition: all 500ms cubic-bezier(0.645, 0.045, 0.355, 1);\r\n  -webkit-transform: translate3d(0px, -64px, 0px);\r\n  -moz-transform: translate3d(0px, -64px, 0px);\r\n  -o-transform: translate3d(0px, -64px, 0px);\r\n  -ms-transform: translate3d(0px, -64px, 0px);\r\n  transform: translate3d(0px, -64px, 0px);\r\n}\r\n.login-form footer a {\r\n  color: #d2d7d3;\r\n  float: left;\r\n  height: 40px;\r\n  line-height: 0px;\r\n  padding: 30px;\r\n}\r\n.login-form footer .action a {\r\n  background: #22a7f0;\r\n  color: #fff;\r\n  float: right;\r\n  padding: 30px 60px;\r\n}\r\n.login-form footer .action a:hover {\r\n  background: #38b0f2;\r\n}\r\n.login-form.confirming .inner {\r\n  height: 64px;\r\n  border-bottom: 1px solid #f2f1ef;\r\n  -webkit-transform: rotateX(0deg);\r\n  -moz-transform: rotateX(0deg);\r\n  -o-transform: rotateX(0deg);\r\n  -ms-transform: rotateX(0deg);\r\n  transform: rotateX(0deg);\r\n  background-color: #fff;\r\n}\r\n.login-form.confirming footer {\r\n  border-top: 1px solid #d2d7d3;\r\n  -webkit-transform: translate3d(0px, -1px, 0px);\r\n  -moz-transform: translate3d(0px, -1px, 0px);\r\n  -o-transform: translate3d(0px, -1px, 0px);\r\n  -ms-transform: translate3d(0px, -1px, 0px);\r\n  transform: translate3d(0px, -1px, 0px);\r\n}\r\n</style>\r\n\r\n<div class=\"login-form\">\r\n  <form action=\"\">\r\n    <header>\r\n      <div class=\"user-info\">\r\n        <h3>Form Title </h3>\r\n        <h5>Write someting here..</h5>\r\n      </div>\r\n    </header>\r\n    <section style=\"margin-bottom: 63px;\">\r\n      <div class=\"input\">\r\n        <input type=\"email\" placeholder=\"Email Address\"/>\r\n      </div>\r\n      <div class=\"input\">\r\n        <input type=\"text\" placeholder=\"Full Name\"/>\r\n      </div>\r\n   <div class=\"input\">\r\n        <input type=\"text\" placeholder=\"Enter Phone\"/>\r\n      </div>\r\n    </section>\r\n    <footer>\r\n      <div class=\"action\"><input style=\"background: #22a7f0;color: #fff;width: 50%;border: none;padding: 20px 30px;float: right;text-align: center;\" type=\"submit\"  value=\"Submit\"></div>\r\n    </footer>\r\n  </form>\r\n</div>"}]