<?php

namespace App\Modules\Catalog;

use App\Models\Catalog\Category;
use App\Enums\Category as CategoryEnums;
use Illuminate\Support\Facades\Auth;

class Categories
{
    public $params;

    /**
     * Pass parameters array.
     *
     * @param array $params
     * @param $operation
     */
    public function __construct(array $params=[], $operation=1)
    {
        $params = $this->map($params, $operation);
        $this->params = $params;
    }


    /**
     * Get multiple categories
     *
     * @return collection
     */
    public function getAll()
    {
        return Category::where($this->params)->select(CategoryEnums::RETURNS)->get();
    }

    /**
     * Get a single category
     *
     * @return array
     */
    public function getOne()
    {
        return Category::where($this->params)->select(CategoryEnums::RETURNS)->first();
    }

    /**
     * create a new category
     *
     * @return mixed
     */
    public function create()
    {
        dd($this->params);
    }


    public function findOrCreate(array $categories,$lang)
    {
        $user = Auth::Id();
        $all_ids = [];
        $trans_cat = "category_name_".$lang;
        foreach ($categories as $key => $value) {
            $existing = Category::where([$trans_cat => $value, 'user_id' => $user])->first();
            if($existing){
                $all_ids[] = $existing->id;
            }
            else{
                $all_ids[] = Category::create(['user_id' => $user, $trans_cat => $value])->id;
            }
        }
        return $all_ids;
    }

    /**
     * update an existing category
     *
     * @param $pid
     * @return void
     */

    public function update($pid)
    {

    }

    /**
     * Create mapping for request parameters with corresponding operations
     *
     * @param array $params
     * @param int $operation
     * @return array
     */
    public function map(array $params, int $operation)
    {
        $all_params = [];

        foreach ($params as $key => $param) {
            if (isset(CategoryEnums::PARAMS[$key])) {
                $all_params[CategoryEnums::PARAMS[$key]] = $param;
            } else {
                $all_params[$key] = $param;
            }
        }
        return $all_params;
    }
}
