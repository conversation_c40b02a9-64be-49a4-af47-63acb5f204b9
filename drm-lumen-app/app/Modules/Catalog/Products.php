<?php /** @noinspection PhpUnusedParameterInspection */

namespace App\Modules\Catalog;

use App\Models\Catalog\Product;
use App\Models\Country;
use App\Models\Catalog\Ean;
use App\Enums\Product as ProductEnums;
use App\Enums\Status;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Throwable;

class Products
{
    public $params;
    private $user;
    protected $table = "drm_products";

    /**
     * Pass parameters array.
     *
     * @param array $params
     * @param $operation
     * @param $userId
     * @noinspection PhpPossiblePolymorphicInvocationInspection
     */
    public function __construct(array $params, $operation, ...$userId)
    {
        $params = $this->map($params, $operation);
        $this->params = $params;
        if (!$userId) {
            $this->user = Auth::user();
            $this->params['user_id'] = $this->user->id;
        } else {
//           $this->user = Auth::user();
            $this->params['user_id'] = $userId;
        }
    }


    /**
     * Get multiple products
     *
     * @return Collection
     * @noinspection PhpUndefinedMethodInspection
     */
    public function getAll()
    {
        $country_id = $this->params['drm_products.country_id'];
        $country = Country::find($country_id);
        // $trans_table = "drm_translation_" . $country->language_shortcode;

        $data = DB::table($this->table)
            // ->leftjoin($trans_table, $trans_table . '.product_id', '=', $this->table . '.id')
            ->where($this->params)
            ->select(ProductEnums::RETURNS)
            // ->addSelect($trans_table . ".title")
            ->orderBy('id', 'desc')
            ->get();
        return $data;
        // $all_data = array();
        // foreach ($data as $product) {
        //     $record = $product->toArray();
        //     $productlang = $product->details();
        //     $record['title'] = $productlang->title;
        //     $record['description'] = $productlang->description;

        //     $all_data[] = $record;
        // }
        // return $all_data;
    }

    /**
     * Get a single product
     *
     * @return array
     * @noinspection PhpUndefinedMethodInspection
     */
    public function getOne()
    {
        $product = Product::where($this->params)->select(ProductEnums::RETURNS)->first();
        $data = $product->toArray();
        $productlang = $product->details();
        $data['title'] = $productlang->title;
        $data['description'] = $productlang->description;
        return $data;
    }

    /**
     * create a new product
     *
     * @return mixed
     * @throws Exception
     * @noinspection PhpUndefinedMethodInspection
     */
    public function create()
    {
        $valid = $this->eanValidate($this->params['ean'], $this->params['user_id']);

        if ($valid) {
            try {
                $translation = [
                    'title' => $this->params['title'],
                    'description' => $this->params['description'],
                    'ean' => $this->params['ean']
                ];
                unset($this->params['title']);
                unset($this->params['description']);

                $categories = $this->params['categories'];
                unset($this->params['categories']);

                $images = $this->generateImages($this->params['image']);
                $this->params['image'] = $images;

                $pid = Product::create($this->params)->id;

                $translation['product_id'] = $pid;

                $country = Country::find($this->params['drm_products.country_id'])->language_shortcode;

                DB::table('drm_translation_' . $country)->insert($translation);

                $categories = $this->processInsertCategories($categories, $pid, $country);

                DB::table('drm_product_categories')->insert($categories);

                $this->connectChannel($pid, $this->params['ean']);
                return response("", Status::CREATED);
            } catch (Exception $e) {
                throw $e;
            }
        } else {
            throw new Exception("EAN already exists");
        }
    }

    /**
     * update an existing product
     *
     * @param int $ean
     * @return mixed
     * @noinspection PhpPossiblePolymorphicInvocationInspection
     * @noinspection PhpUndefinedMethodInspection
     */

    public function update(int $ean)
    {
        $requests = $this->params;
        $product = Product::where(["user_id" => $this->user->id, "ean" => $ean])->first();

        if (!$product)
            return response()->json("Product not found", 404);

        $pid = $product->id;

        $country = Country::find($this->params["drm_products.country_id"])->language_shortcode;

        unset($this->params["drm_products.country_id"]);

        if (isset($this->params["title"]) || isset($this->params["description"])) {
            $translation = array();

            if (isset($this->params["title"])) {
                $translation["title"] = $this->params["title"];
                unset($this->params["title"]);
            }

            if (isset($this->params["description"])) {
                $translation["description"] = $this->params["description"];
                unset($this->params["description"]);
            }

            $trans_table = "drm_translation_" . $country;

            if (count($translation) > 0)
                DB::table($trans_table)->where("product_id", $pid)->update($translation);
        }

        if (isset($this->params["categories"])) {
            $categories = $this->processInsertCategories($this->params["categories"], $pid, $country);
            DB::table("drm_product_categories")->where("product_id", $pid)->delete();
            DB::table("drm_product_categories")->insert($categories);
            unset($this->params["categories"]);
        }

        if (isset($this->params["tags"]))
            $this->params["tags"] = $this->processTags($this->params["tags"], $product);

        if (isset($this->params["image"])) {
            $images = $this->generateImages($this->params["image"]);
            $this->params["image"] = $images;
        }

        $status = json_decode($product->update_status, true);

        foreach ($this->params as $key => $value)
            $status[$key] = 0;

        $this->params["update_status"] = json_encode($status);

        Product::where("id", $pid)->update($this->params);
        return response()->json(["messages" => "Updated", "requests" => $requests, Product::where("id", $pid)->first()], Status::UPDATED);
    }


    /**
     * delete a product
     *
     * @return mixed
     */
    public function delete()
    {
        dd("ok");
    }

    /**
     * Create mapping for request parameters with corresponding operations
     *
     * @param array $params
     * @param int $operation
     * @return array
     */
    public function map(array $params, int $operation = 0)
    {
        $all_params = [];

        foreach ($params as $key => $param) {
            if (isset(ProductEnums::PARAMS[$key])) {
                $all_params[ProductEnums::PARAMS[$key]] = $param;
            } else {
                $all_params[$key] = $param;
            }
        }
        return $all_params;
    }


    /** @noinspection PhpUndefinedMethodInspection */
    public function eanValidate($ean, $user)
    {
        $exists = Product::where(['ean' => $ean, 'user_id' => $user])->count();
        $custom_ean = Ean::where(['ean' => $ean, 'user_id' => $user])->count();
        if ($exists || $custom_ean) return false;
        return true;
    }


    public function generateImages(array $images)
    {
        $array = [];
        foreach ($images as $key => $image) {
            $array[] = [
                'id' => $key + 1,
                'order' => $key + 1,
                'status' => 1,
                'src' => (string)$image
            ];
        }
        return json_encode($array);
    }


    public function processInsertCategories($categories, $pid, $lang)
    {
        $module = new Categories();
        $categories = $module->findOrCreate($categories, $lang);
        $array = [];
        foreach ($categories as $category_id) {
            $array[] = [
                'category_id' => $category_id,
                'product_id' => $pid
            ];
        }
        return $array;
    }

    public function processTags($array, $product)
    {
        $array = explode(',', $array);
        $old_tags = explode(',', $product->tags);
        $array = array_merge($old_tags, $array);

        $plain_tags = array();
        foreach ($array as $key => $tag) {
            if ($tag[0] == '#') $plain_tags[] = str_replace('#', '', $tag);
            else $plain_tags[] = $tag;
        }
        $array = array_unique($plain_tags);

        $tags = "";
        $i = 0;
        foreach ($array as $value) {
            $i++;
            if ($i == count($array)) {
                if (strpos($value, '#') !== false) {
                    $tags .= $value;
                } else {
                    $tags .= "#" . $value;
                }
            } else {
                if (strpos($value, '#') !== false) {
                    $tags .= $value . ",";
                } else {
                    $tags .= "#" . $value . ",";
                }
            }
        }
        return $tags;
    }


    public function connectChannel($id, $ean)
    {
        try {
            $shop = \App\Models\Shop::where(['channel' => 10, 'user_id' => $this->user->id])->first();
            DB::table('droptienda_products')->insert([
                'ean' => $ean, 'shop_id' => $shop->id, 'user_id' => $this->user->id, 'product_id' => $id
            ]);
        } catch (Throwable $th) {
//            throw $th;
        }

    }
}
