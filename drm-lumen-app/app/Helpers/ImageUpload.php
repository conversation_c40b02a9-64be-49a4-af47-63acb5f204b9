<?php

/**
 * @noinspection PhpUnused
 */

use Illuminate\Support\Facades\Storage;

/**
 * @param $file
 * @param string $path
 * @return string|null
 */
function uploadFileImage($file, $path = 'uploads'): ?string
{
    $name = $file->hashName();
    $file_name = $path . '/' . $name;
    Storage::disk('spaces')->put($file_name, file_get_contents($file), 'public');
    if (Storage::disk('spaces')->exists($file_name)) {
        return Storage::disk('spaces')->url($file_name);
    }
    return null;
}

/**
 * @param $base64
 * @param $filename
 * @param string $extension
 * @param string $path
 * @return string|null
 */
//function uploadBase64Image($base64, $filename, $path = 'uploads'): ?string
//{
//    $split = explode(",", substr($base64, 5), 2);
//    $mime_split = explode(";", $split[0], 2);
//    $mime = explode("/", $mime_split[0], 2);
//    if (count($mime) == 2) {
//        $extension = $mime[1];
//        $fullPath = $path . '/' . $filename . "." . $extension;
//        Storage::disk('spaces')->put($fullPath, base64_decode($split[1]), 'public');
//        if (Storage::disk('spaces')->exists($fullPath)) {
//            return Storage::disk('spaces')->url($fullPath);
//        }
//    }
//    return null;
//}

function uploadBase64Image($base64, $filename, $extension = 'png', $path = 'uploads'): ?string
{
    $fullPath = $path . '/' . $filename . "." . $extension;
    Storage::disk('spaces')->put($fullPath, base64_decode($base64), 'public');
    if (Storage::disk('spaces')->exists($fullPath)) {
        return Storage::disk('spaces')->url($fullPath);
    }
    return null;
}
function drmIsJSON($string){
    return is_string($string) && is_array(json_decode($string, true)) && (json_last_error() == JSON_ERROR_NONE) ? true : false;
}
function billingInfoJson($customer_info){
    $country = isset($customer_info['country_billing'])? $customer_info['country_billing'] : (isset($customer_info['country'])? $customer_info['country'] : null);
    // $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
      'name' => isset($customer_info['billing_name'])? $customer_info['billing_name'] : (isset($customer_info['first_name'])? $customer_info['first_name'] : null),
      'company' => isset($customer_info['billing_company'])? $customer_info['billing_company'] : (isset($customer_info['company'])? $customer_info['company'] : null),
      'street' => isset($customer_info['street_billing'])? $customer_info['street_billing'] : (isset($customer_info['address'])? $customer_info['address'] : null),
      'address' => isset($customer_info['address_billing'])? $customer_info['address_billing'] : null,
      'zip_code' => isset($customer_info['zipcode_billing'])? $customer_info['zipcode_billing'] : (isset($customer_info['postal_code'])? $customer_info['postal_code'] : null),
      'city' => isset($customer_info['city_billing'])? $customer_info['city_billing'] : (isset($customer_info['city'])? $customer_info['city'] : null),
      'state' => isset($customer_info['state_billing'])? $customer_info['state_billing'] : (isset($customer_info['state'])? $customer_info['state'] : null),
      'country' => $country,
    ]);
}
function shippingInfoJson($customer_info){
    $country = isset($customer_info['country_shipping'])? $customer_info['country_shipping'] : (isset($customer_info['country'])? $customer_info['country'] : null);
    // $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
      'name' =>  isset($customer_info['shipping_name'])? $customer_info['shipping_name'] : (isset($customer_info['first_name'])? $customer_info['first_name'] : null),
      'company' => isset($customer_info['shipping_company'])? $customer_info['shipping_company'] : (isset($customer_info['company'])? $customer_info['company'] : null),
      'street' => isset($customer_info['street_shipping'])? $customer_info['street_shipping'] : (isset($customer_info['address'])? $customer_info['address'] : null),
      'address' => isset($customer_info['address_shipping'])? $customer_info['address_shipping'] : null,
      'zip_code' => isset($customer_info['zipcode_shipping'])? $customer_info['zipcode_shipping'] : (isset($customer_info['postal_code'])? $customer_info['postal_code'] : null),
      'city' => isset($customer_info['city_shipping'])? $customer_info['city_shipping'] : (isset($customer_info['city'])? $customer_info['city'] : null),
      'state' => isset($customer_info['state_shipping'])? $customer_info['state_shipping'] : (isset($customer_info['state'])? $customer_info['state'] : null),
      'country' => $country,
    ]);
}
function drmCountryNameFull($country_code){
    $tax_country = null;
    if($country_code){
      $tax_country = DB::table('tax_rates')
      ->where('country_code', 'like', $country_code)
      ->orWhere('country', 'like', $country_code)
      ->orWhere('country_de', 'like', $country_code)
      ->select('country')->first();
    }
    return ($tax_country && $tax_country->country)? $tax_country->country : $country_code;
}
//update billing / shipping address json
function updateBillingShippingAddress($new_value, $old_value){
    if (drmIsJSON($new_value) && drmIsJSON($old_value)) {
      $json_data = [];
      $new_json = json_decode($new_value, true);
      $old_json = json_decode($old_value, true);

      foreach ($new_json as $key => $value) {
        $json_data[$key] = ( (is_null($new_json[$key]) || ($new_json[$key]=='') ) && isset($old_json[$key]) )? $old_json[$key] : $new_json[$key];
      }
      foreach ($old_json as $key => $value) {
        if ( !isset($json_data[$key]) ) $json_data[$key] = $old_json[$key];
      }
      return json_encode($json_data);
    }
    return drmIsJSON($new_value)? $new_value : (drmIsJSON($old_value)? $old_value : billingInfoJson([]));
  }