<?php

namespace App\Jobs;

use App\Models\DroptiendaPullHistory;
use App\Models\DroptiendaSyncHistory;
use App\Services\DroptiendaSyncService;
use App\Services\Product\ProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DroptiendaNotifyJob implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 180;
    protected $pullHistory;
    public $connectionName = 'droptienda';

    /**
     * Create a new job instance.
     *
     * @param DroptiendaPullHistory $pullHistory
     */
    public function __construct(DroptiendaPullHistory $pullHistory)
    {
        $this->pullHistory = $pullHistory;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $pullHistory = $this->pullHistory;
        $droptiendaSyncService = new DroptiendaSyncService($pullHistory->user_id);
        $response = $droptiendaSyncService->notifyForSync();

        if ($response['data']) {
            array_map(function ($item) use ($pullHistory) {
                DroptiendaPullHistory::where([
                    'user_id' => $pullHistory->user_id,
                    'dt_ref_id' => $item['dt_ref_id'],
                ])->update(['synced_at' => $item['synced_at']]);
            }, $response['data']);
        }
    }
}
