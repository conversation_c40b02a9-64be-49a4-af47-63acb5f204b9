<?php

namespace App\Jobs;

use App\Models\DroptiendaSyncHistory;
use App\Services\Product\ProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DroptiendaSyncJob implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 120;
    public int $user_id = 0;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(int $user_id = 0)
    {
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $syncHistories = DroptiendaSyncHistory::whereNull('synced_at')
            ->where('tries', '<', 3)
            ->limit(env('SYNC_ITEM_LIMIT', 50))
            ->orderBy('id', 'desc');

        if ($this->user_id) {
            $syncHistories->where('user_id', $this->user_id);
        }

        $syncHistories = $syncHistories->get();

        foreach ($syncHistories as $syncHistory) {
            switch ($syncHistory->sync_type) {
                case \App\Enums\DroptiendaSyncType::CATEGORY:
                    app(\App\Services\Category\CategoryService::class)->syncCategoryToDroptienda($syncHistory);
                    break;

                case \App\Enums\DroptiendaSyncType::PRODUCT:
                    app(ProductService::class)->syncProductToDroptienda($syncHistory);
                    break;
            }
        }
    }
}
