<?php

namespace App\Models\trello;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @method static create(array $array)
 * @method static where(string $string, $id)
 */
class CommentModel extends Model
{
    protected $table = 'drm_task_comments';

    protected $fillable = [
        "task_id", "comment", "files", "cms_user_id", "audio_file"
    ];

    public function task(): HasOne
    {
        return $this->hasOne(TaskModel::class, "id", "task_id");
    }
}
