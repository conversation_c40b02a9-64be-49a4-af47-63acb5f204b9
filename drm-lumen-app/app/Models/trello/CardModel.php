<?php

namespace App\Models\trello;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @method static select(string $string, string $string1, string $string2)
 * @method static where(string $string, mixed $id)
 */
class CardModel extends Model
{
    protected $table = 'drm_project_cards';

    public function tasks(): HasMany
    {
        return $this->hasMany(TaskModel::class, "drm_project_card_id", "id")->orderBy('position');
    }

}
