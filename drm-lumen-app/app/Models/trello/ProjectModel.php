<?php

namespace App\Models\trello;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

/**
 * @method static select(string $string, string $string1, string $string2, string $string3, string $string4, string $string5)
 * @method static where(string $string, mixed $user_id)
 */
class ProjectModel extends Model
{
    protected $table = 'drm_projects';

    public function members()
    {
    	return $this->belongsToMany(User::class,"drm_project_members","drm_project_id","cms_user_id");
    }

    public function cards()
    {
        return $this->hasMany(CardModel::class,'drm_project_id','id');
    }
}
