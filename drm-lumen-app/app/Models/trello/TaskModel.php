<?php

namespace App\Models\trello;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @method static select(string $string, string $string1, string $string2, string $string3, string $string4, string $string5)
 * @method static where(string $string, mixed $id)
 */
class TaskModel extends Model
{
    protected $table = 'drm_project_tasks';

    public function card(): HasOne
    {
        return $this->hasOne(CardModel::class, "id", "drm_project_card_id");
    }

    public function comments(): HasMany
    {
        return $this->hasMany(CommentModel::class, "task_id", "id");
    }

    public function checklists(): Has<PERSON>any
    {
        return $this->hasMany(ChecklistModel::class, "task_id", "id");
    }
}
