<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    protected $table = 'countries';

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function categories()
    {
        return $this->hasMany(Category::class);
    }
    
    public function tax($user_id)
    {  
        $small_business = \DB::table('drm_invoice_setting')
        ->select('small_business')
        ->where('cms_user_id',$user_id)
        ->first();

        if($small_business)
        {   
            if(!$small_business->small_business){
                return \DB::table('tax_rates')
                ->where('id', '=', 4)
                ->select('charge')
                ->first()->charge;
            }
            else
            {
                return 0;
            }

        }
        else{
            return \DB::table('tax_rates')
            ->where('id', '=', 4)
            ->select('charge')
            ->first()->charge;
        }
    }
}
