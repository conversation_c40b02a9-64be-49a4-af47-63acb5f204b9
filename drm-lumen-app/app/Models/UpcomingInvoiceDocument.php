<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;
class UpcomingInvoiceDocument extends Model
{
    //  use SoftDeletes;
     /**
       * The attributes that should be mutated to dates.
       *
       * @var array
       */
      protected $table = 'upcoming_invoice_documents';
    //   protected $dates = ['deleted_at'];
  
      /**
       * The attributes that aren't mass assignable.
       *
       * @var array
       */
      // public function supplier()
      // {
      //   return $this->belongsTo('App\DeliveryCompany','id','delivery_company_id');
      // }

      // public function invoicePDF()
      // {
      //   return $this->belongsTo('App\DeliveryCompany','id','delivery_company_id');
      // }


      protected $guarded = ['id'];
}
