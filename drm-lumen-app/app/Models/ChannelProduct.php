<?php

namespace App\Models;

use App\DrmProduct;
use App\Enums\Channel;
use App\Enums\ChannelProductConnectedStatus;
use App\Shop;
use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class ChannelProduct extends Model
{
    protected $fillable = [
        'user_id',
        'drm_product_id',
        'channel',
        'shop_id',
        'status',
        'title',
        'item_number',
        'ean',
        'additional_eans',
        'description',
        'short_description',
        'internal_comments',
        'images',
        'ek_price',
        'vk_price',
        'stock',
        'category_id',
        'item_weight',
        'item_unit',
        'item_size',
        'item_color',
        'note',
        'production_year',
        'brand',
        'materials',
        'tags',
        'gender',
        'industry_template_data',
        'update_status',
        'calculation_id',
        'delivery_company_id',
        'delivery_days',
        'drm_import_id',
        'country_id',
        'attributes',
        'variants',
        'is_connected',
        'price_droptienda',
        'shipping_cost',
        'connection_status',
        'missing_attributes',
        'error_response',
        'rq_limit',
        'rq',
        'price_on_request',
        'offer_uvp',
        'offer_options',
        'tax_included_price',
        'metadata',
        'drm_categories',
        'manufacturer',
        'manufacturer_link',
        'manufacturer_id',
        'custom_tariff_number',
        'region',
        'country_of_origin',
        'min_stock',
        'gross_weight',
        'net_weight',
        'product_length',
        'product_width',
        'product_height',
        'shipping_method_id',
        'shipping_company_id',
        'options',
        'stock_of_status',
        'price_of_status',
        'packaging_dimensions',
        'packaging_length',
        'packaging_width',
        'packaging_height',
        'min_order',
        'packaging_unit',
        'tax_type'
    ];
    protected $attributes = [
        'custom_tariff_number' => 0,
    ];
    protected $casts = [
        'title' => 'array',
        'description'   => 'array',
        'short_description' => 'array',
//        'images'      => 'array',
        'update_status' => 'array',
        'attributes'    => 'array',
        'variants'      => 'array',
        'metadata'      => 'array',
        'offer_options' => 'array'
    ];

    public function getNameAttribute()
    {
        return Arr::get($this->title, 'de', '');
    }

    public function getConnectedStatusAttribute()
    {
        if ($this->is_connected) {
            return ChannelProductConnectedStatus::CONNECTED;
        }

        $lang = 'de';
        $requiredFields = array_merge(config('channel.required_fields.default', []), config('channel.required_fields.'.$this->channel, []));
        $readyToExport = true;
        foreach ($requiredFields as $field) {

            if (in_array($field, ['title', 'description'])) {
                if(empty($this->{$field}[$lang])){
                    $readyToExport = false;
                }
            } elseif ($field == 'vk_price') {
                if(empty((float) $this->{$field}) && empty((float) $this->uvp)){
                    $readyToExport = false;
                }
            } elseif ($field == 'category') {
                if(empty($this->category_name)){
                    $readyToExport = false;
                }
            } elseif (empty($this->{$field})) {
                $readyToExport = false;
            }
        }

        return $readyToExport ? ChannelProductConnectedStatus::READY_TO_EXPORT : ChannelProductConnectedStatus::MANDATORY_FIELD_MISSING;
    }

    public function getConnectedStatusLabelAttribute()
    {
        if ($this->is_connected) {
            //  return '<strong><span style="color: green;">Exported</span></strong>';
            return [
                'status'        => 'Connected',
                'color'         => 'rgb(46, 205, 111)',
                'details'       => '',
                'fieldMissing'  => false,
                'actionBtn'     => '<i class="fa fa-caret-left"></i>'
            ];
        }

        $lang = 'de';
        $requiredFields = array_merge(config('channel.required_fields.default', []), config('channel.required_fields.'.$this->channel, []));
        $readyToExport = true;
        $infoDetail = '<ul>';

        foreach ($requiredFields as $field) {
            if (in_array($field, ['title', 'description']) && empty($this->{$field}[$lang])) {
                $readyToExport = false;
                $infoDetail .= '<li><strong>'. ucfirst(strtolower(str_replace('_', ' ', $field))).' field required.</strong></li>';
            } elseif ($field == 'vk_price' && empty((float) $this->{$field})) {
                $readyToExport = false;
                $infoDetail .= '<li><strong>Vk price field is required.</strong></li>';
            } elseif ($field == 'category' && empty($this->category_name)) {
                $readyToExport = false;
                $infoDetail .= '<li><strong>Category field is required.</strong></li>';
            } elseif (!in_array($field, ['title', 'description', 'vk_price', 'category']) && empty($this->{$field})) {
                $infoDetail .= '<li><strong>'. ucfirst(strtolower(str_replace('_', ' ', $field))).' field required.</strong></li>';
                $readyToExport = false;
            }
        }
        $infoDetail .= '</ul>';

        if($readyToExport){
            return [
                'status'        => 'Ready &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',
                'color'         => '#5c738699',
                'details'       => '',
                'fieldMissing'  => false,
                'actionBtn'     => '<i class="fa fa-caret-right"></i>'
            ];
        }
        else {
            return [
                'status'        => 'Incomplete',
                'color'         => 'rgb(229, 0, 0)',
                'details'       => $infoDetail,
                'fieldMissing'  => true,
                'actionBtn'     => '<i class="fa fa-info-circle"></i>'
            ];
        }
        // return $readyToExport ? '<strong><span style="color: grey;">Ready to Export</span></strong>' : '<strong onclick=\'showRequiredField("'.$infoDetail.'")\'><span style="color: red; cursor: pointer;">Mandatory field missing <i class="fa fa-info-circle"></i></span></strong>';
    }

    public function getImagesAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    public function setImagesAttribute($value)
    {
        $this->attributes['images'] = json_encode($value);
    }

   // Industry template data
    public function getIndustryTemplateDataAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    public function setIndustryTemplateDataAttribute($value)
    {
        $this->attributes['industry_template_data'] = json_encode($value);
    }

    public function getTransDescriptionAttribute()
    {
        return Arr::get($this->description, 'de', '');
    }

    public function getTransShortDescriptionAttribute()
    {
        return Arr::get($this->short_description, 'de', '');
    }

    public function getFirstImageAttribute()
    {
        return Arr::get($this->images, '0');
    }

    public function getProfitAttribute()
    {
        $sellingPrice = $this->vk_price;
        if ($sellingPrice) {
            return round(($sellingPrice - $this->ek_price) / $this->ek_price * 100, 2) . ' %';
        }

        return '';
    }

    public function getCategoryNameAttribute()
    {
        $categories = [];
        $trans_cat = "category_name_" . 'de';

        foreach($this->channel_categories as $channel_category) {
            $drmCat = $channel_category->category;
            $category = !empty($drmCat->$trans_cat) ? $drmCat->$trans_cat : $drmCat->category_name;
            if($category!="" && $category!=null){
                $categories[] = $category.'<span onclick="unlinkCategory('.$channel_category->category_id.','.$channel_category->channel_product_id.')" style="color:red" class="btn btn-xs"><i class="fa fa-times"></i></i></span>';
            }

        }
        $categories = array_filter($categories);
        return implode('<br>', array_filter($categories));
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function base_product()
    {
        return $this->belongsTo(DrmProduct::class, 'drm_product_id');
    }

    public function shop()
    {
        return $this->belongsTo(Shop::class, 'channel', 'channel')
            ->where(['user_id' => $this->user_id])
            ->first();
    }

    public function channel_categories()
    {
        return $this->hasMany(ChannelProductCategory::class, 'channel_product_id');
    }

    public function getVariantProductsAttribute()
    {
        $variant_ids = $this->variants ?? array();
        return ChannelProduct::find($variant_ids) ?? collect([]);
    }

    public function getVariantSizesAttribute(): array
    {
        return array_unique($this->variant_products->pluck('item_size')->toArray());
    }

    public function getVariantColorsAttribute(): array
    {
        return array_unique($this->variant_products->pluck('item_color')->toArray());
    }

    public function getVariantMaterialsAttribute(): array
    {
        return array_unique($this->variant_products->pluck('materials')->toArray());
    }

    public static function boot()
    {
        parent::boot();
        static::updated(function ($item) {
            $changes = array_keys($item->getChanges());
            if ($item->channel == Channel::DROPTIENDA) {
                if (in_array('stock', $changes)) {
                    $old_stock = $item->getOriginal('stock');
                    if ($old_stock == 0 && $item->stock > 0) {
                        notify_stock_update($item->id,$item->user_id);
                    }
                }
            }
        });
    }

}
