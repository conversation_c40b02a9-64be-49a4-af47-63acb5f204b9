<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Synced extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = "mysql2";

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        "event",
        "shop_id",
        "last_updated",
    ];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [
        "created_at",
        "updated_at",
    ];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = "synced";
}
