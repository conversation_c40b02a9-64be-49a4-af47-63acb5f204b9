<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DrmProduct extends Model
{
    protected $table = 'drm_products';

    protected $fillable = [
        'id',
        'drm_import_id',
        'title',
        'item_number',
        'ean',
        'additional_eans',
        'description',
        'short_description',
        'internal_comments',
        'image',
        'ek_price',
        'vk_price',
        'vat',
        'stock',
        'category',
        'update_enabled',
        'status',
        'gender',
        'user_id',
        'delivery_company_id',
        'country_id',
        'language_id',
        'item_weight',
        'item_unit',
        'item_color',
        'note',
        'production_year',
        'materials',
        'brand',
        'tags',
        'update_status',
        'ean_field',
        'item_size',
        'delivery_days',
        'cloned_from',
        'uvp',
        'original_ek',
        'industry_template_data',
        'marketplace_supplier_id',
        'marketplace_product_id',
        'marketplace_shipping_method',
        'shipping_cost',
        'tax',
        'tax_type',
        'rq_limit',
        'product_type',
        'offer_options',
        'mp_offer',
        'manufacturer',
        'manufacturer_link',
        'manufacturer_id',
        'custom_tariff_number',
        'region',
        'country_of_origin',
        'min_stock',
        'gross_weight',
        'net_weight',
        'product_length',
        'product_width',
        'product_height',
        'shipping_method_id',
        'shipping_company_id',
        'options',
        'stock_of_status',
        'price_of_status',
        'packaging_dimensions',
        'packaging_length',
        'packaging_width',
        'packaging_height',
        'min_order',
        'mp_category_offer',
        'handling_time_updated',
        'packaging_unit'
    ];

    protected $guarded = [];

    protected $casts = [
        'title' => 'array',
        'description' => 'array',
        'image' => 'array'
    ];

    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = json_encode(['de' => $value]);
    }

    public function setDescriptionAttribute($value)
    {
        $this->attributes['description'] = json_encode(['de' => $value]);
    }

    public function setImageAttribute($value)
    {
        $this->attributes['image'] = json_encode($value);
    }
}
