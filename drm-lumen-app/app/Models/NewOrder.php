<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NewOrder extends Model
{
    use SoftDeletes;

    protected $table = 'new_orders';

    protected $fillable = [
        'order_id_api',
        'cms_user_id',
        'shop_id',
        'drm_customer_id',
        'invoice_number',
        'order_date',
        'invoice_date',
        'total',
        'sub_total',
        'total_tax',
        'discount',
        'discount_type',
        'adjustment',
        'shipping_cost',
        'payment_type',
        'currency',
        'customer_info',

        // 'tmp_invoice_number',

        'billing',
        'shipping',
        'client_note',
        'mail_sent',
        'status',
        'insert_type',

        'trash',
        'supplier',
        'cart',
        'char_status',
        'payment_status',
        'cms_client',

        'test_order',

        'supplier_id',
        'supplier_time',

        'credit_number'

    ];

    //Customer who buy this
    public function customer()
    {
        return $this->belongsTo(NewCustomer::class, 'drm_customer_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'cms_user_id');
    }

    public function client()
    {
        return $this->belongsTo(User::class, 'cms_client');
    }

    public function shop()
    {
        return $this->belongsTo(Shop::class, 'shop_id');
    }

    public function getProductsAttribute()
    {
        return ($this->cart) ? json_decode($this->cart) : null;
    }

    public function getTableColumns()
    {
        return $this->getConnection()->getSchemaBuilder()->getColumnListing($this->getTable());
    }

    public function parcel(){
        return $this->belongsTo(UserParcelService::class, 'parcel_id');
    }

    public function order_trackings(){
        return $this->hasMany(OrderTrackings::class, 'order_id', 'id');
    }

    protected $casts = [
        'order_history' => 'array',
        'tax_exclude' => 'boolean',
        'shipment_data' => 'array',
        'delivery_day_date' => 'date',
        'mail_sent' => 'datetime',
    ];
}
