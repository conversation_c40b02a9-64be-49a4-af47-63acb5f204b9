<?php

namespace App\Models\Catalog;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Models\Country;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;

/**
 * @method static Builder create(array $attributes = [])
 * @property mixed country
 * @property mixed id
 * @property mixed drm_import_id
 * @property mixed image
 * @property mixed ean
 * @property mixed gender
 * @property mixed tags
 * @property mixed note
 * @property mixed stock
 * @property mixed brand
 * @property mixed materials
 * @property mixed production_year
 * @property mixed item_number
 * @property mixed item_weight
 * @property mixed item_size
 * @property mixed item_color
 * @property mixed categories
 */
class Product extends Model
{
    protected $table = 'drm_products';

    protected $fillable = [
        'title',
        'description',
        'drm_import_id',
        'item_number',
        'ean',
        'image',
        'ek_price',
        'stock',
        'status',
        'gender',
        'user_id',
        'delivery_company_id',
        'country_id',
        'item_weight',
        'item_color',
        'production_year',
        'materials',
        'brand',
        'tags',
        'update_status',
        'item_size',
        'delivery_days',
        'tax_type'
    ];

    protected $casts = [
        'title' => 'array',
        'description' => 'array',
        'image'       => 'array',
        'update_status' => 'array'
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }


    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'drm_product_categories');
    }


    /** @noinspection PhpPossiblePolymorphicInvocationInspection */
    public function details()
    {
        $table = 'drm_translation_' . $this->country->language_shortcode;
        $details = DB::table($table)
            ->where('product_id', '=', $this->id)
            ->select('title', 'description')
            ->first();

        $this->description = $details->description;
        $this->title = $details->title;
        $details->title = trim(str_replace("&nbsp;", " ", strip_tags($this->generateTemplate('title') ?? $this->title)));

        $this->description = $this->generateTemplate('description') ?? $this->description;

        $details->description = $this->description;
        return $details;
    }

    /** @noinspection PhpPossiblePolymorphicInvocationInspection
     * @noinspection PhpUndefinedVariableInspection
     * @param $field
     * @return mixed|string|string[]
     */
    public function generateTemplate($field)
    {
        if ($field == "title") {
            $template = DB::table('drm_import_template')
                ->where('drm_import_id', '=', $this->drm_import_id)->select('title')->first();
            if ($template) {
                $template = $template->title;
            }
        }

        if ($field == "description") {
            $template = DB::table('drm_import_template')
                ->where('drm_import_id', '=', $this->drm_import_id)->select('description')->first();
            if ($template) {
                $template = $template->description;
            }
        }

        if ($template != null) {
            $images = json_decode($this->image);
            if (!$images) {
                $images = [];
            }

            $product_tags = [
                '#ITEM_WEIGHT#',
                '#ITEM_SIZE#',
                '#ITEM_COLOR#',
                '#BRAND#',
                '#MATERIALS#',
                '#PRODUCTION_YEAR#',
                '#DESCRIPTION#',
                '#GENDER#',
                '#ITEM_NUMBER#',
                '#STOCK#',
                '#CATEGORY#',
                '#NOTE#',
                '#TAGS#',
                '#NAME#',
                '#EAN#'
            ];

            $categories = $this->categories->pluck('category_name_' . $this->country->language_shortcode)->toArray();
            $categories = implode('<br>', $categories);
            $template_product = [];
            $template_product['#CATEGORY#'] = $categories;
            $template_product['#DESCRIPTION#'] = $this->description;

            foreach ($images as $key => $value) {
                $i = $key + 1;
                $template_product['#IMAGE_' . $i . "#"] = "<img width='300px' src='$value->src'  alt=''/>";
                $product_tags[] = '#IMAGE_' . $i . "#";
            }

            $template_product['#NAME#'] = $this->title;
            $template_product['#ITEM_NUMBER#'] = $this->item_number;
            $template_product['#ITEM_WEIGHT#'] = $this->item_weight;
            $template_product['#ITEM_SIZE#'] = $this->item_size;
            $template_product['#ITEM_COLOR#'] = $this->item_color;
            $template_product['#BRAND#'] = $this->brand;
            $template_product['#MATERIALS#'] = $this->materials;
            $template_product['#PRODUCTION_YEAR#'] = $this->production_year;
            $template_product['#TAGS#'] = $this->tags;
            $template_product['#NOTE#'] = $this->note;
            $template_product['#STOCK#'] = $this->stock;
            $template_product['#GENDER#'] = $this->gender;
            $template_product['#EAN#'] = $this->ean;
            foreach ($product_tags as $key => $value) {
                $template = str_replace($value, $template_product[$value], $template);
            }
            return $template;
        } else {
            return $this->$field;
        }
    }

}
