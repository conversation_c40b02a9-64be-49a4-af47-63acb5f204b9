<?php

namespace App\Models\Catalog;

use App\Models\Country;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'drm_category';

    protected $fillable = [
        'category_name',
        'user_id',
        'category_name_en',
        'country_id',
        'category_name_de',
        'category_name_fr',
        'category_name_it',
        'category_name_nl',
        'category_name_es',
        'category_name_sv',
        'category_name_pl',
        'category_name_ru'
    ];

    public $timestamps = false;

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

}
