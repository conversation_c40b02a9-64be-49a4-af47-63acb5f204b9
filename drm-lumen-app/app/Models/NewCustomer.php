<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class NewCustomer extends Model
{
    protected $table = 'new_customers';

    protected $fillable = [
        'full_name',
        'company_name',

        'vat_number',
        'phone',
        'website',
        'email',
        'currency',
        'default_language',
        'address',
        'city',
        'state',
        'zip_code',
        'country',

        'billing',
        'shipping',

        'note',
        'insert_type',
        'user_id',
        'status',

        'cc_user_id',
    ];

    public function user()
    {
        return $this->belongsTo('App\User', 'user_id', 'id');
    }

    public function orders()
    {
        return $this->hasMany(NewOrder::class, 'drm_customer_id', 'id');
    }

    public function tags()
    {
        return $this->hasMany(NewCustomerTag::class, 'customer_id', 'id');
    }

    protected $guarded = ['id'];

    // public function getCreatedAtAttribute($date)
    // {
    //     return Carbon::createFromFormat('Y-m-d H:i:s', $date)->format('Y-M-d');
    // }

    // public function getUpdatedAtAttribute($date)
    // {
    //     return Carbon::createFromFormat('Y-m-d H:i:s', $date)->format('Y-m-d');
    // }
}
