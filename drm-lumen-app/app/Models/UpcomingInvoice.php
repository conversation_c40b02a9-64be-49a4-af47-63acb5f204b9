<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;
class UpcomingInvoice extends Model
{
    //  use SoftDeletes;
     /**
       * The attributes that should be mutated to dates.
       *
       * @var array
       */

      protected $fillable = ['delivery_company_id', 'invoice_number', 'date', 'due_date', 'category', 'status', 'amount', 'total_amount', 'tax', 'description', 'user_id'];
      protected $table = 'upcoming_invoices';
    //   protected $dates = ['deleted_at'];
  
      /**
       * The attributes that aren't mass assignable.
       *
       * @var array
       */
      public function supplier()
      {
        return $this->belongsTo('App\DeliveryCompany','delivery_company_id','id');
      }

      public function invoicePDF()
      {
        return $this->belongsTo('App\UpcomingInvoiceDocument','id','upcoming_invoice_id');
      }

      public function invoice_category()
      {
        return $this->belongsTo('App\UpcommingInvoiceCategory','category');
      }


      protected $guarded = ['id'];
}
