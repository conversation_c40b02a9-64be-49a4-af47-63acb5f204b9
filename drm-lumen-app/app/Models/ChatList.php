<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatList extends Model
{
    use SoftDeletes;
    protected $table = 'chat_list';
    protected $fillable = ['order_id', 'customer_id', 'user_id', 'deleted_at', 'updated_at', 'chat_sorting'];
    public function order(){
        return $this->belongsTo(NewOrder::class, 'id');
    }
    public function dt_messages(){
        return $this->hasMany(DroptiendaMessages::class, 'order_id');
    }
}
