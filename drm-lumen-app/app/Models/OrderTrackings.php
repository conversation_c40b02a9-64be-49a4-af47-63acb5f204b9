<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrderTrackings extends Model
{
    use SoftDeletes;

    public const MANUAL_TRACKING = 0;
    public const SHIPCLOUD_TRACKING = 1;

    protected $table = 'order_trackings';
    protected $fillable = ['order_id', 'user_id', 'package_number', 'parcel_id', 'provider', 'shipment_data'];

    public function user(){
        return $this->belongsTo(User::class, 'user_id');
    }

    public function order(){
        return $this->belongsTo(NewOrder::class, 'order_id');
    }

    public function parcel(){
        return $this->belongsTo(UserParcelService::class, 'parcel_id');
    }

    protected $casts = [
        'shipment_data' => 'array',
    ];
}