<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NewCustomerTag extends Model
{
    use SoftDeletes;

    protected $table = 'new_customer_tags';

    protected $fillable = ['customer_id', 'user_id', 'title', 'type_id', 'input_type'];
    protected $guarded = ['id'];

    public function user()
    {
        return $this->belongsTo('App\User', 'user_id', 'id');
    }

    public function customer()
    {
        return $this->belongsTo('App\NewCustomer', 'customer_id', 'id');
    }
}
