<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @method static Builder updateOrCreate(array $array, array $array1)
 * @method static find(string $string, mixed $user_id)
 * @method static where(string $string, mixed $user_id)
 */
class UserFirebaseDeviceToken extends Model
{
    protected $table = "user_firebase_device_tokens";

    protected $fillable = [
        "user_id", "token"
    ];
}
