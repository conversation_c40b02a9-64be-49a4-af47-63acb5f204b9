<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DroptiendaMessages extends Model
{
    use SoftDeletes;

    protected $fillable = ['order_id', 'message', 'user_id', 'customer_id', 'read_at', 'sender', 'chat_preference', 'sent_time'];

    public function order(){
        return $this->belongsTo(NewOrder::class, 'order_id');
    }

    protected $casts = [
        'sender' => 'array',
    ];
}
