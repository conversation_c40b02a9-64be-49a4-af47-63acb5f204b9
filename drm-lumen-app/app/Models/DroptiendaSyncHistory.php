<?php

namespace App\Models;

use App\Jobs\DroptiendaSyncJob;
use App\Services\Product\ProductService;
use Illuminate\Database\Eloquent\Model;

class DroptiendaSyncHistory extends Model
{
    protected $table = 'droptienda_sync_history';

    protected $fillable = [
        'sync_type',
        'sync_event',
        'model_id',
        'user_id',
        'tries',
        'response',
        'exception',
        'synced_at',
    ];

//    static function boot ()
//    {
//        parent::boot();
//
//        static::created(function($item) {
//            $syncHistories = DroptiendaSyncHistory::whereNull('synced_at')
//                ->where('tries', '<', 3)
//                ->limit(env('SYNC_ITEM_LIMIT', 50))
////                ->orderBy('id','desc')
//                ->get();
//
//            foreach ($syncHistories as $syncHistory) {
//                switch ($syncHistory->sync_type) {
//                    case \App\Enums\DroptiendaSyncType::CATEGORY:
//                        app(\App\Services\Category\CategoryService::class)->syncCategoryToDroptienda($syncHistory);
//                        break;
//
//                    case \App\Enums\DroptiendaSyncType::PRODUCT:
//                        app(ProductService::class)->syncProductToDroptienda($syncHistory);
//                        break;
//                }
//            }
//        });
//    }
}
