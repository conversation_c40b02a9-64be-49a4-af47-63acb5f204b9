<?php

namespace App\Models;

use App\Jobs\ChannelManager\ChangeChannelProductConnectionStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ChannelProductCategory extends Model
{
    protected $fillable = [
        'category_id',
        'channel_product_id',
        'category_type',
    ];

    public function category()
    {
        return $this->morphTo();
    }

    public function channel_category()
    {
        return $this->belongsTo(ChannelProduct::class, 'channel_product_id');
    }

    public function product(): HasOne
    {
        return $this->hasOne(ChannelProduct::class,'id','channel_product_id');
    }

    public static function boot()
    {
//        parent::boot();
//        static::created(function($item) {
//            $product = $item->product;
//            if($product)
//                updateConnectionStatus($product->id,$product->user_id);
//        });
//
//        static::updated(function($item) {
//            $product = $item->product;
//            if($product)
//                updateConnectionStatus($product->id,$product->user_id);
//        });
//
//        static::deleted(function($item) {
//            $product = $item->product;
//            if($product)
//                updateConnectionStatus($product->id,$product->user_id);
//        });
    }
}
