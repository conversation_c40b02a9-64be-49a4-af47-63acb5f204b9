<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Queue;
use App\Jobs\DroptiendaNotifyJob;

class DroptiendaPullHistory extends Model
{
    protected $table = 'droptienda_pull_history';

    protected $fillable = [
        'dt_ref_id',
        'user_id',
        'tries',
        'response',
        'exception',
        'synced_at',
    ];

    static function boot()
    {
        parent::boot();

        static::created(function($item) {
            dispatch(new DroptiendaNotifyJob($item))->onQueue('droptienda');




            // Queue::push(new \App\Jobs\DroptiendaNotifyJob($item))->onConnection('droptienda')->onQueue('droptienda');
            // (new DroptiendaNotifyJob($item))->dispatch()->onConnection('droptienda')->onQueue('droptienda');
            // DroptiendaNotifyJob::dispatch($item)->onConnection('droptienda')->onQueue('droptienda');
        });
    }
}
