<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\NewOrder;

/**
 * @property mixed payment_type
 * @property mixed currency
 * @property mixed billing
 * @property mixed client_note
 * @property mixed insert_type
 * @property mixed status
 * @property mixed mail_sent
 * @property mixed trash
 * @property mixed cart
 * @property mixed supplier
 * @property mixed payment_status
 * @property mixed char_status
 * @property mixed shipping
 * @property mixed invoice_date
 * @property mixed test_order
 * @property mixed customer_info
 * @property mixed order_date
 * @property mixed shop_id
 * @property mixed drm_customer_id
 * @property mixed shipping_cost
 * @property mixed credit_number
 * @property mixed supplier_time
 * @property mixed supplier_id
 * @property mixed cms_client
 * @property mixed adjustment
 * @property mixed discount_type
 * @property mixed discount
 * @property mixed invoice_number
 * @property mixed cms_user_id
 * @property mixed order_id_api
 * @property mixed id
 * @property mixed total
 * @property mixed sub_total
 * @property mixed total_tax
 */
class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request)
    {
        $order_credit_ref = NewOrder::where('order_id_api', 'inv_id_' . $this->id)->select('id')->first();
        $insert_type_name = getInsertTypeName($this->insert_type);
        $order_id = (strlen($insert_type_name)) ? $insert_type_name . '-' . $this->id : $this->id;

        return [
            'id' => $this->id,
            'order_id' => $order_id,
            'order_id_api' => $this->order_id_api ?? '',
            'user_name' => ($this->user) ? $this->user->name : null,
            'customer_name' => ($this->customer) ? $this->customer->full_name : null,
            'invoice_number' => inv_number_string($this->invoice_number, $this->inv_pattern),
            'currency' => strtoupper($this->currency),
            'amount' => number_format((float)$this->total, 2, ',', '.'),
            'eur_amount' => number_format((float)$this->eur_total, 2, ',', '.'),

            'is_test_order' => $this->test_order == 1,
            'is_credit' => $this->credit_number > 0,
            'credit_number' => $this->credit_number,
            'has_credit_number' => $order_credit_ref && $order_credit_ref->id,
            'order_date' => date('Y-m-d', strtotime($this->order_date)),


//         'customer_id'       => $this->drm_customer_id,


//         'parcel_id'         => $this->parcel_id,
//         'order_id_api'      => $this->order_id_api,


//         'order_date'        => $this->drm_customer_id,
//         'status'            => $this->drm_customer_id,
//         'total'             => $this->drm_customer_id,
//         'test_order'        => $this->drm_customer_id,
//         'currency'          => strtoupper($this->currency),


//         'eur_total'         => $this->drm_customer_id


//             'payment_type' => $this->payment_type ?: '',
//             'currency' => $this->currency ?: '',
// //            'billing' => $this->billing ? new BillingAndShippingAddressResource(json_decode($this->billing)) : json_decode('{}'),
//             'billing' => json_decode($this->billing),
// //            'shipping' => $this->shipping ? new BillingAndShippingAddressResource(json_decode($this->shipping)) : json_decode('{}'),
//             'shipping' => json_decode($this->shipping),
//             'client_note' => $this->client_note ?: '',
//             'mail_sent' => $this->mail_sent ?: '',
//             'status' => $this->status ?: '',
//             'insert_type' => $this->insert_type ?: '',
//             'trash' => $this->trash ?: '',
//             'cart' => json_decode($this->cart) ?: '',
//             'supplier' => $this->supplier ?: '',
//             'char_status' => $this->char_status ?: '',
//             'payment_status' => $this->payment_status ?: '',
//             'cms_client' => $this->cms_client ?: '',
//             'test_order' => $this->test_order ?: '',
//             'supplier_id' => $this->supplier_id ?: '',
//             'supplier_time' => $this->supplier_time ?: '',
//             'credit_number' => $this->credit_number ?: '',
//             'shipping_cost' => $this->shipping_cost ?: '',
//             'adjustment' => $this->adjustment ?: '',
//             'discount_type' => $this->discount_type ?: '',
//             'discount' => $this->discount ?: '',
//             'total_tax' => $this->total_tax ?: '',
//             'sub_total' => $this->sub_total ?: '',
//             'total' => $this->total ?: '',
//             'id' => $this->id ?: '',
//             'order_id_api' => $this->order_id_api ?: '',
//             'cms_user_id' => $this->cms_user_id ?: '',
// //            'shop_id' => new ShopResources($this->shop_id) ?: json_decode('{}'),
//             'drm_customer_id' => $this->drm_customer_id ?: '',
//             'invoice_number' => $this->invoice_number ?: '',
//             'order_date' => $this->order_date ?: '',
//             'invoice_date' => $this->invoice_date ?: '',
//             'customer_info' => json_decode($this->customer_info),
        ];
    }
}
