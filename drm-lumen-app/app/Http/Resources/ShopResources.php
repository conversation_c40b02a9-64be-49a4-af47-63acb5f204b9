<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed id
 * @property mixed shop_name
 * @property mixed channel
 * @property mixed url
 */
class ShopResources extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */

    public function toArray($request)
    {
        return [
            'id' => $this->id ?: '',
            'shop_name' => $this->shop_name ?: '',
            'channel' => $this->channel ?: '',
            'url' => $this->url ?: '',
        ];
    }
}
