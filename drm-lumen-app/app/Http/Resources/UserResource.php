<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed id
 * @property mixed name
 * @property mixed email
 * @property Carbon created_at
 * @property Carbon updated_at
 */
class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id ?: '',
            'name' => $this->name ?: '',
            'email' => $this->email ?: '',
            'created_at' => Carbon::parse($this->created_at) ?: '',
            'updated_at' => Carbon::parse($this->updated_at) ?: '',
        ];
    }
}
