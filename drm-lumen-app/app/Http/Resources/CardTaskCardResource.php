<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property int id
 * @property int position
 * @property string title
 * @property array tasks
 */
class CardTaskCardResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'position' => $this->position,
            'tasks' => CardTaskTaskResource::collection($this->tasks)
        ];
    }
}
