<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property Carbon updated_at
 */
class ZapierOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request): array
    {
        return [
            "updated_at" => Carbon::parse($this->updated_at)->format("Y-m-d") ?: Carbon::parse(Carbon::now())->format("Y-m-d"),
        ];
    }
}
