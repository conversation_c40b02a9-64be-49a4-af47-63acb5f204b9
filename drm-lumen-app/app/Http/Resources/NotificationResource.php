<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed id
 * @property mixed read_at
 * @property mixed created_at
 * @property mixed updated_at
 * @property mixed data
 */
class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request)
    {
        $data = json_decode($this->data);
        unset($data->hook);
        return [
            'id' => $this->id,
            'data' => $data,
            'read_at' => Carbon::createFromTimestamp($this->read_at)->toDateTimeString(),
            'created_at' => Carbon::createFromTimestamp($this->created_at)->toDateTimeString(),
            'updated_at' => Carbon::createFromTimestamp($this->updated_at)->toDateTimeString(),
        ];
    }
}
