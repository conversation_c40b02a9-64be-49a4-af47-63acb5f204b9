<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property int id
 * @property mixed status
 * @property Carbon created_at
 * @property Carbon updated_at
 * @property mixed shipping
 * @property mixed country
 * @property mixed note
 * @property mixed billing
 * @property mixed vat_number
 * @property mixed phone
 * @property mixed email
 * @property mixed website
 * @property mixed currency
 * @property mixed default_language
 * @property mixed address
 * @property mixed city
 * @property mixed state
 * @property mixed zip_code
 * @property mixed full_name
 * @property mixed company_name
 */
class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id ?: '',
            'full_name' => $this->full_name ?: '',
            'company_name' => $this->company_name ?: '',
            'vat_number' => $this->vat_number ?: '',
            'phone' => $this->phone ?: '',
            'email' => $this->email ?: '',
            'website' => $this->website ?: '',
            'currency' => $this->currency ?: '',
            'default_language' => $this->default_language ?: '',
            'address' => $this->address ?: '',
            'city' => $this->city ?: '',
            'state' => $this->state ?: '',
            'zip_code' => $this->zip_code ?: '',
            'country' => $this->country ?: '',
            'note' => $this->note ?: '',
//            'billing' => $this->billing ? new BillingAndShippingAddressResource(json_decode($this->billing)) : json_decode('{}'),
            'billing' => json_decode($this->billing),
//            'shipping' => $this->shipping ? new BillingAndShippingAddressResource(json_decode($this->shipping)) : json_decode('{}'),
            'shipping' => json_decode($this->shipping),
            'status' => $this->status ?: '',
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
