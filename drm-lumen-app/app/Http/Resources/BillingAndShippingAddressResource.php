<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed name
 * @property mixed company
 * @property mixed street
 * @property mixed address
 * @property mixed zip_code
 * @property mixed city
 * @property mixed state
 * @property mixed country
 */
class BillingAndShippingAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */

    public function toArray($request)
    {
        return [
            'name' => $this->name ?: '',
            'company' => $this->company ?: '',
            'street' => $this->street ?: '',
            'address' => $this->address ?: '',
            'zip_code' => $this->zip_code ?: '',
            'city' => $this->city ?: '',
            'state' => $this->state ?: '',
            'country' => $this->country ?: '',
        ];
    }
}
