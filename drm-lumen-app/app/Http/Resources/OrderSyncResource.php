<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

/**
 * @property mixed id
 * @property mixed user_id
 * @property mixed status
 * @property mixed report
 * @property mixed end_time
 * @property mixed shop_name
 * @property mixed start_time
 * @property mixed item
 * @property mixed shop_id
 */
class OrderSyncResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request)
    {
        $app_interval = DRM_Interval_App_Minute($this->user_id, config('global.interval_app_id'));

        $next = ($this->status != 0) ? Carbon::createFromTimestamp($this->end_time)->addMinutes($app_interval)->diffForHumans() : '--';
        $end = ($this->status != 0) ? Carbon::createFromTimestamp($this->end_time)->toDateTimeString() : '--';

        $error_message = ($this->report) ? $this->report : 'During the last sync. your store was not available as expected. Do not worry, we will try again soon.';
        $status_code = [
            0 => 'Working',
            1 => 'Finish',
            2 => 'Failed',
            'message' => $error_message,
        ];

        $status_label = isset($status_code[$this->status]) ? $status_code[$this->status] : '';
        $retry_id = ($this->status == 2)? $this->id : null;

        return [
            'shop_name' => $this->shop_name,
            'start' => Carbon::createFromTimestamp($this->start_time)->toDateTimeString(),
            'end' => $end,
            'next' => $next,
            'fetch' => $this->item,
            'status' => $status_label,
            'status_code' => $this->status,
            'sort' => $this->start_time,
            'retry_id' => $retry_id,
        ];
    }
}
