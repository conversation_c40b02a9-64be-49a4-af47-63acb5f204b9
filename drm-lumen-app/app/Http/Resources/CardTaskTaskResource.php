<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property int id
 * @property int position
 * @property string name
 */
class CardTaskTaskResource extends JsonResource
{
    private function totalCount($task,$flag)
    {
        switch ($flag) {
            case 'comment':
                $count = $task->comments()->count();
                break;
            case 'checklists':
                $count = $task->checklists()->count();
                break;
            case 'checklistsTicked':
                $count = $task->checklists()->where('status','=','true')->count();
                break;
            default:
                $count = 0;
                break;
        }
        return $count;
    }

    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'position' => $this->position,
            'total_comment' => self::totalCount($this,'comment'),
            'total_checklist' => self::totalCount($this,'checklists'),
            'checked_checklist_count' => self::totalCount($this,'checklistsTicked'),
        ];
    }
}
