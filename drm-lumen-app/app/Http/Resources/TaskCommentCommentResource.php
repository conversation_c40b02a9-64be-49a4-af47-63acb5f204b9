<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

/**
 * @property int id
 * @property string comment
 * @property string files
 * @property string audio_file
 */
class TaskCommentCommentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request): array
    {

        $files = json_decode($this->files, true);
        $convertedFiles = [];
        if (is_array($files) || is_object($files)) {
            foreach ($files as $value) {
                array_push($convertedFiles, Storage::disk('spaces')->url($value));
            }
        }


        return [
            'id' => $this->id,
            'comment' => $this->comment,
            'files' => $convertedFiles,
            'audio_file' => $this->audio_file,
        ];
    }
}
