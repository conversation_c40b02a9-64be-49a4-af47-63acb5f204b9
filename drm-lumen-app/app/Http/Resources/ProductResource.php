<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed id
 * @property mixed country_id
 * @property mixed ean
 * @property mixed suplier
 * @property mixed item_number
 * @property mixed ek_price
 * @property mixed vk_price
 * @property mixed stock
 * @property mixed status
 * @property mixed tags
 * @property mixed title
 * @property mixed image
 * @property mixed description
 */
class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request)
    {
        $data = json_decode($this->image);
        $title = json_decode($this->title);
        $description = json_decode($this->description,true);
        return [
            "id" => $this->id,
            "country_id" => $this->country_id,
            "ean" => $this->ean,
            "suplier" => $this->suplier,
            "item_number" => $this->item_number,
            "ek_price" => $this->ek_price,
            "vk_price" => $this->vk_price,
            "stock" => $this->stock,
            "image" => $data,
            "status" => $this->status,
            "tags" => $this->tags,
            "title" => $title,
            "description" => $description,
        ];
    }
}
