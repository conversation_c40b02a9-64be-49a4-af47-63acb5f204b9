<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'start_date' => Carbon::parse($this->start_date)->format('Y-m-d'),
            'due_date' => Carbon::parse($this->due_date)->format('Y-m-d'),
            'over_due' => self::isOverDue($this->due_date),
            'name' => $this->name,
            'cover_image' => self::getImageUrl($this->cover_image),
            'position' => $this->position,
            'comments' => $this->comments,
            'checklists' => $this->checklists,
        ];
    }

    private function getImageUrl($url)
    {
        if (!strpos($url,'drm-file')) {
            return null;
        }
        return $url;
    }

    private function isOverDue($dueDate)
    {
        $today = Carbon::now();
        $dueDate = Carbon::parse($dueDate);
        if ($today->gt($dueDate)) { // today is newer than due_date
            return true;
        }
        return false;
    }

}

