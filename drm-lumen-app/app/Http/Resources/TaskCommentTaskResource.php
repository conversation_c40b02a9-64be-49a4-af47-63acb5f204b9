<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

/**
 * @property Carbon start_date
 * @property Carbon due_date
 * @property string name
 * @property string cover_image
 * @property int position
 * @property int id
 * @property array comments
 * @property array checklists
 */
class TaskCommentTaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @noinspection PhpMissingParamTypeInspection
     */
    public function toArray($request): array 
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'position' => $this->position,
            'cover_image' => $this->cover_image ? Storage::disk('spaces')->url($this->cover_image) : null,
            'due_date' => $this->due_date ? Carbon::parse($this->due_date) : null,
            'start_date' => $this->start_date ? Carbon::parse($this->start_date) : null,
            'comments' => TaskCommentCommentResource::collection($this->comments),
            'checklists' => TaskCommentChecklistResource::collection($this->checklists),
        ];
    }
}
