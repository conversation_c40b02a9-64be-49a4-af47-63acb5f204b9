<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\DB;

class ZapierAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = getZapierUser($request);
        if (empty($user)) {
            return response()->json(['success' => false, 'message' => 'Unauthorized User'], 401);
        }

        if((int)$user->id_cms_privileges === 4)
        {
            return $next($request);
        }

        $app_id = 54;
        if (!in_array($user->id,[71,98,212])) { // not in this users        
            if (!DrmUserHasPurchasedApp($user->id, $app_id)) {
                // delete secret for this user
                DB::table('oauth_clients')->where(['name' => $request->username, 'secret' => $request->password])->delete();
                return response()->json(['success' => false, 'message' => 'Access Denied'], 403);
            }
        }

        return $next($request);
    }
}
