<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BaseApiModel
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        if (!method_exists($response, 'getData')) {
            return response()->json([
                'success' => false,
                'message' => 'Oops! something went wrong',
                'data' => [], //(object)
                'errors' => [] //(object)
            ], 400);
        }

        $data = $response->getData();
        $responseStatus = $response->status() ? $response->status() : 400;
        $serverErrors = [];
        $responseMessage = '';
        if (!empty($data) && ($responseStatus == 422)) {
            $responseMessage = 'Validation Errors';
            foreach ($data as $key => $error) {
                foreach ($error as $value) {
                    $serverErrors[$key] = $value;
                }
            }
            $data = []; //(object)
        }


        if (!empty($data->message)) {
            $responseMessage = $data->message;
            unset($data->message);
        }

        if (empty($data->data))
            $data = []; //(object)
        else
            $data = $data->data;

        $success = false;
        if ($responseStatus == 200) {
            $success = true;
            $responseMessage = $responseMessage ? $responseMessage : 'success!';
        }

        if (empty($serverErrors)) $serverErrors = []; //(object)

        $responseData = ['success' => $success, 'message' => $responseMessage, 'data' => $data, 'errors' => $serverErrors];

        $response->setData($responseData);
//        $response->setStatusCode(200);
        return $response;
    }
}
