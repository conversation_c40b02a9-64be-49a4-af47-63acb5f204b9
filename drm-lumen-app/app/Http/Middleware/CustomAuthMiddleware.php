<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Firebase\JWT\JWT;
use Illuminate\Http\Request;

class CustomAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            $header = $request->bearerToken();
            $decoded = JWT::decode($header, "JWT_SECRET", array("HS256"));

            if ($decoded->sub->id <= 0) {
                return response()->json(['message' => 'Unauthorized.'], 401);
            }

            /**
             * GET USER ID FROM TOKEN AND SET IT TO REQUEST FOR FUTURE DEVELOPMENT
             *
             * USER ID SET INTO REQUEST UNDER "USER_ID" KEY
             */
            $request["user_id"] = $decoded->sub->id;

            return $next($request);
        } catch (Exception $exception) {
            return response()->json(['message' => 'Unauthorized.'], 401);
        }
    }
}
