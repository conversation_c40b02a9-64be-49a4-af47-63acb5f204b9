<?php

namespace App\Http\Controllers\Api\V1\Droptienda;

use App\Enums\Channel;
use App\Enums\V2UserAccess;
use App\Http\Controllers\Controller;
use App\Models\ChannelProduct;
use App\Models\DrmProduct;
use App\Services\DropmatixApiService;
use App\Services\Product\ProductService;
use Illuminate\Http\Request;
use App\Models\AllCountry;
use Illuminate\Support\Facades\DB;

class ProductApiController extends Controller
{
    private ProductService $service;
    private $user_id;

    private string $token;
    private string $secret;

    public function __construct (ProductService $service) {
        $this->service = $service;

        $this->token = request()->header('userToken');
        $this->secret = request()->header('userPassToken');

        $this->user_id = get_api_user_id($this->token, $this->secret);

        if (empty($this->user_id)) {
            return redirect()->to('/unauthorized')->send();
        }
    }

    public function index ()
    {
        return $this->service->index();
    }

    public function store(Request $request)
    {
        if(in_array($this->user_id,V2UserAccess::USERS)){
            $data = $request->all();
            (new DropmatixApiService($this->token,$this->secret))->transfer($data,'product','create');
        }
        else{
            try{
                $data = $request->all();
                debug_log(json_encode($data),'DT_CREATE');
                $data['title'] = ["de" => $request->title];
                $data['description'] = ["de" => $request->description];
                $data['short_description'] = ["de" => $request->short_description];

                $data['region'] = null;
                if(!empty($request->region)){
                    $data['region'] = AllCountry::where('country', $request->region)->value('id');
                }

                $data['manufacturer_id'] = (int)$data['manufacturer_id'] ?? 0;
                $data['custom_tariff_number'] = (int)$data['custom_tariff_number'] ?? 0;

                $data['country_of_origin'] = null;

                if(!empty($request->country_of_origin)){
                    $data['country_of_origin'] = AllCountry::where('country', $request->country_of_origin)->value('id');
                }

                $data['product_length'] = (float)$request->item_length ?? 0.0;
                $data['product_height'] = (float)$request->item_height ?? 0.0;
                $data['product_width'] = (float)$request->item_width ?? 0.0;
                $data['basic_price'] = (float)$request->basic_price ?? 0.0;

                if(isset($data['stock']) && $data['stock'] == 'nolimit'){
                    $data['stock'] = -1;
                }

                if(isset($data['brand']))
                {
                    $data['brand'] = $this->findOrCreateBrand($data['brand']);
                }

                try {
                    $drmProduct = $this->service->storeToDrm($data);
                    $data['drm_product_id'] = $drmProduct->id;
//                if($request->drm_ref_id){
//                    $data['id'] = $request->drm_ref_id;
//                }
                    $data['country_id'] = 1;
                    $data['is_connected'] = 1;
                    $data['connection_status'] = 1;
                    if(array_key_exists('vk_price',$data))
                    {
                        $data['price_droptienda'] = true;
                    }

                }catch(\Exception $e){
                    throw $e;
                }

                return $this->service->store($data);
            } catch (\Exception $e) {
                throw $e;
            }
        }

    }

    public function update($id, Request $request)
    {

        if(in_array($this->user_id,V2UserAccess::USERS)){
            $data = $request->all();
            (new DropmatixApiService($this->token,$this->secret))->transfer(['id' => $id, 'data' => $data],'product','update');
        }
        else {
            try{
                $data = $request->all();
                debug_log(json_encode($data),'DT_UPDATE');
                $data['title'] = ["de" => $request->title];
                $data['description']   =  ["de" => $request->description];
                $data['short_description'] = ["de" => $request->short_description];
                $data['id'] = $id;
                $data['connection_status'] = 1;
                $data['is_connected'] = 1;
//            unset($data['ek_price']);
                unset($data['drm_product_id']);

                if(array_key_exists('vk_price',$data))
                {
                    $data['price_droptienda'] = true;
                    if((int)$request->vk_price == 0 && $request->stock > 0){
                        $data['price_on_request'] = true;
                    }else{
                        $data['price_on_request'] = false;
                    }
                }

                if(array_key_exists('brand',$data))
                {
                    $data['brand'] = $this->findOrCreateBrand($data['brand']);
                }

                if(array_key_exists('tax_type',$data))
                {
                    DrmProduct::where([
                        'user_id' => $this->user_id,
                        'ean' => $data['ean']
                    ])->update(['tax_type' => (int) $data['tax_type']]);
                }

                $data['region'] = null;

                if(!empty($request->region)){
                    $data['region'] = AllCountry::where('country', $request->region)->value('id');
                }

                $data['country_of_origin'] = null;

                if(!empty($request->country_of_origin)){
                    $data['country_of_origin'] = AllCountry::where('country', $request->country_of_origin)->value('id');
                }

                $data['product_length'] = $request->item_length;
                $data['product_height'] = $request->item_height;
                $data['product_width'] = $request->item_width;
                $data['basic_price'] = $request->basic_price;

                if(isset($data['stock']) && $data['stock'] == 'nolimit'){
                    $data['stock'] = -1;
                }

                return $this->service->update($data, $id);
            } catch (\Exception $e) {
                trace($e->getMessage());
                return $e->getMessage();
            }
        }
    }

    public function delete($id)
    {
        if(in_array($this->user_id,V2UserAccess::USERS)){
            (new DropmatixApiService($this->token,$this->secret))->transfer(['id' => $id],'product','delete');
        }
        else {
            return $this->service->delete($id);
        }
    }


    private function findOrCreateBrand(string $bandName) : int {
        $brand = DB::table("dropmatix_product_brands")->where([
            'brand_name' => $bandName,
            'user_id' => $this->user_id,
        ])->value('id');

        if(!$brand) {
            $brand = DB::table("dropmatix_product_brands")->insertGetId([
                'brand_name' => $bandName,
                'user_id' => $this->user_id,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
        return $brand;
    }

    public function setIcon(Request $request)
    {
        $product_ids = json_decode($request->product_ids);
        $products = ChannelProduct::where([
            'channel' => Channel::DROPTIENDA,
            'user_id' => $this->user_id
        ])->whereIn('id',$product_ids)->get(); //->whereJsonLength('metadata->icon', '=', 0)

        foreach ($products as $product)
        {
            $metadata = $product->metadata ?? [];
            $metadata['icon'] = '<i class="fa fa-eye-slash" aria-hidden="true"></i>';

            $product->metadata = $metadata;
            $product->save();
        }

        $products = ChannelProduct::where([
            'channel' => Channel::DROPTIENDA,
            'user_id' => $this->user_id
        ])->whereNotIn('id',$product_ids)->get(); //->whereJsonLength('metadata->icon', '>', 0)

        foreach ($products as $product)
        {
            $metadata = $product->metadata ?? [];
            unset($metadata['icon']);

            $product->metadata = $metadata;
            $product->save();
        }
    }

}
