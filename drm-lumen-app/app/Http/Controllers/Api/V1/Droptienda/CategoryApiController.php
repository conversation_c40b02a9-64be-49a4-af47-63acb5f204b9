<?php

namespace App\Http\Controllers\Api\V1\Droptienda;

use App\Enums\V2UserAccess;
use App\Http\Controllers\Controller;
use App\Services\Category\CategoryService;
use App\Http\Resources\CategoriesResource;
use App\Services\DropmatixApiService;
use Illuminate\Http\Request;

class CategoryApiController extends Controller
{
    private $service;
    private $user_id;
    private $shop_id;

    private string $token;
    private string $secret;

    public function __construct(CategoryService $service)
    {
        $this->service = $service;
        $this->user_id = get_api_user_id(request()->header('userToken'), request()->header('userPassToken'));
        $this->shop_id = get_api_shop_id(request()->header('userToken'), request()->header('userPassToken'));

        $this->token = request()->header('userToken');
        $this->secret = request()->header('userPassToken');

        if (empty($this->user_id)) {
            return redirect()->to('/unauthorized')->send();
        }
    }

    public function index(Request $request)
    {
        $data = $this->service->all($request->all());

        return response()->json($data);
    }

    public function show($id)
    {
        $category = $this->service->getById($id);

        return response()->json($category);
    }

    public function store(Request $request)
    {
        if (in_array($this->user_id, V2UserAccess::USERS)) {
            (new DropmatixApiService($this->token, $this->secret))->transfer($request->all(), 'category', 'create');
        } else {
            try {
                $drmCategory = $this->service->storeToDrm(array_merge($request->all(), [
                        'user_id' => $this->user_id,
                        'lang' => 'de',
                        'country_id' => 1
                    ]
                ));
                $drmCategory['category_name'] = $drmCategory['category_name_de'] ?? $drmCategory['category_name'];

                $drmId = $drmCategory['id'];
                unset($drmCategory['id']);
            } catch (\Exception $e) {
                $drmCategory = $request->all();
            }

            $data = $this->service->store(array_merge($drmCategory, [
                'user_id' => $this->user_id,
                'channel' => \App\Enums\Channel::DROPTIENDA,
                'shop_id' => $this->shop_id,
                'drm_category_id' => $drmId ?? 0,
                'parent' => $request->parent ?? 0,
                'level' => $request->level ?? 1,
                'shop_category_id' => $request->shop_category_id ?? null
            ]));

            return response()->json($data);
        }
    }

    public function update($id, Request $request)
    {
        if(in_array($this->user_id, V2UserAccess::USERS)) {
            (new DropmatixApiService($this->token, $this->secret))->transfer(['id' => $id, 'data' => $request->all()], 'category', 'update');
        }
        else{
            $data = $this->service->update($id, $request->all());
            return response()->json($data);
        }
    }

    public function destroy($id)
    {
        if(in_array($this->user_id, V2UserAccess::USERS)) {
            (new DropmatixApiService($this->token, $this->secret))->transfer(['id' => $id], 'category', 'delete');
            return response()->json(['success' => true, 'message' => 'Category deleted successfully.']);
        }
        else{
            $data = $this->service->destroy($id);
            return response()->json(['success' => true, 'message' => 'Category deleted successfully.']);
        }
    }

    public function syncCategory(Request $request)
    {
        $sync_event = $request->sync_event ?? '';

        $categories = $this->service->getCategories('categories', $sync_event, 2);
    }

}
