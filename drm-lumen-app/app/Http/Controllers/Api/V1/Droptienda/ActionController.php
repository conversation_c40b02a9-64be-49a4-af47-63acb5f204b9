<?php

namespace App\Http\Controllers\Api\V1\Droptienda;

use App\Http\Controllers\Controller;
use App\Jobs\DroptiendaSyncJob;
use Illuminate\Http\Request;
use App\Models\Shop;
use Illuminate\Support\Facades\DB;
use App\Models\CurrencyRate;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

class ActionController extends Controller
{
    public function dtExportSync(Request $request)
    {
        if($request->app_key == config('app.app_key') && $request->app_secret == config('app.app_secret')){
            $connection = 'local_database';
            if($request->user_id == env('DT_SPECIFIC_USER',62)){
                $connection = 'dt_specific';
            }
            dispatch(new DroptiendaSyncJob($request->user_id))->onConnection($connection)->onQueue($connection);
        }
        else{
            return response('FAILED',401);
        }
        return response('SUCCESS',200);
    }

    static public function dtConnectionCheck(){
        $dt_shop = DB::table('shops')
                        ->leftJoin('server_statuses', 'shops.id', 'server_statuses.shop_id')
                        ->join('cms_users', 'shops.user_id', 'cms_users.id')
                        ->where('shops.channel', 10)
                        ->select('shops.url', 'shops.username', 'shops.password', 'shops.shop_name', 'shops.user_id', 'shops.category')
                        ->get()
                        ->toArray();
        $dt_shop = json_decode(json_encode($dt_shop), true);
        if($dt_shop){
            $userToken = $userPassToken = false;
            $connected_shop = $disconnected_shop = $not_found_shop = 0;
            $mail_info = [];
            $mail_info['from'] = '<EMAIL>';
            $mail_info['subject'] = 'Droptienda shop connection status:';
            $mail_info['body'] = '<h2>Hi there, </h2>
                                    <p>Given below all Droptienda shop lists with shop connection status. </p>';
            $mail_info['attachment'] = '<table style="border-collapse: collapse; width: 100%;">
                                        <tr style="background-color: #f9f9f9;">
                                        <th style="text-align: left; padding: 8px; background-color: #f2f2f2; border: 1px solid rgba(0, 0, 0, .1)">Shop name</th>
                                        <th style="text-align: left; padding: 8px; background-color: #f2f2f2; border: 1px solid rgba(0, 0, 0, .1)">User ID</th>
                                        <th style="text-align: left; padding: 8px; background-color: #f2f2f2; border: 1px solid rgba(0, 0, 0, .1)">Shop URL</th>
                                        <th style="text-align: left; padding: 8px; background-color: #f2f2f2; border: 1px solid rgba(0, 0, 0, .1)">Version</th>
                                        <th style="text-align: left; padding: 8px; background-color: #f2f2f2; border: 1px solid rgba(0, 0, 0, .1)">Active Template</th>
                                        <th style="text-align: left; padding: 8px; background-color: #f2f2f2; border: 1px solid rgba(0, 0, 0, .1)">Status</th>
                                        </tr>';
            foreach($dt_shop as $shop){
                if(str_contains($shop['url'], 'localhost') or str_contains($shop['url'], '-update') or str_contains($shop['url'], '-install') or str_contains($shop['url'], "/./") or $shop['category'] == 3){
                    continue;
                }
                $shop_info = array();
                $shop_info['url'] = $shop['url'];
                $shop_info['user_token'] = $shop['username'];
                $shop_info['user_pass_token'] = $shop['password'];
                $shop_info['name'] = $shop['shop_name'];
                $shop_info['user_id'] = $shop['user_id'];
                $get_data = ['userToken'=>$shop_info['user_token'], 'userPassToken'=>$shop_info['user_pass_token']];
                $get_data = http_build_query($get_data);
                if(substr($shop_info['url'], -1) != '/'){
                    $shop_info['url'] .= '/';
                }
                $ch = curl_init($shop_info['url'].'api/v1/validate-droptienda-shop?'.$get_data);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $api_response = json_decode(curl_exec($ch), true);
                $shop_info['template'] = $api_response['template']??'';
                // $shop_info['version'] = $api_response['1']??'';
                $shop_info['version'] = $api_response['version']??'';
                $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                if($httpcode == 200 and !$userToken and !$userPassToken){
                    $userToken = $shop_info['user_token'];
                    $userPassToken = $shop_info['user_pass_token'];
                }
                if($httpcode == 200){
                    $connected_shop++;
                }else if($httpcode == 401){
                    $disconnected_shop++;
                }else{
                    $not_found_shop++;
                }
                curl_close($ch);
                if(isset($_GET['email'])){
                    $body = '<tr>
                            <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['name'] .'</td>
                            <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['user_id'] .'</td>
                            <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['url'] . '</td>
                            <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['version'] . '</td>
                            <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['template'] . '</td>
                            <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1); ';
                    if($httpcode == 200){
                        $body .= 'color:green;">Connected';
                    }else if($httpcode == 401){
                        $body .= ' color:blue;">Not Connected';
                    }else{
                        $body .= 'color:red;">Not found';
                    }
                    $body .= '</td>
                            </tr>';
                    $mail_info['attachment'] .= $body;
                }else{
                    if($httpcode != 200){
                        $body = '<tr>
                                <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['name'] .'</td>
                                <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['user_id'] .'</td>
                                <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['url'] . '</td>
                                <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['template'] . '</td>
                                <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1)">'. $shop_info['version'] . '</td>
                                <td style="text-align: left; padding: 8px; border: 1px solid rgba(0, 0, 0, .1); ';
                        if($httpcode == 200){
                            $body .= 'color:green;">Connected';
                        }else if($httpcode == 401){
                            $body .= ' color:blue;">Not Connected';
                        }else{
                            $body .= 'color:red;">Not found';
                        }
                        $body .= '</td>
                                </tr>';
                        $mail_info['attachment'] .= $body;
                    }
                }

            }
            $mail_info['attachment'] .= '</table>';
            if(isset($_GET['email'])){
                $mail_info['body'] .= '<h3 style="color:green;">Total Connected shop is: '.$connected_shop.'</h3>';
                $mail_info['subject'] .= 'Connect-'.$connected_shop.', ';
            }
            $mail_info['body'] .= '<h3 style="color:blue;">Total Disconnect shop is: '.$disconnected_shop.'</h3><h3 style="color:red;">Total not found dt shop is: '.$not_found_shop.'</h3>';
            $mail_info['subject'] .= 'Disconnect-'.$disconnected_shop.', Not found-'.$not_found_shop;
            $mail_info['body'] .= '<p>Sincerely, The Droptienda Devs Team </p>';
            if(isset($_GET['email'])){
                $devs_email = ['devs'=>$_GET['email']];
            }else{
                $devs_email = [
                    'Zunaid'=> '<EMAIL>',
                    'Moniruz Zaman' => '<EMAIL>',
                    'Fabian Sielger' => '<EMAIL>',
                    'Rico' => '<EMAIL>'
                ];
            }
            if(($not_found_shop > 0 or $disconnected_shop > 0) or isset($_GET['email'])){
                foreach($devs_email as $name => $email){
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://drm.software/api/dt-send-email',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => array('mail_to'=>$email,'mail_from'=>$mail_info['from'],'mail_body'=>$mail_info['body'],'mail_subject'=>$mail_info['subject'], 'attachment'=>$mail_info['attachment']),
                    CURLOPT_HTTPHEADER => array(
                        'userToken: '.$shop_info['user_token'],
                        'userPassToken: '.$shop_info['user_pass_token']
                    ),
                    ));

                    $response = curl_exec($curl);
                    dump($response);
                    curl_close($curl);
                }
            }
            return 'Successfully email send';
        }
    }

    static public function dtConnectionCheckFromDt(Request $request){
        if($request->input('userToken') and $request->input('userPassToken')){
            $has_shop = Shop::where('username', $request->input('userToken'))->where('password', $request->input('userPassToken'))->where('channel', 10)->first();
            if(!empty($has_shop)) {
                return response()->json(['success' => true, 'message' => 'Droptienda Shop is connected with DRM.'], 200);
            }

            return response()->json(['success' => false, 'message' => 'Droptienda Shop not found.'], 401);
        }else{
            return response()->json(['success' => false, 'message' => 'Required user token and user pass token!'], 203);
        }
    }

    static public function dtShopLockUnlock(){
        $dt_shop = DB::table('shops')
                        ->leftJoin('drm_users_lock_unlock', 'shops.user_id', 'drm_users_lock_unlock.user_id')
                        ->where('shops.channel', 10)
                        ->select('shops.url', 'shops.username', 'shops.password', 'shops.shop_name', 'shops.user_id', 'drm_users_lock_unlock.status', 'shops.category')
                        ->get()
                        ->toArray();
        $dt_shop = json_decode(json_encode($dt_shop), true);
        foreach($dt_shop as $shop){
            if(str_contains($shop['url'], 'localhost') or str_contains($shop['url'], '-update') or str_contains($shop['url'], '-install') or str_contains($shop['url'], "/./") or $shop['category'] == 3){
                continue;
            }
            $ids = array(3337, 3380, 3385, 3418, 3430, 3434, 3433);
            if(!in_array($shop['user_id'], $ids)){
                $shop_info = array();
                $shop_info['url'] = $shop['url'];
                $shop_info['user_token'] = $shop['username'];
                $shop_info['user_pass_token'] = $shop['password'];
                if(substr($shop_info['url'], -1) != '/'){
                    $shop_info['url'] .= '/';
                }
                if(!is_null($shop['status'])){
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                        CURLOPT_URL => $shop_info['url'].'api/v1/shop-control',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => array('isLocked'=> ($shop['status']==1) ? 'true' : 'false'),
                        CURLOPT_HTTPHEADER => array(
                            'userToken: '.$shop_info['user_token'],
                            'userPassToken: '.$shop_info['user_pass_token']
                        ),
                    ));

                    $response = curl_exec($curl);
                    curl_close($curl);
                }
            }
        }
    }

    static public function dtShopProductCheck(){
        $dt_shop = DB::table('shops')
                        ->leftJoin('drm_users_lock_unlock', 'shops.user_id', 'drm_users_lock_unlock.user_id')
                        ->where('shops.channel', 10)
                        ->select('shops.url', 'shops.username', 'shops.password', 'shops.shop_name', 'shops.user_id', 'drm_users_lock_unlock.status', 'shops.category')
                        ->get()
                        ->toArray();
        $dt_shop = json_decode(json_encode($dt_shop), true);
        foreach($dt_shop as $shop){
            if(str_contains($shop['url'], 'localhost') or str_contains($shop['url'], '-update') or str_contains($shop['url'], '-install') or str_contains($shop['url'], "/./") or $shop['category'] == 3){
                continue;
            }
            $shop_info = array();
            $shop_info['url'] = $shop['url'];
            $shop_info['user_token'] = $shop['username'];
            $shop_info['user_pass_token'] = $shop['password'];
            if(substr($shop_info['url'], -1) != '/'){
                $shop_info['url'] .= '/';
            }
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $shop_info['url'].'api/v1/check-product-image-category',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => array(),
                CURLOPT_HTTPHEADER => array(
                    'userToken: '.$shop_info['user_token'],
                    'userPassToken: '.$shop_info['user_pass_token']
                ),
            ));

            $response = curl_exec($curl);
            curl_close($curl);
        }
    }

    static public function getCurrency()
    {
        $date = Carbon::now()->format('Y-m-d');
        $rates = CurrencyRate::where('updated_at', '>', Carbon::parse($date)->subHours(12))
            ->orderBy('id', 'desc')
            ->value('rates');
        if (empty($rates)) {
            $access_key = env('CURRENCY_ACCESS_KEY');
            $base = 'EUR';

            // Initialize CURL:
            $ch = curl_init('http://data.fixer.io/api/'.$date.'?access_key='.$access_key.'&base='.$base);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            // Store the data:
            $json = curl_exec($ch);
            curl_close($ch);

            // Decode JSON response:
            $res = json_decode($json, true);

            // Process rates
            if ($res['success'] && $res['rates']) {
                $apiData = ['rates' => $res['rates'], 'date' => $res['date']];
            } else {
            }
            $rates = $apiData['rates']??[];

            if (!empty($rates)) {
                CurrencyRate::create([
                    'date' => $date,
                    'fetching_date' => $apiData['date'],
                    'rates' => $rates,
                ]);
            }
        }
        if (!empty($rates)) {
            $currency_data = json_encode($rates);
            $dt_shop = DB::table('shops')
                        ->leftJoin('drm_users_lock_unlock', 'shops.user_id', 'drm_users_lock_unlock.user_id')
                        ->where('shops.channel', 10)
                        ->select('shops.url', 'shops.username', 'shops.password', 'shops.shop_name', 'shops.user_id', 'drm_users_lock_unlock.status', 'shops.category')
                        ->get()
                        ->toArray();
            $dt_shop = json_decode(json_encode($dt_shop), true);
            foreach($dt_shop as $shop){
                if(str_contains($shop['url'], 'localhost')){
                    continue;
                }
                $shop_info = array();
                $shop_info['url'] = $shop['url'];
                $shop_info['user_token'] = $shop['username'];
                $shop_info['user_pass_token'] = $shop['password'];
                if(substr($shop_info['url'], -1) != '/'){
                    $shop_info['url'] .= '/';
                }
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => $shop_info['url'].'api/v1/sync-currency-rate-to-drm',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => array('currency_rate'=> $currency_data),
                    CURLOPT_HTTPHEADER => array(
                        'userToken: '.$shop_info['user_token'],
                        'userPassToken: '.$shop_info['user_pass_token']
                    ),
                ));

                $response = curl_exec($curl);
                curl_close($curl);
            }
            dump("file updated");
        } else {
            // Exception throw from here
        }
    }

    public function dtEmergencyBackup(){
        $dt_shop = DB::table('shops')
                        ->leftJoin('server_statuses', 'shops.id', 'server_statuses.shop_id')
                        ->join('cms_users', 'shops.user_id', 'cms_users.id')
                        ->where('shops.channel', 10)
                        ->select('shops.url', 'shops.username', 'shops.password', 'shops.shop_name', 'shops.user_id', 'shops.category')
                        ->get()
                        ->toArray();
        $dt_shop = json_decode(json_encode($dt_shop), true);
        foreach($dt_shop as $shop){
            if(str_contains($shop['url'], 'localhost') or str_contains($shop['url'], "/./")){
                continue;
            }
            $shop_info = array();
            $shop_info['url'] = $shop['url'];
            $shop_info['user_token'] = $shop['username'];
            $shop_info['user_pass_token'] = $shop['password'];
            if(substr($shop_info['url'], -1) != '/'){
                $shop_info['url'] .= '/';
            }
            // $curl = curl_init();
            // curl_setopt_array($curl, array(
            //     CURLOPT_URL => $shop_info['url'].'api/v1/get-table-data?table=menus&update=true&updateField1=default_image&updateValue1=&updateField2=rollover_image&updateValue2=',
            //     CURLOPT_RETURNTRANSFER => true,
            //     CURLOPT_CUSTOMREQUEST => 'GET',
            //     CURLOPT_HTTPHEADER => array(
            //         'userToken: '.$shop_info['user_token'],
            //         'userPassToken: '.$shop_info['user_pass_token']
            //     ),
            // ));

            // $response = curl_exec($curl);
            // curl_close($curl);
            // dump($response);
        }
    }

    public function dtShopTariffCheck(Request $request){

        if($request->input('userToken') and $request->input('userPassToken')){
            $today = Carbon::now();
            $shop = Shop::where('username', $request->input('userToken'))->where('password', $request->input('userPassToken'))->where('channel', 10)->select('id', 'user_id')->first();
            $user_id = $shop->user_id;
            $is_dt_user = DB::table('user_group_relations')->where('user_id', $user_id)->where('group_id', 2)->exists();
            if(!$is_dt_user){
                $has_purchase_plan = DB::table('purchase_import_plans')->where('cms_user_id', $user_id)->whereDate('end_date', '>=', $today)->exists();
                if($has_purchase_plan){
                    return response()->json(['success' => $has_purchase_plan, 'message' => 'successful'], 200);
                }
            }else{
                $data = DB::table('dt_tariff_purchases')->where('user_id', $user_id)->where('end_date', '>=', \Carbon\Carbon::now())->exists();
                return response()->json(['success' => $data, 'message' => 'successful'], 200);
            }
        }else{
            return response()->json(['success' => false, 'message' => 'Required user token and user pass token!'], 203);
        }
    }

    public function getBrandLogo(Request $request){
        if($request->header('userToken') and $request->header('userPassToken')){
            $brand = $request->input('brand');
            $shop = Shop::where('username', $request->header('userToken'))->where('password', $request->header('userPassToken'))->where('channel', 10)->select('id', 'user_id')->first();
            $user_id = $shop->user_id;
            $brandLogo = DB::table('dropmatix_product_brands')->where('brand_name', $brand)->where('user_id', $user_id)->value('brand_logo');
            return response()->json(['success' => true, 'data' => $brandLogo, 'message' => 'Found Brand logo!'], 200);
        }else{
            return response()->json(['success' => false, 'message' => 'Required user token and user pass token!'], 203);
        }
    }

    public function getOrderStatus(Request $request){
        if($request->header('userToken') and $request->header('userPassToken')){
            $shop = Shop::where('username', $request->header('userToken'))->where('password', $request->header('userPassToken'))->where('channel', 10)->select('id', 'user_id')->first();
            if(!isset($shop) || empty($shop)){
                return response()->json(['success' => false, 'message' => 'Shop noot found'], 203);
            }
            $dt_order_id = $request->input('dt_order_id');
            $status  = [];
            $status['Shipped']       = 'Versendet';
            $status['mahnung']       = 'Mahnung';
            $status['paid'   ]       = 'Zahlung eingegangen';
            $status['inkasso']       = 'Inkasso';
            $status['Erstatte]t']     = 'Zahlung erstattet';
            $status['mp_return'] = 'Marketplace return';
            $status['return_shipped'] = 'Return shipped';
            $status['return_received'] = 'Return received';
            $status['mp_rejected'] = 'Order rejected';
            $status['stock_unavailable'] = 'No availability';
            $status['payment_in_progress'] = 'Zahlung in Bearbeitung';
            $user_id = $shop->user_id;
            return response()->json(['success' => true, 'data' => json_encode($status), 'message' => 'Status updated successfully!'], 200);
        }else{
            return response()->json(['success' => false, 'message' => 'Required user token and user pass token!'], 203);
        }
    }

    public function updateOrderStatus(Request $request){
        if($request->header('userToken') and $request->header('userPassToken')){
            $shop = Shop::where('username', $request->header('userToken'))->where('password', $request->header('userPassToken'))->where('channel', 10)->select('id', 'user_id')->first();
            if(!isset($shop) || empty($shop)){
                return response()->json(['success' => false, 'message' => 'Shop noot found'], 203);
            }
            $status = $request->input('status');
            $dt_order_id = $request->input('dt_order_id');
            $user_id = $shop->user_id;
            return response()->json(['success' => true, 'message' => 'Status updated successfully!'], 200);
        }else{
            return response()->json(['success' => false, 'message' => 'Required user token and user pass token!'], 203);
        }
    }

    public function updateDTTaxSetting(Request $request){
        if($request->header('userToken') and $request->header('userPassToken')){
            $shop = Shop::where('username', $request->header('userToken'))->where('password', $request->header('userPassToken'))->where('channel', 10)->select('id', 'user_id')->first();
            if(!isset($shop) || empty($shop)){
                return response()->json(['success' => false, 'message' => 'Shop noot found'], 203);
            }
            if(isset($request->type) and isset($request->value)){
                $user_id = $shop->user_id;
                if($request->type == 'invoiceSetting'){
                    if(isset($request->value)){
                        $setting = DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();
                        $already_used = $this->isInvoiceSettingAlreadyUsed($setting);
                        if($request->value == 2){
                            $row = ['small_business' => 0];
                        }else{
                            $row = ['small_business' => 1];
                        }
                        if($already_used){
                            $this->insertInvoiceSetting($row, $user_id, $setting);
                        }else {
                            if($setting && isset($setting->id)){
                                DB::table('drm_invoice_setting')->where('id', '=', $setting->id)->update($row);
                            }else {
                                $this->insertInvoiceSetting($row, $user_id, $setting);
                            }
                        }

                    }
                }
                return response()->json(['success' => true, 'message' => 'Status updated successfully!'], 200);
            }
            return response()->json(['success' => false, 'message' => 'Something went wrong!'], 200);

        }else{
            return response()->json(['success' => false, 'message' => 'Required user token and user pass token!'], 203);
        }
    }

    private function insertInvoiceSetting($row, $user_id, $setting)
    {
        if($setting->watermark_photo)
        {
            $row['watermark_photo'] = $this->cloneSpacesUrl($setting->watermark_photo);
        }

        if($setting->logo)
        {
            $row['logo'] = $this->cloneSpacesUrl($setting->logo);
        }

        $row['cms_user_id'] = $user_id;
        $row['created_at'] = Carbon::now();
        $row['updated_at'] = Carbon::now();
        return DB::table('drm_invoice_setting')->insert($row);
    }

    private function isInvoiceSettingAlreadyUsed($setting)
    {
        if($setting && $setting->id)
        {
            //Update existing orders layout
            DB::table('new_orders')
            ->where('cms_user_id', '=', $setting->cms_user_id)
            ->whereNull('invoice_layout_id')
            ->whereNull('deleted_at')
            ->update(['invoice_layout_id' => $setting->id]);
            usleep(1000);

            return DB::table('new_orders')
                ->whereNull('deleted_at')
                ->where('cms_user_id', '=', $setting->cms_user_id)
                ->where('invoice_layout_id', '=', $setting->id)
                ->exists();
        }

        return false;
    }

    private function cloneSpacesUrl($url)
    {
        if(empty($url)) return;
        $search = '.com/';
        $file = substr($url, strpos($url, $search) + strlen($search));

        if (Storage::disk('spaces')->exists($file)) {

            $rand = strtolower(\Str::random(6));
            $rand1 = strtolower(\Str::random(6));
            $new_file = pathinfo($file, PATHINFO_DIRNAME).'/clone_'.$rand1.time().$rand.'.'.pathinfo($file, PATHINFO_EXTENSION);

            Storage::disk('spaces')->put($new_file, Storage::disk('spaces')->get($file), 'public');
            return Storage::disk('spaces')->url($new_file);
        }
    }

    public function getMarketingTag(Request $request){
        if($request->header('userToken') and $request->header('userPassToken')){
            $shop = Shop::where('username', $request->header('userToken'))->where('password', $request->header('userPassToken'))->where('channel', 10)->select('id', 'user_id')->first();
            if(!isset($shop) || empty($shop)){
                return response()->json(['success' => false, 'message' => 'Shop noot found'], 203);
            }
            $user_id = $shop->user_id;
            $tags = DB::table('dropfunnel_tags')->where('user_id', $user_id)->select('tag')->get()->pluck('tag');
            return response()->json(['success' => true, 'data' => $tags, 'message' => 'Tags data return!'], 200);
        }else{
            return response()->json(['success' => false, 'message' => 'Required user token and user pass token!'], 203);
        }
    }
}
