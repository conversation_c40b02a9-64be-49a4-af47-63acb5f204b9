<?php

namespace App\Http\Controllers\Api\V1\Droptienda;

use App\Http\Controllers\Controller;
use App\Services\Order\OrderService;
use Illuminate\Http\Request;
use PHPUnit\Util\Exception;

class OrderApiController extends Controller
{
    private $service;
    private $user_id;

    public function __construct(OrderService $service)
    {
        $this->service = $service;
        $this->user_id = get_api_user_id(request()->header('userToken'), request()->header('userPassToken'));

        if (empty($this->user_id)) {
            return redirect()->to('/unauthorized')->send();
        }
    }

    public function index(Request $request)
    {
        $data = $this->service->all($request->all());

        return response()->json($data);
    }

    public function show($id)
    {
        $category = $this->service->getById($id);

        return response()->json($category);
    }

    public function store(Request $request)
    {
        $data = $this->service->store(array_merge($request->all(), ['user_id' => $this->user_id, 'channel' => \App\Enums\Channel::DROPTIENDA]));

        return response()->json($data);
    }
    public function storeTest(Request $request)
    {
        $data = $this->service->storeTest(array_merge($request->all(), ['user_id' => $this->user_id, 'channel' => \App\Enums\Channel::DROPTIENDA]));

        return response()->json($data);
    }

    public function update($id, Request $request)
    {
        $data = $this->service->update($id, $request->all());

        return response()->json($data);
    }

    public function syncCategory(Request $request)
    {
        $sync_event = $request->sync_event ?? '';

        $categories = $this->service->getCategories('categories', $sync_event, 2);
    }
}
