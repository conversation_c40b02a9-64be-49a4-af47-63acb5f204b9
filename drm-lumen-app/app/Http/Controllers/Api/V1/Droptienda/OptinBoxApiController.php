<?php

namespace App\Http\Controllers\Api\V1\Droptienda;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class OptinBoxApiController extends Controller
{

    public function getTemplates(Request $request)
    {
        try {
            $password = $request->header('userPassToken');
            $token = $request->header('userToken');

            $shop = \App\Models\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();

            $path = storage_path() . "/optin_templates.json";
            $default_templates = json_decode(file_get_contents($path), true);

            $custom_tempalte = DB::table('optin_pages')->where('user_id', $shop->user_id)->select('id','html', 'css')->get();



            return response()->json([
                'success' => true,
                'data' => [
                    'user_id' => $shop->user_id,
                    'default' => $default_templates,
                    'custom' => $custom_tempalte
                ],
            ]);
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage() . ' ' . $e->getLine(),
            ];
        }
    }

    public function protectedShop(Request $request)
    {
        try {
            $password = $request->header('userPassToken');
            $token = $request->header('userToken');

            $shop = \App\Models\Shop::where('username', $token)
                ->where('password', $password)
                ->value('protected_shop');
           $data =  json_decode($shop,true);
            if ($shop) {
                return response()->json([
                    'success' => true,
                    'message' => $data
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => " "
                ]);
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage() . ' ' . $e->getLine(),
            ];
        }
    }


}
