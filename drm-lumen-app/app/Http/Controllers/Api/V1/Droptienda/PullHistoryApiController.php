<?php

namespace App\Http\Controllers\Api\V1\Droptienda;

use App\Http\Controllers\Controller;
use App\Models\DroptiendaPullHistory;
use App\Services\Category\CategoryService;
use Illuminate\Http\Request;

class PullHistoryApiController extends Controller
{
    private $service;
    private $user_id;

    public function __construct(CategoryService $service)
    {
        $user_credential = [
            'userToken' => request()->header('userToken'),
            'userPass' => request()->header('userPassToken'),
        ];

        debug_log(json_encode($user_credential, true),'DT CREDENTIAL');
        
        $this->service = $service;
        $this->user_id = get_api_user_id(request()->header('userToken'), request()->header('userPassToken'));
        

        if (empty($this->user_id)) {
            //$this->user_id =387;
            return redirect()->to('/unauthorized')->send();
        }
    }

    public function store(Request $request)
    {
        $data = [];

        if(!empty($this->user_id))
        {
            $data = DroptiendaPullHistory::create([
                'dt_ref_id' => $request->dt_ref_id,
                'user_id' => $this->user_id,
            ]);
        }

        return response()->json($data);
    }
}
