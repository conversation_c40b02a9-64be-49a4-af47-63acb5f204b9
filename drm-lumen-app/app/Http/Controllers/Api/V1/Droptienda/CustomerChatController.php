<?php

namespace App\Http\Controllers\Api\V1\Droptienda;

use DateTime;
use DateTimeZone;
use Carbon\Carbon;
use GuzzleHttp\Client;
use App\Models\ChatList;
use App\Models\NewOrder;
use Illuminate\Http\Request;
use App\Models\DroptiendaMessages;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class CustomerChatController extends Controller
{

	public function fetch(Request $request, $order_id)
	{
		try {
			$token   = $request->header('userToken');
			$password = $request->header('userPassToken');
			$order = $this->getOrderInfo($token, $password, $order_id);

			$messages = DroptiendaMessages::where('order_id', $order['id'] )
			->select('id', 'message', 'read_at', 'order_id', 'user_id', 'created_at', 'sender', 'sent_time')
			->orderBy('id', 'asc')
			->get()
			->map(function($m) {

                $date = new DateTime("now", new DateTimeZone('Europe/Berlin') );
                $time_now = Carbon::parse($date->format('Y-m-d H:i:s'));
                $sent_time = Carbon::parse($m->sent_time);
                $m->time = $sent_time->diffForHumans($time_now);
                $m->recipient = empty($m->user_id)? 'me' : 'other';
				return $m;
			});

			$unread = collect($messages)->whereNull('read_at')->whereNotNull('user_id')->count();

			$meta = [
				'status' => $order['status'],
				'order_id' => $order['id'],
				'unread'	=> $unread,
				'parcels' => $order['parcels'],
			];

			return response()->json([
				'success' => true,
				'data' => ['messages' => $messages, 'meta' => $meta],
			]);
		}catch(\Exception $e) {
			return response()->json([
				'success' => false,
				'message' => $e->getMessage().' '.$e->getLine(),
			]);
		}
	}

	public function send(Request $request, $order_id)
	{
        $chat_preference = get_option('chat_preference', 'chat')->option_value ?? null;
		$this->validate($request, [
			'message' => 'required|min:1|max:500',
			'sender_name' => 'required|min:2|max:80',
			'sender_email' => 'required|min:2|max:80|email',
		]);

		try {
			$token   = $request->header('userToken');
			$password = $request->header('userPassToken');
			$order = $this->getOrderInfo($token, $password, $order_id);
			if(empty($order)) return;
			$sender = [
				'name' => $request->sender_name,
				'email' => $request->sender_email,
			];

            $date = new DateTime("now", new DateTimeZone('Europe/Berlin') );
            $sent_time = $date->format('Y-m-d H:i:s');
            // return $sent_time;
			$data = DroptiendaMessages::create([
				'order_id' => $order['id'],
				'customer_id' => $order['drm_customer_id'],
				'message' => $request->message,
				'sender' => $sender,
                'chat_preference' => $chat_preference,
                'sent_time' => $sent_time
			]);

            // if($request->auto_reply){
            //     $data = DroptiendaMessages::create([
            //         'order_id' => $order['id'],
            //         'customer_id' => $order['drm_customer_id'],
            //         'message' => $request->auto_reply,
            //         'sender' => $sender,
            //         'user_id' => $order['user_id']
            //     ]);
            // }

	        if($data) {
				//Send notification
				$user_id = $order['user_id'];
				$drm_order_id = $order['id'];

				$notify_data = [
					'user_id'    => $user_id,
					'message'    => $request->message,
					'hook'       => 'DROPTIENDA_ORDER_CHAT',
					'url'		 => env('DRM_URL').'/admin/droptienda-chat?room='.$drm_order_id,
					'room_id'	 => $drm_order_id,
	                'sender' => $sender,
	                'created_at' => $data->created_at->toDateTimeString(),
	                'time' => $data->created_at->diffForHumans(),
	                'action' => 'push_chat',
	                'order_id' => $order['id']
				];
				$this->sendNotification($notify_data);
			}

			$data->time = $data->created_at->diffForHumans();
			$data->recipient = empty($data->user_id) ? 'me' : 'other';
			$data->order_key = $order['id'];

            $chat_list = DB::table('chat_list')
            ->where(['order_id' => $order['id']]);

            $chat_sorting = DroptiendaMessages::get()->count();
            if( $chat_list->get()->count() ){
                $chat_list->update([
                    'deleted_at' => Null,
                    'updated_at' => Carbon::now()->toDateTimeString(),
                    'chat_sorting' => $chat_sorting
                ]);
            }else{
                ChatList::create([
                    'order_id' => $order['id'],
                    'user_id' => $user_id,
                    'customer_id' => $order['drm_customer_id'],
                    'chat_sorting' => $chat_sorting
                ]);
            }


			return response()->json([
				'success' => true,
				'data' => $data,
			]);
		}catch(\Exception $e) {
			return response()->json([
				'success' => false,
				'message' => $e->getMessage().' '.$e->getLine(),
				'data' => $order
			]);
		}

	}



	public function getChatQuestion(Request $request){
        $password = $request->header('userPassToken');
        $token = $request->header('userToken');

        $shop = \App\Models\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();

        if(empty($shop)) {
			return response()->json([
				'success' => false,
				'message' => 'Shop not found!',
			]);
		}

        $questions =  DB::table('chat_answers')
            ->select('*')
            ->where('user_id', $shop->user_id)
            ->get()
            ->toArray();
		return response()->json([
			'success' => true,
			'data' => $questions,
		]);
    }


	public function read(Request $request, $order_id)
	{
		$token   = $request->header('userToken');
		$password = $request->header('userPassToken');
		$order = $this->getOrderInfo($token, $password, $order_id);
        if(!$order) {
            return [
                'success' => false
            ];
        }
		$update = false;

		if($order) {
			$update = DB::table('droptienda_messages')
				->where('order_id', $order['id'])
				->whereNotNull('user_id')
				->whereNull('read_at')
				->update(['read_at' => Carbon::now()]);
		}

		return response()->json([
			'success' => (bool)$update,
			'order_id' => $order_id,
		]);
	}


	public function unreadMessages(Request $request)
	{
		$token   = $request->header('userToken');
		$password = $request->header('userPassToken');

		$this->validate($request, [
			'orders' => 'required|array|min:1',
		]);

		$orders_id = $request->orders;
		if(empty($orders_id)) return [];

		$shop = \App\Models\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();
		if(empty($shop)) {
			return response()->json([
				'success' => false,
				'message' => 'Shop not found!',
			]);
		}

		$shop_id = $shop->id;
		$orders_api_id = collect($orders_id)->map(function($order_id) use ($shop_id) {
			return 'drop_t' . $shop_id . '_' . $order_id;
		})
		->toArray();


		//Get orders id
		$order_list = NewOrder::with('order_trackings.parcel')->whereIn('order_id_api', $orders_api_id)->where('cms_user_id', $shop->user_id)->get();
		$order_id_list = collect($order_list)->pluck('id')->toArray();


		$tracking = collect($order_list)->map(function($item) {
			$parcel_data = [];
		    if(!empty($item->order_trackings)){
		    	$trackings_data = $item->order_trackings;
	            $trackings_data = collect($trackings_data)->sortByDesc('id');
	            foreach($trackings_data as $tracking){
	            	$parcel_data[] = [
	            		'package_number' => $tracking->package_number,
	            		'parcel_name' => ($tracking->parcel) ? ($tracking->parcel->parcel_name) : '',
	            	];
	            }
	        }

	        return [
	        	'id' => $item->id,
	        	'data' => $parcel_data,
	        ];
		})
		->pluck('data', 'id')
		->toArray();


		$data = DroptiendaMessages::whereIntegerInRaw('order_id', $order_id_list)
		->with('order:id,order_id_api')
		->whereNotNull('user_id')->whereNull('read_at')
		->select('read_at', 'order_id', 'user_id')
		->orderBy('id', 'asc')
		->get()
		->groupBy('order_id')
		->map(function($items) {
			$item = $items->first();
			$unread = $items->count();
			return [
				'id' => $item->order_id,
				'unread' => $unread,
			];
		})
		->pluck('unread', 'id')
		->toArray();

		$res = [];
		foreach($order_list as $o) {
			$segment = explode('_', $o->order_id_api);
			$order_id_segment = end($segment);
			$res[$order_id_segment] = [
				'unseen' => $data[$o->id] ?? 0,
				'id' => $order_id_segment,
				'status' => drmHistoryLabel($o->status),
				'tracking' => $tracking[$o->id] ?? [],
			];
		}

		return response()->json([
			'success' => true,
			'data' => $res,
		]);
	}


	private function getOrderInfo($token, $password, $order_id)
	{
		try {
			$shop = \App\Models\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();
			if(empty($shop)) throw new \Exception('Shop not found!');

			$order_id_api = 'drop_t' . $shop->id . '_' . $order_id;

			$order = NewOrder::with('parcel', 'order_trackings.parcel')
				->where('cms_user_id', $shop->user_id)
				->where('shop_id', $shop->id)
				->where('order_id_api', $order_id_api)
				->select('id', 'status', 'drm_customer_id', 'cms_user_id', 'parcel_id')->first();

			$parcel_data = [];
		    if(!empty($order->order_trackings)){
		    	$trackings_data = $order->order_trackings;

	            $trackings_data = collect($trackings_data)->sortByDesc('id');
	            foreach($trackings_data as $tracking){
	            	$parcel_data[] = [
	            		'package_number' => $tracking->package_number,
	            		'parcel_name' => ($tracking->parcel) ? ($tracking->parcel->parcel_name) : '',
	            	];
	            }
	        }

			return [
				'id' => $order->id,
				'status' => drmHistoryLabel($order->status),
				'drm_customer_id' => $order->drm_customer_id,
				'user_id' => $order->cms_user_id,
				'parcels' => $parcel_data,
			];
		}catch(\Exception $e) {
			return [
				'success' => false,
				'message' => $e->getMessage().' '.$e->getLine(),
			];
		}
	}


	//Send notification to drm
	private function sendNotification($data)
	{
        $url = 'https://drm.software/api/notification/push';


        // $params = [
        //     'user_id'    => $user_id,
        //     'message'    => $message,
        //     "url"        => "#",
        //     "hook"       => $hook,
        //     'room_id'	=> $drm_order_id,
        // ];

        $options = [
            'form_params' => $data
        ];

        try {
        	$client = new Client();
            $client->post($url, $options);
        } catch (\Exception $e) {}
	}



	//Product request Entry
	public function productStockRequest(Request $request)
	{

		$token   = $request->header('userToken');
		$password = $request->header('userPassToken');

		$this->validate($request, [
			'customer' => 'required|array|min:2',
			'customer.name' => 'required|min:2|max:40',
			'customer.email' => 'required|min:6|max:50|email',

			'product' => 'required|array|min:4',
			'product.name' => 'required',
			'product.ean' => 'required|min:12|max:15',
			'product.id' => 'required',
			'product.url'   => 'required|url',

		]);

		try {

			$shop = \App\Models\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();

			$data = $request->only(['customer', 'product']);
			$data['shop_id'] = $shop->id;
			$data['user_id'] = $shop->user_id;

			$payload = [
				'data' => $data,
				'channel_product_id' => $data['product']['id'],
			];

			$req = [
				'payload' => $payload,
				'token' => 'd946gm34gd1rb5',
			];

            $url = "http://165.22.24.129/api/dt-product-stock-request";

            $client = new Client();
	        $r = $client->request('POST', $url, [
	            'form_params' => $req
	        ]);

            return $r->getBody()->getContents();

        } catch (\Exception $e) {
        	return response()->json([
        		'success' => false,
        		'message' => $e->getMessage() .' '.$e->getLine(),
        	], 400);
        }
	}


	//Suppliers
	public function suppliers(Request $request)
	{

		$token   = $request->header('userToken');
		$password = $request->header('userPassToken');

		try{

			$user_id = \App\Models\Shop::where('username', $token)->where('password', $password)->value('user_id');
			if(empty($user_id)) throw new \Exception('Invalid access');


			$suppliers = DB::table('delivery_companies')->where('user_id', $user_id)->get();

			return response()->json([
				'success' => true,
				'data' => $suppliers,
				'message' => null,
			]);

        } catch (\Exception $e) {
        	return response()->json([
        		'success' => false,
        		'message' => $e->getMessage(),
        		'data' => [],
        	], 400);
        }
	}

	//Supplier
	public function supplierDetail(Request $request, $id)
	{

		$token   = $request->header('userToken');
		$password = $request->header('userPassToken');

		try{

			$user_id = \App\Models\Shop::where('username', $token)->where('password', $password)->value('user_id');
			if(empty($user_id)) throw new \Exception('Invalid access');

			$supplier = DB::table('delivery_companies')->where('user_id', $user_id)->where('id', $id)->first();

			return response()->json([
				'success' => true,
				'data' => $supplier,
				'message' => null,
			]);

        } catch (\Exception $e) {
        	return response()->json([
        		'success' => false,
        		'message' => $e->getMessage(),
        		'data' => [],
        	], 400);
        }
	}

	//Tax list
	public function taxList(Request $request)
	{

		$token   = $request->header('userToken');
		$password = $request->header('userPassToken');

		try{

			$user_id = \App\Models\Shop::where('username', $token)->where('password', $password)->value('user_id');
			if(empty($user_id)) throw new \Exception('Invalid access');


			$taxData = DB::table('tax_rates')->get();

			return response()->json([
				'success' => true,
				'data' => $taxData,
				'message' => null,
			]);

        } catch (\Exception $e) {
        	return response()->json([
        		'success' => false,
        		'message' => $e->getMessage(),
        		'data' => [],
        	], 400);
        }
	}

    //Test order
	public function testOrderCount(Request $request)
	{

		$token   = $request->header('userToken');
		$password = $request->header('userPassToken');

		try {
            $data = array();
			$shop = \App\Models\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();
            if(empty($shop)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Shop not found!',
                ]);
            }

            $data['limit'] = 5;
            $data['orders'] = 0;

            return response()->json([
                'success' => true,
                'data' => $data,
            ]);


			// $limit = DB::table('cms_users')->where('id', $shop->user_id)->select('test_order_limit')->value('test_order_limit');

   //          $data['limit'] = is_null($limit) ? 5: $limit;

   //          $data['orders'] = DB::table('new_orders')->where('cms_user_id', $shop->user_id)->where('test_order', 1)->count();

   //          return response()->json([
   //              'success' => true,
   //              'data' => $data,
   //          ]);

        } catch (\Exception $e) {
        	return response()->json([
        		'success' => false,
        		'message' => $e->getMessage() .' '.$e->getLine(),
        	], 400);
        }
	}

}
