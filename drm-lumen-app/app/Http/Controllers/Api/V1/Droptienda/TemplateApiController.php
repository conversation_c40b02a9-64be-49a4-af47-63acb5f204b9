<?php

namespace App\Http\Controllers\Api\V1\Droptienda;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class TemplateApiController extends Controller
{

    public function checkPurchase(Request $request)
    {
        try{

            $user_id = get_api_user_id(request()->header('userToken'), request()->header('userPassToken'));
            if (empty($user_id)) throw new \Exception('Sorry! Shop not found');
            
            $template_id = $request->template_id;
            $active_flat_rate = DB::table('dt_flat_rates')->where('user_id', $user_id)->where('end_date', '>=', \Carbon\Carbon::now())->value('plan_id');
            $purchases = DB::table('template_purchases')->where('user_id', $user_id)
                        ->whereNull('deleted_at')
                        ->when($template_id, function ($query, $template_id) {
                            return $query->where('template_id', $template_id);
                        })
                        ->where(function($q){
                            $q->whereNull('end_date')->orWhere('end_date', '>=', \Carbon\Carbon::now());
                        })
                        ->select('plan_id', 'template_id', 'end_date')
                        ->get()
                        ->keyBy('template_id')
                        ->map(function($item){
                            $remaining = $item->end_date? \Carbon\Carbon::now()->diffInSeconds($item->end_date, false) : 0;
                            $remaining = $remaining > 0 ? $remaining : 0;
                            $is_trial = $item->plan_id == 10 ? true : false;

                            return [
                                'plan_id' => $item->plan_id,
                                'end_date' => $item->end_date,
                                'remaining' => $remaining,
                                'is_trial' => $is_trial,
                                'template_id' => $item->template_id,
                            ];
                        })
                        ->toArray();

            if(empty($template_id) || in_array($template_id, [7, 33]) ){

                $purchases[7] = [
                    'plan_id' => 9,
                    'end_date' => null,
                    'remaining' => 0,
                    'is_trial' => false,
                    'template_id' => 7,
                ];

                $purchases[33] = [
                    'plan_id' => 9,
                    'end_date' => null,
                    'remaining' => 0,
                    'is_trial' => false,
                    'template_id' => 33,
                ];
            }

            if(!empty($active_flat_rate) || !empty($purchases)){
                $has_flat_rate = $active_flat_rate? true : false;
                return response()->json([
                    'success' => true,
                    'message' => 'User have purchased this template',
                    'purchase' => $purchases,
                    'has_flat_rate' => $has_flat_rate,
                    'active_flat_rate' => $active_flat_rate
                ], 200);
            }

            throw new \Exception('You have not purchase this template yet');
        
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'purchase' => []

            ], 400); 
        }
    }


    public function getImportProductData(Request $request)
    {
        try{
            $user_id = get_api_user_id(request()->header('userToken'), request()->header('userPassToken'));
            if (empty($user_id)) throw new \Exception('Sorry! Shop not found');
            $token = 'dtm3decr435mpdt';
            $url = 'https://drm.software/api/drm-product-count';

            $response = postApiCall([
                'user_id' => $user_id,
                'token' => $token,
            ], $url);

            return response()->json([
                'success' => true,
                'data' => $response
            ], 200);

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400); 
        }
    }

    //DT license
    public function dtLicense(Request $request)
    {
        return response()->json([
            'success' => true,
            'data' => [
                'plan_id' => 10,
                'status' => 1,
            ],
            'message' => 'License active!',
        ], 200);


        try{
            $user_id = get_api_user_id(request()->header('userToken'), request()->header('userPassToken'));
            if (empty($user_id)) throw new \Exception('Sorry! Shop not found');

            $data = DB::table('dt_licenses')->where('user_id', $user_id)->where('end_date', '>=', \Carbon\Carbon::now())->orderBy('status', 'desc')->select('plan_id', 'status')->first();

            if($data && $data->plan_id){
                return response()->json([
                    'success' => true,
                    'data' => $data,
                    'message' => 'License active!',
                ], 200);                
            }

            throw new \Exception('License not active!');
            
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400); 
        }
    }
}
