<?php

namespace App\Http\Controllers\Api\V1\Droptienda;

use App\Http\Controllers\Controller;
use App\Http\Middleware\Authenticate;
use App\Services\Customer\CustomerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\NewCustomer;
use Carbon\Carbon;

use Auth;

class CustomerApiController extends Controller{

	private $servcie;
	private $user_id;

	public function __construct(CustomerService $service) {
		$this->service = $service;
		$this->user_id = get_api_user_id(request()->header('userToken'), request()->header('userPassToken'));

		if (empty($this->user_id)) {
		    return redirect()->to('/unauthorized')->send();
		}
	}

	public function index (Request $request)
	{
		$customers = $this->service->all($request->all());

		return response()->json($customers);
	}

	public function show ($id)
	{
		$customer = $this->service->getById($id);

		return response()->json($customer);
	}

	public function store (Request $request)
	{
		$customer = $this->service->store( array_merge( $request->all(), ['user_id'=>$this->user_id]) );

		return response()->json($customer);
	}

	public function update ($id, Request $request)
	{
		$customer = $this->service->update($id, $request->all() );

		return response()->json($customer);
	}

	public function delete ($id)
	{
		$response = $this->service->destroy($id);

		return response()->json($response);
	}


	// new customer

	public function droptiendaNewCustomer(Request $customer_info){
		$user_id = 2439;
		if(empty($user_id)) return false;

		DB::table('cms_user_infos')->updateOrInsert(
			['cms_user_id' => $user_id],
			request()->input(),
		);

		$this->validate($customer_info, [
			'email' => 'required',
			// 'country' => 'required',
			// 'city' => 'required',
			// 'postal_code' => 'required',
			// 'address' => 'required',
			// 'first_name' => 'required',
		]);

		$customer_data = [
			'full_name' => $customer_info['first_name'],
			'company_name' => $customer_info['company'],
			'country' => drmCountryNameFull($customer_info['country']),
			'phone' => $customer_info['phone'],
			'city' => $customer_info['city'],
			'zip_code' => $customer_info['postal_code'],
			'address' => $customer_info['address'],
            'vat_number' => $customer_info['valid_id']
		];

		//customer_check
		$customer_check = [
			'email' => $customer_info['email'],
			'user_id' => $user_id,
		];

		//customer_data
		// $customer_data = array_filter($customer_data);

		// customer add
		$customer = NewCustomer::updateOrCreate($customer_check, $customer_data);

		$customer->update([
			'billing' => updateBillingShippingAddress(billingInfoJson($customer_info), $customer->billing),
			'shipping' => updateBillingShippingAddress(shippingInfoJson($customer_info), $customer->shipping)
		]);

		return $customer->id;

	}

	public function droptiendaCustomerGroupTitle(Request $customer_info){

		$user_id = $this->user_id;
		$userGroup = array();
		$userGroup['user_id'] = $user_id;
		$userGroup['group_name'] = $customer_info->group_name;
		$userGroup['email'] = $customer_info->email;
		if($customer_info->has('type')){
			$userGroup['type'] = $customer_info->type;
		}else{
			$userGroup['type'] = 'dt';
		}

		$curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'http://*************/api/droptineda-customer-group-title',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $userGroup,
        ));
        $response = curl_exec($curl);

        curl_close($curl);

        $response = json_decode($response, true);
        return $response;
	}

    public function getTariffOrderInfo(){
		$user_id = $this->user_id;
		$dt_tariff_type = 12;
		$dt_tariff_plan = DB::table('import_plans')->where('status', 1)->where('dt_tariff_active', 1)->pluck('id')->toArray();

		$orders_info = DB::table('new_orders')
		->where('cms_client', $user_id)
		->where('insert_type', $dt_tariff_type)
		// ->whereRaw("(JSON_EXTRACT(cart, '$[*].sub_end_date') > 2023-05-31")
		// ->where('cart->sub_end_date', '>=', Carbon::now()->toDateString())
		->orderBy('id', 'desc')
		->get()
		->map(function ($item) use ($dt_tariff_plan){
			$cart_item = json_decode($item->cart, true);

			if($cart_item[0]['sub_end_date'] > Carbon::now()->toDateString() && in_array($cart_item[0]['plan_id'], $dt_tariff_plan)) {
                $order_info['id']      = $item->id;
                $order_info['title']   = $cart_item[0]['product_name'];
                $order_info['amount']  = $cart_item[0]['amount'];
                $order_info['end_date']= $cart_item[0]['sub_end_date'];
				return $order_info;
			};
		})
		->toArray();
		$orders_info = array_values(array_filter($orders_info));

        return $orders_info ?? [];
	}
}
