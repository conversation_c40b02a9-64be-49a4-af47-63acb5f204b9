<?php

namespace App\Http\Controllers\Api\V1\Accounting;

use App\Http\Controllers\Controller;
use App\Models\NewOrder;
use App\Models\UpcommingInvoiceCategory;
use App\Models\DeliveryCompany;
use App\Models\UpcomingInvoice;
use App\Models\AccountingFileCache;
use Exception;
use Firebase\JWT\JWT;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class AccountingController extends Controller
{
    //Get index
    public function getIndex(Request $request)
    {
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0) {
            return response()->json([], 401);
        }

        $orders = NewOrder::where('cms_user_id', $userId)->get();
        $accounts = UpcomingInvoice::where('user_id', $userId)->get();

        return response()->json([
            'orders' => $orders,
            'accounts' => $accounts,
        ], 200);
    }

    //Save invoice
    public function add(Request $request)
    {
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0) {
            return response()->json([
                'success' => false,
                'message' => 'Authorization failed'
            ], 401);
        }

        //Validate request
        $this->validate($request, $this->rules(), $this->rulesError());
        try {

            $input_data = $request->all();
            $input_data['user_id'] = $userId;


            $amount = $input_data['amount'];
            $tax = $input_data['tax'];

            $input_data['amount'] = removeCommaPrice($amount);
            $input_data['tax'] = removeCommaPrice($tax);

            $accounting = UpcomingInvoice::create($input_data);
            if ($accounting) {
                //Process to upload files
                $this->upload_files($accounting->id, $userId);
            } else {
                throw new \Exception('Data insert failed!');
            }
            return response()->json([
                'success' => true,
                'message' => 'Data added successfully!'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 302);
        }
    }


    //Get archive files
    public function archiveFiles(Request $request)
    {
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0) {
            return response()->json([
                'success' => false,
                'message' => 'Authorization failed'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'data' => AccountingFileCache::where(['user_id' => $userId])->select('id', 'path', 'file_ext', 'url')->get()->toArray(),
        ], 200);
    }

    //Suppliers
    public function supplier(Request $request)
    {

        $perpgae = $request->perpgae ?? 20;
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0) {
            return response()->json([
                'success' => false,
                'message' => 'Authorization failed'
            ], 401);
        }

        $data = DeliveryCompany::select('name', 'id')->where('user_id', $userId)->simplePaginate($perpgae);
        return response()->json([
            'success' => true,
            'message' => 'Success',
            'data' => $data,
        ], 200);
    }

    public function category(Request $request)
    {
        $perpgae = $request->perpgae ?? 20;
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0) {
            return response()->json([
                'success' => false,
                'message' => 'Authorization failed'
            ], 401);
        }

        $data = UpcommingInvoiceCategory::orderBy('category_name')->select('id', 'category_name')->select('category_name', 'id')->simplePaginate($perpgae);
        return response()->json([
            'success' => true,
            'message' => 'Success',
            'data' => $data,
        ], 200);
    }


    //Store upcomming invoice cache files
    public function fileUloadToCache(Request $request)
    {

        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0) {
            return response()->json([
                'success' => false,
                'message' => 'Authorization failed'
            ], 401);
        }

        $files = $request->file;
        $type = null;
        $f = null;

        Log::info('File log- app');
        Log::info($files);

        $this->validate($request, [
            'file' => 'required|array',
            'file.*.type' => 'in:jpeg,jpg,bmp,png,gif,svg,pdf',
            'file.*.base64' => 'required',
        ]);

        try {
            if (is_array(request()->file)) {
                $files = request()->file;

                foreach ($files as $key => $file) {

                    $rand = Str::random(40);
                    $extension = $file['type'];
                    $base64 = $file['base64'];

                    $unique_file_name = $rand . '.' . $extension;
                    $accounting_cache_folder = 'accounting/invoice_files_' . $userId;
                    $path = $accounting_cache_folder . '/' . $unique_file_name;

                    Storage::disk('spaces')->put($path, base64_decode($base64), 'public');
                    $file_path = Storage::disk('spaces')->url($path);

                    $archive = AccountingFileCache::create([
                        'user_id' => $userId,
                        'url' => $file_path,
                        'file_name' => $unique_file_name,
                        'path' => $path,
                        'file_ext' => $extension
                    ]);

                    if ($archive) {
                        return response()->json([
                            'success' => true,
                            'message' => 'Data added successfully',
                            'data' => $archive,
                        ]);
                    }
                }
            }

            throw new \Exception('Data not inserted.');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 302);
        }
    }


    //Upload files
    private function upload_files($id, $userId)
    {
        if (request()->delete_f_lists && is_array(request()->delete_f_lists)) {
            foreach (request()->delete_f_lists as $file_id) {
                DB::table('upcoming_invoice_documents')->where('id', $file_id)->where('upcoming_invoice_id', $id)->delete();
            }
        }

        if (is_array(request()->file)) {
            $files = request()->file;

            foreach ($files as $key => $file) {

                $rand = Str::random(40);
                $extension = $file['type'];
                $base64 = $file['base64'];

                $unique_file_name = $rand . '.' . $extension;
                $accounting_cache_folder = 'accounting/invoice_files_' . $userId;
                $path = $accounting_cache_folder . '/' . $unique_file_name;

                Storage::disk('spaces')->put($path, base64_decode($base64), 'public');
                $file_path = Storage::disk('spaces')->url($path);

                DB::table('upcoming_invoice_documents')->insert([
                    "upcoming_invoice_id" => $id,
                    "file" => $file_path
                ]);
            }
        }

        //Upload cache files
        if (isset(request()->cache_files) && is_array(request()->cache_files)) {
            foreach (request()->cache_files as $cache_file) {
                $file = AccountingFileCache::where(['id' => $cache_file, 'user_id' => $userId])->select('id', 'path', 'file_ext')->first();
                $path = ($file) ? $file->path : null;

                if ($path) {
                    $rand = Str::random(40);
                    $file_name = 'storage/invoice_files/' . $rand . '.' . $file->file_ext;

                    if (Storage::disk('spaces')->exists($path)) {
                        $tmp_file = Storage::disk('spaces')->get($path);
                        Storage::disk('spaces')->put($file_name, $tmp_file, 'public');

                        $file_path = Storage::disk('spaces')->url($file_name);
                        DB::table('upcoming_invoice_documents')->insert([
                            "upcoming_invoice_id" => $id,
                            "file" => $file_path
                        ]);

                        if ($file->delete()) {
                            Storage::disk('spaces')->delete($path);
                        }
                    }
                }
            }
        }
    }

    //accountion invoice rules
    private function rules()
    {
        return [
            'status' => 'required|in:paid,unpaid',
            'delivery_company_id' => 'required|exists:delivery_companies,id',
            'category' => 'nullable|exists:upcomming_invoice_categories,id',
            'invoice_number' => 'required',
            'date' => 'required',
            'due_date' => 'nullable|required_if:status,unpaid',
            'tax' => 'required',
            'amount' => 'required',
            'cache_files' => 'required_without:file|array',
            'cache_files.*' => 'required_with:cache_files|integer|exists:accounting_file_caches,id',

            'file' => 'required_without:cache_files|array',
            'file.*.type' => 'required_with:file|nullable|in:jpeg,jpg,bmp,png,gif,svg,pdf',
            'file.*.base64' => 'required_with:file|nullable|',

            'description' => 'nullable|string|min:5|max:5000',
            'delete_f_lists' => 'nullable|array'
        ];
    }

    private function rulesError()
    {
        return [
            'delivery_company_id.required' => 'Supplier required',
        ];
    }

    //Get user id from request
    private function getUserIdFromHeader(Request $request)
    {
        $header = $request->bearerToken();
        $decoded = JWT::decode($header, "JWT_SECRET", array("HS256"));

        return $decoded->sub->id > 0 ? $decoded->sub->id : 0;
    }

    /**
     * Return all tax rates
     */
    public function taxRates(){
        return DB::table('tax_rates')->get();
    }
}
