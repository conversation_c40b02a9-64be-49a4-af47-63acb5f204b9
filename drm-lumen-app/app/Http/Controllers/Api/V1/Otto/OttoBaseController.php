<?php

namespace App\Http\Controllers\Api\V1\Otto;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\DB;

class OttoBaseController extends Controller
{
//    protected $username = "sandbox_dl10045";
    protected $username = "api_1013069";
//    protected $password = "Testpassword123!@#";
    protected $password = "t6W52ra%APp@";
//    protected $baseUrl = "https://sandbox.api.otto.market";
    protected $baseUrl = "https://api.otto.market";
    protected $client_id = "token-otto-api";

    /**
     * @param string $url
     * @return array
     * @throws GuzzleException
     */
    public function getMethod(string $url)
    {
        $client = new Client();
        $res = $client->request("GET", $url, [
            "headers" => [
                "Authorization" => "Bearer {$this->getAccessToken()}"
            ]
        ]);
        $status = $res->getStatusCode(); // 200
        $body = $res->getBody(); // "{"id": 1420053, "name": "guzzle", ...}"

        return ["status" => $status, "body" => json_decode($body)];
    }

    /**
     * @param string $url
     * @param array $body
     * @return array
     * @throws GuzzleException
     */
    public function postMethod(string $url, array $body)
    {
        $client = new Client();
        $res = $client->request("POST", $url, [
            "form_params" => $body
        ]);

        $status = $res->getStatusCode(); // 200
        $body = $res->getBody(); // "{"id": 1420053, "name": "guzzle", ...}"

        return ["status" => $status, "body" => json_decode($body)];
    }

    /**
     * @param string $url
     * @param array $body
     * @return array
     * @throws GuzzleException
     */
    public function postMethod2(string $url, array $body)
    {
        $client = new Client();
        $res = $client->request("POST", $url, [
            "json" => $body,
            "headers" => [
                "Authorization" => "Bearer {$this->getAccessToken()}"
            ]
        ]);

        $status = $res->getStatusCode();
        $body = $res->getBody();

        return ["status" => $status, "body" => json_decode($body)];
    }

    /**
     * UPDATE $this->accessToken
     * UPDATE $this->refreshToken
     * response body
     * @param string $username
     * @param string $password
     * @return mixed|string
     */
    public function makeAccessToken(string $username, string $password)
    {
        $body = [];
        $body["username"] = $username;
        $body["password"] = $password;
        $body["grant_type"] = "password";
        $body["client_id"] = $this->client_id;
        try {
            $response = $this->postMethod($this->baseUrl . "/v1/token", $body);
            if ($response["status"] === 200) {
                return $this->getTokenAndUpdate($response);
            } else {
                return "invalid";
            }
        } catch (GuzzleException $e) {
            return $e->getMessage();
        }
    }

    /**
     * @return string
     */
    public function getAccessToken(): string
    {
        $sessions = DB::table("otto_sessions")->get()->last();
        if ($sessions) {
            return $sessions->access_token;
        } else {
            return "Invalid";
        }
    }

    /**
     * @return string
     */
    public function getRefreshToken(): string
    {
        $sessions = DB::table("otto_sessions")->get()->last();
        if ($sessions) {
            return $sessions->refresh_token;
        } else {
            return "Invalid";
        }
    }

    /**
     * @return string
     */
    public function getAccessTokenTime(): string
    {
        $sessions = DB::table("otto_sessions")->get()->last();
        if ($sessions) {
            return $sessions->expires_in;
        } else {
            return "Invalid";
        }
    }

    /**
     * @return string
     */
    public function getRefreshTokenTime(): string
    {
        $sessions = DB::table("otto_sessions")->get()->last();
        if ($sessions) {
            return $sessions->refresh_expires_in;
        } else {
            return "Invalid";
        }
    }

    /**
     * UPDATE $this->accessToken
     * UPDATE $this->refreshToken
     * response body
     */
    public function updateAccessToken()
    {
        $body = [];
        $body["refresh_token"] = $this->getRefreshToken();
        $body["grant_type"] = "refresh_token";
        $body["client_id"] = $this->client_id;
        try {
            $response = $this->postMethod($this->baseUrl . "/v1/token", $body);
            if ($response["status"] === 200) {
                return $this->getTokenAndUpdate($response);
            } else {
                return "invalid";
            }
        } catch (GuzzleException $e) {
            return $e->getMessage();
        }
    }

    function getTokenAndUpdate($response)
    {
        try {
            $currentTime = Carbon::now();
            DB::table("otto_sessions")->insert([
                "access_token" => $response["body"]->access_token,
                "expires_in" => date("Y-m-d g:i:s a", ($response["body"]->expires_in + time())),
                "refresh_expires_in" => date("Y-m-d g:i:s a", ($response["body"]->refresh_expires_in + time())),
                "refresh_token" => $response["body"]->refresh_token,
                "token_type" => $response["body"]->token_type,
                "session_state" => $response["body"]->session_state,
                "scope" => $response["body"]->scope,
                "created_at" => $currentTime->toDateTimeString(),
                "updated_at" => $currentTime->toDateTimeString(),
            ]);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}
