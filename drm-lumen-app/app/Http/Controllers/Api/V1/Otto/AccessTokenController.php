<?php

namespace App\Http\Controllers\Api\V1\Otto;

use App\Http\Controllers\Controller;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AccessTokenController extends Controller
{
    protected $baseUrl = "https://api.otto.market";
    protected $client_id = "token-otto-api";

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required',
            'password' => 'required'
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $body = [];
        $body["username"] = $request['username'];
        $body["password"] = $request['password'];
        $body["grant_type"] = "password";
        $body["client_id"] = $this->client_id;

        $client = new Client();
        try {
            $res = $client->request("POST", $this->baseUrl . "/v1/token", [
                "form_params" => $body
            ]);
            return response()->json($res->getBody(), $res->getStatusCode());
        } catch (GuzzleException $e) {
            return response()->json($e->getMessage(), 500);
        }
//            echo $res->getStatusCode();


//            if ($response["status"] === 200) {
//                return $this->getTokenAndUpdate($response);
//            } else {
//                return "invalid";
//            }
//        } catch (GuzzleException $e) {
//            return $e->getMessage();
//        }
//        return response()->json($response, 200);
    }
//    public function __construct()
//    {
////        $accessToken = parent::getAccessToken();
////        if ($accessToken === "Invalid") {
////            parent::makeAccessToken();
////        }
////
////        $getAccessTokenTime = parent::getAccessTokenTime();
////        $getRefreshTokenTime = parent::getRefreshTokenTime();
////
////        if (time() >= strtotime($getAccessTokenTime)) {
////            if (time() < strtotime($getRefreshTokenTime)) {
////                parent::updateAccessToken();
////            } else {
////                parent::makeAccessToken();
////            }
////        }
//    }
//
    public function access_token()
    {
        $accessToken = parent::getAccessToken();
        $refreshToken = parent::getRefreshToken();
        $getAccessTokenTime = parent::getAccessTokenTime();
        $getRefreshTokenTime = parent::getRefreshTokenTime();

        return response()->json([
            "token1" => $accessToken,
            "token2" => $refreshToken,
            "time1" => $getAccessTokenTime,
            "time2" => $getRefreshTokenTime,
        ]);
    }


    public function getCategories(Request $request)
    {
        $query["page"] = $request->query("page") ?? 0;
        $query["limit"] = $request->query("limit") ?? 100;

        $url = $this->baseUrl . "/v1/products/categories?" . http_build_query($query);

        try {
            $res = parent::getMethod($url);
            return response()->json($res, 200);
        } catch (GuzzleException $e) {
            return response()->json([
                "invalid response",
                "message" => $e->getMessage()
            ]);
        }
    }

    public function getOrders(Request $request)
    {
        $query["page"] = $request->query("page") ?? 0;
        $query["limit"] = $request->query("limit") ?? 100;

        $url = $this->baseUrl . "/v2/orders?" . http_build_query($query);

        try {
            $res = parent::getMethod($url);
            return response()->json($res, 200);
        } catch (GuzzleException $e) {
            return response()->json([
                "invalid response"
            ]);
        }
    }

    public function getBrands(Request $request)
    {
        $query["page"] = $request->query("page") ?? 0;
        $query["limit"] = $request->query("limit") ?? 100;

        $url = $this->baseUrl . "/v1/products/brands?" . http_build_query($query);

        try {
            $res = parent::getMethod($url);
            return response()->json($res, 200);
        } catch (GuzzleException $e) {
            return response()->json([
                "invalid response"
            ]);
        }
    }

    public function getMarketplaceStatus(Request $request)
    {
        $query = [];
        $query["page"] = $request->query("page") ?? 0;
        $query["limit"] = $request->query("limit") ?? 100;
        if ($request->query("sku")) $query["sku"] = $request->query("sku");
        if ($request->query("productName")) $query["productName"] = $request->query("productName");
        if ($request->query("category")) $query["category"] = $request->query("category");
        if ($request->query("brand")) $query["brand"] = $request->query("brand");
        if ($request->query("marketPlaceStatus")) $query["marketPlaceStatus"] = $request->query("marketPlaceStatus");

        $url = $this->baseUrl . "/v1/products/marketplace-status?" . http_build_query($query);

        try {
            $res = parent::getMethod($url);
            return response()->json($res, 200);
        } catch (GuzzleException $e) {
            return response()->json([
                "invalid response",
                "message" => $e->getMessage()
            ]);
        }
    }

    public function testOrders()
    {
        $url = $this->baseUrl . "/v2/testorders/generation";

        try {
            $res = parent::postMethod2($url, []);
            return response()->json($res, 200);
        } catch (GuzzleException $e) {
            return response()->json([
                "invalid response",
                "message" => $e->getMessage()
            ]);
        }
    }

    public function createOrUpdateProduct(Request $request)
    {
//        $validator = Validator::make($request->all(), [
//            'productName' => 'required',
//            'sku' => 'required',
//            'ean' => 'required',
//            'deliveryType' => 'required|in:PARCEL,FORWARDER_PREFERREDLOCATION,FORWARDER_CURBSIDE', // PARCEL, FORWARDER_PREFERREDLOCATION and FORWARDER_CURBSIDE
//            'deliveryTime' => 'required|integer|between:1,999', // 1 to 999
//            'maxOrderQuantity' => 'integer',
//        ]);
//        if ($validator->fails()) {
//            return response()->json($validator->errors(), 422);
//        }


        $body['productName'] = 'asdf-asdf';
        $body['sku'] = '3858389911563';
        $body['ean'] = '3858389911563';
        $body['gtin'] = '00012345600013';
        $body['isbn'] = '978-3-16-148410-0';
        $body['upc'] = '042100005264';
        $body['pzn'] = 'PZN-4908802';
        $body['mpn'] = 'H2G2-42';
        $body['moin'] = '93992000200';
        $body['offeringStartDate'] = '2020-10-19T10:00:15.000+02:00';
        $body['releaseDate'] = '2021-10-19T10:00:15.000+02:00';
        $body['maxOrderQuantity'] = 5;
        $body['productDescription'] = [
            'category' => 'Outdoorjacke',
            'brand' => 'Adidas',
            'productLine' => '501',
            'manufacturer' => '3M',
            'productionDate' => '2021-10-19T10:00:15.000+02:00',
            'multiPack' => true,
            'bundle' => false,
            'fscCertified' => true,
            'disposal' => false,
            'productUrl' => 'http://myproduct.somewhere.com/productname/',
            'description' => "<p>Some example words...<b>in bold</b>...some more</p>",
            'bulletPoints' => [
                "My top key information..."
            ],
            'attributes' => [
                [
                    "name" => "Bundweite",
                    "values" => [
                        "34"
                    ],
                    "additional" => true
                ]
            ]
        ];
//        $body['mediaAssets'] = [
//            [
//                "type" => "IMAGE",
//                "location" => "https://droptienda.rocks/userfiles/media/templates.microweber.com/4__71728.1557124344.jpg"
//            ]
//        ];
        $body['delivery']['type'] = 'PARCEL';
        $body['delivery']['deliveryTime'] = 5;
        $body['pricing']['standardPrice']['amount'] = 19.95;
        $body['pricing']['standardPrice']['currency'] = 'EUR';
        $body['pricing']['vat'] = 'FULL';
        $body['pricing']['msrp']['amount'] = 19.95;
        $body['pricing']['msrp']['currency'] = 'EUR';
        $body['pricing']['sale']['salePrice']['amount'] = 19.95;
        $body['pricing']['sale']['salePrice']['currency'] = 'EUR';
        $body['pricing']['sale']['startDate'] = '2021-10-19T10:00:15.000+02:00';
        $body['pricing']['sale']['endDate'] = '2021-10-26T10:00:15.000+02:00';
        $body['pricing']['normPriceInfo']['normAmount'] = 100;
        $body['pricing']['normPriceInfo']['normUnit'] = 'g';
        $body['pricing']['normPriceInfo']['salesAmount'] = 500;
        $body['pricing']['normPriceInfo']['salesUnit'] = 'g';
        $body['logistics']['packingUnitCount'] = 3;
        $body['logistics']['packingUnits'] = [
            ['weight' => 365, 'width' => 600, 'height' => 200, 'length' => 300]
        ];

        $finalBody = [];
        array_push($finalBody, $body);


//        return response()->json($finalBody);

        $url = $this->baseUrl . "/v1/products/";
        try {
            $res = parent::postMethod2($url, $finalBody);
            return response()->json($res, 200);
        } catch (GuzzleException $e) {
            return response()->json([
                "invalid response",
                "message" => $e->getMessage()
            ]);
        }
    }

    public function getSingleProduct(Request $request)
    {
        $url = $this->baseUrl . "/v1/products/" . $request->query("sku");

        try {
            $res = parent::getMethod($url);
            return response()->json($res, 200);
        } catch (GuzzleException $e) {
            return response()->json([
                "invalid response",
                "message" => $e->getMessage()
            ]);
        }
    }
}
