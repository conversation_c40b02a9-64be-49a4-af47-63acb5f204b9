<?php

namespace App\Http\Controllers\Api\V1\Trello;

use App\Http\Controllers\Controller;
use App\Http\Resources\TaskCommentTaskResource;
use App\Models\trello\CommentModel;
use App\Models\trello\TaskModel;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class TrelloTaskCommentController extends Controller
{
    /**
     * ALL TASKS LIST
     *
     * REQUIRED PARAM task_id = INT
     *
     * @param Request $request
     * @return JsonResponse
     * @noinspection PhpUnused
     * @noinspection DuplicatedCode
     */
    public function index(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "task_id" => "required|integer",
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $taskId = $request['task_id'];

        $tasks = TaskModel::select("start_date", "due_date", "name", "cover_image", "position", "id")
            ->where('id', $taskId)
            ->with("comments:task_id,id,comment,files,audio_file")
            ->with("checklists:task_id,id,title,image,status")
            ->first();


        return response()->json(new TaskCommentTaskResource($tasks));
    }

    /**
     * CREATE A NEW COMMENT
     *
     * @param Request $request
     * @return JsonResponse
     * @noinspection DuplicatedCode
     */
    public function store(Request $request): JsonResponse
    {
        $this->validate($request,[
            "task_id" => "required|integer",
            "comment" => "required",
            "files" => "nullable",
            "audio_file" => "nullable",
        ]);

        try {
//            $files = [];
//            if ($request["files"]) {
//                $request['files'] = json_decode($request['files']);
//                foreach ($request['files'] as $key => $file) {
//                    $filename = time();
//                    $x = $this->uploadBase64Image($file, $filename);
//                    $files['file' . ($key + 1)] = $x;
//                }
//            }

            $created = CommentModel::create([
                "task_id" => $request["task_id"],
                "comment" => $request["comment"],
//                "files" => count($files) > 0 ? json_encode($files) : null,
                "cms_user_id" => $request["user_id"],
//                "audio_file" => strlen($audioFiles) > 0 ? $audioFiles : null,
            ]);
            if ($created)
                return response()->json(['message' => 'Comment Successfully Posted']);
            else
                return response()->json(['message' => 'Failed to Create'],400);
        } catch (Exception $exception) {
            return response()->json($exception->getMessage(), 418);
        }
    }

//    /**
//     * SHOW A TASK FROM LIST
//     *
//     * @param $id
//     * @return JsonResponse
//     * @noinspection PhpUnused
//     */
//    public function show($id): JsonResponse
//    {
//        try {
//            $cards = DB::table("drm_task_comments")->where('id', $id)->select("task_id", "comment", "files", "audio_file")->first();
//            return response()->json($cards);
//        } catch (Exception $exception) {
//            return response()->json($exception->getMessage(), 418);
//        }
//    }

    /**
     * UPDATE TASK
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function update(Request $request, $id): JsonResponse
    {
        $this->validate($request,[
            "task_id" => "required|integer",
            "comment" => "required",
            "files" => "nullable",
            "audio_file" => "nullable",
        ]);

        DB::beginTransaction();
        try {
            $updatedData = [];
            $updatedData["task_id"] = $request["task_id"];
            $updatedData["comment"] = $request["comment"];
            if (isset($request["files"])) $updatedData["files"] = $request["files"];
            if (isset($request["audio_file"])) $updatedData["audio_file"] = $request["audio_file"];

            $updated = CommentModel::where("id", $id)->update($updatedData);
            if ($updated) {
                DB::commit();
                return response()->json(['message' => 'Comment Successfully Updated']);
            } else {
                return response()->json(['message' => 'Failed to Update'], 400);
            }
        } catch (Exception $exception) {
            DB::rollBack();
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    /**
     * DELETE COMMENT DETAILS
     *
     * @param $id
     * @return JsonResponse
     * @noinspection DuplicatedCode
     * @noinspection PhpUnused
     */
    public function destroy($id): JsonResponse
    {
        try {
//            $data = CommentModel::where("id", $id)->first();
//            if ($data["files"]) {
//                $json = json_decode($data["files"]);
//                foreach ($json as $key => $val) {
//                    if ($val) {
//                        unlink(realpath("public/img/" . $val));
//                    }
//                }
//            }
//
//            if ($data["audio_file"]) {
//                unlink(realpath("public/img/" . $data["audio_file"]));
//            }

            $comment = CommentModel::find($id);

            if (!empty($comment)) {
                if ($comment->delete())
                    return response()->json(['message' => 'Comment Delete Successful']);
                else
                    return response()->json(['message' => 'Failed to Delete'],400);
            } else {
                return  response()->json(['message' => 'No Data Found']);
            }
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    /** @noinspection DuplicatedCode */
    function uploadBase64Image($base64, $filename, $path = ''): ?string
    {
        $split = explode(",", substr($base64, 5), 2);
        $mime_split = explode(";", $split[0], 2);
        $mime = explode("/", $mime_split[0], 2);
        if (count($mime) == 2) {
            $extension = $mime[1];
            $fullPath = $path . '/' . $filename . "." . $extension;
            Storage::disk('spaces')->put($fullPath, base64_decode($split[1]), 'public');
            return $fullPath;
        }
        return null;
    }
}
