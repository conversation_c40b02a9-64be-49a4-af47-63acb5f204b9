<?php
/**
 * @noinspection PhpUnusedParameterInspection
 * @noinspection PhpUnused
 */

namespace App\Http\Controllers\Api\V1\Trello;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class TrelloFileController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function storeImage(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                "image" => "required",
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors(), 422);
            }

            $filename = time();
            $allowedType = ["jpeg", "png"];
            $x = $this->save_base64_image($request["image"], $filename, $allowedType, "images");
            if ($x == false) {
                return response()->json([
                    "status" => false,
                    "url" => ""
                ], 418);
            }
            return response()->json([
                "status" => true,
                "url" => Storage::disk('spaces')->url($x)
            ], 418);
        } catch (Exception $exception) {
            return response()->json([
                "status" => false,
                "url" => ""
            ], 418);
        }
    }

    /**
     * @param $base64
     * @param $filename
     * @param array $allowedType
     * @param string $dirName
     * @return string
     */
    private function save_base64_image($base64, $filename, array $allowedType, string $dirName): string
    {
        $split = explode(",", substr($base64, 5), 2);
        $mime_split = explode(";", $split[0], 2);
        $mime = explode("/", $mime_split[0], 2);
        if (count($mime) == 2) {
            $extension = $mime[1];
            if ($extension == "jpeg") $extension = "jpg";
            else if ($extension == "png") $extension = "png";
            else return false;
            $fullPath = $filename . "." . $extension;
            Storage::disk("spaces")->put($dirName . "/" . $fullPath, base64_decode($split[1]), "public");
            return $dirName . "/" . $fullPath;
        }
        return false;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function storeAudio(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                "audio" => "required",
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), 422);
            }
            $filename = time();
            $allowedType = ["mp4"];
            $x = $this->save_base64_image($request["audio"], $filename, $allowedType, "audio");
            if ($x == false) {
                return response()->json([
                    "status" => false,
                    "url" => ""
                ], 418);
            }
            return response()->json([
                "status" => true,
                "url" => $x
            ], 418);
        } catch (Exception $exception) {
            return response()->json([
                "status" => false,
                "url" => ""
            ], 418);
        }
    }
}
