<?php /** @noinspection PhpUnused */

namespace App\Http\Controllers\Api\V1\Trello;

use App\Http\Controllers\Controller;
use App\Http\Resources\CardTaskCardResource;
use App\Http\Resources\TaskCommentTaskResource;
use App\Models\trello\CardModel;
use App\Models\trello\ChecklistModel;
use App\Models\trello\CommentModel;
use App\Models\trello\TaskModel;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class TrelloCardController extends Controller
{
    /**
     * CREATE A NEW CARD
     *
     * @param Request $request
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function store(Request $request): JsonResponse
    {
        $this->validate($request, [
            "title" => "required",
            "position" => "required|integer",
            "drm_project_id" => "required|integer",
        ]);

        try {
            DB::table("drm_project_cards")->insertGetId([
                "title" => $request["title"],
                "position" => $request["position"],
                "drm_project_id" => $request["drm_project_id"],
                "created_at" => Carbon::now(),
                "updated_at" => Carbon::now(),
            ]);
            return response()->json(['message' => 'Card Successfully Created']);
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    /**
     * SHOW A CARD FROM LIST
     *
     * @param $id
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function show($id): JsonResponse
    {
        try {
            $cards = DB::table("drm_project_cards")->select('id', "title", "position")->where('id', $id)->first();
            return response()->json($cards);
        } catch (Exception $exception) {
            return response()->json($exception->getMessage(), 418);
        }
    }

    /**
     * UPDATE CARD
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function update(Request $request, $id): JsonResponse
    {
        $this->validate($request, [
            "title" => "required",
            "position" => "required|integer",
            "drm_project_id" => "required|integer",
        ]);

        try {
            $card = CardModel::find($id);
            if (!empty($card)) {
                $card->title = $request->title;
                $card->position = $request->position;
                $card->drm_project_id = $request->drm_project_id;
                $card->save();
                return response()->json(['message' => 'Card Successfully Updated']);
            }
            return response()->json(['message' => 'No Data Found']);
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    /**
     * DELETE A CARD
     *
     * @param $id
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function destroy($id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $card = CardModel::find($id);
            if (!empty($card)) {
                $taskIds = $card->tasks()->pluck('id')->toArray();
                $card->tasks()->delete();
                ChecklistModel::whereIn("task_id", $taskIds)->delete();
                CommentModel::whereIn("task_id", $taskIds)->delete();
                $card->delete();
                DB::commit();
                return response()->json(['message' => 'Card Successfully Deleted']);
            } else {
                return response()->json(['message' => 'No Data Found']);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json(['message' => $exception->getMessage()],400);
        }

    }

    public function cardTask(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "project_id" => "required|integer",
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $projectId = $request['project_id'];

        $cards = CardModel::select("id", "title", "position")
            ->where('drm_project_id', $projectId)
            ->orderBy('position')
            ->with(["tasks" => function ($q) { // :drm_project_card_id,id,name,position
                $q->orderBy('position');
            }])
            ->get();

        return response()->json(CardTaskCardResource::collection($cards));
    }

    /** @noinspection DuplicatedCode */
    public function taskComments(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "task_id" => "required|integer",
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $taskId = $request['task_id'];

        $tasks = TaskModel::select("start_date", "due_date", "name", "cover_image", "position", "id")
            ->where('id', $taskId)
            ->with("comments:task_id,id,comment,files,audio_file")
            ->with("checklists:task_id,id,title,image,status")
            ->first();


        return response()->json(new TaskCommentTaskResource($tasks));
    }

    public function updatePositions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "cards" => "required|array",
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        /*
         * ====================
         * FORMAT OF TASK POSITION UPDATE
         * ====================
         * [
         *      {
         *          "id" : "1",
         *          "position" : "1",
         *      },
         *      {
         *          "id" : "1",
         *          "position" : "1",
         *      },
         *      {
         *          "id" : "1",
         *          "position" : "1",
         *      }
         * ]
         */
        $output = [];
        $request['cards'] = json_decode($request['cards']);
        foreach ($request["cards"] as $task) {
            $r = CardModel::where("id", $task["id"])
                ->update([
                    "position" => $task["position"]
                ]);
            array_push($output, $r);
        }

        return response()->json($output);

    }

//    /**
//     * COPY CARD TO ANOTHER PROJECT
//     *
//     * @param Request $request
//     * @return JsonResponse
//     * @noinspection PhpUnused
//     */
//    public function postCopyCard(Request $request): JsonResponse
//    {
//        try {
//            $data["card_id"] = $card_id = DB::table("drm_project_cards")->insertGetId([
//                "title" => DB::table("drm_project_cards")->find($request["card_id"])->title,
//                "position" => DB::table("drm_project_cards")->where("drm_project_id", $request["project_id"])->count() + 1,
//                "drm_project_id" => $request["project_id"],
//            ]);
//
//            $request["count_card"] = 0;
//            DB::table("drm_project_tasks")->where("drm_project_card_id", $request["card_id"])->orderBy("id")->each(function ($task) use ($card_id) {
//                $cover_image = null;
//
//                if ($task->cover_image && realpath("./storage/list_cover/" . $task->cover_image)) {
//                    $fileArray = explode(".", $task->cover_image);
//                    $ext = end($fileArray);
//                    $cover_image = "tc" . (++$_GET["count_card"]) . time() . "." . $ext;
//                    $data["copyed"][] = copy(realpath("./storage/list_cover/" . $task->cover_image), "./storage/list_cover/" . $cover_image);
//                }
//
//                $task_id = DB::table("drm_project_tasks")->insertGetId([
//                    "drm_project_card_id" => $card_id,
//                    "name" => $task->name,
//                    "title" => $task->title,
//                    "description" => $task->description,
//                    "start_date" => $task->start_date,
//                    "due_date" => $task->due_date,
//                    "priority" => $task->priority,
//                    "status" => $task->status,
//                    "position" => $task->position,
//                    "cover_image" => $cover_image,
//                ]);
//
//                foreach (DB::table("drm_task_checklist")->where("task_id", $task->id)->get() as $checklist) {
//                    $image_name = null;
//                    if ($checklist->image && realpath("./storage/checklist_images/" . $checklist->image)) {
//                        $fileArray2 = explode(".", $checklist->image);
//                        $ext = end($fileArray2);
//                        $image_name = "cl" . (++$_GET["count_card"]) . time() . "." . $ext;
//                        $data["name"][] = $image_name;
//                        $data["copyed"][] = copy(realpath("./storage/checklist_images/" . $checklist->image), "./storage/checklist_images/" . $image_name);
//                    }
//
//                    $userId = $request["user_id"];
//                    $checklist_id = DB::table("drm_task_checklist")->insertGetId([
//                        "task_id" => $task_id,
//                        "title" => $checklist->title,
//                        "status" => $checklist->status,
//                        "image" => $image_name,
//                        "cms_user_id" => $userId,
//                    ]);
//                }
//
//            });
//
//            return response()->json($data);
//        } catch (Exception $exception) {
//            return response()->json($exception->getMessage(), 418);
//        }
//    }
}

