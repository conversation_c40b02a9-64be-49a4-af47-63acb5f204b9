<?php /** @noinspection PhpUnused */

namespace App\Http\Controllers\Api\V1\Trello;

use App\Http\Controllers\Controller;
use App\Models\trello\ChecklistModel;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TrelloChecklistController extends Controller
{
    /**
     * CREATE A NEW CARD
     *
     * @param Request $request
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function store(Request $request): JsonResponse
    {
        $this->validate($request,[
            "title" => "required",
            "status" => "required",
            "task_id" => "required|integer",
        ]);

        try {
            $createdData = [
                "task_id" => $request->task_id,
                "cms_user_id" => $request->user_id,
                "title" => $request->title,
                "status" => $request->status,
            ];

            if ($request->image) {
                $filename = time();
                $path = uploadBase64Image($request->image, $filename);
                $createdData['image'] = $path;
            }
            $created = ChecklistModel::create($createdData);
            if ($created) {
                return response()->json(['message' => 'Checklist Successfully Created']);
            } else {
                return response()->json(['message' => 'Failed to Create']);
            }
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    /**
     * UPDATE CARD
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function update(Request $request, $id): JsonResponse
    {
        $this->validate($request,[
            "title" => "required",
            "status" => "required",
            "task_id" => "required|integer",
            "start_date" => "nullable|date",
            "end_date" => "nullable|date",
        ]);
        DB::beginTransaction();
        try {
            $updatedData = [];
            $updatedData["title"] = $request["title"];
            $updatedData["status"] = $request["status"];
            if (!empty($request["start_date"])) $updatedData["start_date"] = $request["start_date"];
            if (!empty($request["end_date"])) $updatedData["end_date"] = $request["end_date"];
            if (isset($request['image']) && !empty($request['image'])) {
                $filename = time();
                $path = uploadBase64Image($request->image, $filename);
                $updatedData['image'] = $path;
            }

            $updated = ChecklistModel::where("id", $id)->update($updatedData);

            if ($updated) {
                DB::commit();
                return response()->json(['message' => 'Checklist Updated Successful']);
            } else {
                return response()->json(['message' => 'Failed to update'], 304);
            }
        } catch (Exception $exception) {
            DB::rollBack();
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    /**
     * DELETE A CARD
     *
     * @param $id
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function destroy($id): JsonResponse
    {
        try {
            $checklist = ChecklistModel::find($id);
            if (!empty($checklist)) {
                if ($checklist->delete())
                    return response()->json(['message' => 'Checklist Successfully Deleted']);
                else
                    return response()->json(['message' => 'Failed to Delete'],400);
            } else {
                return response()->json(['message' => 'No Data Found']);
            }
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }
}
