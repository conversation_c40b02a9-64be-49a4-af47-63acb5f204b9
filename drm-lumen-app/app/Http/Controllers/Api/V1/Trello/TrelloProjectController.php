<?php /** @noinspection PhpUnused */

namespace App\Http\Controllers\Api\V1\Trello;

use App\Http\Controllers\Controller;
use App\Http\Resources\CardTaskCardResource;
use App\Models\trello\CardModel;
use App\Models\trello\ChecklistModel;
use App\Models\trello\CommentModel;
use App\Models\trello\ProjectModel;
use App\Models\trello\TaskModel;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class TrelloProjectController extends Controller
{
    /**
     * ALL CARDS LIST
     *
     * REQUIRED PARAM drm_project_id = INT
     *
     * @param Request $request
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function index(Request $request)
    {
        try {
            $projects = ProjectModel::select('id', 'title', 'start_date', 'due_date', 'status', 'description')
                ->where('cms_user_id', $request['user_id'])
                ->get();
            if ($projects->isNotEmpty())
                return response()->json(['message' => 'Project List', 'data' => $projects]);
            else
                return response()->json(['message' => 'No Project Found', 'data' => []]);
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage(), 'data' => []], 418);
        }
    }

    public function getProjectActivity($id): JsonResponse
    {
        try {
            $cards = CardModel::select('id', 'title', 'position')
                ->with('tasks:drm_project_card_id,id,name,position')
                ->where('drm_project_id', $id)
                ->get();
            if ($cards->isNotEmpty())
                return response()->json(['message' => 'Card List', 'data' => CardTaskCardResource::collection($cards)]);
            else
                return response()->json(['message' => 'No Card Found']);
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    public function getAllMembers(): JsonResponse
    {
        $members = User::select('id', 'email', 'name')->whereNotNull('email_verified_at')->get();
        if ($members->isNotEmpty()) {
            return response()->json(['message' => 'Members List', 'data' => $members]);
        } else {
            return response()->json(['message' => 'No Data Found'], 400);
        }
    }

    public function store(Request $request)
    {
        $this->validate($request, [
            "title" => "required|string",
            "drm_project_member_ids" => "nullable|string",
            "start_date" => "required|date",
            "due_date" => "required|date",
            "status" => "required|string",
            "description" => "nullable|string",
        ]);

        try {
            $project = new ProjectModel();
            $project->title = $request->title;
            if (!empty($request->drm_customer_id)) {
                $project->drm_customer_id = $request->drm_customer_id;
            }
            $project->start_date = $request->start_date;
            $project->due_date = $request->due_date;
            $project->status = $request->status;
            $project->description = $request->description;
            $project->cms_user_id = $request->user_id;
            $project->save();

            $data = [];
            if (!empty($request->drm_project_member_ids)) {
                $memberIds = json_decode($request->drm_project_member_ids, true);
                foreach ($memberIds as $id) {
                    $data[] = ['drm_project_id' => $project->id, 'cms_user_id' => $id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];
                }
            }

            $data[] = ['drm_project_id' => $project->id, 'cms_user_id' => $request->user_id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];

            DB::table('drm_project_members')->insert($data);

            return response()->json(['message' => 'Project Successfully Created']);
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage(),'data' => $request->all()], 400);
        }
    }

    public function update(Request $request, $id)
    {
        $this->validate($request, [
            "title" => "required|string",
            "drm_project_member_ids" => "nullable|string",
            "start_date" => "required|date",
            "due_date" => "required|date",
            "status" => "required|string",
            "description" => "nullable|string",
        ]);

        DB::beginTransaction();
        try {
            $project = ProjectModel::find($id);
            $project->title = $request->title;
            if (!empty($request->drm_customer_id)) {
                $project->drm_customer_id = $request->drm_customer_id;
            }
            $project->start_date = $request->start_date;
            $project->due_date = $request->due_date;
            $project->status = $request->status;
            $project->description = $request->description;
            $project->cms_user_id = $request->user_id;
            $project->save();

            $data = [];
            if (!empty($request->drm_project_member_ids)) {
                // detach members
                DB::table('drm_project_members')->where('drm_project_id', $project->id)->delete();

                $memberIds = json_decode($request->drm_project_member_ids, true);
                foreach ($memberIds as $id) {
                    $data[] = ['drm_project_id' => $project->id, 'cms_user_id' => $id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];
                }
                $data[] = ['drm_project_id' => $project->id, 'cms_user_id' => $request->user_id, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];

                DB::table('drm_project_members')->insert($data);
            }

            DB::commit();
            return response()->json(['message' => 'Project Successfully Updated']);
        } catch (Exception $exception) {
            DB::rollBack();
            return response()->json(['message' => $exception->getMessage()], 400);
        }
    }

    public function show($id)
    {
        $project = ProjectModel::select('id', 'title', 'start_date', 'due_date', 'status', 'description')
            ->where('id', $id)
            ->first();
        if (!empty($project)) {
            return response()->json(['message' => 'Project Details', 'data' => $project]);
        }
        return response()->json(['message' => 'No Data Found']);
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $project = ProjectModel::find($id);
            if (!empty($project)) {
                DB::table('drm_project_members')->where('drm_project_id', $project->id)->delete();
                $cardIds = $project->cards()->pluck('id')->toArray();
                $project->cards()->delete();
                $taskIds = TaskModel::whereIn('drm_project_card_id', $cardIds)->pluck('id')->toArray();
                TaskModel::whereIn('drm_project_card_id', $cardIds)->delete();
                ChecklistModel::whereIn("task_id", $taskIds)->delete();
                CommentModel::whereIn("task_id", $taskIds)->delete();
                $project->delete();
                DB::commit();
                return response()->json(['message' => 'Project Successfully Deleted']);
            } else {
                return response()->json(['message' => 'No Data Found']);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json(['message' => $exception->getMessage()], 400);
        }
    }
}
