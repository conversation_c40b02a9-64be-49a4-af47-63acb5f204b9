<?php /** @noinspection PhpUnused */

namespace App\Http\Controllers\Api\V1\Trello;

use App\Http\Controllers\Controller;
use App\Http\Resources\TaskResource;
use App\Models\trello\ChecklistModel;
use App\Models\trello\CommentModel;
use App\Models\trello\TaskModel;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class TrelloTaskController extends Controller
{
    public function index($id): JsonResponse
    {
        try {
            $task = TaskModel::select('id','title','description','start_date','due_date','name','cover_image','position')
                ->with('comments:task_id,id,comment,files,audio_file,cms_user_id','checklists:task_id,id,title,image,status,start_date,end_date,position,cms_user_id')
                ->find($id);
            if (!empty($task))
                return response()->json(['message' => 'Task Details', 'data' => new TaskResource($task)]);
            else
                return response()->json(['message' => 'No Data Found']);
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    /**
     * CREATE A NEW TASK
     *
     * @param Request $request
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function store(Request $request): JsonResponse
    {
        $this->validate($request,[
            "name" => "required",
            "card_id" => "required|integer"
        ]);

        try {
            $isCardExists = DB::table("drm_project_cards")->where("id", $request["card_id"])->exists();

            if (!$isCardExists) {
                return response()->json(['message' => 'No Data Found']);
            }

            $position = DB::table("drm_project_tasks")->where("drm_project_card_id", $request["card_id"])->count() + 1;
            $taskId = DB::table("drm_project_tasks")->insertGetId([
                "name" => $request["name"],
                "position" => $position,
                "drm_project_card_id" => $request["card_id"],
                "drm_card_list_id" => '',
                "created_at" => Carbon::now(),
                "updated_at" => Carbon::now(),
            ]);
            if ($taskId)
                return response()->json(['message' => 'Task Successfully Created']);
            else
                return response()->json(['message' => 'Failed To create']);
        } catch (Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    /**
     * SHOW A TASK FROM LIST
     *
     * @param $id
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function show($id): JsonResponse
    {
        try {
            $cards = DB::table("drm_project_tasks")->where("id", $id)->select("id", "title", "description", "start_date", "due_date", "priority", "tags", "attachmens", "status", "name", "cover_image", "position")->first();
            return response()->json($cards);
        } catch (Exception $exception) {
            return response()->json($exception->getMessage(), 418);
        }
    }

    /**
     * UPDATE TASK
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function update(Request $request, $id): JsonResponse
    {
        $this->validate($request,[
            'title' => 'nullable|string',
            'description' => 'nullable|string',
            'start_date' => 'nullable|date',
            'due_date' => 'nullable|date',
            'priority' => 'nullable',
            'status' => 'nullable|string',
            'name' => 'nullable|string',
            'position' => 'nullable|string',
            'cover_image' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $task = TaskModel::find($id);
            if (!empty($task)) {
                $task->title = $request->title;
                $task->description = $request->description;
                $task->start_date = $request->start_date ? Carbon::parse($request->start_date)->format('Y-m-d') : null;
                $task->due_date = $request->due_date ? Carbon::parse($request->due_date)->format('Y-m-d') : null;
                $task->priority = $request->priority;
                $task->status = $request->status;
                $task->name = $request->name;
                $task->position = (int)$request->position;
                if (isset($request->cover_image) && !empty($request->cover_image)) {
                    $filename = time();
                    $path = uploadBase64Image($request->cover_image, $filename);
                    $task->cover_image = $path;
                }
                $task->save();
                DB::commit();
                return response()->json(['message' => 'Task Successfully Updated']);
            } else {
                return response()->json(['message' => 'No Data Found']);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json(['message' => $exception->getMessage()],400);
        }
    }

    /**
     * DELETE A TASK
     *
     * @param $id
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function destroy($id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $task = TaskModel::find($id);
            if (!empty($task)) {
                ChecklistModel::where("task_id", $id)->delete();
                CommentModel::where("task_id", $id)->delete();
                $task->delete();
                DB::commit();
                return response()->json(['message' => 'Task Deleted Successfully']);
            }
            return response()->json(['message' => 'No Data Found']);
        } catch (Exception $exception) {
            DB::rollBack();
            return response()->json(['message' => $exception->getMessage()], 418);
        }
    }

    public function updatePositions(Request $request): JsonResponse
    {
        return response()->json(['success' => true, 'message' => 'request', 'data' => $request->all()]);
        $validator = Validator::make($request->all(), [
            "tasks" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $request['tasks'] = json_decode($request['tasks'], true);
        try {
            $output = [];
            foreach ($request["tasks"] as $task) {
                $r = TaskModel::where("id", $task["id"])
                    ->update([
                        "drm_project_card_id" => $task["cardId"],
                        "position" => $task["position"]
                    ]);
                array_push($output, $r);
            }
            return response()->json($output);
        } catch (Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }
}
