<?php

namespace App\Http\Controllers\Api\V1\Catalog;

use App\Http\Controllers\Controller;
use App\Http\Middleware\Authenticate;
use App\Modules\Catalog\Products;
use App\Services\Product\ProductService;
use Exception;
use Firebase\JWT\JWT;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Enums\Operations;
use Illuminate\Validation\ValidationException;

class ProductsController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //$this->middleware(Authenticate::class);
    }

    public function getUserIdFromHeader(Request $request)
    {
        $header = $request->bearerToken();
        $decoded = JWT::decode($header, "JWT_SECRET", array("HS256"));
        return $decoded->sub->id > 0 ? $decoded->sub->id : 0;
    }
    /**
     * Process the request for getting multiple products.
     *
     * @param Request $request
     * @return JsonResponse
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     */
    public function getProducts(Request $request): JsonResponse
    {
        $user_id = $this->getUserIdFromHeader($request);
        $this->validate($request, $this->get_rules());
        try {
            $data = app(ProductService::class)->getProducts($request->only(array_keys($this->get_rules())));
            return response()->json($data);
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Process the request for getting a single product.
     *
     * @param int $pid
     * @return JsonResponse
     * @throws Exception
     */
    public function getProduct(int $pid)
    {
        if ($pid) {
            try {
                $module = new Products(['product_id' => $pid], Operations::READ);
                $data = $module->getOne();
                return response()->json($data);
            } catch (Exception $e) {
                throw $e;
            }
        } else {
            return response()->json([], 404);
        }
    }

    /**
     * Process the request for creating a new product.
     *
     * @param Request $request
     * @return mixed
     * @throws ValidationException
     * @throws Exception
     */
    public function createProduct(Request $request)
    {
        $this->validate($request, $this->insert_rules());
        try {
            $module = new Products($request->only(array_keys($this->insert_rules())), Operations::CREATE);
            return $module->create();
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Process the request for creating a new product.
     *
     * @param int $ean
     * @param Request $request
     * @return mixed
     * @throws ValidationException
     */
    public function updateProduct(int $ean, Request $request)
    {
        $this->validate($request, $this->update_rules());

        try {
            $module = new Products($request->only(array_keys($this->update_rules())), Operations::UPDATE);
            return $module->update($ean);
        } catch (Exception $e) {
            return response()->json(["message" => "Internal Server Error", "error" => $e->getMessage()], 500);
        }
    }

    /**
     * Process the request for creating a new product.
     *
     * @param int $pid
     * @return mixed
     * @throws Exception
     */
    public function deleteProduct(int $pid)
    {
        if ($pid) {
            try {
                $module = new Products(['product_id' => $pid], Operations::DELETE);
                $data = $module->delete();
                return json_encode($data);
            } catch (Exception $e) {
                throw $e;
            }
        } else {
            return response()->json(['Page Not Found'], 404);
        }
    }

    /**
     * Request validation rules for getting multiple products
     *
     * @return string[]
     */
    private function get_rules()
    {
        return [
            'feed_id' => 'nullable',
            'lang' => 'required|string',
        ];
    }

    /**
     * Request validation rules for updating a product
     *
     * @return string[]
     */
    private function update_rules()
    {
        return [
            'title' => 'nullable|string',
            'description' => 'nullable|string',
            'image' => 'nullable|array',
            'ek_price' => 'nullable|numeric',
            'vk_price' => 'nullable|numeric',
            'stock' => 'nullable|numeric',
            'categories' => 'nullable|array',
            'status' => 'nullable|numeric',
            'gender' => 'nullable|string',
            'item_weight' => 'nullable|numeric',
            'item_color' => 'nullable|string',
            'production_year' => 'nullable|string',
            'materials' => 'nullable|string',
            'brand' => 'nullable|string',
            'tags' => 'nullable|string',
            'item_size' => 'nullable|string',
            'delivery_days' => 'nullable|numeric',
            'country' => 'required|numeric',
        ];
    }

    /**
     * Request validation rules fro inserting a new product
     *
     * @return string[]
     */
    private function insert_rules()
    {
        return [
            'title' => 'required|string',
            'item_number' => 'required|string',
            'ean' => 'required|numeric|digits_between:8,13',
            'description' => 'required|string',
            'image' => 'required|array',
            'ek_price' => 'required|numeric',
            'vk_price' => 'required|numeric',
            'stock' => 'required|numeric',
            'categories' => 'required|array',
            'status' => 'nullable|numeric',
            'gender' => 'nullable|string',
            'suplier' => 'required|numeric',
            'country' => 'required|numeric',
            'item_weight' => 'nullable|numeric',
            'item_color' => 'nullable|string',
            'production_year' => 'nullable|string',
            'materials' => 'nullable|string',
            'brand' => 'nullable|string',
            'tags' => 'nullable|string',
            'item_size' => 'nullable|string',
            'delivery_days' => 'required|numeric',
        ];
    }
}
