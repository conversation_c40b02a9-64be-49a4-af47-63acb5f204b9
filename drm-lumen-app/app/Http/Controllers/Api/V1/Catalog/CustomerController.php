<?php

namespace App\Http\Controllers\Api\V1\Catalog;

use App\Http\Controllers\Controller;
use App\Services\Customer\CustomerService;
use Illuminate\Http\Request;
use Exception;

class CustomerController extends Controller
{
    public function get_rules()
    {
        return [

        ];
    }

    public function getUserCustomers(Request $request)
    {
        try {
            $data = app(CustomerService::class)->getUsersCustomers($request);
            if ($data->isNotEmpty()) {
                return response()->json(['success' => true, 'message' => 'Customer List', 'data' => $data], 200);
            } else {
                throw new Exception('No Data Found!');
            }
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage(), 'data' => collect()]);
        }
    }
}
