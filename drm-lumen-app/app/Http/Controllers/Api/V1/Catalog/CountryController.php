<?php

namespace App\Http\Controllers\Api\V1\Catalog;

use App\Http\Controllers\Controller;
use App\Http\Middleware\Authenticate;
use App\Models\Country;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CountryController extends Controller
{
    /**
     * @var User
     */
    private $user;

    /**
     * @var Country
     */
    private $country;

    /**
     * Create a new controller instance.
     * @param Country $country
     */
    public function __construct(Country $country)
    {
        $this->middleware(Authenticate::class);
        $this->user = Auth::user();
        $this->country = $country;
    }

    /**
     * Process the request for getting multiple products.
     *
     * @return JsonResponse
     */
    public function index()
    {
        $countries = $this->country->get();

        return response()->json($countries, 200);
    }
}
