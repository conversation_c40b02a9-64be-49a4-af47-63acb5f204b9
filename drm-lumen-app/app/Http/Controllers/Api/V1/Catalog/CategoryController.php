<?php

namespace App\Http\Controllers\Api\V1\Catalog;

use App\Http\Controllers\Controller;
use App\Http\Middleware\Authenticate;
use App\Models\Catalog\Category;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use App\Models\Country;

class CategoryController extends Controller
{
    /**
     * @var User
     */
    private $user;

    /**
     * @var Category
     */
    private $category;

    /**
     * Create a new controller instance.
     * @param Category $category
     */
    public function __construct(Category $category)
    {
        $this->middleware(Authenticate::class);
        $this->user = Auth::user();
        $this->category = $category;
    }

    /**
     * Process the request for getting multiple products.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     * //     * @throws ValidationException
     */
    public function index(Request $request)
    {
        // $country = $request->header('country');
        $this->validate($request, $this->get_rules());
        $params = $request->only(array_keys($this->get_rules()));
        $country = $params['country'];
        // if (!empty($country)) {
            $data = $this->category->where(['user_id' => $this->user->id,'country_id' => $country])->get();
            return response()->json($data, 200);
        // } else {
        //     return response()->json(['Country id needed'], 200);
        // }

//        $this->validate($request, $this->get_rules());
//        try {
//            $module = new Categories($request->only(array_keys($this->get_rules())), Operations::READ);
//
//            $data = $module->getAll();
//
//            return response()->json($data, 200);
//        } catch (Exception $e) {
//            throw $e;
//        }
    }

    /**
     * Process the request for getting a single product.
     *
     * @param int $cid
     * @return JsonResponse
     * @throws Exception
     */
    public function show(int $cid=null)
    {
        if($cid)
        {
            $data = $this->category->where('id', $cid)->first();
            return response()->json($data, 200);

    //            try {
    //                $module = new Categories(['category_id' => $cid], Operations::READ);
    //                $data = $module->getOne(); 
    //                return response()->json($data, 200);
    //            } catch (Exception $e) {
    //                throw $e;
    //            }
        }
        else{
            return response()->json(['Category ID is required'], 422);
        }

    }


    public function getByName(Request $request)
    {
        $rules = [
            'category_name' => 'required|string',
            'country'       => 'required|numeric'
        ];
        $this->validate($request,$rules);
        $data = $request->only(array_keys($rules));

        try {
            $country = Country::find($data['country']);
            $lang = $country->language_shortcode;
            $cat_name = 'category_name_'.$lang;
            $category = $this->category->where([$cat_name => $data['category_name'] , 'user_id' => $this->user->id])->first();
            return response()->json([$category, $data]);
        } catch (Exception $e) {
            return response()->json([$e->getMessage()],422);
        }
    }

    /**
     * Process the request for creating a new product.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     */
    public function store(Request $request)
    {
        $this->validate($request, $this->insert_rules());
        $data = $request->only(array_keys($this->insert_rules()));
        try {
            $country = Country::find($data['country_id']);
            $trans_cat = "category_name_".$country->language_shortcode;
            $existing = $this->category->where([$trans_cat => $data[$trans_cat], 'user_id' => $this->user->id])->count();
            
            if(!$existing){
                $category = $this->category->create([
                    'user_id' => $this->user->id,
                    'country_id' => $data['country_id'],
                    'category_name_en' => $data['category_name_en']??"",
                    'category_name_de' => $data['category_name_de']??"",
                    'category_name_fr' => $data['category_name_fr']??"",
                    'category_name_it' => $data['category_name_it']??"",
                    'category_name_nl' => $data['category_name_nl']??"",
                    'category_name_es' => $data['category_name_es']??"",
                    'category_name_sv' => $data['category_name_sv']??"",
                    'category_name_pl' => $data['category_name_pl']??"",
                    'category_name_ru' => $data['category_name_ru']??"",
                ]);
                return response()->json([$category, $data]);
            }
            else{
                return response("Category Already Exists",200);
            }

            // $category->save();

           
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Process the request for creating a new product.
     *
     * @param int $cid
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws Exception
     */
    public function update(int $cid, Request $request)
    {
        $this->validate($request, $this->update_rules());
        $data = $request->only(array_keys($this->update_rules()));
        try {
            $category = $this->category->where('id', $cid)->first();
            $category->country_id = $request->post('country_id');
            $category->category_name_en = $data['category_name_en']??"";
            $category->category_name_de = $data['category_name_de']??"";
            $category->category_name_fr = $data['category_name_fr']??"";
            $category->category_name_it = $data['category_name_it']??"";
            $category->category_name_nl = $data['category_name_nl']??"";
            $category->category_name_es = $data['category_name_es']??"";
            $category->category_name_sv = $data['category_name_sv']??"";
            $category->category_name_pl = $data['category_name_pl']??"";
            $category->category_name_ru = $data['category_name_ru']??"";

            $category->save();
            return response()->json($category, 200);

//            $module = new Products($request->only(array_keys($this->update_rules())), Operations::UPDATE);
//            return $module->update($pid);
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Request validation rules for getting multiple products
     *
     * @return string[]
     */
    private function get_rules()
    {
        return [
            'country' => 'required|numeric',
        ];
    }

    /**
     * Request validation rules for updating a product
     *
     * @return string[]
     */
    private function update_rules()
    {
        return [
            'country_id' => 'required|numeric',
            'category_name_en' => 'string',
            'category_name_de' => 'string',
            'category_name_fr' => 'string',
            'category_name_it' => 'string',
            'category_name_nl' => 'string',
            'category_name_es' => 'string',
            'category_name_sv' => 'string',
            'category_name_pl' => 'string',
            'category_name_ru' => 'string',
        ];
    }

    /**
     * Request validation rules fro inserting a new product
     *
     * @return string[]
     */
    private function insert_rules()
    {
        return [
            'country_id' => 'required|numeric',
            'category_name_en' => 'string',
            'category_name_de' => 'string',
            'category_name_fr' => 'string',
            'category_name_it' => 'string',
            'category_name_nl' => 'string',
            'category_name_es' => 'string',
            'category_name_sv' => 'string',
            'category_name_pl' => 'string',
            'category_name_ru' => 'string',
        ];
    }
}
