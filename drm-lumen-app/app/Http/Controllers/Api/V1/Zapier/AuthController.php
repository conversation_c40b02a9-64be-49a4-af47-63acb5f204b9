<?php

namespace App\Http\Controllers\Api\V1\Zapier;

use App\Http\Controllers\Controller;
use Firebase\JWT\JWT;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     * @noinspection PhpParamsInspection
     * @noinspection PhpUnused
     */
    public function issueToken(Request $request): JsonResponse
    {

        $data = json_decode(request()->getContent(), true);
        Log::channel("uscreen")->info($data);

        Log::channel("uscreen")->info($_REQUEST);

        $validator = Validator::make($request->all(), [
            "username" => "required|max:255",
            "password" => "required|max:255"
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $user = DB::table("oauth_clients")->where("name", $request["username"])
            ->where("secret", $request["password"])->first();
        

        $userInfo = DB::table("cms_users")->where("id", optional($user)->user_id)->first();
        if ($user && $userInfo) {
            $jwtAccess = $this->jwtAccess($user);
            $jwtRefresh = $this->jwtRefresh($user);

            $success["success"] = true;
            $success["name"] = $userInfo->name;
            $success["access_token"] = $jwtAccess;
            $success["renewal_token"] = $jwtRefresh;
            $success["data"] = $request->all();

            return response()->json($success);
        }

        return response()->json([
            "message" => "Unauthorized",
            "data" => $request->all(),
        ], 401);
    }

    /**
     * @param $user
     * @return string
     */
    protected function jwtAccess($user): string
    {
        $payload = [
            "iss" => "lumen-jwt",
            "sub" => $user,
            "iat" => time(),
            "exp" => time() + 60 * 60 * 24 * 15
        ];

        return JWT::encode($payload, "JWT_SECRET");
    }

    /**
     * @param $user
     * @return string
     */
    protected function jwtRefresh($user): string
    {
        if (isset($user->password)) unset($user->password);

        $payload = [
            "iss" => "lumen-jwt",
            "sub" => $user,
            "iat" => time(),
            "exp" => time() + 60 * 60 * 24 * 30
        ];

        return JWT::encode($payload, "JWT_SECRET");
    }
}
