<?php /** @noinspection PhpUnused */

namespace App\Http\Controllers\Api\V1\Zapier;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\DroptiendaMessages;

class TriggerController extends Controller
{
    protected static $_invalidUserMessage = "Invalid User";
    protected static $_invalidUserStatus = 422;
    protected static $_exceptionStatus = 408;

    public function newContact(Request $request): JsonResponse
    {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }


            $data = DB::table('new_customers')
                    ->select('id', 'updated_at')
                    ->where('cc_user_id', $user->id)
                    ->where('created_at','>=', Carbon::today()->subDays(6))
                    ->orderBy("id", "desc")
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                        $string .= $datum->id;
                        $string .= $datum->updated_at;
                    }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'New Contact Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function newOrder(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $data = DB::table("new_orders")
                    ->select('id', 'updated_at')
                    ->where("cms_user_id", $user->id)
                    ->orderBy("id", "desc")
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'New Orders Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
//                    "req" => $request->all(),
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
//                    "req" => $request->all(),
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function newChannel(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $data = \App\Models\Shop::select('id', 'updated_at')
                    ->where("user_id", $user->id)
                    ->orderBy("id", "desc")
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'New Channel Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
//                    "req" => $request->all(),
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
//                    "req" => $request->all(),
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function publishProductOnChannel(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $data = DB::table("channel_products")
                    ->select('id', 'updated_at')
                    ->where("user_id", $user->id)
                    ->orderBy("id", "desc")
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'Publish Product On Channel Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
//                    "req" => $request->all(),
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
//                    "req" => $request->all(),
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function newProductOnDrm(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $data = DB::table("drm_products")
                    ->select('id', 'updated_at')
                    ->where("user_id", $user->id)
                    ->orderBy("id", "desc")
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'New Product On Drm Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
//                    "req" => $request->all(),
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
//                    "req" => $request->all(),
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function newSubuser(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $data = DB::table("cms_users")
                ->select('id', 'updated_at')
                ->where("parent_id", $user->id)
                ->whereDate("created_at", Carbon::now())
                ->orderBy("id", "desc")
                ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'New Subuser On Drm Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
//                    "req" => $request->all(),
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
//                    "req" => $request->all(),
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function newLead(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            return response()->json([
                [
                    "message" => 'New Lead Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => '',
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function updateContact(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $data = DB::table('new_customers')
                    ->select('id', 'updated_at')
                    ->where('cc_user_id', $user->id)
                    ->where('updated_at','>=', Carbon::today()->subDays(6))
                    ->orderBy("id", "desc")
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'Update Contact Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function newImport(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $data = DB::table('drm_imports')
                    ->select('id', 'updated_at')
                    ->where('user_id', $user->id)
                    ->where('import_finished', 1)
                    ->orderBy('id', 'desc')
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);


            return response()->json([
                [
                    "message" => 'New Import Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function newInvoice(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $data = DB::table('drm_invoices')
                    ->select('id', 'updated_at')
                    ->where('user_id', $user->id)
                    ->orderBy('id', 'desc')
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'New Invoice Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function updateProductFeed(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            return response()->json([
                [
                    "message" => 'Update Product Feed Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => '',
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function manualNotification(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                throw new Exception(self::$_invalidUserMessage);
            }

            $manualNotifications = DB::table('manual_notification')
                                    ->select('title','description')
                                    ->where('publish',1)
                                    ->orderBy('id','desc')
                                    ->first();

            if (empty($manualNotifications)) {
                throw new Exception('No Data Found');
            }

            $temp = ['title' => $manualNotifications->title,'description' => strip_tags($manualNotifications->description)];

            $data = [
                    'message' => 'All Published Manual Notifications',
                    "id" => '',
                    'data' => $temp
                ];

            return response()->json([
                $data
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "data" => '',
                    "id" => null
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function allTag(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                throw new Exception(self::$_invalidUserMessage);
            }

            $allTags = DB::table('dropfunnel_tags')->where('user_id',$user->id)->orderBy("id", "desc")->select('tag','id')->get();

            if (empty($allTags)) {
                throw new Exception('No Data Found');
            }

            return response()->json($allTags);
        } catch (Exception $exception) {
            return response()->json(collect([]));
        }
    }


    public function statusUpdateCancel(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $orderStatus = config('drm_order_status.Canceled');

            $data = DB::table("new_orders")
                    ->select('id', 'updated_at')
                    ->where("cms_user_id", $user->id)
                    ->where('status', $orderStatus)
                    ->orderBy("id", "desc")
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'Status Update (Cancel)',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function statusUpdateInkasso(Request $request): JsonResponse
    {

        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $orderStatus = config('drm_order_status.inkasso');

            $data = DB::table("new_orders")
                    ->select('id', 'updated_at')
                    ->where('cms_user_id',$user->id)
                    ->where("status", $orderStatus)
                    ->orderBy("id", "desc")
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'Status Update (Inkasso)',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function statusUpdateShipped(Request $request): JsonResponse
    {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $orderStatus = config('drm_order_status.Shipped');

            $data = DB::table("new_orders")
                    ->select('id', 'updated_at')
                    ->where('cms_user_id',$user->id)
                    ->where("status", $orderStatus)
                    ->orderBy("id", "desc")
                    ->take(5)
                    ->get();

            $string = "";
            foreach ($data as $datum) {
                $string .= $datum->id;
                $string .= $datum->updated_at;
            }
            $string = md5($string);

            return response()->json([
                [
                    "message" => 'Status Update (Shipped)',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }


    public function errorNotification(Request $request): JsonResponse
    {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (empty($user)) {
                return response()->json([
                    [
                        "message" => self::$_invalidUserMessage,
                        "date" => '',
                        "id" => null
                    ]
                ], self::$_invalidUserStatus);
            }

            $data = DB::table('notifications')
                    ->select('id', 'updated_at')
                    ->where('notifiable_id', $user->id)
                    ->where('data->hook','like',"%error%")
                    ->orderBy('id','desc')
                    ->take(5)
                    ->get();
            $string = "";

            foreach ($data as $datum) {
                    $string .= $datum->id;
                    $string .= $datum->updated_at;
                }

            $string = md5($string);

            return response()->json([
                [
                    "message" => 'Error Notification Collection',
                    "date" => Carbon::now()->format("Y-m-d"),
                    "id" => $string,
                ]
            ]);
        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }



    //send droptienda message
    public function sendDroptiendaMessage(Request $request): JsonResponse
    {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required',
        ]);

        try {
            $user = getZapierUser($request);
            if (is_null($user)) throw new Exception(self::$_invalidUserMessage);
            $user_id = $user->id;

            $data = DroptiendaMessages::whereHas('order', function($order) use ($user_id) {
                $order->where('cms_user_id', $user_id);
            })
            ->whereNull('user_id')->whereNull('read_at')
            ->where('chat_preference', 'zend_desk')
            ->select('order_id', 'customer_id', 'message', 'sender', 'id', 'created_at')
            ->orderBy('id', 'asc')
            ->get()
            ->map(function($item) {
                $row = [];
                $row['message'] = $item->message;
                $sender = $item->sender;

                $row['name'] = $sender['name'];
                $row['email'] = $sender['email'];
                $row['order_id'] = $item->order_id;
                $row['id']  = $item->id;
                $row['timestamp'] = $item->created_at;
                return $row;
            })
            ->toArray();

            if(empty($data)) {
                $data = [
                    [
                        'message' => 'Zendesk is now ready',
                        'name' => 'DRM Zendesk test',
                        'email' => '<EMAIL>',
                        'order_id' => 1,
                        'id'  => 1,
                        'timestamp' => '2021-10-11 20:54:50',
                    ]
                ];
            }

            return response()->json($data); //Response data

        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }



     //all projects
     public function allProject(Request $request): JsonResponse
     {
 
         $this->validate($request, [
             'username' => 'required',
             'password' => 'required'
         ]);
 
         try {
             $user = getZapierUser($request);
             if (empty($user)) {
                 throw new Exception(self::$_invalidUserMessage);
             }
 
             $allProjects = DB::table('drm_projects')->orderBy("id", "desc")->select('title','id')->get();
 
             if (empty($allProjects)) {
                 throw new Exception('No Data Found');
             }
 
             return response()->json($allProjects);
         } catch (Exception $exception) {
             return response()->json(collect([]));
         }
     }
       //all cards
       public function allCards(Request $request): JsonResponse
       {
   
           $this->validate($request, [
               'username' => 'required',
               'password' => 'required'
           ]);
   
           try {
               $user = getZapierUser($request);
               if (empty($user)) {
                   throw new Exception(self::$_invalidUserMessage);
               }
   
               $allCards = DB::table('drm_project_cards')->orderBy("id", "desc")->select('title','id')->where('drm_project_id',$request->project)->get();
   
               if (empty($allCards)) {
                   throw new Exception('No Data Found');
               }
   
               return response()->json($allCards);
           } catch (Exception $exception) {
               return response()->json(collect([]));
           }
       }
 


 

}
