<?php

namespace App\Http\Controllers\Api\V1\Zapier;

use App\Http\Controllers\Controller;
use App\Models\DrmProduct;
use App\Models\NewCustomer;
use App\Models\DroptiendaMessages;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ActionController extends Controller
{
    protected static $_invalidUserMessage = "Invalid User";
    protected static $_invalidUserStatus = 422;
    protected static $_exceptionStatus = 408;

    public function updateCustomer(Request $request): JsonResponse
    {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required',
            'name' => 'required',
            'email' => 'required',
            'tag' => 'nullable',
            'phone' => 'nullable',
            'surname' => 'nullable',
            'street' => 'nullable',
            'city' => 'nullable',
            'state' => 'nullable',
            'country' => 'nullable',
            'currency' => 'nullable'
        ]);

        try {
            $user = getZapierUser($request);
            if (is_null($user)) throw new Exception(self::$_invalidUserMessage);
            $data = [];
            if (!empty($request->currency))
                $data['currency'] = strtoupper($request->currency);
            if (!empty($request->country))
                $data['country'] = $request->country;
            if (!empty($request->phone))
                $data['phone'] = $request->phone;
            if (!empty($request->street))
                $data['street'] = $request->street;
            if (!empty($request->city))
                $data['city'] = $request->city;
            if (!empty($request->state))
                $data['state'] = $request->state;

            $customerEmail = is_array($request->email) ? $request->email : explode(',', $request->email);
            $customerName = is_array($request->name) ? $request->name : explode(',', $request->name);
            $customerNameCount = count($customerName);
            $baseDrmUrl = 'https://drm.software/api';

            foreach ($customerEmail as $key => $email) {
                if ($key <= $customerNameCount)
                    $data['full_name'] = $customerName[$key];

                $alreadyExists = DB::connection('mysql::write')
                    ->table('new_customers')
                    ->where([
                        'user_id' => $user->id,
                        'email' => $email
                    ])->exists();

                if($alreadyExists) continue;

                $customer = NewCustomer::updateOrCreate([
                    'user_id' => $user->id,
                    'email' => $email
                ], $data);

                // Create sales pipeline
                $url = $baseDrmUrl.'/zapier/create/customer-lead';
                $data = ['user_id' => $user->id, 'customer_id' => $customer->id];
                postApiCall($data, $url);

                // Create tags
                if (!empty($request->tag)) {

                    $tags = $request->tag;
                    if(is_array($tags))
                    {
                        $tags = implode(',', $tags);
                    }

                    $url = $baseDrmUrl.'/zapier/create/tag';
                    $data = ['tag' => $tags, 'user_id' => $user->id,'customer_id' => $customer->id, 'insert_type' => 6];
                    postApiCall($data,$url);
                }
            }

            return response()->json([
                "success" => true,
                "message" => 'Process Successfully done',
                "full_name" => "",
                "email" => ""
            ]);
        } catch (Exception $exception) {
            return response()->json([
                "success" => false,
                "message" => $exception->getMessage(),
                "full_name" => null,
                "email" => null
            ], self::$_exceptionStatus);
        }
    }


    //Create order
    public function createOrder(Request $request): JsonResponse
    {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required',
            'customer_name' => 'required',
            'customer_email' => 'required',
            'order_date' => 'required',
            'shipping_cost' => 'required',
            'product_name' => 'required',
            'product_price' => 'required',
            'product_quantity' => 'required',
            'currency' => 'required',
            'status' => 'required',
            'company_name' => 'nullable',
            'phone' => 'nullable',
            'state' => 'nullable',
            'city' => 'required',
            'zip_code' => 'required',
            'country' => 'required'
        ]);

        try {
            $user = getZapierUser($request);
            if (is_null($user)) throw new Exception(self::$_invalidUserMessage);
            $subTotal = $request->product_quantity * $request->product_price;
            $data = [
                'insert_type' => 6,
                'shop_id' => null,
                'user_id' => $user->id,
                'total_tax' => 0,
                'discount' => 0,
                'customer_full_name' => $request->customer_name,
                'email' => $request->customer_email,
                'order_date' => $request->order_date,
                'product_name' => $request->product_name,
                'product_price' => $request->product_price,
                'product_quantity' => $request->product_quantity,
                'sub_total' => $subTotal,
                'shipping_cost' => $request->shipping_cost,
                'total' => $subTotal + $request->shipping_cost,
                'currency' => strtoupper($request->currency),
                'status' => $request->status,
                'company_name' => $request->company_name,
                'phone' => $request->phone,
                'state' => $request->state,
                'city' => $request->city,
                'zip_code' => $request->zip_code,
                'country' => $request->country
            ];
            $url = 'https://drm.software/api/zapier/create/order';
            $server = postApiCall($data,$url);
            $json = @json_decode($server);
            if ($json && !$json->success)
                throw new Exception('Failed to Create Order');

            return response()->json([
                'success' => true,
                'message' => 'Successfully Order Placed',
                'request' => null
            ]);
        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
                'request' => $request->all()
            ]);
        }
    }

    public function createProduct(Request $request)
    {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required',
            'product_name' => 'required|string',
            'product_price' => 'required|numeric',
            'product_stock' => 'required|integer',
            'item_number' => 'required|string',
            'ean' => 'required|integer',
            'description' => 'required|string',
            'image' => 'required|array',
            'category' => 'required|string',
        ]);

        try {
            $user = getZapierUser($request);
            if (is_null($user)) throw new Exception(self::$_invalidUserMessage);

            $url = [];
            foreach ($request->image as $image) {
                $url[] = ['src' => $image];
            }
            DrmProduct::updateOrCreate([
                'user_id' => $user->id,
                'ean' => $request->ean,
            ], [
                'item_number' => $request->item_number,
                'image' => $url,
                'category' => $request->category,
                'stock' => $request->product_stock,
                'description' => $request->description,
                'title' => $request->product_name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Successfully Product Created',
                'request' => null
            ]);
        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
                'request' => $request->all()
            ]);
        }
    }

    public function addDocumentToArchive(Request $request)
    {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required',
            'url' => 'required|string'
        ]);
        try {
            $user = getZapierUser($request);
            if (is_null($user)) throw new Exception(self::$_invalidUserMessage);

            $url = $request->url;
            $array = explode("/", $url);
            $fileName = $array[count($array) - 1];
            $now = Carbon::now();
            DB::table('accounting_file_caches')->insert([
                'user_id' => $user->id,
                'url' => $request->url,
                'file_name' => $fileName,
                'file_ext' => 'png',
                'created_at' => $now,
                'updated_at' => $now
            ]);
            return response()->json([
                'success' => true,
                'message' => 'Successfully Stored',
                'request' => null
            ]);
        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
                'request' => $request->all()
            ]);
        }
    }

    public function getProductData(Request $request)
    {
        try {
            $user_id = get_api_user_id($request->userToken, $request->userPassToken);
            $url = 'https://drm.software/api/get-product-import-data';
            $data = ['userId' => $user_id];
            $response = postApiCall($data,$url);
//            return [$request->all(), $request->userToken, $response];
            $json = @json_decode($response);
            if (!$json) {
                throw new Exception('something went wrong');
            }
            return response()->json(['success' => true, 'data' => $json]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'data' => $exception->getMessage()]);
        }
    }

    public function insertTag(Request $request)
    {
        try {
            $user_id = get_api_user_id($request->userToken, $request->userPassToken);
            $url = 'http://165.22.24.129/api/v1/tag-insert';
            $data = ['user_id' => $user_id, 'tag' => $request->tag, 'customer' => $request->customer];
            $response = postApiCall($data,$url);
            $json = @json_decode($response);
            if (!$json) {
                throw new Exception('Something went wrong');
            }
            return response()->json(['success' => true, 'data' => $json->message]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'data' => $exception->getMessage()]);
        }
    }


    //Create droptienda message
    public function createDroptiendaMessage(Request $request): JsonResponse
    {
        $this->validate($request, [
            'username' => 'required',
            'password' => 'required',

            'email' => 'required',
            'message' => 'required',
            'name' => 'required',
            'title' => 'required',
            'order_id' => 'required',
        ]);

        try {
            $user = getZapierUser($request);
            if (is_null($user)) throw new Exception(self::$_invalidUserMessage);

            $user_id = $user->id;

            $email = $request->email;
            $message = $request->message;
            $name = $request->name;
            $order_id = $request->order_id; //Droptienda order id

            $title = $request->title;


            $customer_id = 10;

            $sender = [
                'name' => $user->name,
                'email' => $user->email,
            ];

            $data = DroptiendaMessages::create([
                'order_id' => $order_id,
                'customer_id' => $customer_id,
                'message' => $message,
                'sender' => $sender,
                'user_id' => $user_id,
            ]);

            if($data) {
                return response()->json([
                    'success' => true,
                    'message' => 'Successfully Droptienda message created',
                    'request' => null
                ]);
            }

            throw new Exception('Failed to Create Droptienda message');

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
                'request' => $request->all()
            ]);
        }
    }

    //create new task
    public function createNewTask(Request $request): JsonResponse
    {

        try {
            $user = getZapierUser($request);
            if (is_null($user)) throw new Exception(self::$_invalidUserMessage);
            $user_id = $user->id;

            $data = DB::table('drm_project_tasks')->insert([
                'name'=> $request->title,
                'drm_project_id'=>$request->project,
                'description' => $request->description,
                'drm_project_card_id'=>$request->card,
                'drm_card_list_id'=>$request->card,
                'position'=>1
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Successfully Task Created',
                'request' => null
            ]);

        } catch (Exception $exception) {
            return response()->json([
                [
                    "message" => $exception->getMessage(),
                    "date" => '',
                    "id" => null,
                ]
            ], self::$_exceptionStatus);
        }
    }

}
