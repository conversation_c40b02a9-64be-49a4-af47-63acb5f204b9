<?php

namespace App\Http\Controllers\Api\V1\Sales;

use App\Enums\Operations;
use App\Enums\Product as ProductEnums;
use App\Http\Controllers\Controller;
use App\Http\Resources\CustomerResource;
use App\Http\Resources\NotificationResource;
use App\Http\Resources\OrderResource;
use App\Http\Resources\OrderSyncResource;
use App\Http\Resources\ProductResource;
use App\Models\DrmProduct;
use App\Models\NewCustomer;
use App\Models\NewOrder;
use App\Models\User;
use App\Models\Shop;
use App\Modules\Catalog\Products;
use Carbon\Carbon;
use Exception;
use Firebase\JWT\JWT;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class CustomOrdersController extends Controller
{
    /**
     * @var NewOrder
     */
    private $newOrder;


    /**
     * Create a new controller instance.
     *
     * @param NewOrder $newOrder
     */
    public function __construct(NewOrder $newOrder)
    {
        $this->newOrder = $newOrder;
    }

    /** @noinspection PhpUndefinedMethodInspection */
    public function customIndex(Request $request)
    {
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0)
            return response()->json([], 302);

        $perpage = $request->perpage ?? 20;

        $orders = NewOrder::with(['customer:id,full_name,vat_number', 'user:id,name'])->where('cms_user_id', $userId);

        if ($request->order_id) {
            $orders = $orders->where('id', 'LIKE', '%' . $request->order_id . '%');
        }

        if ($request->customer_name) {
            $orders = $orders->whereHas('customer', function ($query) use ($request) {
                return $query->where('full_name', 'LIKE', '%' . $request->customer_name . '%');
            });
        }

        $orders = $orders->select('id', 'drm_customer_id', 'invoice_number', 'credit_number', 'inv_pattern', 'parcel_id', 'order_id_api', 'cms_user_id', 'order_date', 'status', 'total', 'test_order', 'currency', 'eur_total', 'insert_type')
            ->simplePaginate($perpage); //->where('cms_user_id', $userId)

//        $orders = NewOrder::with(['customer:id,full_name,vat_number', 'user:id,name'])
//            ->select('id', 'drm_customer_id', 'invoice_number', 'credit_number', 'inv_pattern', 'parcel_id', 'order_id_api', 'cms_user_id', 'order_date', 'status', 'total', 'test_order', 'currency', 'eur_total', 'insert_type')
//            ->simplePaginate(20);
        if ($orders) {
            return response()->json(['success' => true, 'data' => OrderResource::collection($orders)], 200);
        }

        return response()->json(['success' => false, 'data' => []], 204);
    }


    //Single order
    public function singleOrder(Request $request, $id)
    {

        try {
            $userId = $this->getUserIdFromHeader($request);
            if ($userId == 0)
                return response()->json([], 302);

            $order = NewOrder::with(['customer:id,full_name,vat_number', 'user:id,name'])->where('cms_user_id', $userId)->where('id', $id)->first();
            if (empty($order)) return response()->json([], 204);

            $countries = DB::table('tax_rates')->pluck('country', 'country_code');
            $setting = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->first();
            $product_list = json_decode($order->cart);

            $country_name = '';
            $data = [];
            $data['status'] = drmHistoryLabel($order->status);
            $customer = [];

            if ($order->customer) {
                $billing = $order->billing;
                if ($billing) {
                    if (drmIsJSON($billing)) {
                        $address_json = json_decode($billing);
                        if ($address_json) {
                            $customer['name'] = $address_json->name;
                            $customer['company'] = $address_json->company;
                            $customer['street'] = $address_json->street;
                            $customer['zip_code'] = $address_json->zip_code;
                            $customer['city'] = $address_json->city;

                            $country_name = ($address_json->country) ? $address_json->country : null;
                            if ($countries && $country_name && (strlen($country_name) < 5)) {
                                foreach ($countries as $iso => $value) {
                                    if (strcasecmp($iso, $country_name) == 0) {
                                        $country_name = $value;
                                        break;
                                    }
                                }
                            }


                        }
                    }
                }
            }

            $customer['country'] = ($country_name) ? $country_name : 'Germany';
            $data['customer'] = $customer;
            $data['is_test_order'] = $order->test_order == 1;


            //TAX CALCULATION PART ==================================
            $cus = $order->customer;
            // Order date before 1 July
            $order_date = strtotime($order->order_date);
            $date = strtotime("07/01/2020");
            $customer_country = $cus->country;
            $cuntry_code = countryCodeTax($customer_country, true);
            $is_german = (is_null($customer_country) || ($customer_country == '') || ($cuntry_code == 4)) ? true : false;

            $january_first = strtotime("01/01/2021");
            $tax_rate = ($is_german && (($order_date >= $date) && ($order_date < $january_first))) ? 16 : countryCodeTax($customer_country);
            $tax_rate = ($order->cms_user_id == 98) ? 21 : $tax_rate;

            //Charge rate vat
            if ($order->insert_type == 4) {
                $vat_number = ($order->customer) ? $order->customer->vat_number : null;
                if ($vat_number) {
                    $tax_rate = 0;
                }
            }

            $tax_rate = (($setting->small_business == 1) && $is_german) ? 0 : $tax_rate;

            $carts = [];
            if ($product_list) {
                foreach ($product_list as $value) {
                    $item = [];


                    $item['name'] = strip_tags(preg_replace_array('/"/', [' '], $value->product_name));
                    $item['qty'] = $value->qty;
                    $item['rate'] = number_format((float)($value->rate), 2, '.', '');
                    $item['tax_rate'] = ($order->insert_type == 6) ? floatval((float)($value->tax)) : $tax_rate;
                    $item['amount'] = number_format((float)($value->amount), 2, '.', '');
                    $item['currency'] = $order->currency;
                    $item['description'] = strip_tags(preg_replace_array('/"/', [' '], $value->description));


                    //Image
                    if ($value->image) {
                        $image_array = explode('|', $value->image);
                        $item['image'] = reset($image_array);
                    }

                    //Carts
                    $carts[] = $item;
                }
            }

            $data['carts'] = $carts;
            return response()->json(['success' => true, 'data' => $data], 200);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'data' => [], 'message' => $e->getMessage()], 204);
        }
    }


    public function syncOrderData(Request $request)
    {
        $userId = $this->getUserIdFromHeader($request);
        $interval_plan = null;
        if ($userId == 0)
            return response()->json(["message" => "Invalid User"], 302);

        $user = User::find($userId, ['id', 'id_cms_privileges']);
        $sync_order = DB::table("new_order_sync_reports")
            ->join("shops", "shops.id", "new_order_sync_reports.shop_id")
            ->orderBy("new_order_sync_reports.end_time", "DESC");
        if (!$user->isSuperAdmin()) {
            $sync_order->where("shops.user_id", $userId);
            $interval_plan = app_user_interval(app_user_plan_id($userId, config('global.interval_app_id')));
        }
        $sync_order = $sync_order->select("shops.shop_name", "shops.id as shopId", "new_order_sync_reports.*", "shops.user_id as user_id")->get();

        return response()->json([
            'interval_plan' => $interval_plan,
            'data' => OrderSyncResource::collection($sync_order),
        ], 200);
    }

    public function retryOrderSync(Request $request)
    {
        try {
            $id = $request->id;
            if (empty($id)) {
                throw new \Exception('Invalid sync ID');
            }

            $failed_sync = DB::table('new_order_sync_reports')->where(['id' => $id, 'status' => 2, 'retry' => 0])->select('shop_id', 'id')->first();

            if ($failed_sync && $failed_sync->shop_id) {
                $curl = curl_init('http://165.22.24.129/sync/' . $failed_sync->shop_id);
                // Returns the data/output as a string instead of raw data
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
                // get stringified data/output. See CURLOPT_RETURNTRANSFER
                $data = curl_exec($curl);
                // get info about the request
                $info = curl_getinfo($curl);
                // close curl resource to free up system resources
                $responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                curl_close($curl);
                if ($responseCode == 401) {
                    throw new \Exception("Something went wrong!");
                }

                if ($responseCode != 200) {
                    throw new \Exception("Process failed!");
                }
                return response()->json(json_decode($data), 200);
            } else {
                throw new \Exception("This process might be running!");
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 401);
        }
    }

    //Customer shop summery data
    public function dashboard(Request $request)
    {

        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0) return response()->json(["message" => "Invalid User"], 302);

        $user = User::find($userId, ['id', 'id_cms_privileges']);

        $allshop = [];
        $dashboard_data = [];
        $dashboard_data = orderStatisticsArrData($user);

        $shops = Shop::has('orders')->where('user_id', $userId)->select('id', 'shop_name')->withCount('orders')->get();
        $manual_order = DB::table('new_orders')->where('cms_user_id', $userId)->where('shop_id', null)->whereNull('new_orders.deleted_at')->count();

        $loop_count = 0;
        if (count($shops) > 0) {
            $shop_data = array();
            foreach ($shops as $key => $value) {
                $shop_data['value'] = $value->orders_count;
                $shop_data['label'] = $value->shop_name;
                $allshop[] = $shop_data;
                $loop_count = $loop_count + 1;
            }
        }

        // Manual Order
        if ($manual_order > 0) {
            $manual_order_data = [];
            $manual_order_data['value'] = $manual_order;
            $manual_order_data['label'] = "Manual Order";
            $allshop[] = $manual_order_data;
        }

        $dashboard_data['shops'] = $allshop;
        return response()->json(['success' => true, 'data' => $dashboard_data], 200);
    }


    public function getProducts(Request $request)
    {
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0)
            return response()->json(["message" => "Invalid User"], 200);

        try {
            $this->validate($request, [
                "feed_id" => "nullable",
                "country" => "required|numeric",
            ]);
        } catch (ValidationException $e) {
            return response()->json($e);
        }

        try {
//            $module = new Products($request->only(array_keys([
//                "feed_id" => "nullable",
//                "country" => "required|numeric",
//            ])), Operations::READ, $userId);
////            dd($module);
//            $data = $module->getAll();
            $data = DB::table('drm_products')->select(ProductEnums::RETURNS)->where('country_id', '=', $request->country)->where('user_id', '=', $userId);

            if (!empty($request->product_id)) {
                $data->where('id', 'LIKE', '%' . $request->product_id . '%');
            }

            if (!empty($request->product_name)) {
                $data->whereRaw("CONVERT(title using 'utf8') LIKE '%" . $request->product_name . "%'");
//                $data->where('title->de', 'LIKE', '%' . $request->product_name . '%');
            }

            $data = $data->orderBy('id', 'desc')->get();
            return response()->json(['success' => true, 'message' => 'Product List', 'lang' => $request->lang, 'data' => ProductResource::collection($data)], 200);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage(), 'lang' => null, 'data' => null]);
        }
    }

    public function getCustomers(Request $request)
    {
        $userId = $this->getUserIdFromHeader($request);
        $perpgae = $request->perpgae ?? 20;
        if ($userId == 0)
            return response()->json(["message" => "Invalid User"], 200);

        $result = DB::table("new_customers")->where("user_id", $userId)->simplePaginate($perpgae);

        return response()->json(CustomerResource::collection($result), 200);
    }

    public function getNotifications(Request $request)
    {
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0)
            return response()->json(["message" => "Invalid User"], 200);

        $result = DB::table("notifications")->where("notifiable_id", $userId)->get();

        return response()->json(NotificationResource::collection($result), 200);
    }


    /** @noinspection PhpUndefinedMethodInspection */
    private function old_dashboard(Request $request) // NOT USED
    {
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0)
            return response()->json(["message" => "Invalid User"], 200);

        $orders = NewOrder::with(["shop", "user", "client"])
            ->where("cms_user_id", "=", $userId);

        $data["best_products"] = Cache::remember("best_8_products_" . $userId, 05.0, function () use ($orders) {
            return $this->bestProducts($orders->get());
        });

        $data["latest_order"] = $orders->orderByRaw("CAST(invoice_number AS SIGNED) desc")->take(10)->get();

        $data["all_orders_count"] = $orders->count();

        $data["orders_value"] = (isset($order_statt->total_amount)) ? $order_statt->total_amount : 0;

        $data["total_shipped_order"] = (isset($order_statt->shipped_order)) ? $order_statt->shipped_order : 0;

        $data["total_customer"] = NewCustomer::where("user_id", $userId)->count();

        $data["total_subscription"] = DB::table("subscriptions")->count();

        $data["t_products"] = $this->drmTotalProduct($userId);

        $shops = Shop::has("orders")->withCount("orders")->where("user_id", $userId)->get();

        $manual_order = DB::table("new_orders")->where("cms_user_id", $userId)->where("shop_id", null)->count();

        $allShops = array();

        //Colors
        $colors = ["green", "red", "yellow", "blue", "orange", "aqua", "gray", "GoldenRod", "Olive", "LawnGreen", "Indigo", "Purple"];

        $loop_count = 0;

        if (count($shops) > 0) {
            $shop_data = array();
            foreach ($shops as $key => $value) {
                $color = $colors[$key];
                $shop_data["color"] = $color;
                $shop_data["value"] = $value->orders_count;
                $shop_data["label"] = $value->shop_name;
                $shop_data["highlight"] = $color;
                $allShops[] = $shop_data;
                $loop_count = $loop_count + 1;
            }
        }

        if ($manual_order > 0) {
            $manual_order_data = array();
            $manual_order_data["color"] = $data["color"][$loop_count];
            $manual_order_data["value"] = $manual_order;
            $manual_order_data["label"] = "Manual Order";
            $manual_order_data["highlight"] = $data["color"][$loop_count];

            array_push($allShops, $manual_order_data);
        }

        if (empty($allShops))
            $allShops = array(
                array("color" => "#F7464A", "value" => "300", "label" => "green", "highlight" => "#FF5A5E"),
                array("color" => "#46BFBD", "value" => "50", "label" => "red", "highlight" => "#5AD3D1"),
                array("color" => "#FDB45C", "value" => "100", "label" => "yellow", "highlight" => "#FFC870"),
                array("color" => "#949FB1", "value" => "40", "label" => "aqua", "highlight" => "#A8B3C5"),
                array("color" => "#4D5360", "value" => "120", "label" => "light-blue", "highlight" => "#616774"),
                array("color" => "#a9a9a9", "value" => "60", "label" => "gray", "highlight" => "#612374")
            );

        $data["allShops"] = $allShops;

        $data["notification"] = DB::table("manual_notification")
            ->select("id", "title", "description")
            ->where("publish", "=", 1)
            ->orderBy("id", "DESC")
            ->get();

        $data["app_promote"] = DB::table("app_stores")
            ->select("id", "monthly_price", "yearly_price", "fixed_price", "icon", "menu_name")
            ->where("promotion", "Active")
            ->get();

        return response()->json($data, 200);
    }

    public function getUserIdFromHeader(Request $request)
    {
        $header = $request->bearerToken();
        $decoded = JWT::decode($header, "JWT_SECRET", array("HS256"));
        return $decoded->sub->id > 0 ? $decoded->sub->id : 0;
    }

    private function bestProducts($data_orders, $item = 8)
    {
        $array_product = [];
        foreach ($data_orders as $order) {
            if (!is_null($order->products)) {
                foreach ($order->products as $product) {
                    $product->order_id = $order->id;
                    $array_product[] = $product;
                }
            }
        }

        $products_collection = new Collection($array_product);
        $best_product = $products_collection->groupBy("product_name");

        $best_product = $best_product->sortBy(function ($value) {
            return $value->sum("qty");
        }, SORT_REGULAR, true)->take($item);

        $best_10_products = [];
        foreach ($best_product as $product) {
            $count = count($product);
            $qty = $product->sum("qty");
            $item = reset($product);
            if (isset($item[0])) {
                $item[0]->count = $count;
                $item[0]->sell_qty = $qty;
                $best_10_products[] = $item[0];
            }
        }
        return $best_10_products;
    }

    public function drmTotalProduct($user_id)
    {
        return Cache::remember("User_" . $user_id, 05.0, function () use ($user_id) {
            $countries = DB::table("countries")->where("is_active", 1)
                ->select("language_shortcode")->where("status", 1)->get();
            $total = 0;
            foreach ($countries as $country) {
                $table_name = "drm_translation_" . $country->language_shortcode;
                $total_products = DB::table("drm_products")
                    ->join($table_name, $table_name . ".product_id", "=", "drm_products.id")
                    ->where("drm_products.user_id", "=", $user_id)
                    ->wherenull("drm_products.deleted_at")
                    ->count();
                $total = $total + $total_products;
            }
            return $total;
        });
    }

    public function updateFirebaseDeviceToken(Request $request)
    {
        $userId = $this->getUserIdFromHeader($request);
        if ($userId == 0)
            return response()->json(["message" => "Invalid User"], 200);

        try {
            $this->validate($request, [
                "token" => "required",
            ]);
        } catch (ValidationException $e) {
            return response()->json($e->response->original);
        }

        try {
            DB::table("user_firebase_device_tokens")->updateOrInsert([
                "user_id" => $userId,
                "token" => $request["token"],
            ], [
                "user_id" => $userId,
                "token" => $request["token"],
                "created_at" => Carbon::now(),
                "updated_at" => Carbon::now(),
            ]);

            return response()->json(["message" => true], 200);
        } catch (Exception $exception) {
            return response()->json(["message" => false], 200);
        }
    }
}
