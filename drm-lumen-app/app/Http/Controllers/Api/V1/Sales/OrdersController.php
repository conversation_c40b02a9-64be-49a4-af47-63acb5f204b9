<?php

namespace App\Http\Controllers\Api\V1\Sales;

use App\Http\Controllers\Controller;
use App\Http\Middleware\Authenticate;
use App\Http\Resources\OrderResource;
use App\Models\NewOrder;
use App\Models\Shop;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class OrdersController extends Controller
{
    /**
     * @var User
     */
    private $user;

    /**
     * @var NewOrder
     */
    private $newOrder;

    /**
     * @var Shop
     */
    private $shop;

    /**
     * Create a new controller instance.
     *
     * @param NewOrder $newOrder
     * @param Shop $shop
     */
    public function __construct(NewOrder $newOrder, Shop $shop)
    {
        $this->middleware(Authenticate::class);
        $this->user = Auth::user();
        $this->newOrder = $newOrder;
        $this->shop = $shop;
    }

    /**
     * Get All Ordered History.
     *
     * @return JsonResponse
     */
    public function index()
    {
//        dd($this->user->isSuperAdmin());
        if ($this->user->isSuperAdmin()) {
            $result = $this->newOrder->paginate(15);
        } else {
            $result = $this->newOrder->where('cms_user_id', $this->user->id)->get()->paginate(15);
        }

//        $result->map(function ($da) {
//            if ($da['shop_id']) {
//                $da['shop_id'] = $this->shop->all()->where('id', $da['shop_id'])->first();
//            }
//        });

        return response()->json(OrderResource::collection($result), 200);
    }

    /**
     * Get All Ordered History.
     *
     * @param int id
     *
     * @return JsonResponse
     */
    public function show(int $id)
    {
        $result = NewOrder::all()->where('cms_user_id', $this->user->id)->where('id', $id)->first();

//        dd($result);
//        $result->map(function ($da) {
//            if ($da['shop_id']) {
//                $da['shop_id'] = $this->shop->all()->where('id', $da['shop_id'])->first();
//            }
//        });

        return response()->json($result ? new OrderResource($result) : '{}', 200);
    }
}
