<?php

namespace App\Http\Controllers\Api\V1\Sales;

use App\Http\Controllers\Controller;
use App\Http\Middleware\Authenticate;
use App\Http\Resources\CustomerResource;
use App\Models\NewCustomer;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CustomersController extends Controller
{
    /**
     * @var User
     */
    private $user;

    /**
     * @var NewCustomer
     */
    private $newCustomer;

    /**
     * Create a new controller instance.
     *
     * @param NewCustomer $newCustomer
     */
    public function __construct(NewCustomer $newCustomer)
    {
        $this->middleware(Authenticate::class);
        $this->user = Auth::user();
        $this->newCustomer = $newCustomer;
    }

    public function index()
    {
        if ($this->user->isSuperAdmin()) {
            $result = $this->newCustomer->all();

            $result = CustomerResource::collection($result);
        } else {
            $result = ['Unauthorized'];
        }

        return response()->json($result, $this->user->isSuperAdmin() ? 200 : 401);
    }

    /**
     * Get Single Customer Info.
     *
     * @param int id
     *
     * @return JsonResponse
     */
    public function show(int $id)
    {
        $result = $this->newCustomer->all()->where('id', $id)->first();
        return response()->json(new CustomerResource($result), 200);
    }
}
