<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Middleware\Authenticate;
use App\Http\Resources\UserResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ProfileController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //$this->middleware(Authenticate::class);
    }

    /**
     * Get Logged In user profile.
     *
     * @OA\Get(
     *     path="/sample/{category}/things",
     *     operationId="/sample/category/things",
     *     tags={"yourtag"},
     *     @OA\Parameter(
     *         name="category",
     *         in="path",
     *         description="The category parameter in path",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="criteria",
     *         in="query",
     *         description="Some optional other parameter",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Returns some sample category things",
     *         @OA\JsonContent()
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Error: Bad request. When required parameters were not supplied.",
     *     ),
     * )
     *
     * @return JsonResponse
     */
    public function profile()
    {
        $user = Auth::user();
        return response()->json(new UserResource($user), 200);
    }
    
    
        // Download protected shops file
    public function downloadProtectedShopDocument($shopId, $type, $contentFormat)
    {
        $partnerId = 'droptienda';
        $url = 'https://api.protectedshops.de/v2.0/de/partners/'.$partnerId.'/shops/'.$shopId.'/documents/'.$type.'/contentformat/'.$contentFormat.'/format/json';

        $header = [
            "Accept: application/json",
            "Content-Type: application/json",
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);


        if(isset($api_response['error'])) abort(404);

        $content = $api_response['content'];
        $contentType = $api_response['contentType'];


        if($api_response['contentEncoding'] === 'base64')
        {
            $content = base64_decode($api_response['content'], true);
            if(empty($content)) abort(404);
        }

        $ext = strtolower($contentFormat);
        $disposition = 'inline';

        if(in_array($ext, ['html', 'html-snippet']))
        {
            $ext = 'html';
        } elseif ($ext === 'text') {
            $ext = 'txt';
            $api_response['contentType'] = 'text/plain';
        }

        $filename = $api_response['title'].'.'.$ext;
        $response = response($content, 200);
        $response->header('Content-Type', $api_response['contentType']);
        $response->header('Content-Disposition', $disposition.'; filename="'.$filename.'"');

        return $response;
    }
}
