<?php

namespace App\Http\Controllers\Api\V1\Auth;

use App\Enums\Channel;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Models\Shop;
use Firebase\JWT\JWT;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class LoginController extends Controller
{
    public function issueToken(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "grant_type" => "required|max:10",
            "client_id" => "required|max:255",
            "client_secret" => "required|max:255",
            "username" => "required|max:255",
            "password" => "required|max:255"
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        if ($request["grant_type"] !== "password") {
            return response()->json([
                "grant_type" => [
                    "The grant type field must be enum type."
                ],
            ], 422);
        }

        $user = DB::table("cms_users")->where("email", $request["username"])->get()->first();

        if ($user) {
            if (Hash::check($request["password"], $user->password)) {
                $user = new UserResource($user);

                $jwtAccess = $this->jwtAccess($user);
                $jwtRefresh = $this->jwtRefresh($user);

                $success = [];
                $success["data"] = [];
                $success["data"]["access_token"] = $jwtAccess;
                $success["data"]["email"] = $request["username"];
                $success["data"]["id"] = $user->id;
                $success["data"]["renewal_token"] = $jwtRefresh;

                //Save device token
                try{
                    if(isset($request["device_token"]) && !empty($request["device_token"]))
                    {
                        $device_token = $request["device_token"];
                        $user_id = $user->id;

                        $device_token_id = DB::table('user_firebase_device_tokens')->where('user_id', $user_id)->where('token', $device_token)->value('id');
                        if(empty($device_token_id))
                        {
                            DB::table('user_firebase_device_tokens')->insert([
                                'user_id' => $user_id,
                                'token' => $device_token,
                                'created_at' => \Carbon\Carbon::now(),
                                'updated_at' => \Carbon\Carbon::now()
                            ]);
                        }else{
                            DB::table('user_firebase_device_tokens')->where('id', $device_token_id)->update(['updated_at' => \Carbon\Carbon::now()]);
                        }
                    }
                }catch(\Exception $e){}

                return response()->json($success, 200);
            }

            return response()->json(["message" => "Invalid password!", "status" => false], 401);
        }

        return response()->json(["message" => "Invalid username!", "status" => false], 401);
    }

    protected function jwtAccess($user): string
    {
        $payload = [
            "iss" => "lumen-jwt",
            "sub" => $user,
            "iat" => time(),
            "exp" => time() + 60 * 60 * 24 * 15
        ];

        return JWT::encode($payload, "JWT_SECRET");
    }

    protected function jwtRefresh($user): string
    {
        if (isset($user->password)) unset($user->password);

        $payload = [
            "iss" => "lumen-jwt",
            "sub" => $user,
            "iat" => time(),
            "exp" => time() + 60 * 60 * 24 * 30
        ];

        return JWT::encode($payload, "JWT_SECRET");
    }

    public function droptiendaActivation(Request $request): JsonResponse
    {
       try {
        $errors = [];
        $email = $request['email'];
        $password = $request['password'];
        $shop_url = $request['url'];
        $response = [];
        if($request['is_register']=='dt') {

            $name = $request['name'];

            $values = array(
                'name' => $name,
                'email'=> $email,
                'password'=> $password
            );
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL,"https://drm.software/api/auth/sign-up");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $values);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            //        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
            $server = curl_exec($ch);
            curl_close($ch);
            $successmsg = response()->json(json_decode($server));
        } else {
            $successmsg='Connect with DRM Droptienda channel successfully!';
            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'url' => 'required',
                'email' => ['required', Rule::exists('cms_users')->where(function ($query) use ($request) {
                    return $query->where(['status' => 'Active']);
                })],
            ]);

            if ($validator->fails()) {
                $errors = $validator->errors();
                throw new \Exception('Your DRM credentials is Invalid! Please try with correct data.');
            }
        }


            $user = DB::table("cms_users")->where("email", $email)->get()->first();
            if ($user) {
                if (Hash::check($request["password"], $user->password)) {
                    $user = new UserResource($user);

                    $jwtAccess = $this->jwtAccess($user);
                    $jwtRefresh = $this->jwtRefresh($user);

                    $response["data"]["access_token"] = $jwtAccess;
                    $response["data"]["email"] = $request["username"];
                    $response["data"]["id"] = $user->id;
                    $response["data"]["renewal_token"] = $jwtRefresh;
                }
            }

            if (empty($response)) {
                //dd($user);
                throw new \Exception('Token generate failed!');
            }

            $data = $response['data'];
            $token = $data['access_token'];
            $id = $data['id'];

            //$user = DB::table('cms_users')->where('id', $id)->where('status', 'Active')->select('id', 'name')->first();
           // if (empty($user)) {
            //    throw new \Exception('Your DRM account is Invalid or Blocked!');
           // }

            $user_token = Str::random(40);
            $userpasToken = (string)Str::uuid();
            $user_token = 'drm' . $user_token;
            $userpasToken = 'drm' . $userpasToken;

            $shop_name = 'Droptienda - ' . $user->name;

            $shop = Shop::where('user_id', $id)->where('channel', 10)->first();
            if ($shop) {
                $shop->update(['username' => $user_token, 'password' => $userpasToken]);
            } else {
                $shop = Shop::updateOrCreate([
                    'user_id' => $id,
                    'channel' => Channel::DROPTIENDA,
                ], [
                    'shop_name' => $shop_name,
                    'lang' => 'de',
                    'url' => $shop_url,
                    'username' => $user_token,
                    'password' => $userpasToken,
                    'category' => 8,
                ]);

                if($shop){
                    $this->addDTTagToUser($shop->user_id);
                }

            }

            // CREATE DROPTIENDA SERVER ENTRY
            if($shop && $shop->id && !(DB::table('server_statuses')->where('shop_id', $shop->id)->exists()) )
            {
                DB::table('server_statuses')->insert([
                    'shop_id' => $shop->id,
                    'server_status' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

// Create DT charge invoice
try {
	$curl = curl_init();
	curl_setopt_array($curl, array(
	  CURLOPT_URL => 'https://drm.software/api/droptienda/create-shop-sign-group/'.$shop->id,
	  CURLOPT_RETURNTRANSFER => true,
	  CURLOPT_ENCODING => '',
	  CURLOPT_MAXREDIRS => 10,
	  CURLOPT_TIMEOUT => 0,
	  CURLOPT_FOLLOWLOCATION => true,
	  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
	  CURLOPT_CUSTOMREQUEST => 'POST',
	  CURLOPT_HTTPHEADER => array(
	    'token: Zd6tQv8Cvd'
	  ),
	));

	$response = curl_exec($curl);
	curl_close($curl);
} catch(\Exception $e){}




            }

            return response()->json([
                'success' => true,
                'message' => $successmsg,
                'userToken' => $user_token,
                'userPassToken' => $userpasToken,
                'userName' => $user->name,
            ]);

            throw new \Exception('Sorry, Connect with DRM Droptienda channel failed! Please try again!');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'errors' => $errors,
            ]);
        }
    }


    public function droptiendaNewActivation(Request $request): JsonResponse
    {
       try {
        $errors = [];
        $email = $request['email'];
        $password = $request['password'];
        $shop_url = $request['url'];
        $version = $request['version'] ?? null;
        $is_automatic = $request['is_register'] ?? '';
        $response = [];
        $status = true;
        $user = DB::table("cms_users")->where("email", $email)->get()->first();
        // return response()->json($user);
        if(isset($user) && !empty($user)) {
            $successmsg='Connect with DRM Droptienda channel successfully!';
            $validator = Validator::make($request->all(), [
                'password' => 'required',
                'url' => 'required',
                'email' =>'required',
            ]);

            if ($validator->fails()) {
                $errors = $validator->errors();
                $status = false;
                throw new \Exception('Your DRM credentials is Invalid! Please try with correct data.');
            }
        }else{

            $name = strtok($email,  '@');
            $name = preg_replace('/[^A-Za-z0-9\-]/', ' ', $name);

            $values = array(
                'name' => $name,
                'email'=> $email,
                'password'=> $password
            );
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL,"https://drm.software/api/auth/sign-up");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $values);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            //        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
            $server = curl_exec($ch);
            curl_close($ch);
            $successmsg = response()->json(json_decode($server));
            $user = DB::table("cms_users")->where("email", $email)->get()->first();

        }


        if ($user) {
                if ((Hash::check($request["password"], $user->password)) or (isset($is_automatic) and $is_automatic == 'automatic_connection')) {
                    $user = new UserResource($user);

                    $jwtAccess = $this->jwtAccess($user);
                    $jwtRefresh = $this->jwtRefresh($user);

                    $response["data"]["access_token"] = $jwtAccess;
                    $response["data"]["email"] = $request["username"];
                    $response["data"]["id"] = $user->id;
                    $response["data"]["renewal_token"] = $jwtRefresh;
                }
            }

            if (empty($response)) {
                throw new \Exception('Token generate failed!');
            }

            $data = $response['data'];
            $token = $data['access_token'];
            $id = $data['id'];

            //$user = DB::table('cms_users')->where('id', $id)->where('status', 'Active')->select('id', 'name')->first();
           // if (empty($user)) {
            //    throw new \Exception('Your DRM account is Invalid or Blocked!');
           // }

            $user_token = Str::random(40);
            $userpasToken = (string)Str::uuid();
            $user_token = 'drm' . $user_token;
            $userpasToken = 'drm' . $userpasToken;

            $shop_name = 'Droptienda - ' . $user->name;

            $shop = Shop::where('user_id', $id)->where('channel', 10)->first();
            if ($shop) {
                $shop->update(['username' => $user_token, 'url' => $shop_url, 'password' => $userpasToken, 'shop_version' => $version]);
            } else {
                $shop = Shop::updateOrCreate([
                    'user_id' => $id,
                    'channel' => Channel::DROPTIENDA,
                ], [
                    'shop_name' => $shop_name,
                    'lang' => 'de',
                    'url' => $shop_url,
                    'username' => $user_token,
                    'password' => $userpasToken,
                    'category' => 8,
                    'shop_version' => $version
                ]);

                if($shop){
                    $this->addDTTagToUser($shop->user_id);
                }

            }
            return response()->json([
                'success' => true,
                'message' => $successmsg,
                'userToken' => $user_token,
                'userPassToken' => $userpasToken,
                'userName' => $user->name,
            ]);

            throw new \Exception('Sorry, Connect with DRM Droptienda channel failed! Please try again!');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'errors' => $errors,
                'status' => $status,
            ]);
        }
    }

    public function userRegisterNewShop(Request $request)
    {
        Log::info(json_encode($request));
        $request = $request->json()->all();
        try {
            $errors = [];
            $email = $request['customer']['email'];
            $password = $request['customer']['password'];
            $shop_url = $request['shop_url'];
            $response = [];
            $status = true;
            $name = $request['customer']['name'] ?? strtok($email,  '@');
            $name = preg_replace('/[^A-Za-z0-9\-]/', ' ', $name);
            $values = array(
                'name' => $name,
                'email'=> $email,
                'password'=> $password
            );

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL,config('app.drm_url')."/api/auth/sign-up");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $values);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $server = curl_exec($ch);
            curl_close($ch);
            $successmsg = json_decode($server);
            if($successmsg->success){
                $user = DB::table("cms_users")->where("email", $email)->get()->first();

                if ($user) {
                    if (Hash::check($request["password"], $user->password)) {
                        $user = new UserResource($user);

                        $jwtAccess = $this->jwtAccess($user);
                        $jwtRefresh = $this->jwtRefresh($user);

                        $response["data"]["access_token"] = $jwtAccess;
                        $response["data"]["email"] = $request["username"];
                        $response["data"]["id"] = $user->id;
                        $response["data"]["renewal_token"] = $jwtRefresh;
                    }
                }

                if (empty($response)) {
                    throw new \Exception('Token generate failed!');
                }

                $data = $response['data'];
                $token = $data['access_token'];
                $id = $data['id'];

                $user_token = Str::random(40);
                $userpasToken = (string)Str::uuid();
                $user_token = 'drm' . $user_token;
                $userpasToken = 'drm' . $userpasToken;

                $shop_name = 'Droptienda - ' . $user->name;


                $shop = Shop::updateOrCreate([
                    'user_id' => $id,
                    'channel' => Channel::DROPTIENDA,
                ], [
                    'shop_name' => $shop_name,
                    'lang' => 'de',
                    'url' => $shop_url,
                    'username' => $user_token,
                    'password' => $userpasToken,
                    'category' => 8,
                ]);

                if($shop){
                    $this->addDTTagToUser($shop->user_id);
                }

                $dtTokenSet = array(
                    'userToken' => $user_token,
                    'userPassToken'=> $userpasToken,
                    'userName'=> $name
                );

                $curl = curl_init();

                curl_setopt_array($curl, array(
                CURLOPT_URL => trim( $shop_url, '/').'/api/config_set_drm',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $dtTokenSet,
                ));

                $response = curl_exec($curl);

                curl_close($curl);

                return response()->json([
                    'success' => true,
                    'message' => $successmsg,
                    'userToken' => $user_token,
                    'userPassToken' => $userpasToken,
                    'userName' => $user->name,
                ]);
            }else{
                return response()->json([
                    'success' => false,
                    'message' => $successmsg,
                ]);
            }

            throw new \Exception('Sorry, Connect with DRM Droptienda channel failed! Please try again!');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'errors' => $errors,
                'status' => $status,
            ]);
        }
    }

    public function createShop(Request $request){
        try{
            $email = $request['email'];
            $password = $request['password'];
            $shop_url = $request['url'];
            $response = [];
            $status = true;
            $name = $request['name'] ?? strtok($email,  '@');
            $name = preg_replace('/[^A-Za-z0-9\-]/', ' ', $name);

            $errors = [];
            $values = array(
                'name' => $name,
                'email'=> $email,
                'password'=> $password
            );

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL,config('app.drm_url')."/api/auth/sign-up");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $values);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $server = curl_exec($ch);
            curl_close($ch);
            $successmsg = json_decode($server);
            if($successmsg->success){
                $user = DB::table("cms_users")->where("email", $email)->get()->first();

                if ($user) {
                    if (Hash::check($request["password"], $user->password)) {
                        $user = new UserResource($user);

                        $jwtAccess = $this->jwtAccess($user);
                        $jwtRefresh = $this->jwtRefresh($user);

                        $response["data"]["access_token"] = $jwtAccess;
                        $response["data"]["email"] = $request["username"];
                        $response["data"]["id"] = $user->id;
                        $response["data"]["renewal_token"] = $jwtRefresh;
                    }
                }

                if (empty($response)) {
                    throw new \Exception('Token generate failed!');
                }

                $data = $response['data'];
                $token = $data['access_token'];
                $id = $data['id'];

                $user_token = Str::random(40);
                $userpasToken = (string)Str::uuid();
                $user_token = 'drm' . $user_token;
                $userpasToken = 'drm' . $userpasToken;

                $shop_name = 'Droptienda - ' . $user->name;


                $shop = Shop::updateOrCreate([
                    'user_id' => $id,
                    'channel' => Channel::DROPTIENDA,
                ], [
                    'shop_name' => $shop_name,
                    'lang' => 'de',
                    'url' => $shop_url,
                    'username' => $user_token,
                    'password' => $userpasToken,
                    'category' => 8,

                ]);

                if($shop){
                    $this->addDTTagToUser($shop->user_id);
                }
            }else{
                return response()->json([
                    'success' => false,
                    'message' => $successmsg,
                ]);
            }
            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://gitlab.droptienda.eu/api/v4/projects/18/trigger/pipeline',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array(
                    'token' => '7147ba99d3d5d8003d25cc55c7635d',
                    'ref' => 'main',
                    'variables[cust_name]' => $name,
                    'variables[cust_email]' => $email,
                    'variables[shop_name]' => $name.' shop',
                    'variables[userToken]' => $user_token,
                    'variables[userPassToken]' => $userpasToken,
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            return response()->json([
                'success' => true,
                'data' => $response,
                'status' => true,
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'status' => false,
            ]);
        }
    }

    public function uidUrlUpdate(Request $request){
        $userToken = $request['userToken'];
        $userPassToken = $request['userPassToken'];
        $shop_url = $request['url'];

        Shop::where('password' , $userPassToken)
        ->where('username' , $userToken)
        ->update([
            'url' => $shop_url
        ]);
        $shop = \App\Models\Shop::where('password' , $userPassToken)
        ->where('username' , $userToken)->first();
        $user = DB::table("cms_users")->where("id", $shop->user_id)->first();
        return response()->json([
            'success' => true,
            'name' => $user->name??''
        ]);
    }

    //Add DT Tag
    private function addDTTagToUser($user_id){
        try{
            $curl = curl_init('http://165.22.24.129/api/droptienda-tag-add?user_id='.$user_id);
            // Returns the data/output as a string instead of raw data
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
            // get stringified data/output. See CURLOPT_RETURNTRANSFER
            curl_exec($curl);
            curl_close($curl);
        }catch(\Exception $e){}
    }
}
