<?php

namespace App\Http\Controllers\Api\V1\Auth;

use App\Http\Controllers\Controller;
use App\Models\UserFirebaseDeviceToken;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class FirebaseDeviceTokenController extends Controller
{
    public function update(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            "token" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        try {
            $update = UserFirebaseDeviceToken::updateOrCreate(
                ["user_id" => $request["user_id"]],
                ["user_id" => $request["user_id"], "token" => $request["token"]],
            );

            return response()->json([
                "message" => $update,
                "status" => true,
            ]);
        } catch (Exception $exception) {
            return response()->json([
                "message" => "",
                "status" => false,
            ], 408);
        }
    }

    public function notify(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "user_id" => "required",
            "body" => "required",
            "title" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        try {
            $firebase = UserFirebaseDeviceToken::where("user_id", $request["user_id"])->orderBy('id','desc')->first();

            $response = Http::withHeaders([
                "Authorization" => "key=" . env("FIREBASE_SECRET_KEY"),
                "Content-Type" => "application/json",
            ])->post("https://fcm.googleapis.com/fcm/send", [
                "to" => $firebase["token"],
                "data" => [
                    "body" => $request["body"],
                    "title" => $request["title"],
                ],
            ]);
            $responseJson = $response->json();
            if ($response->status() === 200) {
                return response()->json([
                    "message" => $responseJson,
                    "status" => true,
                ]);
            } else {
                return response()->json([
                    "message" => $responseJson,
                    "status" => false,
                ]);
            }
        } catch (Exception $exception) {
            return response()->json([
                "message" => $exception->getMessage(),
                "status" => false,
            ], 408);
        }
    }
}
