<?php

namespace App\Http\Controllers;

use <PERSON><PERSON>\Lumen\Routing\Controller as BaseController;
use Symfony\Component\Console\Output\ConsoleOutput;

class Controller extends BaseController
{
    /**
     * @OA\Info(
     *   title="DRM Lumen API",
     *   version="1.0",
     *   @OA\Contact(
     *     email="<EMAIL>",
     *     name="Support Team"
     *   )
     * )
     */

    public function consoleOutput()
    {
        $out = new ConsoleOutput();
        foreach (func_get_args() as $arg) {
            $out->writeln($arg);
        }
    }
}
