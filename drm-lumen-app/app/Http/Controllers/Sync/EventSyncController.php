<?php

namespace App\Http\Controllers\Sync;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Schema;

class EventSyncController extends Controller
{
    protected $_drmCategoryUpdate = "http://localhost/drm_v7/api/postCategoryFromDT";
    protected $_drmProductUpdate = "http://localhost/drm_v7/api/postProductFromDT";

    /**
     * @noinspection PhpUnused
     * @param Request $request
     * @return JsonResponse
     * @noinspection DuplicatedCode
     */
    public function getCategoryFromDT(Request $request): JsonResponse
    {
        /** @var array $userAndPass */
        $userAndPass = $this->getUsernamePasswordFromHeader($request);
        $username = $userAndPass[0];
        $password = $userAndPass[1];

        if ($username == null || $password == null) {
            return response()->json([], 401);
        }

        $shop = \App\Models\Shop::where(['username' => $username, 'password' => $password])->first();

        if (empty($shop)) {
            return response()->json([], 401);
        }

        $url = $shop->url . "/api/get_categories_sync";

        $current_page = 1;
        $total = 1;
        $response2All = [];

        do {
            $time = Carbon::now()->format("Y-m-d H:i:s");
//            $time = Carbon::create(2019, 1, 1)->format("Y-m-d H:i:s");
            $response = Http::withBasicAuth($username, $password)->get($url, ["time" => $time, "page" => $current_page++]);

            if ($response->status() === 200) {
                $responseJson = $response->json();
                $data = $responseJson["categories"]["data"];

                $total = $responseJson["categories"]["total"] ?? 1;

                foreach ($data as $item) {
                    $response2 = Http::withBasicAuth($username, $password)->post($this->_drmCategoryUpdate, [
                        'user_id' => $shop->user_id,
                        'country_id' => 1,
                        'category_name_de' => $item["title"],
                    ]);
                    if ($response2->status() === 200) {
                        array_push($response2All, ["title" => $item["title"], "message" => $response2->json()["message"]]);
                    } else {
                        array_push($response2All, ["title" => $item["title"], "message" => "un-success"]);
                    }
                }
            }

        } while ($current_page <= $total);

        return response()->json($response2All);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @noinspection PhpUnused
     * @noinspection DuplicatedCode
     */
    public function getProductFromDT(Request $request): JsonResponse
    {
        /** @var array $userAndPass */
        $userAndPass = $this->getUsernamePasswordFromHeader($request);
        $username = $userAndPass[0];
        $password = $userAndPass[1];

        if ($username == null || $password == null) {
            return response()->json([], 401);
        }

        $shop = \App\Models\Shop::where(['username' => $username, 'password' => $password])->first();

        if (empty($shop)) {
            return response()->json([], 401);
        }

        $url = $shop->url . "/api/get_products_sync";

        $current_page = 1;
        $total = 1;
        $response2All = [];

        do {
            $time = Carbon::now()->format("Y-m-d H:i:s");
//            $time = Carbon::create(2019, 1, 1)->format("Y-m-d H:i:s");
            $response = Http::withBasicAuth($username, $password)->get($url, ["time" => $time, "page" => $current_page++]);

            if ($response->status() === 200) {

                $responseJson = $response->json();
                $data = $responseJson["products"]["data"];

                $total = $responseJson["products"]["last_page"] ?? 1;

                foreach ($data as $item) {
                    $ean = $this->makeProductEan($item, $shop->user_id);
                    if ($ean !== 0) {
                        $insertableProduct = $this->makeProduct($item, $ean, $shop->user_id);
                        $productId = 1;
                        $categories = $this->makeProductCategory($item, $productId);
                        $tags = $this->makeProductTag($item);
                        $productTranslations = $this->productTranslations($item, $ean, $shop->user_id);

                        $response2 = Http::withBasicAuth($username, $password)->post($this->_drmProductUpdate, [
                            'insertableProduct' => $insertableProduct,
                            'categories' => $categories,
                            'tags' => $tags,
                            'productTranslations' => $productTranslations,
                            'country_id' => 1,
                        ]);
                        if ($response2->status() === 200) {
                            array_push($response2All, ["title" => $item["title"], "message" => $response2->json()["message"]]);
                        } else {
                            array_push($response2All, ["title" => $item["title"], "message" => "un-success"]);
                        }
                    }
                }
            }

        } while ($current_page <= $total);

        return response()->json($response2All);
    }

    /**
     * @param Request $request
     * @return array|null[]
     */
    private function getUsernamePasswordFromHeader(Request $request): array
    {
        $header = $request->header('Authorization');
        $token = explode(" ", $header);
        $originalToken = explode(":", base64_decode($token[1]));
        $username = $originalToken[0];
        $password = $originalToken[1];

        if (strlen($username) == 0 || strlen($password) == 0) {
            return [null, null];
        } else {
            return [$username, $password];
        }
    }

    /**
     * @param $product
     * @param $userId
     * @return int
     */
    private function makeProductEan($product, $userId): int
    {
        $ean = $product['ean'];
        $product_exist = DB::table('drm_products')->where(['user_id' => $userId, "ean" => $ean])->count();
        $ean_exist = DB::table('custom_eans')->where(['user_id' => $userId, "ean" => $ean])->count();
        if ($product_exist || $ean_exist) $ean = false;

        if (!$ean) return 0;

        return $ean;
    }

    /**
     * @param $product
     * @param $ean
     * @param $userId
     * @return array
     */
    private function makeProduct($product, $ean, $userId): array
    {
        foreach ($product["custom_field"] as $item) {
            $product[$item["name"]] = $item["custom_field_value"][0]["value"];
        }

        foreach ($product["content_data"] as $item) {
            $product[$item["field_name"]] = $item["field_value"];
        }

        $product["images"] = [];
        foreach ($product["media"] as $item) {
            array_push($product["images"], $item["filename"]);
        }

        unset($product["custom_field"]);
        unset($product["content_data"]);
        unset($product["media"]);

        $imageArray = [];
        $i = 1;
        foreach ($product["images"] as $image) {
            $imageSingle = [];
            $imageSingle["id"] = $i++;
            $imageSingle["status"] = 1;
            $imageSingle["src"] = $image;
            array_push($imageArray, $imageSingle);
        }

        $json_image = (!empty($imageArray)) ? json_encode($imageArray) : null;

        $productReturn = [];
        if ($userId) $productReturn['user_id'] = $userId;
        if ($ean) $productReturn['ean'] = $ean;
        if ($json_image) $productReturn['image'] = $json_image;
        if (isset($product['price'])) $productReturn['ek_price'] = $product['price'];
        if (isset($product['price'])) $productReturn['vk_price'] = floatval($product['price']);
        if (isset($product['shipping_width']) && isset($product['shipping_height']) && isset($product['shipping_depth'])) $productReturn['item_size'] = $product['shipping_width'] . 'x' . $product['shipping_height'] . 'x' . $product['shipping_depth'];
        if (isset($product['shipping_weight'])) $productReturn['item_weight'] = $product['shipping_weight'];
        $productReturn['update_enabled'] = '1';

//            'stock' => $product['stock'],
//            'brand' => $product['brand'],
//            'item_color' => $product['item_color'],
//            'materials' => $product['materials'],
//            'production_year' => $product['production_year'],
//            'note' => $product['note'],
//            'delivery_company_id' => $product['suplier'],
//            'country_id' => $item_countries,
//            'status' => $product['status'],
//            'gender' => $product['gender'],
//            'delivery_days' => $product['delivery_days'],
//            'item_number' => $product['item_number'],

        return $productReturn;
    }

    /**
     * @param $product
     * @param $productId
     * @return array
     */
    private function makeProductCategory($product, $productId): array
    {
        $product["categories"] = [];
        foreach ($product["category_item"] as $item)
            array_push($product["categories"], $item["category"]["title"]);

        $item_countries = "1";
        $categories = [];
        if (is_array($product['categories'])) {
            foreach ($product['categories'] as $cat) {
                $insert[] = [
                    'category_id' => $cat,
                    'country_id' => $item_countries,
                    'product_id' => $productId
                ];
            }
            if (!empty($insert)) {
                array_push($categories, $insert);
            }
        }
        return $categories;
    }

    /**
     * @param $product
     * @return array
     */
    private function makeProductTag($product): array
    {
        $product["tags"] = [];
        foreach ($product["tagging_tagged"] as $item)
            array_push($product["tags"], $item["tag_name"]);

        $data['tags'] = $this->generateTags($product["tags"]);
        return $data;
    }

    /**
     * @param array $array
     * @return string
     * @noinspection DuplicatedCode
     */
    private function generateTags($array = []): string
    {
        $tags = "";
        $i = 0;
        $array = $this->removeNulls($array);
        foreach ($array as $value) {
            $value = str_replace(' - ', '_', $value);
            $value = str_replace('-', '_', $value);
            $i++;
            if ($i == count($array)) {
                if (strpos($value, '#') !== false) {
                    $tags .= $value;
                } else {
                    $tags .= "#" . $value;
                }
            } else {
                if (strpos($value, '#') !== false) {
                    $tags .= $value . ",";
                } else {
                    $tags .= "#" . $value . ",";
                }
            }
        }
        return $tags;
    }

    /**
     * @param array $array
     * @return array
     */
    private function removeNulls($array = []): array
    {
        if (is_array($array)) {
            foreach ($array as $key => $value) {
                if ($value == null && $value == "") {
                    unset($array[$key]);
                }
            }
        }
        return $array;
    }

    /**
     * @param $product
     * @param $ean
     * @param $productId
     * @return array
     */
    private function productTranslations($product, $ean, $productId): array
    {
        $drmTranslationProduct['title'] = $product['title'];
        $drmTranslationProduct['description'] = '';
        $drmTranslationProduct['ean'] = $ean;
        $drmTranslationProduct['product_id'] = $productId;

        if (isset($_COOKIE['languageShortcode'])) {
            $table = "drm_translation_" . $_COOKIE['languageShortcode'];
        } else {
            $table = "drm_translation_de";
        }

        if (isset($_COOKIE['languageShortcode']) && Schema::hasTable($table)) {
            return [$table, $drmTranslationProduct];
        }

        return ['drm_translation_de', $drmTranslationProduct];
    }


    /*
// test
Route::post('postProductFromDT', 'HomeController@postProductFromDT');
Route::post('postCategoryFromDT', 'HomeController@postCategoryFromDT');
        // just copy paste this code to drm

        public function postProductFromDT(Request $request)
        {
            $categories = [];
            foreach ($request["categories"] as $category) {
                array_push($categories, [
                    "category_id" => "Accessoaries",
                    "country_id" => "1",
                    "product_id" => 10
                ]);
            }
            $request["categories"] = $categories;

    //        $productId = Product::create($request["insertableProduct"])->id;
    //        DB::table('drm_product_categories')->insert($request["categories"]);
    //        DB::table('drm_products')->where('id', $productId)->update($request["tags"]);
    //        $request["productTranslations"];

            return response()->json([
                'message' => $request->all()
            ]);
        }

        public function postCategoryFromDT(Request $request)
        {
            return response()->json([
                'message' => $request->all()
            ]);
        }*/
}
