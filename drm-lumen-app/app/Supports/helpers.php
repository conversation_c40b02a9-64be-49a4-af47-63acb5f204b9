<?php

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Log\Logger;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;

if (!function_exists('auth')) {
    /**
     * Get the available auth instance.
     *
     * @param string|null $guard
     * @return \Illuminate\Support\Facades\Auth
     */
    function auth($guard = null)
    {
        if (is_null($guard)) {
            $guards = array_keys(config('auth.guards'));
            $auth = app('auth');
            foreach ($guards as $grd) {
                if ($auth->guard($grd)->check()) {
                    return $auth->guard($grd);
                }
            }

            return $auth;
        }

        return app('auth')->guard($guard);
    }
}

if (!function_exists('json_response')) {
    function json_response($response_data = null, $status_code = 200, $errors = [])
    {
        $response = $response_data;

        if (count($errors) > 0 || $status_code >= 300) {
            $response = array_filter(['status' => 'error', 'errors' => $errors, 'data' => $response]);
        }

        if (blank($response)) {
            $response = [];
        }

        return response()->json($response, $status_code);
    }
}

if (!function_exists('trans_table_column')) {
    function trans_table_column($column)
    {
        if (!is_array($column) && !is_object($column)) return $column;

        $locale = strtolower(app()->getLocale() ?? 'en');
        return data_get($column, $locale, null) ?? data_get($column, 'en');
    }
}

if (!function_exists('to_array')) {
    function to_array($data)
    {
        if ($data instanceof Collection) {
            return $data->toArray();
        }

        if ($data instanceof Model || $data instanceof \Illuminate\Pagination\LengthAwarePaginator) {
            return $data->toArray();
        }

        if (is_object($data)) {
            return (array)$data;
        }

        return $data;
    }
}

if (!function_exists('decimal_point')) {
    function decimal_point($number, $point = 2)
    {
        return number_format((float)$number, $point, '.', '');
    }
}

if (!function_exists('db_escape')) {
    function db_escape($value, $connection = null)
    {
        return \DB::connection($connection)->getPdo()->quote($value);
    }
}

if (!function_exists('request')) {
    /**
     * @param null $key
     * @return \Illuminate\Http\Request|mixed
     */
    function request($key = null)
    {
        if ($key) {
            return app('request')->input($key);
        }

        return app('request');
    }
}

if (!function_exists('debuglog')) {
    function debuglog($msg, $context = [], $level = 'debug')
    {
        if (!config('logging.enable')) {
            return;
        }

        /**
         * @var $logger Logger
         */
        $logger = app(Logger::class);

        if (in_array(gettype($context), [
            'int',
            'integer',
            'float',
            'double',
            'string'
        ])) {
            $context = [$context];
        }

        if (!is_array($context)) {
            $context = [];
        }

        $logger->channel('debug')->write($level, $msg, $context);
    }

}

if (!function_exists('uuid')) {
    function uuid($name)
    {
        return Uuid::uuid5(Uuid::uuid4(), $name);
    }
}

if (!function_exists('config_path')) {
    function config_path($path = '')
    {
        return app()->basePath() . '/config' . ($path ? '/' . $path : $path);
    }
}

if (!function_exists('debug_log')) {
    function debug_log(string $message, string $flag)
    {
        \App\Models\Log::create([
            'message' => $message,
            'module' => $flag
        ]);
    }
}

if (!function_exists('get_api_user_id')) {
    function get_api_user_id($userToken, $userPassToken)
    {
        try {
            $shop = \App\Models\Shop::where([
                'username' => $userToken,
                'password' => $userPassToken,
            ])->first();
            return !empty($shop) ? $shop->user_id : null;
        } catch (Exception $e) {
            return false;
        }
    }
}

if (!function_exists('get_api_shop_id')) {
    function get_api_shop_id($userToken, $userPassToken)
    {
        try {
            return \App\Models\Shop::where([
                'username' => $userToken,
                'password' => $userPassToken,
            ])->value('id') ?? NULL;
        } catch (Exception $e) {
            return NULL;
        }
    }
}

if (!function_exists('trace')) {
    function trace(string $message, array $data = [])
    {
        \Illuminate\Support\Facades\Log::info(
            'IP ' . request()->ip() . 'Message ' . $message,
            $data
        );
    }
}


//Interval in minutes
if (!function_exists('DRM_Interval_App_Minute')) {
    function DRM_Interval_App_Minute($user_id, $app_id): int
    {
        $user_plan = app_user_plan_id($user_id, $app_id);
        $time = 1440;
        switch ($user_plan) {
            case config('global.interval_bronze_plan'):
                $time = 720;
                break;
            case config('global.interval_silver_plan'):
                $time = 360;
                break;
            case config('global.interval_gold_plan'):
                $time = 180;
                break;
            case config('global.interval_platinum_plan'):
                $time = 30;
                break;
        }
        return $time;
    }
}

//User app plan id
if (!function_exists('app_user_plan_id')) {
    function app_user_plan_id($user_id, $app_id)
    {
        $user_has_plan = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('plan_id')->first();
        $user_has_assign = \DB::table('app_assigns')->where(['app_id' => $app_id, 'user_id' => $user_id])->select('plan_id')->first();
        $purchase_plan = ($user_has_plan) ? $user_has_plan->plan_id : 0;
        $assign_plan = ($user_has_assign) ? $user_has_assign->plan_id : 0;
        $fast_plan = ($purchase_plan > $assign_plan) ? $purchase_plan : $assign_plan;

        $user_has_trial = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'status' => 'active', 'is_free_trail' => 1, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->first();
        return ($fast_plan) ? $fast_plan : (($user_has_trial) ? config('global.interval_platinum_plan') : null);
    }
}

//user interval
if (!function_exists('app_user_interval')) {
    function app_user_interval($plan = null)
    {
        $text = '24 HOURS';
        switch ($plan) {
            case config('global.interval_bronze_plan'):
                $text = '12 HOURS';
                break;
            case config('global.interval_silver_plan'):
                $text = '6 HOURS';
                break;
            case config('global.interval_gold_plan'):
                $text = '3 HOURS';
                break;
            case config('global.interval_platinum_plan'):
                $text = '30 MINUTES';
                break;
        }
        return $text;
    }
}

function orderStatisticsArrData($user)
{
    $currency = $performa = $credit_note_amount = $order_average_currency = [];
    $currency['EUR'] = $performa['EUR'] = $credit_note_amount['EUR'] = 0;

    $orders_group = null;
    $total = $other = $shipped_order = $proforma_invoice = $credit_note_count = 0;
    $total_sum = $pro_sum = 0;

    $eur_total_sum = $eur_total_sum_count = 0;
    $eur_credit_note_sum = 0;
    $eur_prof_sum = 0;

    $best_selling_shop = null;
    $best_selling_shop_item = 0;

    $orders_group = \App\Models\NewOrder::select('currency', 'invoice_number', 'total', 'test_order', 'credit_number', 'eur_total');

    $order_top_shop = \App\Models\NewOrder::has('shop');
    if (!$user->isSuperAdmin()) {
        $order_top_shop->where('cms_user_id', $user->id);
    }
    $order_top_shop = $order_top_shop->selectRaw(DB::raw('count(*) as order_count, shop_id'))->groupBy('shop_id')->orderBy('order_count', 'desc')->first();

    if ($order_top_shop) {
        $best_selling_shop_type = \App\Models\Shop::where('id', $order_top_shop->shop_id)->first(['channel']);
        $best_selling_shop = drm_shop_type_name($best_selling_shop_type->channel);
        $best_selling_shop_item = $order_top_shop->order_count;
    }

    if (!$user->isSuperAdmin()) {
        $orders_group->where('cms_user_id', '=', $user->id);
    }

    $orders_group = $orders_group->get()->groupBy('currency');


    if ($orders_group->isNotEmpty()) {

        foreach ($orders_group as $key => $orders) {
            $order_sum = $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->where('credit_number', 0)->sum("total");
            $total += $orders->count(); //whereNotIn('status',['Storniert','Canceled'])->
            $total_sum += $order_sum;

            //EUR converted value
            $eur_total_sum += $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->where('credit_number', 0)->sum("eur_total");

            $eur_total_sum_count += $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->where('credit_number', 0)->count();


            $eur_credit_note_sum += abs($orders->where('test_order', '!=', 1)->where('credit_number', '>', 0)->sum("eur_total"));
            $eur_prof_sum += $orders->where('invoice_number', -1)->sum("eur_total");

            $credit_note_sum = $orders->where('test_order', '!=', 1)->where('credit_number', '>', 0)->sum("total");

            $proforma_invoice += $orders->where('invoice_number', -1)->count();
            $prof_sum = $orders->where('invoice_number', -1)->sum("total");
            $pro_sum += $prof_sum;


            if (($key == '') || (strtolower($key) == 'eur')) {
                $performa['EUR'] += $prof_sum;
                $currency['EUR'] += $order_sum;
                $credit_note_amount['EUR'] += abs($credit_note_sum);
            } else {
                $currency[$key] = $order_sum;
                $performa[$key] = $prof_sum;
                $credit_note_amount[$key] = abs($credit_note_sum);
            }
            $shipped_order = 0;

            // $shipped_order +=  $orders->filter(function ($item) use ($attribute, $value) {
            //     return strtolower($item['status']) == 'shipped';
            // })->count();


            $credit_note_count += $orders->filter(function ($item) {
                return ($item['credit_number'] > 0);
            })->count();

        }
    }

    //$others_order = $total - $shipped_order; // - $proforma_invoice;
    $performa_statt = ['count' => $proforma_invoice, 'amount' => $performa]; //Remove soon

    try {
        $order_average_value = $eur_total_sum / $eur_total_sum_count;
        foreach ($currency as $k => $val) {
            $order_average_currency[$k] = $val / $eur_total_sum_count;
        }
    } catch (\Exception $e) {
        $order_average_value = 0;
    }

    $customer_count = \App\Models\NewCustomer::where('user_id', $user->id)->count();

    return [
        'total_order' => $total,
        'total_proforma' => $proforma_invoice,

        'total_products' => $user->products()->count(),
        // 'shipped_order' => $shipped_order,
        // 'others_order' => $others_order,
        'credit_note_count' => $credit_note_count,
        'best_selling_shop' => $best_selling_shop,
        'best_selling_shop_item' => $best_selling_shop_item,


        'order_average_value' => [
            'type' => 'amount',
            'total' => $order_average_value,
            'details' => $order_average_currency,
        ],

        'eur_total_sum' => [
            'type' => 'amount',
            'total' => $eur_total_sum,
            'details' => $currency,
        ],
        'eur_credit_note_sum' => [
            'type' => 'credit',
            'total' => $eur_credit_note_sum,
            'details' => $credit_note_amount,
        ],
        'eur_prof_sum' => [
            'type' => 'proforma',
            'total' => $eur_prof_sum,
            'details' => $performa,
            'count' => $proforma_invoice,
        ],

        'total_amount' => $total_sum, 'currency' => $currency, 'performa' => $performa_statt, 'credit_note' => $credit_note_amount, 'customer_count' => $customer_count
    ];
}

//Get shop type name by type id value
function drm_shop_type_name($value)
{
    $data = drm_shop_channels();
    return isset($data[$value]) ? $data[$value] : '';
}

//drm shops
function drm_shop_channels()
{
    return [
        1 => 'GAMBIO',
        2 => 'LENGOW',
        3 => 'YATEGO',
        4 => 'EBAY',
        5 => 'AMAZON',
        6 => 'SHOPIFY',
        7 => 'WooCommerce',
        8 => 'ClouSale',
        9 => 'Chrono24',
        10 => 'Droptienda',
        200 => 'VOD',
        11 => 'Etsy',
        12 => 'Otto',
    ];
}

//remove comma price
if (!function_exists('removeCommaPrice')) {
    function removeCommaPrice($rate)
    {
        if (strpos($rate, ",")) {
            $have = [".", ","];
            $will_be = ["", "."];
            $rate = str_replace($have, $will_be, $rate);
        }
        return round($rate, 2);
    }
}

//invoice number format -- used to show inv number
if (!function_exists('inv_number_string')) {
    function inv_number_string($inv, $inv_number_string = null)
    {
        return ($inv_number_string) ? $inv_number_string : $inv;
    }
}

//drm insert type codes
if (!function_exists('getInsertTypeName')) {
    function getInsertTypeName($id)
    {
        $arr = [
            1 => 'API', //Shop order sync
            2 => 'VOD', //Daily order sync
            3 => 'Stripe',
            4 => 'Charge', //Not used - paywall charge
            5 => 'Import',
            6 => 'Manual',
            7 => 'Marketplace'
        ];
        return (isset($arr[$id])) ? $arr[$id] : '';
    }
}

//Order history label
if (!function_exists('drmHistoryLabel')) {
    function drmHistoryLabel($status)
    {
        $drm_status = config('drm_order_status');
        return isset($drm_status[$status]) ? $drm_status[$status] : $status;
    }
}

//Order history label
if (!function_exists('drmIsJSON')) {
    function drmIsJSON($string)
    {
        return is_string($string) && is_array(json_decode($string, true)) && (json_last_error() == JSON_ERROR_NONE) ? true : false;
    }
}


//Return tax by country name
if (!function_exists('countryCodeTax')) {
    function countryCodeTax($country_code, $country_id = false)
    {
        $tax_rate = 16;
        $tax_rate_data = null;

        $id = null;
        if ($country_code) {
            // $country_code = strtoupper($country_code);
            $tax_rate_data = DB::table('tax_rates')
                ->select('id', 'charge')
                ->where('country_code', 'like', $country_code)
                ->orWhere('country', 'like', $country_code)
                ->orWhere('country_de', 'like', $country_code)
                ->first();
            $id = $tax_rate_data->id;
        }

        if ($country_id) return $id;
        return ($tax_rate_data) ? (float)$tax_rate_data->charge : (float)$tax_rate;
    }
}

function getZapierUser($credentials)
{
    $authClient = DB::table('oauth_clients')
        ->select('user_id')
        ->where(['name' => $credentials->username, 'secret' => $credentials->password])
        ->first();
    if (empty($authClient)) {
        return null;
    }
    $drmUser = DB::table('cms_users')->where('id', $authClient->user_id)->whereNotNull('status')->select('id', 'name', 'email', 'id_cms_privileges')->first();
    if (empty($drmUser)) {
        return null;
    }
    return $drmUser;
}

function postApiCall(array $data, $url)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    // Receive server response ...
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    $server = curl_exec($ch);
    curl_close($ch);
    return $server;
}

//Recurring invoices purchased or not
function DrmUserHasPurchasedApp($user_id, $app_id)
{
    $assigned = DB::table('app_assigns')->where(['app_id' => $app_id, 'user_id' => $user_id])->exists();
    $purchased = DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', Carbon::now())->exists();
    return $assigned || $purchased;
}

if (!function_exists('notify_stock_update')) {
    function notify_stock_update($product_id, $user_id)
    {
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => "http://*************/api/notify_stock_update",
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => [
                'user_id' => $user_id,
                'product_id' => $product_id
            ]
        ));
        curl_exec($ch);
        curl_close($ch);
    }
}

if(!function_exists('get_option')){

    /**
     * Return single option
     */
    function get_option($key, $group, $user_id = null)
    {
        $op = DB::table('options')->select('*');
        $op->where('option_key', '=', $key);
        $op->where('option_group', '=', $group);
        if($user_id){
            $op->where('user_id', '=', $user_id);
        }

        return $op->first();
    }
}


if(!function_exists('_log')){
    function _log($string, $filename = 'logs.txt'){
        $string=print_r($string, true);
        file_put_contents($filename, "[".date('Y-m-d H:i:s')."] ".$string.PHP_EOL , FILE_APPEND | LOCK_EX);
    }
}

if(!function_exists('convert_utf8')){
    function convert_utf8($text){
        try {
            if(!is_array($text)){
                $incov = iconv('UTF-8', 'WINDOWS-1252//TRANSLIT', $text);
                if($incov==false){
                    $incov = iconv('UTF-8', 'ISO-8859-15',$text);
                }
                if($incov == false){
                    return $text;
                }
                else{
                    $utf8 = mb_convert_encoding($incov, 'UTF-8',
                        mb_detect_encoding($incov, 'UTF-8, ISO-8859-15', true));
                }
                return $utf8;
            }
            else{
                return convert_array_utf8($text);
            }
        } catch (\Throwable $th) {
            return $text;
        }
    }
}

if(!function_exists('convert_array_utf8')){
    function convert_array_utf8($array = []){
        if(is_array($array)){
            $array = trim_array($array);
            $keys = array_keys($array);
            $keys_utf8 = array_map('convert_utf8',$keys);
            $keys_utf8 = array_map('remove_dots',$keys_utf8);
            $utf8 = array_map('convert_utf8',$array);
            return array_combine($keys_utf8,$utf8);
        }else{
            return $array;
        }
    }
}

if(! function_exists('de_number_format')) {
    function de_number_format($number,$format = 1){
        $sign = array('£','€','$','kg');
        $number = str_replace($sign,'',$number);
        $number = str_replace(' ', '', $number);
        try {
            if($format == 1){
                $fmt = new NumberFormatter( 'de_DE', NumberFormatter::DECIMAL );
            }
            else{
                $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
            }
            $price = $fmt->parse($number);

            if($price == 0){
                if($format == 1){
                    $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
                    $price = $fmt->parse($number);
                }
                else {
                    $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
                    $price = $fmt->parse($number);
                }
            }
            return $price;
        } catch (Exception $e) {
            $val = str_replace(",",".",$number);
            $val = preg_replace('/\.(?=.*\.)/', '', $val);
            return floatval($val);
        }
    }
}

if (!function_exists('updateConnectionStatus')) {
    function updateConnectionStatus($product_id, $user_id, $errors = array())
    {
        $data = [
            'product_id' => $product_id,
            'user_id' => $user_id,
            'errors' => $errors
        ];
        $ch = curl_init(config('app.drm_url') . "/api/channel-products/status/update");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("accept: application/json", "content-type: application/json"));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_exec($ch);
        curl_close($ch);
    }
}
