<?php

namespace App\Services\Customer;

use App\Enums\DroptiendaSyncType;
use App\Models\NewCustomer;
use App\Services\BaseService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Log;

class CustomerService extends BaseService
{

    public $user_id;

    public function __construct()
    {
        $this->user_id = get_api_user_id(request()->header('userToken'), request()->header('userPassToken'));
    }

    public function all(array $filters = [])
    {
        $query = NewCustomer::query();

        $limit = Arr::get($filters, 'limit', 20);

        return $limit != '-1' ? $query->paginate($limit) : $query->get();
    }

    public function getUserIdFromHeader(Request $request)
    {
        $header = $request->bearerToken();
        $decoded = JWT::decode($header, "JWT_SECRET", array("HS256"));
        return $decoded->sub->id > 0 ? $decoded->sub->id : 0;
    }

    public function getUsersCustomers(Request $request)
    {
        $customers = NewCustomer::where('user_id', $this->getUserIdFromHeader($request))
            ->select('id', 'full_name', 'company_name', 'email', 'address', 'billing', 'city', 'state', 'zip_code', 'country', 'user_id');

        $perpage = $request->perpage ?? 20;

        if (!empty($request->customer_id)) {
            $customers->where('id', 'LIKE', '%' . $request->customer_id . '%');
        }

        if (!empty($request->customer_name)) {
            $customers->where('full_name', 'LIKE', '%' . $request->customer_name . '%');
        }
        $customers = $customers->simplePaginate($perpage);
        // $customers = DB::table('new_customers')
        //             ->where('user_id',$this->getUserIdFromHeader($request))
        //             ->select('id','full_name', 'company_name', 'email', 'address', 'billing', 'city', 'state', 'zip_code', 'country')
        //             ->simplePaginate(20);
        return $customers;
    }

    public function store(array $data)
    {
        return $this->saveCustomer($data);
    }

    public function getById($id)
    {
        return NewCustomer::find($id);
    }

    public function update($id, array $data)
    {
        return $this->saveCustomer($data, $id);
    }

    public function destroy($id)
    {
        $customer = NewCustomer::find($id);
        if ($customer) {
            $customer->delete();
            return ['success' => true, 'message' => 'Customer deleted successfully.'];
        }
        return ['success' => false, 'message' => 'Customer cant be deleted.'];
    }

    public function saveCustomer($data, $id = null)
    {
        try{
            $user = NewCustomer::firstOrNew(['id' => $id]);

            $user->fill($data);

            if (!empty($user->billing) && is_array($user->billing)) {

                $user->billing = json_encode($user->billing);
            }
            if (!empty($user->shipping) && is_array($user->shipping)) {
                $user->shipping = json_encode($user->shipping);
            }
            Log::info('Before saving customer.');
            $user->save();
            if(isset($data['tag']) and !empty($data['tag'])){
                Log::info('have tag');
                $customer_id = $user->id;
                $tag = $data['tag'];
                Log::info($customer_id);
                Log::info($tag);
                Log::info(json_encode($user));
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => '*************/api/droptineda-customer-tag-add',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_POSTFIELDS => array('customer_id' => $customer_id, 'tag' => $tag),
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json',
                        'userToken: '.request()->header('userToken'),
                        'userPassToken: '.request()->header('userPassToken')
                    ),
                ));
                $response = curl_exec($curl);
                Log::info(json_encode($response));
                curl_close($curl);
            }


            return $user;
        } catch (Exception $e) {
            Log::error($e);
        }
    }

}
