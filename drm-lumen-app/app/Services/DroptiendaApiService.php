<?php

namespace App\Services;

// use Apiz\AbstractApi;

class DroptiendaApiService
{

	private $baseUrl = 'http://127.0.0.1:8080/';
	private $prefix = 'api/v1/';

	public function setBaseUrl ($url): DroptiendaApiService
    {
		$this->baseUrl = trim($url, '/').'/';
		return $this;
	}

	public function setPrefix ($url)
	{
		$this->prefix = trim( $url, '/').'/';
		return $this;
	}


	private function getResponse ($url, $sync_event = NULL , $limit=20)
	{
		$data = [];
		$url = $this->baseUrl.$this->prefix.trim($url, '/').'?sync_event='.$sync_event.'&limit='.$limit;

		$ch = curl_init();
		$current_page = 0;
		while ( 1 ) {
		    curl_setopt($ch, CURLOPT_URL, $url.'&page='.++$current_page);
		    curl_setopt($ch, CURLOPT_POST, 0);
		    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		    $response = curl_exec ($ch);
		    $r = json_decode( $response, true );

		    $data = array_merge($data, $r['data']);

		    $err = curl_error($ch);  //if you need

		    if ( $r['current_page'] == $r['last_page']  ) {
		    	break;
		    }
		}

	    if ( !$err ) {
		    curl_close ($ch);
		    return $data;
	    }

	    curl_close($ch);
        return [];
	}

	public function getCategories ($url, $sync_event = NULL, $limit=20)
	{
		$categories = $this->getResponse($url, $sync_event, $limit);
		return $categories;
	}
}
