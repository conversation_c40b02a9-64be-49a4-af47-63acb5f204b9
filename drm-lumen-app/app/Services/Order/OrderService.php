<?php


namespace App\Services\Order;


use App\Models\NewOrder;
use App\Services\BaseService;
use Illuminate\Support\Arr;

class OrderService extends BaseService
{
    public function all(array $filters = [])
    {
        $query = NewOrder::query();

        $limit = Arr::get($filters, 'limit', 20);

        return $limit != '-1' ? $query->paginate($limit) : $query->get();
    }

    public function getById($id)
    {
        return NewOrder::find($id);
    }

    public function store(array $data)
    {
        return $this->saveOrder($data);
    }

    public function update($id, array $data)
    {
        return $this->saveOrder($data, $id);
    }

    private function saveOrder($data, $id = null)
    {
        $product = NewOrder::firstOrNew(['id' => $id]);
        $product->fill($data);
        $product->save();
        return $product;
    }
    public function storeTest(array $data)
    {
        $product = NewOrder::firstOrNew(['id' => $id]);
        $product->fill($data);
        $product->save();
        return $product;
    }
}
