<?php

namespace App\Services\Product;

use App\Enums\DroptiendaSyncEvent;
use App\Models\Catalog\Product;
use App\Models\ChannelProduct;
use App\Models\ChannelProductCategory;
use App\Models\ChannelUserCategory;
use App\Models\DrmProduct;
use App\Models\DRMProductCategory;
use App\Models\DroptiendaSyncHistory;
use App\Services\BaseService;
use App\Services\DroptiendaSyncService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Models\Shop;

class ProductService extends BaseService
{
    public $user_id;
    public $shop_id;

	public function __construct ()
	{
        $this->user_id = get_api_user_id(request()->header('userToken'), request()->header('userPassToken'));
        $this->shop_id = get_api_shop_id(request()->header('userToken'), request()->header('userPassToken'));
	}

	public function index()
	{
		return ChannelProduct::all();
	}

    public function getProducts($filters): \Illuminate\Http\JsonResponse
    {
        $products = DB::table('drm_products')
                    ->where('user_id', $this->user_id)
                    ->select('id','name','ean','ek_price','image')
                    ->simplePaginate(20);
        return response()->json(['products'=>$products, 'user_id'=>$this->user_id]);
    }

	public function store(array $data)
	{
	    $data['user_id'] = $this->user_id;
		return $this->saveProduct($data);
	}

	public function storeToDrm(array $data)
    {
        $data['image'] = $data['images'];
        $data['country_id'] = 1;
        $data['update_status'] = \App\Enums\Product::UPDATE_STATUS;

        $product = Product::updateOrCreate([
            'user_id' => $this->user_id,
            'ean'     => $data['ean'],
        ],$data);

        foreach ( Arr::get($data, 'categories', []) as $category )
        {
            $channel_category = ChannelUserCategory::find($category);
            if(!empty($channel_category))
            {
                DRMProductCategory::updateOrCreate([
                    'category_id'   => $channel_category->drm_category_id,
                    'product_id' => $product->id
                ],[]);
            }
        }
        return $product;
    }

    public function getUpdateStatus()
    {
        return json_encode([
            'title' => 1,
            'description' => 1,
            'image' => 1,
            'ek_price' => 1,
            'stock' => 1,
            'status' => 1,
            'gender' => 1,
            'item_weight' => 1,
            'item_color' => 1,
            'production_year' => 1,
            'materials' => 1,
            'brand' => 1,
            'item_size' => 1,
            'category' => 1,
            'delivery_days' => 1,
            'uvp' => 1,
            'shipping_cost' => 1
        ]);
    }

	public function update(array $data, $id)
	{
        $data['user_id'] = $this->user_id;
		return $this->saveProduct($data, $id);
	}

	public function delete($id)
	{
		ChannelProduct::find($id)->delete();
		return response()->json([
		    'Success'=> true,
            'Message'=> 'Product deleted successfully.'
        ]);
	}

	public function saveProduct($data, $id = null)
	{
        if($id){
            $product = ChannelProduct::firstOrNew(['id'=>$id]);
        }
		else{
            $product = ChannelProduct::updateOrCreate([
                'user_id' => (int)$data['user_id'],
                'ean'     => $data['ean'],
                'channel' => $data['channel'],
                'shop_id' => $this->shop_id
            ],$data);
        }

        $data['metadata'] = [
            'url' => $this->getDTResponseUrl((int)$data['user_id'],$data['url']),
//            'image_urls' => json_encode($data['images'])
        ];
		$product->fill($data);
		$product->save();

        $this->disableUpdateStatus($product);

        if(isset($data['categories'])){
            $categories = array_filter(array_unique($data['categories']));
            if($categories){
                DB::table('channel_product_categories')->where('channel_product_id',$product->id)->delete();
                foreach ( $data['categories'] as $category ) {
                    debug_log($category,'categories exists');
                    DB::table('channel_product_categories')
                    ->updateOrInsert(
                        [
                            'category_id' => $category,
                            'channel_product_id' => $product->id
                         ],
                        ['category_type' => 1]
                    );

                }
            }
        }

        $this->updateConnectionStatus($product->id,$product->user_id);
        return $product;
	}


    private function disableUpdateStatus(ChannelProduct $product)
    {
        $changes = $product->getChanges();
        unset($changes['updated_at']);
        $changedColumns = array_keys($changes);
        $updateStatus = $product->update_status;
        foreach ($changedColumns as $changedColumn) {
            $updateStatus[$changedColumn] = 0;
        }
        if($updateStatus != $product->update_status){
            $product->update_status = $updateStatus;
            $product->save();
        }
    }

    private function getProductData(ChannelProduct $product): array
    {
        $variant_products = $product->variant_products;
        $price_on_request_check = $product->price_on_request;
        $uvp = $product->uvp;
        $categories = ChannelUserCategory::whereIn('id',$product->channel_categories->pluck('category_id')->toArray())
                      ->pluck('id')->toArray();

        $productData = [
            'title'                 => $product->name,
            'drm_ref_id'            => $product->id,
            'description'           => preg_replace("/\s+/", " ", strip_tags($product->trans_short_description)),
            'qty'                   => $product->stock,
            'ek_price'              => $product->ek_price,
            'price'                 => $product->vk_price,
            'price_on_request'      => $price_on_request_check,
            'ean'                   => $product->ean,
            'sku'                   => $product->item_number,
            'content_type'          => 'product',
            'content_body'          => $product->trans_description,
            'subtype'               => 'product',
            'tags'                  => $product->tags,
            'images'                => $product->images,
            'categories'            => $categories,
            'is_active'             => 1,
            'color'                 => $product->item_color, //varchar
            'size'                  => $product->item_size, //varchar
            'weight'                => de_number_format($product->item_weight),
            'unit'                  => $product->item_unit,
            'basic_price'           => $product->basic_price,
            'materials'             => $product->materials,
            'colors'                => array(),
            'sizes'                 => array(),
            'brand'                 => $product->brand,
            'production_year'       => $product->production_year,
            'status'                => $product->status,
            'note'                  => $product->note,
            'gender'                => $product->gender,
            'delivery_company_id'   => $product->delivery_company_id,
            'offer_options'         => json_encode($product->offer_options),
            'handling_time' => [
                'min' => (int)$product->delivery_days,
                'max' => (int)$product->delivery_days + 2
            ],
            'tax_type' => $this->getTaxType($product)
        ];
        $productData['uvp'] = 0;
        if($price_on_request_check == 0 && (($product->vk_price > $uvp) || $product->offer_uvp)){
            $productData['uvp'] = $uvp;
        }

        $productData['variants'] = array();
        foreach ($variant_products as $variant) {
            $variantData = [
                'qty' => $variant->stock, //int
                'price' => $variant->vk_price, // float
//              'uvp'                   => $variant->uvp, // float-
                'ean' => $variant->ean, //varchar
                'sku' => $variant->item_number, // varchar
                'color' => $variant->item_color, //varchar
                'size' => $variant->item_size, //varchar
                'materials' => $variant->materials, //varchar
                'title' => $variant->name, //text
                'drm_ref_id' => $variant->id, //int
                'description' => $variant->trans_short_description, // text
                'images' => $variant->images,
                'brand' => $variant->brand,
                'weight' => de_number_format($variant->item_weight),
                'content_body' => $variant->trans_description,
                'tags' => $variant->tags,
                'delivery_company_id' => $variant->delivery_company_id
            ];
            $variantData['uvp'] = 0;
            if ($variant->price_on_request == 0 && (($variant->vk_price > $variant->uvp) || $variant->offer_uvp)) {
                $variantData['uvp'] = $variant->uvp;
            }
            $productData['variants'][] = $variantData;
        }

        return $productData;
	}

    private function getWeight($weight): string
    {
        return number_format($weight,2,'.',',');
    }

    private function getTaxType($product): int
    {
        return DrmProduct::where(['id' => $product->drm_product_id])->value('tax_type') ?? 1;
    }

	private function getDTResponseUrl($user_id,$url): ?string
    {
        $shop = Shop::where([
            'user_id' => $user_id,
            'channel'  => 10
        ])->first();

        if(!empty($url))
        {
            if(substr($shop->url , -1) == '/'){
                $url = $shop->url.$url;
            }else {
                $url = $shop->url.'/'.$url;
            }
            return $url;
        }
        return null;
    }

    private function updateHistory($syncHistory,$product,$response)
    {
        $metadata = $product->metadata ?? array();
        $metadata['url'] = $this->getDTResponseUrl($product->user_id,$response['url']);
//        $metadata['image_urls'] = $response['images'];

        $errors = array();
        if (!empty($response['id'])) {
            $syncHistory->update([
                'synced_at' => date('Y-m-d H:i:s'),
                'response' => json_encode($response)
            ]);
            $product->update([
                'is_connected' => true,
                'metadata'     => $metadata,
                'error_response' => null,
                'connection_status' => 1
            ]);

            $variant_products = $product->variants ?? array();

            ChannelProduct::whereIn('id',$variant_products)->update([
                'is_connected' => true,
                'error_response' => null,
                'connection_status' => 1
            ]);
        }
        elseif(!empty($response['message'])){
            $errors[] = $response['message'];
        }
        $this->updateConnectionStatus($product->id,$product->user_id,$errors);
    }

    public function syncProductToDroptienda(DroptiendaSyncHistory $syncHistory)
    {
        $droptiendaSyncService = new DroptiendaSyncService($syncHistory->user_id);
        switch ($syncHistory->sync_event) {
            case DroptiendaSyncEvent::CREATE:
                try {
                    $product = ChannelProduct::with(['channel_categories'])->find($syncHistory->model_id);
                    if ($product) {
                        $response = $droptiendaSyncService->storeProduct($this->getProductData($product));
                        $this->updateHistory($syncHistory,$product,$response);
                    }
                    $syncHistory->increment('tries');
                } catch (\Exception $e) {
                    $syncHistory->update([
                        'exception'=>json_encode($e->getMessage()),
                        'tries' => $syncHistory->tries + 1
                    ]);
//                    trace($e->getMessage());
                }
                break;

            case DroptiendaSyncEvent::UPDATE:
                try {
                    $product = ChannelProduct::with(['channel_categories'])->find($syncHistory->model_id);
                    if ($product) {
                        $response = $droptiendaSyncService->updateProduct($product->id, $this->getProductData($product));
                        $this->updateHistory($syncHistory,$product,$response);
                    }

                    $syncHistory->increment('tries');
                } catch (\Exception $e) {
                    $syncHistory->update([
                        'exception'=>json_encode($e->getMessage()),
                        'tries' => $syncHistory->tries + 1
                    ]);
//                    trace($e->getMessage());
                }
                break;

            case DroptiendaSyncEvent::DELETE:
                try {
                    $response = $droptiendaSyncService->deleteProduct($syncHistory->model_id);
                    $syncHistory->update([
                        'synced_at' => date('Y-m-d H:i:s'),
                        'tries' => $syncHistory->tries + 1,
                        'response' => json_encode($response)
                    ]);
                    $product = ChannelProduct::find($syncHistory->model_id);
                    if ($product) {
                        $product->update([
                            'is_connected' => false
                        ]);

                        $this->updateConnectionStatus($product->id, $product->user_id,array());

                        $variant_products = $product->variants ?? array();

                        ChannelProduct::whereIn('id',$variant_products)->update([
                            'is_connected' => false
                        ]);

                        foreach ($variant_products as $variant_product) {
                            $this->updateConnectionStatus($variant_product, $product->user_id,array());
                        }

                    }
                } catch (\Exception $e) {
                    $syncHistory->update([
                        'exception' => json_encode($e->getMessage()),
                        'tries' => $syncHistory->tries + 1
                    ]);
//                    trace($e->getMessage());
                }
                break;
        }
    }

    public function updateConnectionStatus($product_id, $user_id, $errors = array())
    {
        $data = [
            'product_id' => $product_id,
            'user_id'    => $user_id,
            'errors'     => $errors
        ];
        $ch = curl_init(config('app.drm_url')."/api/channel-products/status/update");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("accept: application/json","content-type: application/json"));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($data));
        curl_exec($ch);
        curl_close($ch);
    }
}
