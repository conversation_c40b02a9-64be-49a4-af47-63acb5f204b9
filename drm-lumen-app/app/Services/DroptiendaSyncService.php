<?php

namespace App\Services;

use Apiz\AbstractApi;
use App\Enums\Channel;
use App\Models\Log;

class DroptiendaSyncService extends AbstractApi
{
    protected $prefix = 'api/v1';
    protected $userId;

    public function __construct($userId)
    {
        $this->userId = $userId;
        parent::__construct();
    }

    protected function baseUrl()
    {
        $shop = \App\Models\Shop::where([
            'channel' => Channel::DROPTIENDA,
            'user_id' => $this->userId
        ])->first();

        return $shop ? $shop->url : '';
    }

    /*
     * Category Sync to Droptienda
     */
    public function storeCategory($data): array
    {
        return $this->apizRequest('categories', 'post', $data);
    }

    public function updateCategory($id, $data)
    {
//        $url = trim($this->baseUrl, '/') . '/' . $this->prefix.'/categories/' . $id;
//        $ch = curl_init();
//        curl_setopt_array($ch, array(
//            CURLOPT_URL => $url,
//            CURLOPT_CUSTOMREQUEST => "PUT",
//            CURLOPT_POSTFIELDS => $data
//        ));
//        $res = curl_exec($ch);
//        curl_close($ch);
//        trace($res);
//        return $res;

        return $this->apizRequest('categories/' . $id, 'put', $data);
    }

    public function updateCategoryTree($data)
    {
        $url = trim($this->baseUrl, '/') . '/' . $this->prefix.'/change_category_parent';
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $data
        ));
        $res = curl_exec($ch);
        curl_close($ch);
        return $res;
    }

    public function deleteCategory($id)
    {
        $url = trim($this->baseUrl, '/') . '/' . $this->prefix.'/categories/' . $id;
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => "DELETE"
        ));
        $res = curl_exec($ch);
        curl_close($ch);
        return $res;
//        return $this->apizRequest('categories/' . $id, 'delete');
    }

    /*
     * Sync Product to Droptienda
     */
    public function storeProduct($data): array
    {
//        return $this->apizRequest('products', 'post', $data);
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => trim($this->baseUrl, '/') . '/' . $this->prefix.'/products/',
            CURLOPT_HTTPHEADER => array("accept: application/json", "content-type: application/json"),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($data)
        ));
        $res = curl_exec($ch);
        curl_close($ch);

        return json_decode($res, true);
    }


    public function updateProduct($id, $data): array
    {
        return $this->apizRequest('products/' . $id, 'put', $data);
    }

    public function deleteProduct($id): array
    {
        return $this->apizRequest('products/' . $id, 'delete');
    }

    /* Pull history */
    public function notifyForSync(): array
    {
        return $this->apizRequest('sync-to-drm', 'get', []);
    }

    public function apizRequest($uri, $method = 'post', $data = []): array
    {
        $response = $this->json($data)->{$method}($uri);
        trace(json_encode($response));
        if ($response->getStatusCode() === 200) {
            return $response()->toArray();
        }
        return [];
    }
}
