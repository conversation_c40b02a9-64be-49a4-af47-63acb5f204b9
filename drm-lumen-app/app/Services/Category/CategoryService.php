<?php


namespace App\Services\Category;


use App\Enums\DroptiendaSyncEvent;
use App\Enums\DroptiendaSyncType;
use App\Models\Catalog\Category;
use App\Models\ChannelProductCategory;
use App\Models\ChannelUserCategory;
use App\Models\DroptiendaSyncHistory;
use App\Services\BaseService;
use App\Services\DroptiendaSyncService;
use Illuminate\Support\Arr;

class CategoryService extends BaseService
{
    public function all(array $filters = [])
    {
        $query = ChannelUserCategory::query();

        $limit = Arr::get($filters, 'limit', 20);

        return $limit != '-1' ? $query->paginate($limit) : $query->get();
    }

    public function getById($id)
    {
        return ChannelUserCategory::find($id);
    }

    public function store(array $data)
    {
        return $this->saveCategory($data);
    }

    public function storeToDrm(array $data): array
    {
        $data['category_name_de'] = $data['category_name'] ?? $data['category_name_de'];

        $category = Category::updateOrCreate([
            'user_id' => $data['user_id'],
            'country_id' => $data['country_id'],
            'category_name_'.$data['lang'] => $data['category_name_'.$data['lang']]
        ],$data);

        return $category->toArray() ?? array();
    }

    public function update($id, array $data)
    {
        return $this->saveCategory($data, $id);
    }

    public function destroy($id)
    {
        $product_categories = ChannelProductCategory::where([
            'category_id' => $id,
            'category_type' => 1
        ])->get();

        foreach ($product_categories as $product_category) {
            $product_category->delete();
        }
        return ChannelUserCategory::find($id)->delete();
    }

    private function saveCategory($data, $id = null)
    {
        $data['category_name'] = convert_utf8($data['category_name']);
        $category = ChannelUserCategory::firstOrNew(['id' => $id]);
        $category->fill($data);
        $paths = array_merge([$data['category_name']],$this->getParentCategory($category));
        $full_path = implode(' > ',array_reverse($paths));
        $category->full_path = $full_path;
        $category->save();
        return $category;
    }

    private function getParentCategory($category): array
    {
        $paths = array();
        if($category->parent_category){
            $parent_category = $category->parent_category;
            $paths[] = $parent_category->category_name;
            $paths = array_merge($paths,$this->getParentCategory($parent_category));
        }
        return $paths;
    }

    public function syncCategoryToDroptienda(DroptiendaSyncHistory $syncHistory)
    {
        $droptiendaSyncService = new DroptiendaSyncService($syncHistory->user_id);
        switch ($syncHistory->sync_event) {
            case DroptiendaSyncEvent::CREATE:
                try {
                    $category = ChannelUserCategory::find($syncHistory->model_id);
                    if ($category) {
                        $parent_id = 0;
                        If($category->parent){
                            $parent_category = ChannelUserCategory::find($category->parent);
                            $parent_id = $parent_category->shop_category_id ?? 0;
                        }

                        $response = $droptiendaSyncService->storeCategory([
                            'title' => $category->category_name,
                            'drm_ref_id' => $category->id,
                            'parent_id' => $parent_id,
                            'is_hidden' => 1
                        ]);

                        if (!empty($response['id']))
                        {
                            $syncHistory->update(['synced_at' => date('Y-m-d H:i:s'), 'response' => json_encode($response)]);
                            $category->shop_category_id = $response['id'];
                            $category->save();

                            $child = $this->getChildCategories($category->id,$syncHistory->user_id);
                            if(count($child)){
                                $this->syncChildCategory($child,$syncHistory->user_id,DroptiendaSyncEvent::CREATE);
                            }
                        }
                    }
                    $syncHistory->increment('tries');
                } catch (\Exception $e) {
                    $syncHistory->update([
                        'exception'=>json_encode($e->getMessage()),
                        'tries' => $syncHistory->tries + 1
                    ]);
                }

                break;

            case DroptiendaSyncEvent::UPDATE:
                try {
                    $category = ChannelUserCategory::find($syncHistory->model_id);
                    if ($category) {
                        $parent_id = 0;
                        If($category->parent){
                            $parent_category = ChannelUserCategory::find($category->parent);
                            $parent_id = $parent_category->shop_category_id ?? 0;
                        }

                        $response = $droptiendaSyncService->updateCategory($category->id, [
                            'title' => $category->category_name,
                            'parent_id' => $parent_id
                        ]);

                        if (!empty($response['id'])) {
                            $syncHistory->update(['synced_at' => date('Y-m-d H:i:s'), 'response' => json_encode($response)]);

                            $category->shop_category_id = $response['id'];
                            $category->save();

                            $child = $this->getChildCategories($category->id,$syncHistory->user_id);
                            if(count($child)){
                                $this->syncChildCategory($child,$syncHistory->user_id,DroptiendaSyncEvent::UPDATE);
                            }

                        }
                    }
                    $syncHistory->increment('tries');
                } catch (\Exception $e) {
                    $syncHistory->update([
                        'exception'=>json_encode($e->getMessage()),
                        'tries' => $syncHistory->tries + 1
                    ]);
                }

                break;


            case DroptiendaSyncEvent::UPDATE_TREE:
                try {
                    $category = ChannelUserCategory::find($syncHistory->model_id);
                    $meta = $syncHistory->meta;

                    if(!is_array($meta)){
                        $meta = json_decode($meta,true);
                    }

                    if ($category) {
                        $response = $droptiendaSyncService->updateCategoryTree([
                            'old_parent_id'  => $meta['old_parent'],
                            'new_parent_id' => $meta['new_parent'],
                            'id'             => $category->shop_category_id
                        ]);
                        $syncHistory->update(['synced_at' => date('Y-m-d H:i:s'), 'response' => json_encode($response)]);

                        $products = ChannelProductCategory::where([
                            'category_id'   => $category->id
                        ])->pluck('channel_product_id')->toArray();

                        if(count((array)$products)){
                            $this->refreshConnection($products,$category->user_id);
                        }
                    }
                    $syncHistory->increment('tries');
                } catch (\Exception $e) {
//                    throw $e;
                    $syncHistory->update([
                        'exception'=>json_encode($e->getMessage()),
                        'tries' => $syncHistory->tries + 1
                    ]);
                }

                break;

            case DroptiendaSyncEvent::DELETE:
                try {
                    $response = $droptiendaSyncService->deleteCategory($syncHistory->model_id);
                    $syncHistory->update([
                        'synced_at' => date('Y-m-d H:i:s'),
                        'response'  => json_encode($response),
                        'tries'     => $syncHistory->tries + 1,
                    ]);
                } catch (\Exception $e) {
                    $syncHistory->update([
                        'exception'=>json_encode($e->getMessage()),
                        'tries' => $syncHistory->tries + 1
                    ]);
                }
                break;
        }
    }

    public function refreshConnection($products,$user_id)
    {
        foreach ($products as $product)
        {
            DroptiendaSyncHistory::create([
                'sync_type' => DroptiendaSyncType::PRODUCT,
                'sync_event' => DroptiendaSyncEvent::UPDATE,
                'model_id' => $product,
                'user_id' => $user_id,
            ]);
        }
    }

    public function getChildCategories($category_id,$user_id)
    {
        return ChannelUserCategory::where([
            'user_id' => $user_id,
            'parent' => $category_id
        ])->pluck('id')->toArray();
    }

    public function syncChildCategory($ids,$user_id,$event)
    {
        foreach ($ids as $id)
        {
            $syncHistory = DroptiendaSyncHistory::create([
                'sync_type' => DroptiendaSyncType::CATEGORY,
                'sync_event' => $event,
                'model_id' => $id,
                'user_id' => $user_id,
            ]);

            $this->syncCategoryToDroptienda($syncHistory);
        }
    }
}
