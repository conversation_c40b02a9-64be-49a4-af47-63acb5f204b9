<?php

namespace App\Services;

use App\Models\Shop;
use Illuminate\Support\Facades\Http;

class DropmatixApiService
{
    private string $token;
    private string $secret;
    public function __construct($token, $secret) {
        $this->token = $token;
        $this->secret = $secret;
    }
    public function transfer($data, $resourceType, $event)
    {
        $url = config('app.dropmatix_url') . '/dt/'.$resourceType.'/'.$event;
        Http::withHeaders([
            'Content-Type' => 'application/json',
            'shop_token' => $this->token,
            'shop_secret' => $this->secret
        ])->post($url, $data);
    }
}
