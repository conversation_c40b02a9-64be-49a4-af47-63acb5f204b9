<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use <PERSON><PERSON>\Lumen\Console\Kernel as ConsoleKernel;
use App\Http\Controllers\Api\V1\Droptienda\ActionController;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\DTSync::class,
        Commands\DTPull::class,
        Commands\DTConnectionCheck::class,
        Commands\DTUserLockUnlock::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('dt:sync')->everyMinute();
        $schedule->command('dt:pull')->hourly();
        $schedule->command('dt:connection')->dailyAt('4.00');
        $schedule->command('dt:shoplock')->dailyAt('2.00');
    }
}
