<?php

namespace App\Console\Commands;
use Illuminate\Console\Command;
use App\Http\Controllers\Api\V1\Droptienda\ActionController;

class DTUserLockUnlock extends Command
{
    protected $signature = 'dt:shoplock';

    protected $description = 'Check all DT shop user lock/unlock status';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        ActionController::dtShopLockUnlock();
        ActionController::dtShopProductCheck();
    }
}
