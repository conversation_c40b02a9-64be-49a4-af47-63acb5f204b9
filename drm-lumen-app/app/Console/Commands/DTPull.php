<?php

namespace App\Console\Commands;

use App\Jobs\DroptiendaNotifyJob;
use App\Jobs\DroptiendaSyncJob;
use App\Models\DroptiendaPullHistory;
use App\Models\DroptiendaSyncHistory;
use App\Modules\Adapter\Jobs\PendingExports\PrepareSyncPendingProducts;
use App\Services\AdapterService;
use App\Services\Product\ProductService;
use Illuminate\Console\Command;

class DTPull extends Command
{
    protected $signature = 'dt:pull';

    protected $description = 'Pull from DT shop';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $pullHistories = DroptiendaPullHistory::whereNull('synced_at')
            ->where('tries', '<', 3)
            ->limit(env('SYNC_ITEM_LIMIT', 50))
            ->orderBy('id','desc')
            ->get();

        foreach ($pullHistories as $pullHistory) {
            dispatch_now(new DroptiendaNotifyJob($pullHistory));
        }
    }
}
