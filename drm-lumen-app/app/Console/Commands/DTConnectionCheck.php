<?php

namespace App\Console\Commands;
use Illuminate\Console\Command;
use App\Http\Controllers\Api\V1\Droptienda\ActionController;

class DTConnectionCheck extends Command
{
    protected $signature = 'dt:connection';

    protected $description = 'Check all DT shop connection status';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        ActionController::dtConnectionCheck();
        ActionController::getCurrency();
    }
}
