<?php

namespace App\Console\Commands;

use App\Jobs\DroptiendaSyncJob;
use App\Models\DroptiendaSyncHistory;
use App\Modules\Adapter\Jobs\PendingExports\PrepareSyncPendingProducts;
use App\Services\AdapterService;
use App\Services\Product\ProductService;
use Illuminate\Console\Command;

class DTSync extends Command
{
    protected $signature = 'dt:sync';

    protected $description = 'SYNC DT';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $syncHistories = DroptiendaSyncHistory::whereNull('synced_at')
            ->where('tries', '<', 3)
            ->limit(env('SYNC_ITEM_LIMIT', 50))
            ->orderBy('id','desc')
            ->get();

        foreach ($syncHistories as $syncHistory) {
            switch ($syncHistory->sync_type) {
                case \App\Enums\DroptiendaSyncType::CATEGORY:
                    app(\App\Services\Category\CategoryService::class)->syncCategoryToDroptienda($syncHistory);
                    break;

                case \App\Enums\DroptiendaSyncType::PRODUCT:
                    app(ProductService::class)->syncProductToDroptienda($syncHistory);
                    break;
            }
        }
    }
}
