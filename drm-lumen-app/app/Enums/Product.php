<?php

namespace App\Enums;

interface Product
{
    const PARAMS = [
        'product_id' => 'drm_products.id',
        'feed_id' => 'drm_products.drm_import_id',
        'user' => 'drm_products.user_id',
        'country' => 'drm_products.country_id',
        'suplier' => 'delivery_company_id'
    ];

    const RETURNS = [
        'drm_products.id',
        'drm_products.country_id',
        'drm_products.ean',
        'drm_products.delivery_company_id as suplier',
        'drm_products.item_number',
        'drm_products.ek_price',
        'drm_products.vk_price',
        'drm_products.stock',
        'drm_products.image',
        'drm_products.title',
        'drm_products.description',
        'drm_products.status',
        'drm_products.tags',
    ];

    const UPDATE_STATUS = [
        'title' => 1,
        'description' => 1,
        'image' => 1,
        'ek_price' => 1,
        'stock' => 1,
        'status' => 1,
        'gender' => 1,
        'item_weight' => 1,
        'item_color' => 1,
        'production_year' => 1,
        'materials' => 1,
        'brand' => 1,
        'item_size' => 1,
        'category' => 1,
        'delivery_days' => 1,
        'uvp' => 1
    ];
}
