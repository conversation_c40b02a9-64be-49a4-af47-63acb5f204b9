<?php

namespace App\Enums;

abstract class ZapierTriggerMapping
{
    const NEW_CONTACT = 'new_contact';
    const UPDATE_CONTACT = 'update_contact';
    const NEW_INVOICE_CREATED = 'new_invoice_created';
    const NEW_ORDER = 'new_order';
    const NEW_LEAD = 'new_lead';
    const NEW_SUB_USER_IS_CREATED = 'new_sub_user_is_created';
    const NEW_IMPORT_DONE = 'new_import_done';
    const ADD_NEW_PRODUCT_ON_DRM = 'add_new_product_on_drm';
    const PUBLISH_PRODUCT_ON_CHANNEL = 'publish_product_on_channel';
    const UPDATE_PRODUCT_FEED = 'update_product_feed';
    const ERROR_NOTIFICATION = 'error_notification';
    const ADD_NEW_CHANNEL = 'add_new_channel';
    const STATI_UPDATE_CANCEL = 'stati_update_cancel';
    const STATI_UPDATE_INKASSO_COLLECTION = 'stati_update_inkasso_collection';
    const STATI_UPDATE_SHIPPED = 'stati_update_shipped';
}
