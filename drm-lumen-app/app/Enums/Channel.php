<?php


namespace App\Enums;


abstract class Channel
{
    const GAMBIO = 1;
    const _GAMBIO = 'gambio';

    const LENGOW = 2;
    const _LENGOW = 'lengow';

    const YATEGO = 3;
    const _YATEGO = 'yatego';

    const EBAY  = 4;
    const _EBAY = 'e_bay';

    const AMAZON  = 5;
    const _AMAZON = 'amazon';

    const SHOPIFY  = 6;
    const _SHOPIFY = 'shopify';

    const WOOCOMMERCE = 7;
    const _WOOCOMMERCE = 'woo_commerce';

    const CLOUSALE = 8;
    const _CLOUSALE = 'clousale';

    const CHRONO24 = 9;
    const _CHRONO24 = 'chrono24';

    const DROPTIENDA = 10;
    const _DROPTIENDA = 'droptienda';

    const ETSY  = 11;
    const _ETSY= 'etsy';

    const OTTO  = 12;
    const _OTTO = 'otto';

    const KAUFLAND = 13;
    const _KAUFLAND = 'kaufland';

    const MAP = [
        self::WOOCOMMERCE => self::_WOOCOMMERCE,
        self::EBAY => self::_EBAY,
        self::AMAZON => self::_AMAZON,
        self::GAMBIO => self::_GAMBIO,
        self::SHOPIFY => self::_SHOPIFY,
        self::ETSY => self::_ETSY,
        self::OTTO => self::_OTTO,
        self::KAUFLAND => self::_KAUFLAND
    ];

    const ALL = [
        self::GAMBIO,
        self::LENGOW,
        self::YATEGO,
        self::EBAY,
        self::AMAZON,
        self::ETSY,
        self::SHOPIFY,
        self::WOOCOMMERCE,
        self::CLOUSALE,
        self::CHRONO24,
        self::DROPTIENDA,
        self::OTTO,
        self::KAUFLAND
    ];

    const REST_API = [
        self::EBAY,
        self::AMAZON,
        self::SHOPIFY,
        self::WOOCOMMERCE,
        self::ETSY,
        self::OTTO,
        self::KAUFLAND
    ];

    const MAPPING_CHANNELS = [
        self::ETSY,
        self::EBAY,
        self::AMAZON,
        self::YATEGO,
        self::OTTO,
        self::KAUFLAND
    ];

    const CSV_CHANNELS = [
        self::LENGOW,
        self::YATEGO,
        self::CLOUSALE,
        self::CHRONO24
    ];

    const CATEGORY_LIMITS = [
        self::ETSY => 1,
        self::EBAY => 1,
        self::AMAZON => 1,
        self::YATEGO => 3,
        self::OTTO => 1,
        self::KAUFLAND => 1
    ];

    const EXPORT = 'export';
    const DELETE = 'delete';

}
