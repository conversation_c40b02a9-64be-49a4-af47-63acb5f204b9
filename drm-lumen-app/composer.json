{"name": "laravel/lumen", "description": "The Laravel Lumen Framework.", "keywords": ["framework", "laravel", "lumen"], "license": "MIT", "type": "project", "require": {"php": "^7.4", "ext-iconv": "*", "ext-json": "*", "darkaonline/swagger-lume": "8.*", "dusterio/lumen-passport": "^0.3.1", "firebase/php-jwt": "^5.2", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "6.5.5", "illuminate/redis": "^8.25", "laravel/lumen-framework": "^8.0", "nahid/apiz": "3.0.0", "predis/predis": "^1.1", "league/flysystem": " ~1.0", "league/flysystem-aws-s3-v3": "~1.0", "ext-curl": "*"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.8", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^9.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/ImageUpload.php", "app/Supports/helpers.php"]}, "autoload-dev": {"classmap": ["tests/"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}