<?php

/** @var Router $router */

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It is a breeze. Simply tell <PERSON><PERSON> the URIs it should respond to
| and give it the Closure to call when that URI is requested.
|
*/

use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use Laravel\Lumen\Routing\Router;

use Illuminate\Http\Request;

$router->get('/', function () use ($router) {
    return $router->app->version();
});

$router->get('info', function () {
    return phpinfo();
});

$router->get('flush_redis', function () {
    $redis = Redis::connection();
    $redis->set('test_key','abcd');

    $test = $redis->get('test_key');
    dd($test);

    Redis::flushDB();
    Redis::flushDB();
});

$router->get('test', 'ExampleController@test');


$router->get('/key', function () {
    return Str::random(32);
});

$router->get('/unauthorized', function () {
    return response()->json(["success" => false, "message" => "unauthorized"], 400);
});

// Download protectedshops file
$router->get('shop-document/{shopId}/{type}/{contentFormat}/stream', 'Api\V1\ProfileController@downloadProtectedShopDocument');

$router->group(['prefix' => 'api/v1'], function ($router) {
    /*
     |--------------------------------------------------------------------------
     | Catalog
     |--------------------------------------------------------------------------
     |
     | System catalog info description goes on
     |
     */
    // START::Droptienda Sync route
//     $router->get('/categories/{sync_event}', 'Api\V1\Droptienda\CategoryApiController@syncCategory
    $router->post('/dt-export-sync', 'Api\V1\Droptienda\ActionController@dtExportSync');
    $router->post('/dt-product-data', 'Api\V1\Zapier\ActionController@getProductData');
    $router->post('/dt-tag-insert', 'Api\V1\Zapier\ActionController@insertTag');
    $router->post('/check-purchase-template', 'Api\V1\Droptienda\TemplateApiController@checkPurchase');
    $router->post('/dt-product-import-data', 'Api\V1\Droptienda\TemplateApiController@getImportProductData');
    $router->post('/dt-license-check', 'Api\V1\Droptienda\TemplateApiController@dtLicense');
    $router->get('/dt-shop-connection-check', 'Api\V1\Droptienda\ActionController@dtConnectionCheck');
    $router->post('/dt-shop-connection-check-from-dt', 'Api\V1\Droptienda\ActionController@dtConnectionCheckFromDt');
    $router->get('/dt-shop-currency-sync', 'Api\V1\Droptienda\ActionController@getCurrency');
    $router->get('/dt-shop-emergency-backup', 'Api\V1\Droptienda\ActionController@dtEmergencyBackup');
    $router->post('/dt-shop-tariff-check', 'Api\V1\Droptienda\ActionController@dtShopTariffCheck');


    $router->get('/dt-chat-question', 'Api\V1\Droptienda\CustomerChatController@getChatQuestion');
    $router->get('/dt-chat-fetch/{order_id}', 'Api\V1\Droptienda\CustomerChatController@fetch');
    $router->post('/dt-chat-send/{order_id}', 'Api\V1\Droptienda\CustomerChatController@send');
    $router->post('/dt-chat-read/{order_id}', 'Api\V1\Droptienda\CustomerChatController@read');
    $router->post('/unseen', 'Api\V1\Droptienda\CustomerChatController@unreadMessages');
    $router->post('/dt-product-stock-request', 'Api\V1\Droptienda\CustomerChatController@productStockRequest');

    $router->post('/dt-set-icon', 'Api\V1\Droptienda\ProductApiController@setIcon');

    // test order limit
    $router->post('/test-order-limit', 'Api\V1\Droptienda\CustomerChatController@testOrderCount');


    $router->post('/dt-suppliers', 'Api\V1\Droptienda\CustomerChatController@suppliers');
    $router->post('/dt-suppliers/{id}', 'Api\V1\Droptienda\CustomerChatController@supplierDetail');
    $router->post('/dt-tax-list', 'Api\V1\Droptienda\CustomerChatController@taxList');


    $router->get('/optin-box-templates', 'Api\V1\Droptienda\OptinBoxApiController@getTemplates');
    $router->get('/protected-shop', 'Api\V1\Droptienda\OptinBoxApiController@protectedShop');
    $router->post('/get-brand-logo', 'Api\V1\Droptienda\ActionController@getBrandLogo');


    $router->group(['prefix' => 'sync'], function ($router) {
        /* Sync Droptienda Categories */
        $router->get('/categories', 'Api\V1\Droptienda\CategoryApiController@index');
        $router->get('/categories/{id}', 'Api\V1\Droptienda\CategoryApiController@show');
        $router->post('/categories', 'Api\V1\Droptienda\CategoryApiController@store');
        $router->put('/categories/{id}', 'Api\V1\Droptienda\CategoryApiController@update');
        $router->delete('/categories/{id}', 'Api\V1\Droptienda\CategoryApiController@destroy');

        /* Sync Droptienda Products */
        $router->get('/products', 'Api\V1\Droptienda\ProductApiController@index');
        $router->post('/products', 'Api\V1\Droptienda\ProductApiController@store');
        $router->put('/products/{id}', 'Api\V1\Droptienda\ProductApiController@update');
        $router->delete('/products/{id}', 'Api\V1\Droptienda\ProductApiController@delete');

        /* Sync Droptienda Customers */
        $router->get('/customers', 'Api\V1\Droptienda\CustomerApiController@index');
        $router->get('/customers/{id}', 'Api\V1\Droptienda\CustomerApiController@show');
        $router->post('/customers', 'Api\V1\Droptienda\CustomerApiController@store');
        $router->put('/customers/{id}', 'Api\V1\Droptienda\CustomerApiController@update');
        $router->delete('/customers/{id}', 'Api\V1\Droptienda\CustomerApiController@delete');

        $router->post('/store/cms_user_info', 'Api\V1\Droptienda\CustomerApiController@droptiendaNewCustomer');

        $router->post('/store/droptineda-customer-group-title', 'Api\V1\Droptienda\CustomerApiController@droptiendaCustomerGroupTitle');
        $router->get('/get-tariff-order-info', 'Api\V1\Droptienda\CustomerApiController@getTariffOrderInfo');

        /* Sync Droptienda Orders */
        $router->get('/orders', 'Api\V1\Droptienda\OrderApiController@index');
        $router->get('/orders/{id}', 'Api\V1\Droptienda\OrderApiController@show');
        $router->post('/orders', 'Api\V1\Droptienda\OrderApiController@store');
        $router->post('/orderstest', 'Api\V1\Droptienda\OrderApiController@storeTest');
        $router->put('/orders/{id}', 'Api\V1\Droptienda\OrderApiController@update');
        $router->post('/get-dt-order-status', 'Api\V1\Droptienda\ActionController@getOrderStatus');
        $router->post('/update-order-status', 'Api\V1\Droptienda\ActionController@updateOrderStatus');
        $router->post('/update-dt-tax-setting', 'Api\V1\Droptienda\ActionController@updateDTTaxSetting');
        $router->post('/get-marketing-tag', 'Api\V1\Droptienda\ActionController@getMarketingTag');

        /* Sync request from DT */
        $router->post('/pull-history', 'Api\V1\Droptienda\PullHistoryApiController@store');


        $router->post('create-customer', function (Request $request) {
            $customer = new \App\Models\NewCustomer();

            $customer->full_name = $request->name ?? '';
            $customer->email = $request->email ?? '';
            $customer->phone = $request->phone ?? '';
            $customer->website = $request->website ?? '';
            $customer->currency = $request->currency ?? '';
            $customer->address = $request->address ?? '';
            $customer->city = $request->city ?? '';
            $customer->state = $request->state ?? '';
            $customer->zip_code = $request->zip_code ?? '';

            $customer->save();

            return $customer;
        });
    });


    $router->group(['prefix' => 'catalogs'], function ($router) {
        /*
        |--------------------------------------------------------------------------
        | Products CRUD
        |--------------------------------------------------------------------------
        */

        $router->get('/find_category', 'Api\V1\Catalog\CategoryController@getByName');

        $router->group(['prefix' => 'products'], function ($router) {
            $router->get('/', 'Api\V1\Catalog\ProductsController@getProducts');
            $router->get('/{pid}', 'Api\V1\Catalog\ProductsController@getProduct');
            $router->put('/', 'Api\V1\Catalog\ProductsController@createProduct');
            $router->put('/{ean}/update', 'Api\V1\Catalog\ProductsController@updateProduct');
            $router->delete('/{pid}', 'Api\V1\Catalog\ProductsController@deleteProduct');
        });

        /*
        |--------------------------------------------------------------------------
        | Category CRUD
        |--------------------------------------------------------------------------
        */
        $router->group(['prefix' => 'categories'], function ($router) {
            $router->get('/', 'Api\V1\Catalog\CategoryController@index');
            $router->get('/{cid}', 'Api\V1\Catalog\CategoryController@show');
            $router->post('/', 'Api\V1\Catalog\CategoryController@store');
            $router->put('/{cid}', 'Api\V1\Catalog\CategoryController@update');
            $router->delete('/{cid}', 'Api\V1\Catalog\ProductsController@deleteCategory');


        });

        /*
         * --------------------------------------------------------------------------
         * Country
         * --------------------------------------------------------------------------
         */
        $router->group(['prefix' => 'countries'], function ($router) {
            $router->get('/', 'Api\V1\Catalog\CountryController@index');
        });

        /*
         * --------------------------------
         *  Customer
         * --------------------------------
         */
        $router->group(['namespace' => 'Api\V1\Catalog', 'prefix' => 'customers'], function ($router) {
            $router->get('/', 'CustomerController@getUserCustomers');
        });
    });

    /*
    |--------------------------------------------------------------------------
    | Sales
    |--------------------------------------------------------------------------
    |
    | System sales info description goes on
    |
    */
    $router->group(['prefix' => 'sales'], function ($router) {
        /*
        |--------------------------------------------------------------------------
        | Orders CRUD
        |--------------------------------------------------------------------------
        */
        $router->group(['prefix' => 'orders'], function ($router) {
            $router->get('/', 'Api\V1\Sales\OrdersController@index');
            $router->get('/{id}', 'Api\V1\Sales\OrdersController@show');
        });

        /*
        |--------------------------------------------------------------------------
        | Customers CRUD
        |--------------------------------------------------------------------------
        */
        $router->group(['middlewares' => 'auth', 'prefix' => 'customers'], function ($router) {
            $router->get('/', 'Api\V1\Sales\CustomersController@index');
            $router->get('/{id}', 'Api\V1\Sales\CustomersController@show');
        });
    });

    /*
    |--------------------------------------------------------------------------
    | User
    |--------------------------------------------------------------------------
    |
    | User info description goes on
    |
    */
    $router->group(['prefix' => 'users'], function ($router) {
        $router->get('/profile', 'ProfileController@profile');
    });

    $router->group(['prefix' => 'oauth2'], function ($router) {
        $router->post('/token', 'Api\V1\Auth\LoginController@issueToken');
    });

    $router->post('/droptienda-activation', 'Api\V1\Auth\LoginController@droptiendaActivation');
    $router->post('/droptienda-new-activation', 'Api\V1\Auth\LoginController@droptiendaNewActivation');
    $router->post('/user-register-new-shop', 'Api\V1\Auth\LoginController@userRegisterNewShop');
    $router->post('/create-new-shop', 'Api\V1\Auth\LoginController@createShop');
    $router->post('/uid-url-update', 'Api\V1\Auth\LoginController@uidUrlUpdate');
    $router->get('/dtLockUnlockShop', 'Api\V1\Droptienda\ActionController@dtShopLockUnlock');

    /**
     * ZAPIER ROUTES
     */
    $router->group(['namespace' => 'Api\V1\Zapier', 'prefix' => 'zapier', 'middleware' => ['zapier']], function ($router) {
        $router->post('/oauth2/token', 'AuthController@issueToken');

        $router->group(['prefix' => 'triggers'], function ($router) {
            $router->post('/new-contact', 'TriggerController@newContact');
            $router->post('/new-order', 'TriggerController@newOrder');
            $router->post('/new-channel', 'TriggerController@newChannel');
            $router->post('/publish-product-on-channel', 'TriggerController@publishProductOnChannel');
            $router->post('/new-product-on-drm', 'TriggerController@newProductOnDrm');
            $router->post('/new-lead', 'TriggerController@newLead');
            $router->post('/new-subuser', 'TriggerController@newSubuser');
            $router->post('/update-contact', 'TriggerController@updateContact');
            $router->post('/new-import', 'TriggerController@newImport');
            $router->post('/new-invoice', 'TriggerController@newInvoice');
            $router->post('/update-product-feed', 'TriggerController@updateProductFeed');
            $router->post('/error-notification', 'TriggerController@errorNotification');
            $router->post('/manual-notification', 'TriggerController@manualNotification');
            $router->post('/status-update-cancel', 'TriggerController@statusUpdateCancel');
            $router->post('/status-update-inkasso', 'TriggerController@statusUpdateInkasso');
            $router->post('/status-update-shipped', 'TriggerController@statusUpdateShipped');
            $router->post('/all-tag', 'TriggerController@allTag');
            $router->post('/all-project', 'TriggerController@allProject');
            $router->post('/all-cards', 'TriggerController@allCards');
            //$router->post('/new-task', 'TriggerController@createNewTask');
            $router->get('/send-droptienda-message', 'TriggerController@sendDroptiendaMessage');

        });

        $router->group(['prefix' => 'actions'], function ($router) {
            $router->post('/update-customer', 'ActionController@updateCustomer');
            $router->post('/create-task', 'ActionController@createNewTask');
            $router->post('/create-order', 'ActionController@createOrder');
            $router->post('/create-channel-product', 'ActionController@createProduct');
            $router->post('/store-file-to-archive', 'ActionController@addDocumentToArchive');
            $router->post('/create-droptienda-message', 'ActionController@createDroptiendaMessage');
        });
    });
    // ZAPIER END

    $router->group(['namespace' => 'Api\V1\Sales', 'prefix' => 'custom'], function ($router) {
        $router->get('/catalogs/products', 'CustomOrdersController@getProducts');
        $router->get('/catalogs/notifications', 'CustomOrdersController@getNotifications');
        $router->get('/dashboard', 'CustomOrdersController@dashboard');
        $router->get('/sales/orders', 'CustomOrdersController@customIndex');
        $router->get('/sales/order/{id}', 'CustomOrdersController@singleOrder');
        $router->get('/sales/customers', 'CustomOrdersController@getCustomers');
        $router->get('/sales/orders/sync', 'CustomOrdersController@syncOrderData');
        $router->post('/sales/orders/sync-retry', 'CustomOrdersController@retryOrderSync');
        $router->post('/firebase/token', 'CustomOrdersController@updateFirebaseDeviceToken');
    });

    //Accounting module
    $router->group(['prefix' => 'accounting'], function ($router) { //'middleware' => ['custom_auth','api_model'],

        $router->get('/', 'Api\V1\Accounting\AccountingController@getIndex');
        $router->post('/add', 'Api\V1\Accounting\AccountingController@add');
        $router->get('/supplier', 'Api\V1\Accounting\AccountingController@supplier');
        $router->get('/category', 'Api\V1\Accounting\AccountingController@category');

        $router->post('/add-archive', 'Api\V1\Accounting\AccountingController@fileUloadToCache');
        $router->get('/archive', 'Api\V1\Accounting\AccountingController@archiveFiles');
        $router->get('/tax-rates', 'Api\V1\Accounting\AccountingController@taxRates');
    });

    $router->group(['prefix' => 'otto'], function ($router) {
        $router->post('/login', 'Api\V1\Otto\AccessTokenController@login');
//        $router->get('/token', 'Api\V1\Otto\AccessTokenController@access_token');
//        $router->get('/categories', 'Api\V1\Otto\AccessTokenController@getCategories');
//        $router->get('/orders', 'Api\V1\Otto\AccessTokenController@getOrders');
//        $router->get('/brands', 'Api\V1\Otto\AccessTokenController@getBrands');
//        $router->get('/products', 'Api\V1\Otto\AccessTokenController@getMarketplaceStatus');
//        $router->post('/products', 'Api\V1\Otto\AccessTokenController@createOrUpdateProduct');
//        $router->get('/product', 'Api\V1\Otto\AccessTokenController@getSingleProduct');
//        $router->get('/test-orders', 'Api\V1\Otto\AccessTokenController@testOrders');
    });

    $router->get('/getCategoryFromDT', 'Sync\EventSyncController@getCategoryFromDT');
    $router->get('/getProductFromDT', 'Sync\EventSyncController@getProductFromDT');

    $router->group(['middleware' => "App\Http\Middleware\CustomAuthMiddleware", 'prefix' => 'device-token'], function ($router) {
        $router->post('/update', 'Api\V1\Auth\FirebaseDeviceTokenController@update');
    });
    $router->post('/notify', 'Api\V1\Auth\FirebaseDeviceTokenController@notify');

    /*
    |--------------------------------------------------------------------------
    | Trello
    |--------------------------------------------------------------------------
    |
    | Trello App style DRM copy for mobile app
    |
    */
    $router->group(['middleware' => ['custom_auth', 'api_model'], 'namespace' => 'Api\V1\Trello', 'prefix' => 'trello'], function ($router) {
        $router->get('/card-tasks', "TrelloCardController@cardTask");
        $router->group(['prefix' => 'projects'], function ($router) {
            $router->get('/', "TrelloProjectController@index");
            $router->get('/activity/{id}', "TrelloProjectController@getProjectActivity");
            $router->get('/all-members', 'TrelloProjectController@getAllMembers');
            $router->post('/store', "TrelloProjectController@store");
            $router->get('/{id}', "TrelloProjectController@show");
            $router->put('/{id}', "TrelloProjectController@update");
            $router->delete('/{id}', "TrelloProjectController@destroy");
        });
        $router->group(['prefix' => 'cards'], function ($router) {
            $router->post('/', "TrelloCardController@store");
//            $router->get('/{id}', "TrelloCardController@show");
            $router->put('/{id}', "TrelloCardController@update");
            $router->delete('/{id}', "TrelloCardController@destroy");
        });
        $router->group(['prefix' => 'tasks'], function ($router) {
            $router->get('/{id}', "TrelloTaskController@index");
            $router->post('/', "TrelloTaskController@store");
            $router->post('/position-update', "TrelloTaskController@updatePositions");
            $router->put('/{id}', "TrelloTaskController@update");
            $router->delete('/{id}', "TrelloTaskController@destroy");
        });
        $router->put('/tasks-position', "TrelloTaskController@updatePositions");
        $router->put('/cards-position', "TrelloCardController@updatePositions");
        $router->group(['prefix' => 'tasks-checklists'], function ($router) {
//            $router->get('/', "TrelloChecklistController@index");
            $router->post('/', "TrelloChecklistController@store");
//            $router->get('/{id}', "TrelloChecklistController@show");
            $router->put('/{id}', "TrelloChecklistController@update");
            $router->delete('/{id}', "TrelloChecklistController@destroy");
        });
        $router->group(['prefix' => 'task-comments'], function ($router) {
            $router->get('/', "TrelloTaskCommentController@index");
            $router->post('/', "TrelloTaskCommentController@store");
//            $router->get('/{id}', "TrelloTaskCommentController@show");
            $router->put('/{id}', "TrelloTaskCommentController@update");
            $router->delete('/{id}', "TrelloTaskCommentController@destroy");
        });
//        $router->group(['prefix' => 'files'], function ($router) {
//            $router->post('/image', "TrelloFileController@storeImage");
//            $router->post('/audio', "TrelloFileController@storeAudio");
//        });
    });
});

