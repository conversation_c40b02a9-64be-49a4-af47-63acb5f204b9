<?php

/** @var Router $router */

/*
|--------------------------------------------------------------------------
| Product CRUD
|--------------------------------------------------------------------------
|
*/

use Illuminate\Support\Str;
use Laravel\Lumen\Routing\Router;



$router->get('/product','ProductsController@getProducts');
$router->get('/product/{pid}','ProductsController@getProduct');
$router->put('/product','ProductsController@createProduct');
$router->put('/product/{pid}/update','ProductsController@updateProduct');
$router->delete('/product/{pid}','ProductsController@deleteProduct');



/*
|--------------------------------------------------------------------------
| Category CRUD
|--------------------------------------------------------------------------
|
*/

$router->get('/category','CategoryController@getCategories');
$router->get('/category/{cid}','CategoryController@getCategory');
$router->put('/category','ProductsController@createCategory');
$router->put('/category/{cid}/update','ProductsController@updateCategory');
$router->delete('/category/{cid}','ProductsController@deleteCategory');



$router->get('/key', function () {
    return Str::random(32);
});

$router->get('/profile', 'ProfileController@profile');

$router->get('/orders', 'OrderController@all');

