{"info": {"name": "Dropmatix API - Sandbox & Live", "description": "Comprehensive API testing collection for Dropmatix sandbox and live environments", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "https://your-domain.com/api", "type": "string"}, {"key": "supplier_token", "value": "your_supplier_api_token_here", "type": "string"}, {"key": "customer_token", "value": "your_customer_token_here", "type": "string"}], "item": [{"name": "Sandbox API Tests", "description": "Tests for sandbox environment with dummy data", "item": [{"name": "Get Sandbox Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sandbox/orders", "host": ["{{base_url}}"], "path": ["sandbox", "orders"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has sandbox environment header', function () {", "    pm.expect(pm.response.headers.get('X-API-Environment')).to.eql('sandbox');", "});", "", "pm.test('Response contains dummy data only', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.environment).to.eql('sandbox');", "    ", "    if (responseJson.data && responseJson.data.length > 0) {", "        const order = responseJson.data[0];", "        if (order.billing) {", "            pm.expect(order.billing.name).to.eql('<PERSON>');", "            pm.expect(order.billing.company).to.eql('Mustermann GmbH');", "            pm.expect(order.billing.street).to.eql('Musterstraße 12');", "        }", "    }", "});"]}}]}, {"name": "Get Sandbox Order Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sandbox/orders/1", "host": ["{{base_url}}"], "path": ["sandbox", "orders", "1"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 404', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "pm.test('Response has sandbox environment header', function () {", "    pm.expect(pm.response.headers.get('X-API-Environment')).to.eql('sandbox');", "});", "", "if (pm.response.code === 200) {", "    pm.test('Order contains dummy data', function () {", "        const responseJson = pm.response.json();", "        const order = responseJson.data;", "        ", "        if (order.billing) {", "            pm.expect(order.billing.name).to.eql('<PERSON>');", "        }", "        if (order.shipping) {", "            pm.expect(order.shipping.name).to.eql('<PERSON>');", "        }", "    });", "}"]}}]}, {"name": "Get Sandbox MP Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sandbox/mp/products", "host": ["{{base_url}}"], "path": ["sandbox", "mp", "products"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has sandbox environment header', function () {", "    pm.expect(pm.response.headers.get('X-API-Environment')).to.eql('sandbox');", "});", "", "pm.test('Products are sample data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.environment).to.eql('sandbox');", "    pm.expect(responseJson.data).to.be.an('array');", "});"]}}]}]}, {"name": "Live API Tests - Supplier Access", "description": "Tests for live API with supplier token authentication", "item": [{"name": "Get Live Orders (Supplier)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{supplier_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/live/orders", "host": ["{{base_url}}"], "path": ["live", "orders"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has live environment header', function () {", "    pm.expect(pm.response.headers.get('X-API-Environment')).to.eql('live');", "});", "", "pm.test('Response contains supplier-specific data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.environment).to.eql('live');", "    pm.expect(responseJson.supplier_id).to.exist;", "});"]}}]}, {"name": "Get Supplier Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{supplier_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/live/supplier/profile", "host": ["{{base_url}}"], "path": ["live", "supplier", "profile"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has live environment header', function () {", "    pm.expect(pm.response.headers.get('X-API-Environment')).to.eql('live');", "});"]}}]}, {"name": "Get Zapier Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{supplier_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/live/supplier/zapier/status", "host": ["{{base_url}}"], "path": ["live", "supplier", "zapier", "status"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains Zapier status', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.data).to.have.property('connected');", "});"]}}]}]}, {"name": "Security Tests - Unauthorized Access", "description": "Tests to verify security and access controls", "item": [{"name": "Live API without Token (Should Fail)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/live/orders", "host": ["{{base_url}}"], "path": ["live", "orders"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 401 Unauthorized', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('Response indicates unauthorized access', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.false;", "    pm.expect(responseJson.error_code).to.eql('UNAUTHORIZED');", "});"]}}]}, {"name": "Live API with Customer <PERSON> (Should Fail)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{customer_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/live/orders", "host": ["{{base_url}}"], "path": ["live", "orders"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 401 Unauthorized', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('Response indicates supplier role required', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.false;", "    pm.expect(responseJson.message).to.include('Supplier role required');", "});"]}}]}, {"name": "<PERSON><PERSON><PERSON> (Should Fail)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer invalid_token_12345", "type": "text"}], "url": {"raw": "{{base_url}}/live/orders", "host": ["{{base_url}}"], "path": ["live", "orders"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 401 Unauthorized', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('Response indicates invalid token', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.false;", "    pm.expect(responseJson.message).to.include('Invalid or expired');", "});"]}}]}]}, {"name": "Token Management Tests", "description": "Tests for supplier API token management", "item": [{"name": "Get API Tokens", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{supplier_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/supplier/api-tokens", "host": ["{{base_url}}"], "path": ["supplier", "api-tokens"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains token list', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "    pm.expect(responseJson.data).to.be.an('array');", "});"]}}]}, {"name": "Create API Token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{supplier_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test API Token\",\n  \"expires_in_days\": 30,\n  \"abilities\": [\"*\"]\n}"}, "url": {"raw": "{{base_url}}/supplier/api-tokens", "host": ["{{base_url}}"], "path": ["supplier", "api-tokens"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains new token', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "    pm.expect(responseJson.data.plain_token).to.exist;", "    pm.expect(responseJson.data.plain_token).to.include('dmt_');", "});"]}}]}]}]}