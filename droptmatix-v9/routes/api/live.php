<?php

use App\Http\Controllers\Api\Live\LiveOrderController;
use App\Http\Controllers\Api\Live\LiveMpController;
use App\Http\Controllers\Api\Live\LiveSupplierController;
use Illuminate\Support\Facades\Route;

// Live API Routes - Token-based access for suppliers only
Route::middleware(['api.token.supplier'])->prefix('live')->group(function () {
    
    // Order endpoints
    Route::controller(LiveOrderController::class)->prefix('orders')->group(function () {
        Route::get('/', 'index')->name('live.orders.index');
        Route::get('{id}', 'show')->name('live.orders.show');
        Route::get('{id}/details', 'orderDetails')->name('live.orders.details');
        Route::put('{id}', 'update')->name('live.orders.update');
        Route::post('{id}/status', 'updateStatus')->name('live.orders.status');
        Route::post('{id}/tracking', 'saveTracking')->name('live.orders.tracking');
    });

    // Marketplace endpoints
    Route::controller(LiveMpController::class)->prefix('mp')->group(function () {
        Route::get('categories', 'categories');
        Route::get('products', 'products');
        Route::get('search-ean', 'searchEan');
        Route::get('warehouse-stock/{ean}', 'warehouseStock');
        Route::put('update-stock/{ean}', 'updateStock');
        Route::post('order/add', 'addOrder');
    });

    // Supplier-specific endpoints
    Route::controller(LiveSupplierController::class)->prefix('supplier')->group(function () {
        Route::get('profile', 'profile');
        Route::get('orders', 'orders');
        Route::get('products', 'products');
        Route::get('analytics', 'analytics');
        Route::get('zapier/status', 'zapierStatus');
        Route::get('zapier/logs', 'zapierLogs');
        Route::post('zapier/connect', 'zapierConnect');
        Route::delete('zapier/disconnect', 'zapierDisconnect');
    });

    // Token management
    Route::prefix('token')->group(function () {
        Route::get('info', [LiveSupplierController::class, 'tokenInfo']);
        Route::post('refresh', [LiveSupplierController::class, 'refreshToken']);
        Route::delete('revoke', [LiveSupplierController::class, 'revokeToken']);
    });
});
