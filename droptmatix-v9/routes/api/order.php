<?php

use App\Http\Controllers\Api\Country\CountryController;
use App\Http\Controllers\Api\DropmatxPublicApiController;
use App\Http\Controllers\Api\Order\HistoryController;
use App\Http\Controllers\Api\Order\MarketplaceOrderController;
use App\Http\Controllers\Api\Order\OrderActionController;
use App\Http\Controllers\Api\Order\OrderController;
use App\Http\Controllers\Api\Sandbox\SandboxOrderController;
use App\Http\Controllers\Orders\OrderActionController as OrderActionControllerWeb;
use App\Http\Controllers\Orders\OrderBillingEditController;
use App\Http\Controllers\Orders\OrderController as OrderControllerWeb;
use App\Http\Controllers\Orders\OrderShippingEditController;
use App\Http\Controllers\Orders\OrderTrackingController;
use App\Http\Controllers\Supplier\SupplierController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum', 'verified', 'sanctum.2fa', 'not_payment_due', 'has_tariff'])->prefix('orders')->group(function () {
    Route::get('/', [OrderController::class, 'index']);
    Route::post('status-action/{id}', [OrderActionController::class, 'statusChange']);
    Route::post('save-tracking/{id}', [OrderTrackingController::class, 'saveTracking']);
    Route::get('status-list', [OrderController::class, 'getStatusList']);
    Route::get('parcel-list', [OrderController::class, 'getParcelList']);
    Route::get('history/{id}', [HistoryController::class, 'all']);
    Route::post('history/{id}', [HistoryController::class, 'add']);
    Route::post('place-order/{id}', [OrderActionController::class, 'placeOrder']);

    Route::get('detail/{id}', [OrderController::class, 'orderInfoDetails']);
    Route::put('update/{id}', [OrderController::class, 'update']);
    Route::put('update-billing/{id}', [OrderBillingEditController::class, 'updateBilling']);
    Route::put('update-shipping/{id}', [OrderShippingEditController::class, 'updateShipping']);

    Route::get('countries', [CountryController::class, 'list']);

    Route::get('details/{id}/{next_prev?}', [OrderController::class, 'orderDetails']);
    Route::get('document/{id}/{type?}', [OrderActionControllerWeb::class, 'document']);

    Route::get('product-stock/order/{order_id}/product/{product_id}', [OrderControllerWeb::class, 'productStockQtyInfo']);
});

Route::middleware(['auth:sanctum', 'verified', 'sanctum.2fa', 'not_payment_due', 'has_tariff', 'throttle:30,1'])
    ->prefix('marketplace-orders')->group(function () {
        Route::get('/', [MarketplaceOrderController::class, 'index']);
    });

//Supplier Routes
Route::middleware(['auth:sanctum', 'verified', 'sanctum.2fa', 'not_payment_due', 'has_tariff'])
    ->controller(SupplierController::class)
    ->prefix('suppliers')->group(function () {

        //List supplier
        Route::get('/list', 'list');
    });

// Get customer order from drm
Route::post('mp-order-place', [DropmatxPublicApiController::class, 'orderPlaceOnApi']);

// Sandbox API Routes - Open access with dummy data only
Route::prefix('sandbox')->group(function () {
    Route::controller(SandboxOrderController::class)->group(function () {
        Route::get('orders', 'index')->name('sandbox.orders.index');
        Route::get('orders/{id}', 'show')->name('sandbox.orders.show');
        Route::get('orders/{id}/details', 'orderDetails')->name('sandbox.orders.details');
        Route::get('countries', 'countries')->name('sandbox.countries');
    });
});
