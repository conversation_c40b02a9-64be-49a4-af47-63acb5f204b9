<?php

use App\Http\Controllers\Api\Mp\MpController;
use App\Http\Controllers\Api\Sandbox\SandboxMpController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum', 'sanctum.2fa'])->prefix('mp')->group(function () {
    Route::controller(MpController::class)->group(function () {
        Route::get('categories', 'categories');
        Route::get('products', 'products');
        Route::get('search-ean', 'searchEan');
        Route::get('product-transfer-to-core', 'productTransferToCore');
        Route::get('add-to-cart', 'addToCart');
        Route::post('request-procurement', 'requestProcurement');
        Route::get('warehouse-stock/{ean}', 'warehouseStock');
        Route::put('update-stock/{ean}', 'updateStock');
        Route::post('save-tracking/{id}', 'saveTracking');
        Route::post('order/add', 'addOrder');
    });
});

// Sandbox MP API Routes - Open access with dummy data only
Route::prefix('sandbox/mp')->group(function () {
    Route::controller(SandboxMpController::class)->group(function () {
        Route::get('categories', 'categories');
        Route::get('products', 'products');
        Route::get('search-ean', 'searchEan');
        Route::get('warehouse-stock/{ean}', 'warehouseStock');
    });
});
