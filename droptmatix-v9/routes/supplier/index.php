<?php

use App\Http\Controllers\Supplier\ApiTokenController;
use App\Http\Controllers\Supplier\ZapierController;
use Illuminate\Support\Facades\Route;

// Supplier Dashboard Routes - Authenticated suppliers only
Route::middleware(['auth:sanctum', 'verified', 'sanctum.2fa', 'role:supplier'])->prefix('supplier')->group(function () {
    
    // API Token Management
    Route::controller(ApiTokenController::class)->prefix('api-tokens')->group(function () {
        Route::get('/', 'index')->name('supplier.api-tokens.index');
        Route::post('/', 'store')->name('supplier.api-tokens.store');
        Route::get('{id}', 'show')->name('supplier.api-tokens.show');
        Route::put('{id}', 'update')->name('supplier.api-tokens.update');
        Route::delete('{id}', 'destroy')->name('supplier.api-tokens.destroy');
        Route::get('stats', 'stats')->name('supplier.api-tokens.stats');
        Route::post('{id}/regenerate', 'regenerate')->name('supplier.api-tokens.regenerate');
    });

    // Zapier Integration Management
    Route::controller(ZapierController::class)->prefix('zapier')->group(function () {
        Route::get('status', 'status')->name('supplier.zapier.status');
        Route::get('logs', 'logs')->name('supplier.zapier.logs');
        Route::get('webhooks', 'webhooks')->name('supplier.zapier.webhooks');
        Route::post('connect', 'connect')->name('supplier.zapier.connect');
        Route::delete('disconnect', 'disconnect')->name('supplier.zapier.disconnect');
        Route::post('test', 'test')->name('supplier.zapier.test');
    });
});
