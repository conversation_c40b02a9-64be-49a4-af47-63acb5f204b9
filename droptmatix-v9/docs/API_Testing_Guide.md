# Dropmatix API Testing Guide

## Overview

This guide provides comprehensive testing procedures for the Dropmatix API system, covering both sandbox and live environments with proper security validation.

## Environment Setup

### Base URLs
- **Sandbox API**: `https://your-domain.com/api/sandbox`
- **Live API**: `https://your-domain.com/api/live`
- **Dashboard API**: `https://your-domain.com/api/supplier`

### Authentication
- **Sandbox**: No authentication required (open access with dummy data)
- **Live API**: Requires valid supplier API token
- **Dashboard**: Requires Sanctum authentication with supplier role

## Test Categories

### 1. Sandbox API Tests

#### Purpose
Verify that sandbox APIs return only dummy data and are accessible without authentication.

#### Test Cases

**TC-SB-001: Get Sandbox Orders**
```
GET /api/sandbox/orders
Expected: 200 OK
Headers: X-API-Environment: sandbox
Data: Max Mustermann dummy data only
```

**TC-SB-002: Get Sandbox Order Details**
```
GET /api/sandbox/orders/{id}
Expected: 200 OK or 404 Not Found
Headers: X-API-Environment: sandbox
Data: Max Mustermann dummy data in billing/shipping
```

**TC-SB-003: Get Sandbox MP Products**
```
GET /api/sandbox/mp/products
Expected: 200 OK
Headers: X-API-Environment: sandbox
Data: Sample products with no customer data
```

**TC-SB-004: Verify No Real Customer Data**
```
Validation: All responses must contain only:
- Name: "Max Mustermann"
- Company: "Mustermann GmbH"
- Street: "Musterstraße 12"
- ZIP: "12209"
- City: "Musterstadt"
- VAT ID: "DE ***********"
```

### 2. Live API Tests - Authorized Access

#### Purpose
Verify that live APIs work correctly with valid supplier tokens and return only supplier-specific data.

#### Test Cases

**TC-LA-001: Get Live Orders with Valid Token**
```
GET /api/live/orders
Headers: Authorization: Bearer {valid_supplier_token}
Expected: 200 OK
Headers: X-API-Environment: live
Data: Only orders belonging to the authenticated supplier
```

**TC-LA-002: Get Supplier Profile**
```
GET /api/live/supplier/profile
Headers: Authorization: Bearer {valid_supplier_token}
Expected: 200 OK
Data: Supplier profile information
```

**TC-LA-003: Update Order Status**
```
POST /api/live/orders/{id}/status
Headers: Authorization: Bearer {valid_supplier_token}
Body: {"status": 2}
Expected: 200 OK (only for supplier's own orders)
```

**TC-LA-004: Zapier Integration Status**
```
GET /api/live/supplier/zapier/status
Headers: Authorization: Bearer {valid_supplier_token}
Expected: 200 OK
Data: Zapier connection status and logs
```

### 3. Security Tests - Unauthorized Access

#### Purpose
Verify that security controls prevent unauthorized access to live APIs.

#### Test Cases

**TC-SEC-001: Live API without Token**
```
GET /api/live/orders
Headers: None
Expected: 401 Unauthorized
Response: {"success": false, "error_code": "UNAUTHORIZED"}
```

**TC-SEC-002: Live API with Customer Token**
```
GET /api/live/orders
Headers: Authorization: Bearer {customer_token}
Expected: 401 Unauthorized
Response: {"message": "Access denied: Supplier role required"}
```

**TC-SEC-003: Live API with Invalid Token**
```
GET /api/live/orders
Headers: Authorization: Bearer invalid_token_12345
Expected: 401 Unauthorized
Response: {"message": "Invalid or expired API token"}
```

**TC-SEC-004: Live API with Expired Token**
```
GET /api/live/orders
Headers: Authorization: Bearer {expired_token}
Expected: 401 Unauthorized
Response: {"message": "Invalid or expired API token"}
```

**TC-SEC-005: Cross-Supplier Data Access**
```
GET /api/live/orders/{other_supplier_order_id}
Headers: Authorization: Bearer {supplier_token}
Expected: 404 Not Found
Response: {"message": "Order not found or access denied"}
```

### 4. Token Management Tests

#### Purpose
Verify supplier API token management functionality.

#### Test Cases

**TC-TM-001: List API Tokens**
```
GET /api/supplier/api-tokens
Headers: Authorization: Bearer {sanctum_token}
Expected: 200 OK
Data: List of supplier's API tokens (without token values)
```

**TC-TM-002: Create API Token**
```
POST /api/supplier/api-tokens
Headers: Authorization: Bearer {sanctum_token}
Body: {"name": "Test Token", "expires_in_days": 30}
Expected: 200 OK
Data: New token with plain_token starting with "dmt_"
```

**TC-TM-003: Revoke API Token**
```
DELETE /api/supplier/api-tokens/{id}
Headers: Authorization: Bearer {sanctum_token}
Expected: 200 OK
Validation: Token should be marked as inactive
```

### 5. Zapier Integration Tests

#### Purpose
Verify Zapier integration management and webhook functionality.

#### Test Cases

**TC-ZI-001: Connect Zapier Integration**
```
POST /api/supplier/zapier/connect
Headers: Authorization: Bearer {sanctum_token}
Body: {"webhook_url": "https://hooks.zapier.com/...", "events": ["order.created"]}
Expected: 200 OK
```

**TC-ZI-002: Test Zapier Webhook**
```
POST /api/supplier/zapier/test
Headers: Authorization: Bearer {sanctum_token}
Expected: 200 OK
Validation: Webhook should receive test data
```

**TC-ZI-003: View Zapier Logs**
```
GET /api/supplier/zapier/logs
Headers: Authorization: Bearer {sanctum_token}
Expected: 200 OK
Data: List of webhook calls with status and timestamps
```

## GDPR Compliance Validation

### Data Sanitization Checks

1. **Sandbox Environment**
   - Verify no real customer names, addresses, or contact information
   - Confirm all data matches Max Mustermann template
   - Check that order notes are generic

2. **Live Environment**
   - Verify suppliers only see their own customer data
   - Confirm Zapier webhooks exclude sensitive customer information
   - Validate audit logs for data access

3. **Token Security**
   - Verify tokens are properly hashed in database
   - Confirm tokens are not logged in plain text
   - Validate token expiration and revocation

## Running Tests

### Using Postman
1. Import the provided Postman collection
2. Set environment variables:
   - `base_url`: Your API base URL
   - `supplier_token`: Valid supplier API token
   - `customer_token`: Valid customer token (for negative testing)
3. Run the collection with the Postman Runner

### Manual Testing Checklist

- [ ] All sandbox endpoints return dummy data only
- [ ] Live API requires valid supplier tokens
- [ ] Customer tokens are rejected for live API
- [ ] Invalid/expired tokens are rejected
- [ ] Suppliers can only access their own data
- [ ] Zapier integration works correctly
- [ ] Token management functions properly
- [ ] All responses include correct environment headers
- [ ] Error responses are properly formatted
- [ ] GDPR compliance is maintained

## Expected Response Headers

All API responses should include:
- `X-API-Environment`: Either "sandbox" or "live"
- `Content-Type`: "application/json"

## Error Response Format

All error responses should follow this format:
```json
{
  "success": false,
  "message": "Error description",
  "environment": "sandbox|live",
  "error_code": "ERROR_CODE" // For authentication errors
}
```

## Success Response Format

All success responses should follow this format:
```json
{
  "success": true,
  "data": {...},
  "environment": "sandbox|live",
  "supplier_id": 123 // For live API only
}
```
