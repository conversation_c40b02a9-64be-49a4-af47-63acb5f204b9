<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        then: function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api/live.php'));
        },
    )
    ->withBroadcasting(__DIR__.'/../routes/channels.php', [
        'middleware' => ['web', 'auth'],
        //        function (Request $request) {
        //            return $request->hasHeader('authorization') ? ['auth:sanctum'] : ['web', 'auth'];
        //        },
    ])
    ->withMiddleware(function (Middleware $middleware) {

        $middleware->priority([
            \Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests::class,
            \Illuminate\Cookie\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \Illuminate\Foundation\Http\Middleware\ValidateCsrfToken::class,
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            \Illuminate\Routing\Middleware\ThrottleRequests::class,
            \Illuminate\Routing\Middleware\ThrottleRequestsWithRedis::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \Illuminate\Contracts\Auth\Middleware\AuthenticatesRequests::class,
            \Illuminate\Auth\Middleware\Authorize::class,
            \App\Http\Middleware\OnlyLocal::class,
            \App\Http\Middleware\NotDtEntry::class,
            \App\Http\Middleware\HasTariff::class,
            \App\Http\Middleware\NotPaymentDue::class,
        ]);

        $middleware->validateCsrfTokens(
            except: ['rich-validation/*', 'droptienda-embedded-product/*']
        );

        $middleware->web(append: [
            \App\Http\Middleware\LanguageManager::class, // Language
            \App\Http\Middleware\NotBlock::class, // User not blocked
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->alias([
            'verified.2fa' => \Dropmatix\Dropmatix\Auth\Middleware\TwoFactorVerified::class,
            'role' => \App\Http\Middleware\Acl\RoleMiddleware::class,
            'parent_user' => \App\Http\Middleware\Acl\ParentUserMiddleware::class,
            'permission' => \App\Http\Middleware\Acl\PermissionMiddleware::class,
            'role_or_permission' => \App\Http\Middleware\Acl\RoleOrPermissionMiddleware::class,
            'ajax' => \App\Http\Middleware\AjaxRequest::class,
            'not_payment_due' => \App\Http\Middleware\NotPaymentDue::class,
            'has_tariff' => \App\Http\Middleware\HasTariff::class,
            'not_dt_entry' => \App\Http\Middleware\NotDtEntry::class,
            'billing.address' => \App\Http\Middleware\BillingAddress::class,
            'local' => \App\Http\Middleware\OnlyLocal::class,
            'product_limit' => \App\Http\Middleware\ProductLimit::class,
            'sanctum.2fa' => \App\Http\Middleware\SanctumTwoFa::class,
            'api.token.supplier' => \App\Http\Middleware\Api\SupplierTokenMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
