<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('zapier_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('zapier_integration_id');
            $table->string('event_type');
            $table->enum('status', ['success', 'failed', 'pending', 'retrying']);
            $table->text('message');
            $table->json('data')->nullable();
            $table->json('response')->nullable();
            $table->unsignedSmallInteger('http_status')->nullable();
            $table->unsignedInteger('duration_ms')->nullable();
            $table->unsignedTinyInteger('retry_count')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'created_at']);
            $table->index(['zapier_integration_id', 'created_at']);
            $table->index(['event_type', 'status']);
            $table->index('status');
            $table->index('created_at');

            // Foreign keys
            $table->foreign('user_id')->references('id')->on('cms_users')->onDelete('cascade');
            $table->foreign('zapier_integration_id')->references('id')->on('zapier_integrations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('zapier_logs');
    }
};
