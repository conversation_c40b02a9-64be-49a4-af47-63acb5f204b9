<?php

namespace App\Http\Middleware\Api;

use App\Models\User;
use App\Models\SupplierApiToken;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SupplierTokenMiddleware
{
    /**
     * Handle an incoming request for supplier API token authentication
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $this->extractToken($request);

        if (!$token) {
            return $this->unauthorizedResponse('API token is required');
        }

        $supplierToken = SupplierApiToken::where('token_hash', hash('sha256', $token))
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->with('user')
            ->first();

        if (!$supplierToken) {
            return $this->unauthorizedResponse('Invalid or expired API token');
        }

        // Verify user is a supplier
        if (!$supplierToken->user->isSupplier()) {
            return $this->unauthorizedResponse('Access denied: Supplier role required');
        }

        // Update last used timestamp
        $supplierToken->update(['last_used_at' => now()]);

        // Set the authenticated user in the request
        $request->setUserResolver(function () use ($supplierToken) {
            return $supplierToken->user;
        });

        // Add supplier context to request
        $request->attributes->set('supplier_token', $supplierToken);
        $request->attributes->set('supplier_id', $supplierToken->user->id);

        return $next($request);
    }

    /**
     * Extract token from request
     */
    private function extractToken(Request $request): ?string
    {
        // Check Authorization header
        $authHeader = $request->header('Authorization');
        if ($authHeader && str_starts_with($authHeader, 'Bearer ')) {
            return substr($authHeader, 7);
        }

        // Check query parameter as fallback
        return $request->query('api_token');
    }

    /**
     * Return unauthorized response
     */
    private function unauthorizedResponse(string $message): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'environment' => 'live',
            'error_code' => 'UNAUTHORIZED',
        ], 401)->header('X-API-Environment', 'live');
    }
}
