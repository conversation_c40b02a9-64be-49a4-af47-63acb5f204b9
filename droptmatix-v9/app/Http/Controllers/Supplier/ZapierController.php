<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use App\Models\ZapierIntegration;
use App\Models\ZapierLog;
use App\Services\Zapier\ZapierService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ZapierController extends Controller
{
    private ZapierService $zapierService;

    public function __construct(ZapierService $zapierService)
    {
        $this->zapierService = $zapierService;
    }

    /**
     * Get Zapier integration status for the authenticated supplier
     */
    public function status(): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $integration = ZapierIntegration::where('user_id', $user->id)->first();

        $status = [
            'connected' => $integration && $integration->is_active,
            'webhook_url' => $integration?->webhook_url,
            'last_sync' => $integration?->last_sync_at,
            'total_syncs' => $integration?->total_syncs ?? 0,
            'failed_syncs' => $integration?->failed_syncs ?? 0,
            'created_at' => $integration?->created_at,
        ];

        return response()->json([
            'success' => true,
            'data' => $status,
        ]);
    }

    /**
     * Get Zapier sync logs for the authenticated supplier
     */
    public function logs(Request $request): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $query = $request->only(['limit', 'status', 'date_from', 'date_to']) ?? [];
        $limit = isset($query['limit']) ? (int) $query['limit'] : 50;

        $logs = ZapierLog::where('user_id', $user->id)
            ->when(isset($query['status']), function ($q) use ($query) {
                return $q->where('status', $query['status']);
            })
            ->when(isset($query['date_from']), function ($q) use ($query) {
                return $q->where('created_at', '>=', $query['date_from']);
            })
            ->when(isset($query['date_to']), function ($q) use ($query) {
                return $q->where('created_at', '<=', $query['date_to']);
            })
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $logs,
        ]);
    }

    /**
     * Get active Zapier webhooks for the supplier
     */
    public function webhooks(): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $integration = ZapierIntegration::where('user_id', $user->id)->first();

        if (!$integration || !$integration->is_active) {
            return response()->json([
                'success' => true,
                'data' => [],
                'message' => 'No active Zapier integration found',
            ]);
        }

        // Get webhook information from Zapier service
        $webhooks = $this->zapierService->getActiveWebhooks($integration);

        return response()->json([
            'success' => true,
            'data' => $webhooks,
        ]);
    }

    /**
     * Connect Zapier integration
     */
    public function connect(Request $request): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $validated = $request->validate([
            'webhook_url' => 'required|url',
            'events' => 'sometimes|array',
            'events.*' => 'string|in:order.created,order.updated,order.completed,product.updated',
        ]);

        $events = $validated['events'] ?? ['order.created', 'order.updated'];

        $integration = ZapierIntegration::updateOrCreate(
            ['user_id' => $user->id],
            [
                'webhook_url' => $validated['webhook_url'],
                'events' => $events,
                'is_active' => true,
                'connected_at' => now(),
            ]
        );

        // Log the connection
        ZapierLog::create([
            'user_id' => $user->id,
            'zapier_integration_id' => $integration->id,
            'event_type' => 'connection',
            'status' => 'success',
            'message' => 'Zapier integration connected successfully',
            'data' => [
                'webhook_url' => $validated['webhook_url'],
                'events' => $events,
            ],
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Zapier integration connected successfully',
            'data' => $integration,
        ]);
    }

    /**
     * Disconnect Zapier integration
     */
    public function disconnect(): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $integration = ZapierIntegration::where('user_id', $user->id)->first();

        if (!$integration) {
            return response()->json([
                'success' => false,
                'message' => 'No Zapier integration found',
            ], 404);
        }

        $integration->update([
            'is_active' => false,
            'disconnected_at' => now(),
        ]);

        // Log the disconnection
        ZapierLog::create([
            'user_id' => $user->id,
            'zapier_integration_id' => $integration->id,
            'event_type' => 'disconnection',
            'status' => 'success',
            'message' => 'Zapier integration disconnected successfully',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Zapier integration disconnected successfully',
        ]);
    }

    /**
     * Test Zapier integration
     */
    public function test(): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $integration = ZapierIntegration::where('user_id', $user->id)
            ->where('is_active', true)
            ->first();

        if (!$integration) {
            return response()->json([
                'success' => false,
                'message' => 'No active Zapier integration found',
            ], 404);
        }

        // Send test data to Zapier
        $testData = [
            'event' => 'test',
            'supplier_id' => $user->id,
            'timestamp' => now()->toISOString(),
            'data' => [
                'message' => 'This is a test from your Dropmatix integration',
                'supplier_name' => $user->name,
            ],
        ];

        $result = $this->zapierService->sendWebhook($integration, $testData);

        // Log the test
        ZapierLog::create([
            'user_id' => $user->id,
            'zapier_integration_id' => $integration->id,
            'event_type' => 'test',
            'status' => $result['success'] ? 'success' : 'failed',
            'message' => $result['message'],
            'data' => $testData,
            'response' => $result['response'] ?? null,
        ]);

        return response()->json($result);
    }
}
