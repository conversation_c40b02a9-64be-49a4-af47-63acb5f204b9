<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use App\Models\SupplierApiToken;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ApiTokenController extends Controller
{
    /**
     * Get all API tokens for the authenticated supplier
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $tokens = SupplierApiToken::forUser($user->id)
            ->with('creator:id,name')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $tokens,
        ]);
    }

    /**
     * Create a new API token
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'expires_in_days' => 'sometimes|integer|min:1|max:365',
            'abilities' => 'sometimes|array',
            'abilities.*' => 'string',
        ]);

        $expiresInDays = $validated['expires_in_days'] ?? 365;
        $abilities = $validated['abilities'] ?? ['*'];

        $result = SupplierApiToken::generateToken(
            $user,
            $validated['name'],
            $abilities,
            $expiresInDays
        );

        return response()->json([
            'success' => true,
            'message' => 'API token created successfully',
            'data' => [
                'token' => $result['token'],
                'plain_token' => $result['plain_token'],
            ],
        ]);
    }

    /**
     * Show token details (without the actual token)
     */
    public function show($id): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $token = SupplierApiToken::forUser($user->id)
            ->with(['creator:id,name', 'revoker:id,name'])
            ->find($id);

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Token not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $token,
        ]);
    }

    /**
     * Update token (name and abilities only)
     */
    public function update(Request $request, $id): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $token = SupplierApiToken::forUser($user->id)->find($id);

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Token not found',
            ], 404);
        }

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'abilities' => 'sometimes|array',
            'abilities.*' => 'string',
        ]);

        $token->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Token updated successfully',
            'data' => $token->fresh(),
        ]);
    }

    /**
     * Revoke a token
     */
    public function destroy($id): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $token = SupplierApiToken::forUser($user->id)->find($id);

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Token not found',
            ], 404);
        }

        $token->revoke($user->id);

        return response()->json([
            'success' => true,
            'message' => 'Token revoked successfully',
        ]);
    }

    /**
     * Get token usage statistics
     */
    public function stats(): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $tokens = SupplierApiToken::forUser($user->id);

        $stats = [
            'total_tokens' => $tokens->count(),
            'active_tokens' => $tokens->active()->count(),
            'expired_tokens' => $tokens->where('expires_at', '<', now())->count(),
            'revoked_tokens' => $tokens->where('is_active', false)->count(),
            'last_used' => $tokens->whereNotNull('last_used_at')
                ->orderBy('last_used_at', 'desc')
                ->value('last_used_at'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Regenerate token (revoke old and create new)
     */
    public function regenerate(Request $request, $id): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isSupplier()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied: Supplier role required',
            ], 403);
        }

        $oldToken = SupplierApiToken::forUser($user->id)->find($id);

        if (!$oldToken) {
            return response()->json([
                'success' => false,
                'message' => 'Token not found',
            ], 404);
        }

        // Revoke old token
        $oldToken->revoke($user->id);

        // Create new token with same properties
        $result = SupplierApiToken::generateToken(
            $user,
            $oldToken->name . ' (Regenerated)',
            $oldToken->abilities,
            $oldToken->expires_at ? $oldToken->expires_at->diffInDays(now()) : 365
        );

        return response()->json([
            'success' => true,
            'message' => 'Token regenerated successfully',
            'data' => [
                'token' => $result['token'],
                'plain_token' => $result['plain_token'],
            ],
        ]);
    }
}
