<?php

namespace App\Http\Controllers\Api\Sandbox;

use App\Http\Controllers\Controller;
use App\Services\Sandbox\DummyDataService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SandboxMpController extends Controller
{
    private DummyDataService $dummyDataService;

    public function __construct(DummyDataService $dummyDataService)
    {
        $this->dummyDataService = $dummyDataService;
    }

    /**
     * Get categories for sandbox (safe data)
     */
    public function categories(): JsonResponse
    {
        // Categories are safe data, no customer information
        $categories = [
            ['id' => 1, 'name' => 'Electronics', 'parent_id' => null],
            ['id' => 2, 'name' => 'Clothing', 'parent_id' => null],
            ['id' => 3, 'name' => 'Home & Garden', 'parent_id' => null],
            ['id' => 4, 'name' => 'Sports', 'parent_id' => null],
            ['id' => 5, 'name' => 'Books', 'parent_id' => null],
        ];

        return response()->json([
            'success' => true,
            'data' => $categories,
            'environment' => 'sandbox',
        ])->header('X-API-Environment', 'sandbox');
    }

    /**
     * Get products for sandbox with dummy data
     */
    public function products(Request $request): JsonResponse
    {
        // Sample products with no customer data
        $products = [
            [
                'id' => 1,
                'name' => 'Sample Product 1',
                'ean' => '1234567890123',
                'price' => 29.99,
                'stock' => 100,
                'category_id' => 1,
                'description' => 'Sample product description for testing',
                'image' => 'https://via.placeholder.com/300x300',
            ],
            [
                'id' => 2,
                'name' => 'Sample Product 2',
                'ean' => '2345678901234',
                'price' => 49.99,
                'stock' => 50,
                'category_id' => 2,
                'description' => 'Another sample product for testing',
                'image' => 'https://via.placeholder.com/300x300',
            ],
            [
                'id' => 3,
                'name' => 'Sample Product 3',
                'ean' => '3456789012345',
                'price' => 19.99,
                'stock' => 200,
                'category_id' => 3,
                'description' => 'Third sample product for testing',
                'image' => 'https://via.placeholder.com/300x300',
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $products,
            'environment' => 'sandbox',
            'total' => count($products),
        ])->header('X-API-Environment', 'sandbox');
    }

    /**
     * Search EAN for sandbox
     */
    public function searchEan(Request $request): JsonResponse
    {
        $ean = $request->query('ean');

        if (empty($ean)) {
            return response()->json([
                'success' => false,
                'message' => 'EAN parameter is required',
                'environment' => 'sandbox',
            ], 400)->header('X-API-Environment', 'sandbox');
        }

        // Return dummy product data for any EAN search
        $product = [
            'id' => 999,
            'name' => 'Sample Product for EAN: ' . $ean,
            'ean' => $ean,
            'price' => 39.99,
            'stock' => 75,
            'category_id' => 1,
            'description' => 'Sample product found by EAN search in sandbox',
            'image' => 'https://via.placeholder.com/300x300',
        ];

        return response()->json([
            'success' => true,
            'data' => $product,
            'environment' => 'sandbox',
        ])->header('X-API-Environment', 'sandbox');
    }

    /**
     * Get warehouse stock for sandbox
     */
    public function warehouseStock(Request $request, $ean): JsonResponse
    {
        // Return dummy stock data
        $stockData = [
            'ean' => $ean,
            'stock' => rand(10, 500),
            'reserved' => rand(0, 10),
            'available' => rand(10, 490),
            'warehouse' => 'Sandbox Warehouse',
            'last_updated' => now()->toISOString(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stockData,
            'environment' => 'sandbox',
        ])->header('X-API-Environment', 'sandbox');
    }
}
