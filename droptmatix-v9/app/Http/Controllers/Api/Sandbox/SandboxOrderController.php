<?php

namespace App\Http\Controllers\Api\Sandbox;

use App\Http\Controllers\Controller;
use App\Models\Order\Order;
use App\Services\Sandbox\DummyDataService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SandboxOrderController extends Controller
{
    private DummyDataService $dummyDataService;

    public function __construct(DummyDataService $dummyDataService)
    {
        $this->dummyDataService = $dummyDataService;
    }

    /**
     * Get orders with dummy data for sandbox
     */
    public function index(Request $request): JsonResponse
    {
        // Get real orders structure but replace with dummy data
        $query = $request->only(['limit', 'sort', 'sort-by', 'search']) ?? [];
        $limit = isset($query['limit']) ? (int) $query['limit'] : 20;

        $orders = Order::with(['supplier:id,name'])
            ->limit($limit)
            ->orderBy('id', 'desc')
            ->get();

        // Replace all customer data with dummy data
        $sandboxOrders = $orders->map(function ($order) {
            return $this->dummyDataService->sanitizeOrderData($order);
        });

        return response()->json([
            'success' => true,
            'data' => $sandboxOrders,
            'environment' => 'sandbox',
        ])->header('X-API-Environment', 'sandbox');
    }

    /**
     * Get single order with dummy data
     */
    public function show(Request $request, $id): JsonResponse
    {
        $order = Order::with(['supplier:id,name', 'order_trackings.parcel'])
            ->find($id);

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found',
                'environment' => 'sandbox',
            ], 404)->header('X-API-Environment', 'sandbox');
        }

        // Replace with dummy data
        $sandboxOrder = $this->dummyDataService->sanitizeOrderData($order);

        return response()->json([
            'success' => true,
            'data' => $sandboxOrder,
            'environment' => 'sandbox',
        ])->header('X-API-Environment', 'sandbox');
    }

    /**
     * Get order details with dummy data
     */
    public function orderDetails(Request $request, $id): JsonResponse
    {
        $order = Order::with([
            'supplier:id,name',
            'order_trackings.parcel',
            'chat' => function ($query) {
                $query->withSum(['participants as unread_count'], 'support_chat_participants.unread_count');
            },
        ])->find($id);

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found',
                'environment' => 'sandbox',
            ], 404)->header('X-API-Environment', 'sandbox');
        }

        // Replace with dummy data
        $sandboxOrder = $this->dummyDataService->sanitizeOrderData($order);

        return response()->json([
            'success' => true,
            'data' => $sandboxOrder,
            'environment' => 'sandbox',
        ])->header('X-API-Environment', 'sandbox');
    }

    /**
     * Get countries list (safe data, no sanitization needed)
     */
    public function countries(): JsonResponse
    {
        // This is safe data, no customer information
        $countries = [
            ['code' => 'DE', 'name' => 'Germany'],
            ['code' => 'AT', 'name' => 'Austria'],
            ['code' => 'CH', 'name' => 'Switzerland'],
            ['code' => 'FR', 'name' => 'France'],
            ['code' => 'IT', 'name' => 'Italy'],
            ['code' => 'ES', 'name' => 'Spain'],
            ['code' => 'NL', 'name' => 'Netherlands'],
            ['code' => 'BE', 'name' => 'Belgium'],
        ];

        return response()->json([
            'success' => true,
            'data' => $countries,
            'environment' => 'sandbox',
        ])->header('X-API-Environment', 'sandbox');
    }
}
