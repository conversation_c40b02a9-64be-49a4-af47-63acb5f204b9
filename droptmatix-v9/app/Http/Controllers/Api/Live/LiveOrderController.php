<?php

namespace App\Http\Controllers\Api\Live;

use App\Http\Controllers\Controller;
use App\Models\Order\Order;
use App\Services\Live\SupplierDataFilter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class LiveOrderController extends Controller
{
    private SupplierDataFilter $supplierFilter;

    public function __construct(SupplierDataFilter $supplierFilter)
    {
        $this->supplierFilter = $supplierFilter;
    }

    /**
     * Get orders for authenticated supplier only
     */
    public function index(Request $request): JsonResponse
    {
        $supplierId = $request->attributes->get('supplier_id');
        
        $query = $request->only(['limit', 'sort', 'sort-by', 'search', 'status', 'date-from', 'date-to']) ?? [];
        $limit = isset($query['limit']) ? (int) $query['limit'] : 20;

        $orders = Order::with(['supplier:id,name', 'order_trackings.parcel'])
            ->where('cms_user_id', $supplierId) // Only supplier's own orders
            ->when(isset($query['status']), function ($q) use ($query) {
                return $q->where('status', $query['status']);
            })
            ->when(isset($query['date-from']), function ($q) use ($query) {
                return $q->where('order_date', '>=', $query['date-from']);
            })
            ->when(isset($query['date-to']), function ($q) use ($query) {
                return $q->where('order_date', '<=', $query['date-to']);
            })
            ->when(isset($query['search']), function ($q) use ($query) {
                return $q->where(function ($subQ) use ($query) {
                    $subQ->where('order_id_api', 'like', '%' . $query['search'] . '%')
                         ->orWhere('invoice_number', 'like', '%' . $query['search'] . '%');
                });
            })
            ->orderBy('id', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $orders,
            'environment' => 'live',
            'supplier_id' => $supplierId,
        ])->header('X-API-Environment', 'live');
    }

    /**
     * Get single order for authenticated supplier
     */
    public function show(Request $request, $id): JsonResponse
    {
        $supplierId = $request->attributes->get('supplier_id');

        $order = Order::with(['supplier:id,name', 'order_trackings.parcel'])
            ->where('cms_user_id', $supplierId) // Only supplier's own orders
            ->find($id);

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found or access denied',
                'environment' => 'live',
            ], 404)->header('X-API-Environment', 'live');
        }

        return response()->json([
            'success' => true,
            'data' => $order,
            'environment' => 'live',
        ])->header('X-API-Environment', 'live');
    }

    /**
     * Get order details for authenticated supplier
     */
    public function orderDetails(Request $request, $id): JsonResponse
    {
        $supplierId = $request->attributes->get('supplier_id');

        $order = Order::with([
            'supplier:id,name',
            'order_trackings.parcel',
            'chat' => function ($query) use ($supplierId) {
                $query->withSum(['participants as unread_count' => function ($p) use ($supplierId) {
                    $p->where('support_chat_participants.user_id', $supplierId);
                }], 'support_chat_participants.unread_count');
            },
        ])
        ->where('cms_user_id', $supplierId) // Only supplier's own orders
        ->find($id);

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found or access denied',
                'environment' => 'live',
            ], 404)->header('X-API-Environment', 'live');
        }

        return response()->json([
            'success' => true,
            'data' => $order,
            'environment' => 'live',
        ])->header('X-API-Environment', 'live');
    }

    /**
     * Update order (supplier can only update their own orders)
     */
    public function update(Request $request, $id): JsonResponse
    {
        $supplierId = $request->attributes->get('supplier_id');

        $order = Order::where('cms_user_id', $supplierId)
            ->where('is_locked', '<>', 1)
            ->find($id);

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found, locked, or access denied',
                'environment' => 'live',
            ], 404)->header('X-API-Environment', 'live');
        }

        $validated = $request->validate([
            'product_name' => 'sometimes|required|min:3',
            'ean' => 'sometimes|required|min:11',
            'client_note' => 'sometimes|string|max:1000',
        ]);

        $order->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Order updated successfully',
            'data' => $order->fresh(),
            'environment' => 'live',
        ])->header('X-API-Environment', 'live');
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, $id): JsonResponse
    {
        $supplierId = $request->attributes->get('supplier_id');

        $order = Order::where('cms_user_id', $supplierId)->find($id);

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found or access denied',
                'environment' => 'live',
            ], 404)->header('X-API-Environment', 'live');
        }

        $validated = $request->validate([
            'status' => 'required|integer|min:1|max:10',
        ]);

        $order->update(['status' => $validated['status']]);

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully',
            'data' => $order->fresh(),
            'environment' => 'live',
        ])->header('X-API-Environment', 'live');
    }

    /**
     * Save tracking information
     */
    public function saveTracking(Request $request, $id): JsonResponse
    {
        $supplierId = $request->attributes->get('supplier_id');

        $order = Order::where('cms_user_id', $supplierId)->find($id);

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found or access denied',
                'environment' => 'live',
            ], 404)->header('X-API-Environment', 'live');
        }

        $validated = $request->validate([
            'tracking_number' => 'required|string|max:100',
            'parcel_service' => 'required|string|max:50',
        ]);

        // Here you would typically save to order_trackings table
        // For now, just return success
        return response()->json([
            'success' => true,
            'message' => 'Tracking information saved successfully',
            'environment' => 'live',
        ])->header('X-API-Environment', 'live');
    }
}
