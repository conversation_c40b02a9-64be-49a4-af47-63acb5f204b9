<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ZapierIntegration extends Model
{
    use HasFactory;

    protected $table = 'zapier_integrations';

    protected $fillable = [
        'user_id',
        'webhook_url',
        'events',
        'is_active',
        'connected_at',
        'disconnected_at',
        'last_sync_at',
        'total_syncs',
        'failed_syncs',
        'settings',
    ];

    protected $casts = [
        'events' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean',
        'connected_at' => 'datetime',
        'disconnected_at' => 'datetime',
        'last_sync_at' => 'datetime',
    ];

    /**
     * Get the user that owns the integration
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the logs for this integration
     */
    public function logs(): HasMany
    {
        return $this->hasMany(ZapierLog::class);
    }

    /**
     * Scope for active integrations
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for supplier integrations
     */
    public function scopeForSupplier($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Check if integration supports a specific event
     */
    public function supportsEvent(string $event): bool
    {
        return in_array($event, $this->events ?? []);
    }

    /**
     * Increment sync counters
     */
    public function incrementSyncCount(bool $success = true): void
    {
        $this->increment('total_syncs');
        
        if (!$success) {
            $this->increment('failed_syncs');
        }

        $this->update(['last_sync_at' => now()]);
    }

    /**
     * Get success rate percentage
     */
    public function getSuccessRateAttribute(): float
    {
        if ($this->total_syncs === 0) {
            return 100.0;
        }

        $successfulSyncs = $this->total_syncs - $this->failed_syncs;
        return round(($successfulSyncs / $this->total_syncs) * 100, 2);
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute(): string
    {
        if (!$this->is_active) {
            return 'Disconnected';
        }

        if ($this->last_sync_at && $this->last_sync_at->diffInHours(now()) > 24) {
            return 'Inactive';
        }

        return 'Active';
    }

    /**
     * Get available events
     */
    public static function getAvailableEvents(): array
    {
        return [
            'order.created' => 'Order Created',
            'order.updated' => 'Order Updated',
            'order.completed' => 'Order Completed',
            'order.cancelled' => 'Order Cancelled',
            'product.updated' => 'Product Updated',
            'product.stock_changed' => 'Product Stock Changed',
        ];
    }

    /**
     * Validate webhook URL
     */
    public static function validateWebhookUrl(string $url): bool
    {
        // Basic URL validation
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        // Check if it's HTTPS (required for production)
        if (!str_starts_with($url, 'https://')) {
            return false;
        }

        // Check if it contains zapier.com (optional security check)
        if (!str_contains($url, 'zapier.com') && !str_contains($url, 'hooks.zapier.com')) {
            return false;
        }

        return true;
    }
}
