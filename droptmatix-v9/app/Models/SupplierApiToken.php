<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class SupplierApiToken extends Model
{
    use HasFactory;

    protected $table = 'supplier_api_tokens';

    protected $fillable = [
        'user_id',
        'name',
        'token_hash',
        'abilities',
        'expires_at',
        'last_used_at',
        'is_active',
        'created_by',
        'revoked_at',
        'revoked_by',
    ];

    protected $casts = [
        'abilities' => 'array',
        'expires_at' => 'datetime',
        'last_used_at' => 'datetime',
        'revoked_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    protected $hidden = [
        'token_hash',
    ];

    /**
     * Get the user that owns the token
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who created the token
     */
    public function creator(): <PERSON>ongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who revoked the token
     */
    public function revoker(): BelongsTo
    {
        return $this->belongsTo(User::class, 'revoked_by');
    }

    /**
     * Generate a new API token
     */
    public static function generateToken(User $user, string $name, array $abilities = ['*'], int $expiresInDays = 365): array
    {
        $plainToken = 'dmt_' . Str::random(64);
        $tokenHash = hash('sha256', $plainToken);

        $token = self::create([
            'user_id' => $user->id,
            'name' => $name,
            'token_hash' => $tokenHash,
            'abilities' => $abilities,
            'expires_at' => now()->addDays($expiresInDays),
            'is_active' => true,
            'created_by' => auth()->id() ?? $user->id,
        ]);

        return [
            'token' => $token,
            'plain_token' => $plainToken,
        ];
    }

    /**
     * Revoke the token
     */
    public function revoke(int $revokedBy = null): bool
    {
        return $this->update([
            'is_active' => false,
            'revoked_at' => now(),
            'revoked_by' => $revokedBy ?? auth()->id(),
        ]);
    }

    /**
     * Check if token is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if token is active and not expired
     */
    public function isValid(): bool
    {
        return $this->is_active && !$this->isExpired();
    }

    /**
     * Scope for active tokens
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope for user tokens
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get token abilities as string
     */
    public function getAbilitiesStringAttribute(): string
    {
        if (in_array('*', $this->abilities ?? [])) {
            return 'Full Access';
        }

        return implode(', ', $this->abilities ?? []);
    }
}
