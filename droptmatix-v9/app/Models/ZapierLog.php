<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ZapierLog extends Model
{
    use HasFactory;

    protected $table = 'zapier_logs';

    protected $fillable = [
        'user_id',
        'zapier_integration_id',
        'event_type',
        'status',
        'message',
        'data',
        'response',
        'http_status',
        'duration_ms',
        'retry_count',
    ];

    protected $casts = [
        'data' => 'array',
        'response' => 'array',
    ];

    /**
     * Get the user that owns the log
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the integration this log belongs to
     */
    public function integration(): BelongsTo
    {
        return $this->belongsTo(ZapierIntegration::class, 'zapier_integration_id');
    }

    /**
     * Scope for successful logs
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'success');
    }

    /**
     * Scope for failed logs
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for specific event type
     */
    public function scopeEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope for supplier logs
     */
    public function scopeForSupplier($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'success' => 'green',
            'failed' => 'red',
            'pending' => 'yellow',
            'retrying' => 'orange',
            default => 'gray',
        };
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_ms) {
            return 'N/A';
        }

        if ($this->duration_ms < 1000) {
            return $this->duration_ms . 'ms';
        }

        return round($this->duration_ms / 1000, 2) . 's';
    }

    /**
     * Create a log entry
     */
    public static function createLog(
        int $userId,
        int $integrationId,
        string $eventType,
        string $status,
        string $message,
        array $data = [],
        array $response = null,
        int $httpStatus = null,
        int $durationMs = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'zapier_integration_id' => $integrationId,
            'event_type' => $eventType,
            'status' => $status,
            'message' => $message,
            'data' => $data,
            'response' => $response,
            'http_status' => $httpStatus,
            'duration_ms' => $durationMs,
        ]);
    }

    /**
     * Get available event types
     */
    public static function getEventTypes(): array
    {
        return [
            'connection' => 'Connection',
            'disconnection' => 'Disconnection',
            'test' => 'Test',
            'order.created' => 'Order Created',
            'order.updated' => 'Order Updated',
            'order.completed' => 'Order Completed',
            'order.cancelled' => 'Order Cancelled',
            'product.updated' => 'Product Updated',
            'product.stock_changed' => 'Product Stock Changed',
        ];
    }

    /**
     * Get available statuses
     */
    public static function getStatuses(): array
    {
        return [
            'success' => 'Success',
            'failed' => 'Failed',
            'pending' => 'Pending',
            'retrying' => 'Retrying',
        ];
    }
}
