<?php

namespace App\Services\Sandbox;

use App\Models\Order\Order;
use Illuminate\Support\Str;

class DummyDataService
{
    /**
     * Max Mustermann dummy data as specified in requirements
     */
    private const DUMMY_DATA = [
        'name' => '<PERSON> Mustermann',
        'company' => 'Mustermann GmbH',
        'street' => 'Musterstraße 12',
        'zip_code' => '12209',
        'city' => 'Musterstadt',
        'vat_id' => 'DE ***********',
        'email' => '<EMAIL>',
        'phone' => '+49 30 12345678',
    ];

    /**
     * Sanitize order data by replacing all customer information with dummy data
     */
    public function sanitizeOrderData(Order $order): array
    {
        $orderArray = $order->toArray();

        // Replace billing address
        $orderArray['billing'] = $this->getDummyBillingAddress();
        
        // Replace shipping address
        $orderArray['shipping'] = $this->getDummyShippingAddress();
        
        // Replace customer_info if it exists
        if (isset($orderArray['customer_info'])) {
            $orderArray['customer_info'] = $this->sanitizeCustomerInfo($orderArray['customer_info']);
        }

        // Sanitize any other customer-related fields
        $orderArray = $this->sanitizeAdditionalFields($orderArray);

        return $orderArray;
    }

    /**
     * Get dummy billing address
     */
    private function getDummyBillingAddress(): array
    {
        return [
            'name' => self::DUMMY_DATA['name'],
            'company' => self::DUMMY_DATA['company'],
            'street' => self::DUMMY_DATA['street'],
            'zip_code' => self::DUMMY_DATA['zip_code'],
            'city' => self::DUMMY_DATA['city'],
            'country' => 'DE',
            'vat_id' => self::DUMMY_DATA['vat_id'],
            'email' => self::DUMMY_DATA['email'],
            'phone' => self::DUMMY_DATA['phone'],
        ];
    }

    /**
     * Get dummy shipping address
     */
    private function getDummyShippingAddress(): array
    {
        return [
            'name' => self::DUMMY_DATA['name'],
            'company' => self::DUMMY_DATA['company'],
            'street' => self::DUMMY_DATA['street'],
            'zip_code' => self::DUMMY_DATA['zip_code'],
            'city' => self::DUMMY_DATA['city'],
            'country' => 'DE',
            'email' => self::DUMMY_DATA['email'],
            'phone' => self::DUMMY_DATA['phone'],
        ];
    }

    /**
     * Sanitize customer_info field
     */
    private function sanitizeCustomerInfo($customerInfo): array
    {
        if (is_string($customerInfo)) {
            $customerInfo = json_decode($customerInfo, true) ?? [];
        }

        if (!is_array($customerInfo)) {
            return [];
        }

        // Replace all customer-related fields with dummy data
        return array_merge($customerInfo, [
            'name' => self::DUMMY_DATA['name'],
            'full_name' => self::DUMMY_DATA['name'],
            'company_name' => self::DUMMY_DATA['company'],
            'email' => self::DUMMY_DATA['email'],
            'phone' => self::DUMMY_DATA['phone'],
            'vat_number' => self::DUMMY_DATA['vat_id'],
            'address' => self::DUMMY_DATA['street'],
            'city' => self::DUMMY_DATA['city'],
            'zip_code' => self::DUMMY_DATA['zip_code'],
            'country' => 'DE',
        ]);
    }

    /**
     * Sanitize additional fields that might contain customer data
     */
    private function sanitizeAdditionalFields(array $orderArray): array
    {
        // List of fields that might contain customer data
        $fieldsToSanitize = [
            'client_note',
            'intro_note',
            'note',
        ];

        foreach ($fieldsToSanitize as $field) {
            if (isset($orderArray[$field]) && !empty($orderArray[$field])) {
                // Replace with generic note
                $orderArray[$field] = 'Test note for sandbox environment';
            }
        }

        return $orderArray;
    }

    /**
     * Get dummy customer data for any other use cases
     */
    public function getDummyCustomerData(): array
    {
        return self::DUMMY_DATA;
    }

    /**
     * Check if data contains real customer information (for validation)
     */
    public function containsRealCustomerData(array $data): bool
    {
        $dummyValues = array_values(self::DUMMY_DATA);
        
        // Check if any of the customer-related fields contain non-dummy data
        $customerFields = ['name', 'email', 'company', 'street', 'city', 'zip_code'];
        
        foreach ($customerFields as $field) {
            if (isset($data[$field]) && !in_array($data[$field], $dummyValues)) {
                return true;
            }
        }

        return false;
    }
}
