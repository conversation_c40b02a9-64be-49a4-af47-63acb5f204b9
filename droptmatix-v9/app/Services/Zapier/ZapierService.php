<?php

namespace App\Services\Zapier;

use App\Models\ZapierIntegration;
use App\Models\ZapierLog;
use App\Models\Order\Order;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ZapierService
{
    /**
     * Send webhook to Zapier
     */
    public function sendWebhook(ZapierIntegration $integration, array $data): array
    {
        $startTime = microtime(true);

        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'Dropmatix-Zapier-Integration/1.0',
                ])
                ->post($integration->webhook_url, $data);

            $duration = round((microtime(true) - $startTime) * 1000);
            $success = $response->successful();

            // Log the webhook call
            ZapierLog::createLog(
                $integration->user_id,
                $integration->id,
                $data['event'] ?? 'unknown',
                $success ? 'success' : 'failed',
                $success ? 'Webhook sent successfully' : 'Webhook failed: ' . $response->body(),
                $data,
                $response->json(),
                $response->status(),
                $duration
            );

            // Update integration stats
            $integration->incrementSyncCount($success);

            return [
                'success' => $success,
                'message' => $success ? 'Webhook sent successfully' : 'Webhook failed',
                'status_code' => $response->status(),
                'response' => $response->json(),
                'duration_ms' => $duration,
            ];

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000);

            // Log the error
            ZapierLog::createLog(
                $integration->user_id,
                $integration->id,
                $data['event'] ?? 'unknown',
                'failed',
                'Webhook error: ' . $e->getMessage(),
                $data,
                ['error' => $e->getMessage()],
                null,
                $duration
            );

            // Update integration stats
            $integration->incrementSyncCount(false);

            Log::error('Zapier webhook failed', [
                'integration_id' => $integration->id,
                'user_id' => $integration->user_id,
                'error' => $e->getMessage(),
                'data' => $data,
            ]);

            return [
                'success' => false,
                'message' => 'Webhook error: ' . $e->getMessage(),
                'status_code' => null,
                'response' => null,
                'duration_ms' => $duration,
            ];
        }
    }

    /**
     * Send order event to Zapier
     */
    public function sendOrderEvent(Order $order, string $event): void
    {
        $integration = ZapierIntegration::forSupplier($order->cms_user_id)
            ->active()
            ->first();

        if (!$integration || !$integration->supportsEvent($event)) {
            return;
        }

        $orderData = $this->formatOrderData($order);
        
        $webhookData = [
            'event' => $event,
            'timestamp' => now()->toISOString(),
            'supplier_id' => $order->cms_user_id,
            'order' => $orderData,
        ];

        $this->sendWebhook($integration, $webhookData);
    }

    /**
     * Format order data for Zapier (with GDPR compliance)
     */
    private function formatOrderData(Order $order): array
    {
        // Only include non-sensitive order information
        return [
            'id' => $order->id,
            'order_id_api' => $order->order_id_api,
            'status' => $order->status,
            'total' => $order->total,
            'currency' => $order->currency,
            'order_date' => $order->order_date?->toISOString(),
            'invoice_number' => $order->invoice_number,
            'payment_status' => $order->payment_status,
            'supplier_id' => $order->cms_user_id,
            // Note: Customer data is excluded for GDPR compliance
            'items_count' => $order->cart ? count(json_decode($order->cart, true) ?? []) : 0,
        ];
    }

    /**
     * Get active webhooks (mock implementation - Zapier doesn't provide this API)
     */
    public function getActiveWebhooks(ZapierIntegration $integration): array
    {
        // Since Zapier doesn't provide an API to list active Zaps,
        // we return the configured webhook information
        return [
            [
                'id' => 'webhook_' . $integration->id,
                'name' => 'Dropmatix Integration',
                'webhook_url' => $integration->webhook_url,
                'events' => $integration->events,
                'status' => $integration->status_text,
                'last_triggered' => $integration->last_sync_at?->toISOString(),
                'success_rate' => $integration->success_rate,
            ]
        ];
    }

    /**
     * Validate webhook URL by sending a test request
     */
    public function validateWebhookUrl(string $webhookUrl): array
    {
        try {
            $testData = [
                'event' => 'validation',
                'message' => 'This is a validation test from Dropmatix',
                'timestamp' => now()->toISOString(),
            ];

            $response = Http::timeout(10)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'Dropmatix-Zapier-Validation/1.0',
                ])
                ->post($webhookUrl, $testData);

            return [
                'valid' => $response->successful(),
                'status_code' => $response->status(),
                'message' => $response->successful() 
                    ? 'Webhook URL is valid and reachable' 
                    : 'Webhook URL validation failed',
            ];

        } catch (\Exception $e) {
            return [
                'valid' => false,
                'status_code' => null,
                'message' => 'Webhook URL validation error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get integration statistics
     */
    public function getIntegrationStats(ZapierIntegration $integration): array
    {
        $logs = ZapierLog::where('zapier_integration_id', $integration->id);

        return [
            'total_webhooks' => $logs->count(),
            'successful_webhooks' => $logs->successful()->count(),
            'failed_webhooks' => $logs->failed()->count(),
            'success_rate' => $integration->success_rate,
            'last_webhook' => $logs->orderBy('created_at', 'desc')->first()?->created_at,
            'average_response_time' => $logs->whereNotNull('duration_ms')->avg('duration_ms'),
            'events_breakdown' => $logs->selectRaw('event_type, count(*) as count')
                ->groupBy('event_type')
                ->pluck('count', 'event_type')
                ->toArray(),
        ];
    }
}
