<?php

namespace App\Services\Live;

use App\Models\Order\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class SupplierDataFilter
{
    /**
     * Filter orders query to only include supplier's own data
     */
    public function filterOrdersForSupplier(Builder $query, int $supplierId): Builder
    {
        return $query->where('cms_user_id', $supplierId);
    }

    /**
     * Filter products query to only include supplier's own data
     */
    public function filterProductsForSupplier(Builder $query, int $supplierId): Builder
    {
        return $query->where('user_id', $supplierId);
    }

    /**
     * Filter customers query to only include supplier's own customers
     */
    public function filterCustomersForSupplier(Builder $query, int $supplierId): Builder
    {
        return $query->where('user_id', $supplierId);
    }

    /**
     * Verify supplier owns the order
     */
    public function verifyOrderOwnership(int $orderId, int $supplierId): bool
    {
        return Order::where('id', $orderId)
            ->where('cms_user_id', $supplierId)
            ->exists();
    }

    /**
     * Verify user is a supplier
     */
    public function verifySupplierRole(User $user): bool
    {
        return $user->isSupplier();
    }

    /**
     * Get supplier's order statistics
     */
    public function getSupplierOrderStats(int $supplierId): array
    {
        $orders = Order::where('cms_user_id', $supplierId);

        return [
            'total_orders' => $orders->count(),
            'pending_orders' => $orders->where('status', 1)->count(),
            'completed_orders' => $orders->where('status', 5)->count(),
            'total_revenue' => $orders->sum('total'),
            'this_month_orders' => $orders->whereMonth('order_date', now()->month)->count(),
            'this_month_revenue' => $orders->whereMonth('order_date', now()->month)->sum('total'),
        ];
    }

    /**
     * Get supplier's recent activity
     */
    public function getSupplierRecentActivity(int $supplierId, int $limit = 10): array
    {
        $recentOrders = Order::where('cms_user_id', $supplierId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->select('id', 'order_id_api', 'status', 'total', 'created_at')
            ->get();

        return $recentOrders->map(function ($order) {
            return [
                'type' => 'order',
                'id' => $order->id,
                'description' => "Order #{$order->order_id_api} - Status: {$order->status}",
                'amount' => $order->total,
                'date' => $order->created_at,
            ];
        })->toArray();
    }

    /**
     * Validate supplier has access to specific resource
     */
    public function validateSupplierAccess(int $supplierId, string $resourceType, int $resourceId): bool
    {
        switch ($resourceType) {
            case 'order':
                return $this->verifyOrderOwnership($resourceId, $supplierId);
            
            case 'product':
                return \App\Models\Product\Product::where('id', $resourceId)
                    ->where('user_id', $supplierId)
                    ->exists();
            
            case 'customer':
                return \App\Models\Order\Customer::where('id', $resourceId)
                    ->where('user_id', $supplierId)
                    ->exists();
            
            default:
                return false;
        }
    }

    /**
     * Log supplier API access for audit purposes
     */
    public function logSupplierApiAccess(int $supplierId, string $endpoint, string $method, array $data = []): void
    {
        // Log to database or file for audit trail
        \Log::info('Supplier API Access', [
            'supplier_id' => $supplierId,
            'endpoint' => $endpoint,
            'method' => $method,
            'timestamp' => now(),
            'data' => $data,
        ]);
    }
}
